/*
 * Copyright 2011-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.solum.xplain.shared.spring.mongo;

import java.util.List;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationPipeline;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.query.ConvertingParameterAccessor;
import org.springframework.data.mongodb.repository.query.MongoQueryMethod;
import org.springframework.data.mongodb.repository.query.PartTreeMongoQuery;
import org.springframework.data.repository.query.ResultProcessor;
import org.springframework.data.repository.query.ValueExpressionDelegate;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

/**
 * This class is heavily derived from {@link
 * org.springframework.data.mongodb.repository.query.StringBasedAggregation}, so falls under the
 * same license.
 */
@Slf4j
@Getter
public class PartTreeMongoAggregation extends PartTreeMongoQuery implements ExtensibleAggregation {
  private final MongoOperations mongoOperations;
  private final AggregationParameterResolver aggregationParameterResolver;

  public PartTreeMongoAggregation(
      MongoQueryMethod queryMethod,
      MongoOperations operations,
      ValueExpressionDelegate evaluationContextProvider,
      AggregationParameterResolver aggregationParameterResolver) {
    super(queryMethod, operations, evaluationContextProvider);
    this.mongoOperations = operations;
    this.aggregationParameterResolver = aggregationParameterResolver;
  }

  private Aggregation createAggregation(ConvertingParameterAccessor accessor) {
    return fromQuery(super.createQuery(accessor));
  }

  private Aggregation fromQuery(Query query) {
    return Aggregation.newAggregation(Aggregation.match(context -> query.getQueryObject()));
  }

  @Override
  @Nullable
  protected Object doExecute(
      MongoQueryMethod method,
      ResultProcessor resultProcessor,
      ConvertingParameterAccessor accessor,
      @Nullable Class<?> typeToRead) {

    Assert.notNull(typeToRead, "typeToRead must not be null for aggregation");

    var aggregationOperations =
        getDetectedFieldAggregationOperations(
            accessor, typeToRead, method.getEntityInformation().getCollectionEntity().getType());
    if (aggregationOperations.isEmpty() && !hasSortAggregationOperations(accessor)) {
      return super.doExecute(method, resultProcessor, accessor, typeToRead);
    }

    return executeUsingAggregation(method, accessor, typeToRead, aggregationOperations);
  }

  /**
   * This method is derived from {@link
   * org.springframework.data.mongodb.repository.query.StringBasedAggregation#doExecute(MongoQueryMethod,
   * ResultProcessor, ConvertingParameterAccessor, Class)}
   */
  @SneakyThrows
  private <T> @Nullable Object executeUsingAggregation(
      MongoQueryMethod method,
      ConvertingParameterAccessor accessor,
      Class<T> typeToRead,
      List<AggregationOperation> aggregationOperations) {
    var fromDsl = createAggregation(accessor);
    for (AggregationOperation operation : aggregationOperations) {
      fromDsl.getPipeline().add(operation);
    }

    AggregationPipeline pipeline = fromDsl.getPipeline();

    return executePipelineAsAggregation(
        method, accessor, typeToRead, pipeline, isExistsQuery(), isCountQuery());
  }
}
