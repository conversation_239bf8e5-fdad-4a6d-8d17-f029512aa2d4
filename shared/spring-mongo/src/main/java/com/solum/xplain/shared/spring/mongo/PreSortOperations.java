package com.solum.xplain.shared.spring.mongo;

import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.util.Streamable;

/**
 * This interface is used to provide pre-sort operations for MongoDB aggregations. It can be
 * implemented by a subclass of {@link org.springframework.data.domain.Sort} that needs to perform
 * additional operations (e.g. to derive sortable properties) before sorting the results.
 */
public interface PreSortOperations {
  /**
   * Operations returned by this method will be added to the aggregation pipeline immediately before
   * the sort operation.
   *
   * @return a streamable collection of {@link AggregationOperation} to be applied before sorting
   */
  Streamable<AggregationOperation> preSortOperations();
}
