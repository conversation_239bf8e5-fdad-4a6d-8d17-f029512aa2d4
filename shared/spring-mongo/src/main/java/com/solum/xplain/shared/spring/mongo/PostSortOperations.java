package com.solum.xplain.shared.spring.mongo;

import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.util.Streamable;

/**
 * This interface is used to provide post-sort operations for MongoDB aggregations. It can be
 * implemented by a subclass of {@link org.springframework.data.domain.Sort} that needs to perform
 * additional operations (e.g. to remove derived properties from the final output) after sorting the
 * results.
 */
public interface PostSortOperations {
  /**
   * Operations returned by this method will be added to the aggregation pipeline immediately after
   * the sort operation.
   *
   * @return a streamable collection of {@link AggregationOperation} to be applied after sorting
   */
  Streamable<AggregationOperation> postSortOperations();
}
