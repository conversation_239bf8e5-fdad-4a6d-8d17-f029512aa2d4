# PriceIndex configuration

# The providers are the classes that define the enum
# The key is of the form 'provider.full.class.name'
# The value is either
#  'constants', the public static final constants from the class
#  'lookup', the class implements NamedLookup with a no-args constructor
#  'instance', the class has a static field named INSTANCE that is of type NamedLookup
[providers]
com.solum.xplain.extensions.index.ExtendedPriceIndices = constants


# The set of alternate names
# The key is the alternate name
# The value is the standard name (loaded by a provider)
[alternates]
