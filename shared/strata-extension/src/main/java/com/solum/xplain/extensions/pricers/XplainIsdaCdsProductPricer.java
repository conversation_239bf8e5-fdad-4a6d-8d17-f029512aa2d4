package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.collect.tuple.Pair;
import com.opengamma.strata.pricer.common.PriceType;
import com.opengamma.strata.pricer.credit.AccrualOnDefaultFormula;
import com.opengamma.strata.pricer.credit.ConstantRecoveryRates;
import com.opengamma.strata.pricer.credit.CreditDiscountFactors;
import com.opengamma.strata.pricer.credit.CreditRatesProvider;
import com.opengamma.strata.pricer.credit.DoublesScheduleGenerator;
import com.opengamma.strata.pricer.credit.IsdaCdsProductPricer;
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities;
import com.opengamma.strata.pricer.credit.RecoveryRates;
import com.opengamma.strata.product.credit.CreditCouponPaymentPeriod;
import com.opengamma.strata.product.credit.ResolvedCds;
import java.time.LocalDate;

public class XplainIsdaCdsProductPricer extends IsdaCdsProductPricer {

  public static final IsdaCdsProductPricer DEFAULT =
      new XplainIsdaCdsProductPricer(AccrualOnDefaultFormula.ORIGINAL_ISDA);

  public XplainIsdaCdsProductPricer(AccrualOnDefaultFormula formula) {
    super(formula);
  }

  @Override
  protected double riskyAnnuity(
      ResolvedCds cds,
      CreditDiscountFactors discountFactors,
      LegalEntitySurvivalProbabilities survivalProbabilities,
      LocalDate referenceDate,
      LocalDate stepinDate,
      LocalDate effectiveStartDate,
      PriceType priceType) {

    double pv = 0d;
    for (CreditCouponPaymentPeriod coupon : cds.getPaymentPeriods()) {
      if (discountFactors.getValuationDate().isBefore(coupon.getPaymentDate())) {
        double q = survivalProbabilities.survivalProbability(coupon.getEffectiveEndDate());
        double p = discountFactors.discountFactor(coupon.getPaymentDate());
        pv += coupon.getYearFraction() * p * q;
      }
    }

    if (cds.getPaymentOnDefault().isAccruedInterest()) {
      // This is needed so that the code is consistent with ISDA C when the Markit `fix' is used.
      LocalDate start =
          cds.getPaymentPeriods().size() == 1 ? effectiveStartDate : cds.getAccrualStartDate();
      DoubleArray integrationSchedule =
          DoublesScheduleGenerator.getIntegrationsPoints(
              discountFactors.relativeYearFraction(start),
              discountFactors.relativeYearFraction(cds.getProtectionEndDate()),
              discountFactors.getParameterKeys(),
              survivalProbabilities.getParameterKeys());
      for (CreditCouponPaymentPeriod coupon : cds.getPaymentPeriods()) {
        pv +=
            singlePeriodAccrualOnDefault(
                coupon,
                effectiveStartDate,
                integrationSchedule,
                discountFactors,
                survivalProbabilities);
      }
    }
    // roll to the cash settle date
    double df = discountFactors.discountFactor(referenceDate);
    pv /= df;

    if (priceType.isCleanPrice()) {
      pv -= cds.accruedYearFraction(discountFactors.getValuationDate());
    }

    return pv;
  }

  /**
   * Calculates the par spread of the CDS product. Override for the OG method, where riskyAnnuity is
   * calculated using {@link PriceType}.DIRTY instead of {@link PriceType}.CLEAN.
   *
   * <p>The par spread is a coupon rate such that the clean PV is 0. The result is represented in
   * decimal form.
   *
   * @param cds the product
   * @param ratesProvider the rates provider
   * @param referenceDate the reference date
   * @param refData the reference data
   * @return the par spread
   */
  @Override
  public double parSpread(
      ResolvedCds cds,
      CreditRatesProvider ratesProvider,
      LocalDate referenceDate,
      ReferenceData refData) {

    ArgChecker.isTrue(
        cds.getProtectionEndDate().isAfter(ratesProvider.getValuationDate()),
        "CDS already expired");
    LocalDate stepinDate =
        cds.getStepinDateOffset().adjust(ratesProvider.getValuationDate(), refData);
    LocalDate effectiveStartDate = cds.calculateEffectiveStartDate(stepinDate);
    double recoveryRate = recoveryRate(cds, ratesProvider);
    Pair<CreditDiscountFactors, LegalEntitySurvivalProbabilities> rates =
        reduceDiscountFactors(cds, ratesProvider);
    double protectionLeg =
        protectionLeg(
            cds,
            rates.getFirst(),
            rates.getSecond(),
            referenceDate,
            effectiveStartDate,
            recoveryRate); //Recovery rate is used to calculate the protection leg
    double riskyAnnuity =
        riskyAnnuity(
            cds,
            rates.getFirst(),
            rates.getSecond(),
            referenceDate,
            stepinDate,
            effectiveStartDate,
            PriceType.DIRTY);
    return protectionLeg / riskyAnnuity;
  }

  private double recoveryRate(ResolvedCds cds, CreditRatesProvider ratesProvider) {

      //The method recoveryRates has 2 implementations Default and Constant.
      //The default implementation does have lookup for recoveryRate curve ids
      //and  then this Curve curve = (Curve)this.marketData.getValue(curveId);
      RecoveryRates recoveryRates = ratesProvider.recoveryRates(cds.getLegalEntityId());
    ArgChecker.isTrue(
        recoveryRates instanceof ConstantRecoveryRates,
        "recoveryRates must be ConstantRecoveryRates");
    return recoveryRates.recoveryRate(cds.getProtectionEndDate());
  }
}
