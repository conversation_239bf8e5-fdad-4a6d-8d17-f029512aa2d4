package com.solum.xplain.extensions.calibrator;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.collect.tuple.Pair;
import com.opengamma.strata.market.curve.NodalCurve;
import com.opengamma.strata.pricer.common.PriceType;
import com.opengamma.strata.pricer.credit.CreditDiscountFactors;
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider;
import com.opengamma.strata.pricer.credit.IsdaCdsTradePricer;
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors;
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities;
import com.opengamma.strata.product.credit.ResolvedCdsTrade;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import java.util.Objects;
import java.util.function.IntToDoubleFunction;

/** Functions used for building the matrices required by XplainCreditCurveCalibrator */
public final class XplainCreditCurveCalibrationFunctions {

  private XplainCreditCurveCalibrationFunctions() {}

  /**
   * @return array, where each element is the trade's PV sensitivities to the input zero coupon
   *     rates
   */
  public static DoubleArray dPVDZeroCouponRates(
      IsdaCdsTradePricer tradePricer,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray discountCurveZcRates,
      ResolvedCdsTrade cdsTrade,
      CdsQuoteConvention quoteConvention,
      Currency currency,
      ReferenceData referenceData) {

    // shift each zero coupon rate and calculate the trade's change in PV
    IntToDoubleFunction tradeZeroCouponRateSensitivityFunction =
        i ->
            getTradeZeroCouponRateSensitivity(
                tradePricer,
                cdsTrade,
                currency,
                creditRatesProvider,
                quoteConvention,
                shiftElement(discountCurveZcRates, i),
                referenceData);

    return DoubleArray.of(discountCurveZcRates.size(), tradeZeroCouponRateSensitivityFunction);
  }

  /**
   * @return array, where each element is the trade's PV sensitivities to the input zero hazard
   *     rates
   */
  public static DoubleArray dPVDZeroHazardRates(
      IsdaCdsTradePricer tradePricer,
      ImmutableCreditRatesProvider creditRatesProvider,
      StandardId legalEntityId,
      NodalCurve creditCurve,
      DoubleArray creditCurveZhRates,
      ResolvedCdsTrade cdsTrade,
      CdsQuoteConvention quoteConvention,
      Currency currency,
      ReferenceData referenceData) {

    // shift each zero hazard rate and calculate the trade's change in PV
    IntToDoubleFunction tradeZeroHazardRateSensitivityFunction =
        index ->
            getTradeZeroHazardRateSensitivity(
                tradePricer,
                cdsTrade,
                legalEntityId,
                currency,
                creditCurve,
                quoteConvention,
                creditRatesProvider,
                shiftElement(creditCurveZhRates, index),
                referenceData);

    return DoubleArray.of(creditCurveZhRates.size(), tradeZeroHazardRateSensitivityFunction);
  }

  /**
   * @return array where each element represents the trade's PV sensitivity to a zero coupon rate in
   *     discount curve
   */
  @VisibleForTesting
  private static double getTradeZeroCouponRateSensitivity(
      IsdaCdsTradePricer tradePricer,
      ResolvedCdsTrade trade,
      Currency currency,
      ImmutableCreditRatesProvider creditRatesProvider,
      CdsQuoteConvention quoteConvention,
      DoubleArray shiftedDiscountCurveZcRates,
      ReferenceData referenceData) {
    ImmutableCreditRatesProvider ratesProviderWithShiftedZeroCouponRate =
        generateRatesProviderWithShiftedZeroCouponRate(
            currency, creditRatesProvider, shiftedDiscountCurveZcRates);
    return calculateTradeSensitivityValue(
        tradePricer,
        trade,
        creditRatesProvider,
        ratesProviderWithShiftedZeroCouponRate,
        quoteConvention,
        referenceData);
  }

  /**
   * @return the trade's change in PV when using a ratesProvider with a shifted zero hazard rate
   */
  @VisibleForTesting
  private static double getTradeZeroHazardRateSensitivity(
      IsdaCdsTradePricer tradePricer,
      ResolvedCdsTrade trade,
      StandardId legalEntityId,
      Currency currency,
      NodalCurve creditCurve,
      CdsQuoteConvention quoteConvention,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray shiftedDiscountCurveZcRates,
      ReferenceData referenceData) {
    ImmutableCreditRatesProvider ratesProviderWithShiftedZeroHazardRate =
        generateRatesProviderWithShiftedZeroHazardRate(
            currency, legalEntityId, creditCurve, creditRatesProvider, shiftedDiscountCurveZcRates);
    return calculateTradeSensitivityValue(
        tradePricer,
        trade,
        creditRatesProvider,
        ratesProviderWithShiftedZeroHazardRate,
        quoteConvention,
        referenceData);
  }

  /**
   * @return input rates where element at index is shifted by 1 basis point
   */
  public static DoubleArray shiftElement(DoubleArray original, int index) {
    return original.with(
        index, original.get(index) + XplainCreditCurveCalibrator.ZERO_RATE_SHIFT_SIZE);
  }

  /**
   * @param currency calibration currency
   * @param creditRatesProvider original ratesProvider, holds curves and their respective x and y
   *     values
   * @param shiftedCurveZcRates array of zc rates, where one has been shifted
   * @return rates provider with overridden y values for discount curve(s)
   */
  @VisibleForTesting
  private static ImmutableCreditRatesProvider generateRatesProviderWithShiftedZeroCouponRate(
      Currency currency,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray shiftedCurveZcRates) {

    CreditDiscountFactors discountFactors = creditRatesProvider.discountFactors(currency);
    ArgChecker.isTrue(
        creditRatesProvider.discountFactors(currency) instanceof IsdaCreditDiscountFactors,
        "Discount factors for {} must be IsdaCreditDiscountFactors",
        currency);
    IsdaCreditDiscountFactors creditDiscountFactors = (IsdaCreditDiscountFactors) discountFactors;
    NodalCurve discountCurve = creditDiscountFactors.getCurve();

    // override discount curve y values
    NodalCurve shiftedDiscountCurve = discountCurve.withYValues(shiftedCurveZcRates);

    // override discount curve in rates provider
    IsdaCreditDiscountFactors newCreditDiscountFactors =
        IsdaCreditDiscountFactors.of(
            currency, creditDiscountFactors.getValuationDate(), shiftedDiscountCurve);

    ImmutableMap<Currency, CreditDiscountFactors> discountCurvesMap =
        (ImmutableMap<Currency, CreditDiscountFactors>)
            creditRatesProvider.toBuilder().get("discountCurves");
    ImmutableMap.Builder<Currency, CreditDiscountFactors> newDiscountCurvesMap =
        ImmutableMap.builder();
    discountCurvesMap.entrySet().stream()
        .filter(e -> e.getKey() != currency)
        .forEach(newDiscountCurvesMap::put);
    newDiscountCurvesMap.put(currency, newCreditDiscountFactors);

    return creditRatesProvider.toBuilder().discountCurves(newDiscountCurvesMap.build()).build();
  }

  /**
   * @param currency calibration currency
   * @param legalEntityId StandardId
   * @param creditCurve credit curve
   * @param creditRatesProvider original ratesProvider, holds curves and their respective x and y
   *     values
   * @param shiftedCurveZhRates array of zh rates, where one has been shifted
   * @return rates provider with overridden y values for credit curve(s)
   */
  @VisibleForTesting
  private static ImmutableCreditRatesProvider generateRatesProviderWithShiftedZeroHazardRate(
      Currency currency,
      StandardId legalEntityId,
      NodalCurve creditCurve,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray shiftedCurveZhRates) {

    // override credit curve y values
    NodalCurve shiftedCreditCurve = creditCurve.withYValues(shiftedCurveZhRates);

    // override credit curve in rates provider
    IsdaCreditDiscountFactors newCreditDiscountFactors =
        IsdaCreditDiscountFactors.of(
            currency, creditRatesProvider.getValuationDate(), shiftedCreditCurve);
    LegalEntitySurvivalProbabilities newLegalEntitySurvivalProbabilities =
        LegalEntitySurvivalProbabilities.of(legalEntityId, newCreditDiscountFactors);

    ImmutableMap<Pair<StandardId, Currency>, LegalEntitySurvivalProbabilities> creditCurvesMap =
        (ImmutableMap<Pair<StandardId, Currency>, LegalEntitySurvivalProbabilities>)
            creditRatesProvider.toBuilder().get("creditCurves");
    ImmutableMap.Builder<Pair<StandardId, Currency>, LegalEntitySurvivalProbabilities>
        newCreditCurvesMap = ImmutableMap.builder();
    creditCurvesMap.entrySet().stream()
        .filter(e -> !Objects.equals(e.getKey(), Pair.of(legalEntityId, currency)))
        .forEach(newCreditCurvesMap::put);

    newCreditCurvesMap.put(Pair.of(legalEntityId, currency), newLegalEntitySurvivalProbabilities);
    return creditRatesProvider.toBuilder().creditCurves(newCreditCurvesMap.build()).build();
  }

  /**
   * Calculates change in present value or change in par spread when using creditRatesProvider vs
   * shiftedCreditRatesProvider
   */
  @VisibleForTesting
  private static double calculateTradeSensitivityValue(
      IsdaCdsTradePricer tradePricer,
      ResolvedCdsTrade trade,
      ImmutableCreditRatesProvider creditRatesProvider,
      ImmutableCreditRatesProvider shiftedCreditRatesProvider,
      CdsQuoteConvention quoteConvention,
      ReferenceData referenceData) {

    double oneBasisPoint = 1e-4;
    double numberBasisPoints = XplainCreditCurveCalibrator.ZERO_RATE_SHIFT_SIZE / oneBasisPoint;
    double shiftedValue = 0;
    double unshiftedValue = 0;

    if (quoteConvention.equals(CdsQuoteConvention.PAR_SPREAD)) {
      unshiftedValue =
          tradePricer.price(trade, creditRatesProvider, PriceType.CLEAN, referenceData);
      shiftedValue =
          tradePricer.price(trade, shiftedCreditRatesProvider, PriceType.CLEAN, referenceData);
    } else {
      unshiftedValue = tradePricer.parSpread(trade, creditRatesProvider, referenceData);
      shiftedValue = tradePricer.parSpread(trade, shiftedCreditRatesProvider, referenceData);
    }

    return (shiftedValue - unshiftedValue) / numberBasisPoints;
  }
}
