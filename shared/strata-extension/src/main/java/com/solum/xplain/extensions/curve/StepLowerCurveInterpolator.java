/*
 * Copyright (C) 2016 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.curve;

import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.market.curve.interpolator.AbstractBoundCurveInterpolator;
import com.opengamma.strata.market.curve.interpolator.BoundCurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.BoundCurveInterpolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import java.io.Serial;
import java.io.Serializable;

/**
 * Interpolator implementation that uses upper step interpolation.
 *
 * <p>The interpolated value at <i>x</i> s.t. <i>x<sub>1</sub> < x =< x<sub>2</sub></i> is the value
 * at <i>x<sub>2</sub></i>. The flat extrapolation is implemented outside the data range.
 */
public final class StepLowerCurveInterpolator implements CurveInterpolator, Serializable {

  /** The curve name. */
  public static final String NAME = "StepLower";

  /** The curve instance. */
  public static final CurveInterpolator INSTANCE = new StepLowerCurveInterpolator();

  /** The serialization version id. */
  @Serial private static final long serialVersionUID = 1L;

  /**
   * The small parameter.
   *
   * <p>A value will be treated as 0 if its magnitude is smaller than this parameter.
   */
  private static final double EPS = 1.0e-12;

  /** Restricted constructor. */
  private StepLowerCurveInterpolator() {}

  // resolve instance
  @Serial
  private Object readResolve() {
    return INSTANCE;
  }

  // -------------------------------------------------------------------------
  @Override
  public String getName() {
    return NAME;
  }

  @Override
  public BoundCurveInterpolator bind(DoubleArray xValues, DoubleArray yValues) {
    return new Bound(xValues, yValues);
  }

  // -------------------------------------------------------------------------
  @Override
  public String toString() {
    return NAME;
  }

  // -------------------------------------------------------------------------

  /** Bound curve. */
  static class Bound extends AbstractBoundCurveInterpolator {

    private final double[] xValues;
    private final double[] yValues;
    private final int maxIndex;

    Bound(DoubleArray xValues, DoubleArray yValues) {
      super(xValues, yValues);
      this.xValues = xValues.toArrayUnsafe();
      this.yValues = yValues.toArrayUnsafe();
      this.maxIndex = xValues.size() - 1;
    }

    Bound(
        Bound base,
        BoundCurveExtrapolator extrapolatorLeft,
        BoundCurveExtrapolator extrapolatorRight) {
      super(base, extrapolatorLeft, extrapolatorRight);
      this.xValues = base.xValues;
      this.yValues = base.yValues;
      this.maxIndex = base.maxIndex;
    }

    // -------------------------------------------------------------------------
    @Override
    protected double doInterpolate(double xValue) {
      int upperIndex = getLowerBoundIndex(xValue);
      return yValues[upperIndex];
    }

    @Override
    protected double doFirstDerivative(double xValue) {
      return 0d;
    }

    @Override
    protected DoubleArray doParameterSensitivity(double xValue) {
      double[] result = new double[yValues.length];
      int upperIndex = getLowerBoundIndex(xValue);
      result[upperIndex] = 1d;
      return DoubleArray.ofUnsafe(result);
    }

    @Override
    public BoundCurveInterpolator bind(
        BoundCurveExtrapolator extrapolatorLeft, BoundCurveExtrapolator extrapolatorRight) {

      return new Bound(this, extrapolatorLeft, extrapolatorRight);
    }

    private int getLowerBoundIndex(double xValue) {
      int lowerIndex = lowerBoundIndex(xValue, xValues);
      if (lowerIndex < maxIndex && Math.abs(xValues[lowerIndex + 1] - xValue) < EPS) {
        return lowerIndex + 1;
      }
      return lowerIndex;
    }
  }
}
