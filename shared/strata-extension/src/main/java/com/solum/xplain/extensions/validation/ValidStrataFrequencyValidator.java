package com.solum.xplain.extensions.validation;

import static java.lang.String.format;

import com.opengamma.strata.basics.schedule.Frequency;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ValidStrataFrequencyValidator
    implements ConstraintValidator<ValidStrataFrequency, String> {

  private static final String INVALID_FREQUENCY_ERROR_TEMPLATE = "Invalid frequency: %s";

  @Override
  public boolean isValid(String frequency, ConstraintValidatorContext context) {
    if (StringUtils.isEmpty(frequency)) {
      return true;
    }

    try {
      Frequency.parse(frequency);
      return true;
    } catch (IllegalArgumentException e) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(
              format(INVALID_FREQUENCY_ERROR_TEMPLATE, e.getCause().getLocalizedMessage()))
          .addConstraintViolation();
      return false;
    }
  }
}
