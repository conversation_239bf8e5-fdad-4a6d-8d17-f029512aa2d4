package com.solum.xplain.extensions.xccyfixedois;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.swap.SwapTrade;
import java.time.LocalDate;
import java.time.Period;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.joda.beans.gen.PropertyDefinition;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode
@ToString
public final class XCcyFixedOvernightSwapTemplate {
  @PropertyDefinition(validate = "notNull")
  private final Period periodToStart;

  @PropertyDefinition(validate = "notNull", overrideGet = true)
  private final Tenor tenor;

  @PropertyDefinition(validate = "notNull", overrideGet = true)
  private final XCcyFixedOvernightSwapConvention convention;

  public static XCcyFixedOvernightSwapTemplate of(
      Tenor tenor, XCcyFixedOvernightSwapConvention convention) {
    return new XCcyFixedOvernightSwapTemplate(Period.ZERO, tenor, convention);
  }

  public CurrencyPair getCurrencyPair() {
    return convention.getCurrencyPair();
  }

  public SwapTrade createTrade(
      LocalDate tradeDate,
      BuySell buySell,
      double notionalSpreadLeg,
      double notionalFlatLeg,
      double fixedRate,
      ReferenceData refData) {
    return this.convention.createTrade(
        tradeDate,
        this.periodToStart,
        this.tenor,
        buySell,
        notionalSpreadLeg,
        notionalFlatLeg,
        fixedRate,
        refData);
  }

  public Tenor getTenor() {
    return this.tenor;
  }

  public XCcyFixedOvernightSwapConvention getConvention() {
    return this.convention;
  }
}
