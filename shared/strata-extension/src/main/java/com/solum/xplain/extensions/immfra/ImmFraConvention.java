package com.solum.xplain.extensions.immfra;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.SequenceDate;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.TradeConvention;
import com.opengamma.strata.product.TradeInfo;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.fra.FraTrade;
import java.time.LocalDate;
import org.joda.convert.FromString;

public interface ImmFraConvention extends TradeConvention, Named {

  /**
   * Obtains an instance from the specified unique name.
   *
   * @param uniqueName the unique name
   * @return the convention
   * @throws IllegalArgumentException if the name is not known
   */
  @FromString
  static ImmFraConvention of(String uniqueName) {
    ArgChecker.notNull(uniqueName, "uniqueName");
    return ExtendedImmutableImmFraConventions.ALL.stream()
        .filter(c -> c.getName().equals(uniqueName))
        .findFirst()
        .orElseThrow(
            () -> new IllegalArgumentException("ImmFraConvention not found: " + uniqueName));
  }

  IborIndex getIndex();

  FraTrade createTrade(
      LocalDate tradeDate,
      SequenceDate sequenceDate,
      BuySell buySell,
      double notional,
      double fixedRate,
      ReferenceData refData);

  /**
   * Creates a trade based on this convention.
   *
   * <p>This returns a trade based on the specified dates. The notional is unsigned, with buy/sell
   * determining the direction of the trade. If buying the FRA, the floating rate is received from
   * the counterparty, with the fixed rate being paid. If selling the FRA, the floating rate is paid
   * to the counterparty, with the fixed rate being received.
   *
   * @param tradeDate the date of the trade
   * @param startDate the start date, which should be adjusted to be a valid business day
   * @param endDate the end date, which should be adjusted to be a valid business day
   * @param paymentDate the payment date, which should be adjusted to be a valid business day
   * @param buySell the buy/sell flag
   * @param notional the notional amount, in the payment currency of the template
   * @param fixedRate the fixed rate, typically derived from the market
   * @return the trade
   */
  default FraTrade toTrade(
      LocalDate tradeDate,
      LocalDate startDate,
      LocalDate endDate,
      LocalDate paymentDate,
      BuySell buySell,
      double notional,
      double fixedRate) {

    TradeInfo tradeInfo = TradeInfo.of(tradeDate);
    return toTrade(tradeInfo, startDate, endDate, paymentDate, buySell, notional, fixedRate);
  }

  FraTrade toTrade(
      TradeInfo tradeInfo,
      LocalDate startDate,
      LocalDate endDate,
      LocalDate paymentDate,
      BuySell buySell,
      double notional,
      double fixedRate);
}
