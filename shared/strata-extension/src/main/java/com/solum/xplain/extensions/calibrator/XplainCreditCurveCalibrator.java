package com.solum.xplain.extensions.calibrator;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.collect.Guavate;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.collect.array.DoubleMatrix;
import com.opengamma.strata.collect.tuple.Pair;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.CurveInfoType;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.CurveParameterSize;
import com.opengamma.strata.market.curve.IsdaCreditCurveDefinition;
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix;
import com.opengamma.strata.market.curve.NodalCurve;
import com.opengamma.strata.market.param.CurrencyParameterSensitivities;
import com.opengamma.strata.market.param.ParameterMetadata;
import com.opengamma.strata.market.param.ResolvedTradeParameterMetadata;
import com.opengamma.strata.market.sensitivity.PointSensitivities;
import com.opengamma.strata.math.impl.matrix.CommonsMatrixAlgebra;
import com.opengamma.strata.math.impl.matrix.MatrixAlgebra;
import com.opengamma.strata.pricer.common.PriceType;
import com.opengamma.strata.pricer.credit.AccrualOnDefaultFormula;
import com.opengamma.strata.pricer.credit.ArbitrageHandling;
import com.opengamma.strata.pricer.credit.CreditDiscountFactors;
import com.opengamma.strata.pricer.credit.CreditRatesProvider;
import com.opengamma.strata.pricer.credit.FastCreditCurveCalibrator;
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider;
import com.opengamma.strata.pricer.credit.IsdaCdsTradePricer;
import com.opengamma.strata.pricer.credit.IsdaCompliantCreditCurveCalibrator;
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors;
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities;
import com.opengamma.strata.pricer.credit.RecoveryRates;
import com.opengamma.strata.product.credit.CdsCalibrationTrade;
import com.opengamma.strata.product.credit.CdsQuote;
import com.opengamma.strata.product.credit.ResolvedCdsTrade;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import com.solum.xplain.extensions.curve.node.XplainCdsIsdaCreditCurveNode;
import java.time.LocalDate;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/** Custom Xplain Credit Curve Calibrator */
public class XplainCreditCurveCalibrator extends IsdaCompliantCreditCurveCalibrator {
  private static final ArbitrageHandling DEFAULT_ARBITRAGE_HANDLING = ArbitrageHandling.IGNORE;
  private static final AccrualOnDefaultFormula DEFAULT_FORMULA =
      AccrualOnDefaultFormula.ORIGINAL_ISDA;
  private static final MatrixAlgebra MATRIX_ALGEBRA = new CommonsMatrixAlgebra();
  public static final CurveInfoType<JacobianCalibrationMatrix> D_ZHRATES_D_SWAP_RATES_JACOBIAN =
      CurveInfoType.of("Jacobian_d_zhr_d_swap_rates");
  public static final double ZERO_RATE_SHIFT_SIZE = 1.0e-6;

  private final IsdaCdsTradePricer tradePricer;

  public XplainCreditCurveCalibrator() {
    this.tradePricer = new IsdaCdsTradePricer(DEFAULT_FORMULA);
  }

  public LegalEntitySurvivalProbabilities calibrate(
      List<XplainCdsIsdaCreditCurveNode> curveNodes,
      CurveName name,
      MarketData marketData,
      ImmutableCreditRatesProvider ratesProvider,
      DayCount definitionDayCount,
      Currency definitionCurrency,
      boolean computeJacobian,
      boolean storeTrade,
      ReferenceData refData) {

    Iterator<StandardId> legalEntities =
        curveNodes.stream()
            .map(XplainCdsIsdaCreditCurveNode::getLegalEntityId)
            .collect(Collectors.toSet())
            .iterator();
    StandardId legalEntityId = legalEntities.next();
    ArgChecker.isFalse(legalEntities.hasNext(), "legal entity must be common to curve nodes");
    Iterator<Currency> currencies =
        curveNodes.stream()
            .map(n -> n.getTemplate().getConvention().getCurrency())
            .collect(Collectors.toSet())
            .iterator();
    Currency currency = currencies.next();
    ArgChecker.isFalse(currencies.hasNext(), "currency must be common to curve nodes");
    ArgChecker.isTrue(
        definitionCurrency.equals(currency),
        "curve definition currency must be the same as the currency of CDS");
    Iterator<CdsQuoteConvention> quoteConventions =
        curveNodes.stream()
            .map(XplainCdsIsdaCreditCurveNode::getQuoteConvention)
            .collect(Collectors.toSet())
            .iterator();
    CdsQuoteConvention quoteConvention = quoteConventions.next();
    ArgChecker.isFalse(
        quoteConventions.hasNext(), "quote convention must be common to curve nodes");
    LocalDate valuationDate = marketData.getValuationDate();
    ArgChecker.isTrue(
        valuationDate.equals(marketData.getValuationDate()),
        "ratesProvider and marketDate must be based on the same valuation date");
    CreditDiscountFactors discountFactors = ratesProvider.discountFactors(currency);
    ArgChecker.isTrue(
        definitionDayCount.equals(discountFactors.getDayCount()),
        "credit curve and discount curve must be based on the same day count convention");
    RecoveryRates recoveryRates = ratesProvider.recoveryRates(legalEntityId);

    int nNodes = curveNodes.size();
    double[] coupons = new double[nNodes];
    double[] pufs = new double[nNodes];
    double[][] diag = new double[nNodes][nNodes];
    Builder<ResolvedCdsTrade> tradesBuilder = ImmutableList.builder();
    for (int i = 0; i < nNodes; i++) {
      CdsCalibrationTrade tradeCalibration = curveNodes.get(i).trade(1d, marketData, refData);
      ResolvedCdsTrade trade = tradeCalibration.getUnderlyingTrade().resolve(refData);
      tradesBuilder.add(trade);
      double[] temp =
          getStandardQuoteForm(
              trade,
              tradeCalibration.getQuote(),
              valuationDate,
              discountFactors,
              recoveryRates,
              computeJacobian,
              refData);
      coupons[i] = temp[0];
      pufs[i] = temp[1];
      diag[i][i] = temp[2];
    }
    ImmutableList<ResolvedCdsTrade> trades = tradesBuilder.build();
    NodalCurve nodalCurve =
        calibrate(
            trades,
            DoubleArray.ofUnsafe(coupons),
            DoubleArray.ofUnsafe(pufs),
            name,
            valuationDate,
            discountFactors,
            recoveryRates,
            refData);

    if (computeJacobian) {
      nodalCurve =
          curveWithDZeroHazardDCreditRatesMatrix(
              legalEntityId,
              currency,
              valuationDate,
              nodalCurve,
              ratesProvider,
              quoteConvention,
              name,
              nNodes,
              refData,
              diag,
              trades);

      nodalCurve =
          curveWithDZeroHazardDSwapRatesMatrix(
              legalEntityId,
              currency,
              valuationDate,
              nodalCurve,
              ratesProvider,
              quoteConvention,
              refData,
              trades);
    }

    ImmutableList<ParameterMetadata> parameterMetadata;
    if (storeTrade) {
      parameterMetadata =
          IntStream.range(0, nNodes)
              .mapToObj(
                  n ->
                      ResolvedTradeParameterMetadata.of(
                          trades.get(n), curveNodes.get(n).getLabel()))
              .collect(Guavate.toImmutableList());
    } else {
      parameterMetadata =
          IntStream.range(0, nNodes)
              .mapToObj(
                  n ->
                      curveNodes.get(n).metadata(trades.get(n).getProduct().getProtectionEndDate()))
              .collect(Guavate.toImmutableList());
    }
    nodalCurve =
        nodalCurve.withMetadata(nodalCurve.getMetadata().withParameterMetadata(parameterMetadata));

    return LegalEntitySurvivalProbabilities.of(
        legalEntityId, IsdaCreditDiscountFactors.of(currency, valuationDate, nodalCurve));
  }

  @Override
  public LegalEntitySurvivalProbabilities calibrate(
      IsdaCreditCurveDefinition curveDefinition,
      MarketData marketData,
      ImmutableCreditRatesProvider ratesProvider,
      ReferenceData refData) {
    ArgChecker.isTrue(
        curveDefinition.getCurveValuationDate().equals(ratesProvider.getValuationDate()),
        "ratesProvider and curveDefinition must be based on the same valuation date");
    ImmutableList<XplainCdsIsdaCreditCurveNode> curveNodes =
        curveDefinition.getCurveNodes().stream()
            .filter(XplainCdsIsdaCreditCurveNode.class::isInstance)
            .map(XplainCdsIsdaCreditCurveNode.class::cast)
            .collect(Guavate.toImmutableList());
    return this.calibrate(
        curveNodes,
        curveDefinition.getName(),
        marketData,
        ratesProvider,
        curveDefinition.getDayCount(),
        curveDefinition.getCurrency(),
        curveDefinition.isComputeJacobian(),
        curveDefinition.isStoreNodeTrade(),
        refData);
  }

  @Override
  public NodalCurve calibrate(
      List<ResolvedCdsTrade> calibrationCDSs,
      DoubleArray flactionalSpreads,
      DoubleArray pointsUpfront,
      CurveName name,
      LocalDate valuationDate,
      CreditDiscountFactors discountFactors,
      RecoveryRates recoveryRates,
      ReferenceData refData) {

    return new FastCreditCurveCalibrator(DEFAULT_FORMULA, DEFAULT_ARBITRAGE_HANDLING)
        .calibrate(
            calibrationCDSs,
            flactionalSpreads,
            pointsUpfront,
            name,
            valuationDate,
            discountFactors,
            recoveryRates,
            refData);
  }

  // populates nodal curve with ∂ZHRates/∂CreditRates matrix
  @VisibleForTesting
  private NodalCurve curveWithDZeroHazardDCreditRatesMatrix(
      StandardId legalEntityId,
      Currency currency,
      LocalDate valuationDate,
      NodalCurve nodalCurve,
      ImmutableCreditRatesProvider ratesProvider,
      CdsQuoteConvention quoteConvention,
      CurveName name,
      int nNodes,
      ReferenceData refData,
      double[][] diag,
      ImmutableList<ResolvedCdsTrade> trades) {

    // generate ∂ZHRates/∂CreditRates (OG calculated jacobian for CS01 calculations)
    JacobianCalibrationMatrix dZhrDCrJacobian =
        dZeroHazardRatesDCreditRates(
            legalEntityId,
            currency,
            valuationDate,
            nodalCurve,
            ratesProvider,
            quoteConvention,
            name,
            nNodes,
            refData,
            diag,
            trades);

    return nodalCurve.withMetadata(
        nodalCurve.getMetadata().withInfo(CurveInfoType.JACOBIAN, dZhrDCrJacobian));
  }

  // populates nodal curve with ∂ZHRates/∂SwapRates matrix
  @VisibleForTesting
  private NodalCurve curveWithDZeroHazardDSwapRatesMatrix(
      StandardId legalEntityId,
      Currency currency,
      LocalDate valuationDate,
      NodalCurve nodalCurve,
      ImmutableCreditRatesProvider ratesProvider,
      CdsQuoteConvention quoteConvention,
      ReferenceData refData,
      ImmutableList<ResolvedCdsTrade> trades) {

    // generate ∂ZHRates/∂SwapRates matrix
    JacobianCalibrationMatrix dZHRatesDRatesMatrix =
        dZeroHazardRatesDSwapRates(
            legalEntityId,
            currency,
            valuationDate,
            nodalCurve,
            ratesProvider,
            quoteConvention,
            refData,
            trades);

    return nodalCurve.withMetadata(
        nodalCurve.getMetadata().withInfo(D_ZHRATES_D_SWAP_RATES_JACOBIAN, dZHRatesDRatesMatrix));
  }

  /**
   * @return ∂ZHRates/∂CreditRates matrix - partial derivative of ZHRates with respect to credit
   *     rates (OG copy)
   */
  @VisibleForTesting
  private JacobianCalibrationMatrix dZeroHazardRatesDCreditRates(
      StandardId legalEntityId,
      Currency currency,
      LocalDate valuationDate,
      NodalCurve nodalCurve,
      ImmutableCreditRatesProvider ratesProvider,
      CdsQuoteConvention quoteConvention,
      CurveName name,
      int nNodes,
      ReferenceData refData,
      double[][] diag,
      ImmutableList<ResolvedCdsTrade> trades) {

    LegalEntitySurvivalProbabilities creditCurve =
        LegalEntitySurvivalProbabilities.of(
            legalEntityId, IsdaCreditDiscountFactors.of(currency, valuationDate, nodalCurve));
    ImmutableCreditRatesProvider ratesProviderNew =
        ratesProvider.toBuilder()
            .creditCurves(ImmutableMap.of(Pair.of(legalEntityId, currency), creditCurve))
            .build();
    Function<ResolvedCdsTrade, DoubleArray> sensiFunc =
        quoteConvention.equals(CdsQuoteConvention.PAR_SPREAD)
            ? getParSpreadSensitivityFunction(ratesProviderNew, name, currency, refData)
            : getPointsUpfrontSensitivityFunction(ratesProviderNew, name, currency, refData);
    DoubleMatrix sensi =
        DoubleMatrix.ofArrayObjects(nNodes, nNodes, i -> sensiFunc.apply(trades.get(i)));
    sensi = (DoubleMatrix) MATRIX_ALGEBRA.multiply(DoubleMatrix.ofUnsafe(diag), sensi);
    return JacobianCalibrationMatrix.of(
        ImmutableList.of(CurveParameterSize.of(name, nNodes)), MATRIX_ALGEBRA.getInverse(sensi));
  }

  /**
   * @return ∂ZHRates/∂SwapRates matrix - partial derivative of ZHRates with respect to swap rates
   */
  @VisibleForTesting
  private JacobianCalibrationMatrix dZeroHazardRatesDSwapRates(
      StandardId legalEntityId,
      Currency currency,
      LocalDate valuationDate,
      NodalCurve nodalCurve,
      ImmutableCreditRatesProvider ratesProvider,
      CdsQuoteConvention quoteConvention,
      ReferenceData refData,
      ImmutableList<ResolvedCdsTrade> trades) {

    CreditDiscountFactors discountFactors = ratesProvider.discountFactors(currency);
    ArgChecker.isTrue(
        ratesProvider.discountFactors(currency) instanceof IsdaCreditDiscountFactors,
        "Discount factors for {} must be IsdaCreditDiscountFactors",
        currency);
    IsdaCreditDiscountFactors creditDiscountFactors = (IsdaCreditDiscountFactors) discountFactors;

    LegalEntitySurvivalProbabilities creditCurve =
        LegalEntitySurvivalProbabilities.of(
            legalEntityId, IsdaCreditDiscountFactors.of(currency, valuationDate, nodalCurve));
    ImmutableCreditRatesProvider ratesProviderNew =
        ratesProvider.toBuilder()
            .creditCurves(ImmutableMap.of(Pair.of(legalEntityId, currency), creditCurve))
            .build();
    NodalCurve discountCurve = creditDiscountFactors.getCurve();

    // extract discount curve's ∂ZCRates/∂SwapRates matrix (n x n, n = # discount curve
    // nodes)
    JacobianCalibrationMatrix dZCRatesDSwapRatesMatrix =
        discountCurve.getMetadata().getInfo(CurveInfoType.JACOBIAN);

    DoubleArray discountCurveZcRates = discountCurve.getYValues();

    // generate ∂ZHRates/∂ZCRates matrix
    DoubleMatrix dZHRatesDZCRatesMatrix =
        dZeroHazardRatesDZeroCouponRates(
            trades,
            currency,
            ratesProviderNew,
            discountCurveZcRates,
            quoteConvention,
            refData,
            legalEntityId,
            nodalCurve);

    // ∂ZHRates/∂SwapRates = ∂ZHRates/∂ZCRates * ∂ZCRates/∂SwapRates (chain rule)
    DoubleMatrix dZHRatesDSwapRatesMatrix =
        (DoubleMatrix)
            MATRIX_ALGEBRA.multiply(
                dZHRatesDZCRatesMatrix, dZCRatesDSwapRatesMatrix.getJacobianMatrix());

    return JacobianCalibrationMatrix.of(
        ImmutableList.of(
            CurveParameterSize.of(discountCurve.getName(), discountCurve.getParameterCount())),
        dZHRatesDSwapRatesMatrix);
  }

  /**
   * @return ∂ZHRates/∂ZCRates matrix --------------------------------------------------------------
   *     Generation of ∂ZHRates/∂ZCRates is done using the implicit function theorem. We calculate
   *     two matrices, [∂F/∂ZHRates]^-1 and ∂F/∂ZCRates, where F is the vector-valued function that
   *     returns the value of the m CDS trades, and then multiply them (in that order) to form
   *     ∂ZHRates/∂ZCRates
   */
  @VisibleForTesting
  private DoubleMatrix dZeroHazardRatesDZeroCouponRates(
      ImmutableList<ResolvedCdsTrade> trades,
      Currency currency,
      ImmutableCreditRatesProvider ratesProviderNew,
      DoubleArray discountCurveZcRates,
      CdsQuoteConvention quoteConvention,
      ReferenceData refData,
      StandardId legalEntityId,
      NodalCurve creditCurve) {

    DoubleArray creditCurveZhRates = creditCurve.getYValues();

    // generate ∂F/∂ZCRates matrix (m x n, m = credit curve nodes, n = discount curve nodes)
    DoubleMatrix dFDZCRatesMatrix =
        generateDFDZeroCouponRates(
            trades, currency, ratesProviderNew, discountCurveZcRates, quoteConvention, refData);

    // generate [∂F/∂ZHRates]^-1 matrix (m x m, m = credit curve nodes)
    DoubleMatrix dFDZHRatesInvMatrix =
        generateDFDZeroHazardRatesInv(
            trades,
            legalEntityId,
            creditCurve,
            currency,
            ratesProviderNew,
            creditCurveZhRates,
            quoteConvention,
            refData);

    dFDZHRatesInvMatrix = (DoubleMatrix) MATRIX_ALGEBRA.scale(dFDZHRatesInvMatrix, -1d);

    // ∂ZHRates/∂ZCRates = [∂F/∂ZHRates]^-1 * ∂F/∂ZCRates (implicit function theorem)
    // gives m x n matrix (m = credit curve nodes, n = discount curve nodes)
    return (DoubleMatrix) MATRIX_ALGEBRA.multiply(dFDZHRatesInvMatrix, dFDZCRatesMatrix);
  }

  // ∂F/dZCRates matrix (m x n, m = # credit curve nodes, n = # nodes in discount
  // curve)
  // F is the vector-valued function that returns the value of the m CDS trades
  // for each CDS trade (generated from a node), calculate PV sensitivity with respect to ZC rates
  @VisibleForTesting
  private DoubleMatrix generateDFDZeroCouponRates(
      ImmutableList<ResolvedCdsTrade> trades,
      Currency currency,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray discountCurveZcRates,
      CdsQuoteConvention quoteConvention,
      ReferenceData referenceData) {

    int nDiscountCurveNodes = discountCurveZcRates.size();

    // column function -> input is a resolvedCdsTrade (row), output is array of trade's
    // sensitivities to discount curve's zero coupon rates
    Function<ResolvedCdsTrade, DoubleArray> nodeZcTradePvSensitivityFunction =
        resolvedCdsTrade ->
            XplainCreditCurveCalibrationFunctions.dPVDZeroCouponRates(
                tradePricer,
                creditRatesProvider,
                discountCurveZcRates,
                resolvedCdsTrade,
                quoteConvention,
                currency,
                referenceData);

    // matrix generation -> returns n x m matrix
    return DoubleMatrix.ofArrayObjects(
        trades.size(),
        nDiscountCurveNodes,
        i -> nodeZcTradePvSensitivityFunction.apply(trades.get(i)));
  }

  // [∂F/dZHRates]^-1 matrix (m x m, m = # credit curve nodes)
  // for each CDS trade (generated from a node), calculate PV sensitivities with respect to zero
  // hazard rates
  @VisibleForTesting
  private DoubleMatrix generateDFDZeroHazardRatesInv(
      ImmutableList<ResolvedCdsTrade> trades,
      StandardId legalEntityId,
      NodalCurve creditCurve,
      Currency currency,
      ImmutableCreditRatesProvider creditRatesProvider,
      DoubleArray creditCurveZhRates,
      CdsQuoteConvention quoteConvention,
      ReferenceData referenceData) {

    int nCreditCurveNodes = creditCurveZhRates.size();

    // column function -> input is a resolvedCdsTrade (row), output is array of trade's
    // sensitivities to zero hazard rates
    Function<ResolvedCdsTrade, DoubleArray> tradeZeroHazardRateSensitivitiesFunction =
        resolvedCdsTrade ->
            XplainCreditCurveCalibrationFunctions.dPVDZeroHazardRates(
                tradePricer,
                creditRatesProvider,
                legalEntityId,
                creditCurve,
                creditCurveZhRates,
                resolvedCdsTrade,
                quoteConvention,
                currency,
                referenceData);

    DoubleMatrix doubleMatrix =
        DoubleMatrix.ofArrayObjects(
            trades.size(),
            nCreditCurveNodes,
            i -> tradeZeroHazardRateSensitivitiesFunction.apply(trades.get(i)));
    return MATRIX_ALGEBRA.getInverse(doubleMatrix);
  }

  private double[] getStandardQuoteForm(
      ResolvedCdsTrade calibrationCds,
      CdsQuote marketQuote,
      LocalDate valuationDate,
      CreditDiscountFactors discountFactors,
      RecoveryRates recoveryRates,
      boolean computeJacobian,
      ReferenceData refData) {

    double[] res = new double[3];
    res[2] = 1d;
    if (marketQuote.getQuoteConvention().equals(CdsQuoteConvention.PAR_SPREAD)) {
      res[0] = marketQuote.getQuotedValue();
    } else if (marketQuote.getQuoteConvention().equals(CdsQuoteConvention.QUOTED_SPREAD)) {
      double qSpread = marketQuote.getQuotedValue();
      CurveName curveName = CurveName.of("quoteConvertCurve");
      NodalCurve tempCreditCurve =
          calibrate(
              ImmutableList.of(calibrationCds),
              DoubleArray.of(qSpread),
              DoubleArray.of(0d),
              curveName,
              valuationDate,
              discountFactors,
              recoveryRates,
              refData);
      Currency currency = calibrationCds.getProduct().getCurrency();
      StandardId legalEntityId = calibrationCds.getProduct().getLegalEntityId();
      ImmutableCreditRatesProvider rates =
          ImmutableCreditRatesProvider.builder()
              .valuationDate(valuationDate)
              .discountCurves(ImmutableMap.of(currency, discountFactors))
              .recoveryRateCurves(ImmutableMap.of(legalEntityId, recoveryRates))
              .creditCurves(
                  ImmutableMap.of(
                      Pair.of(legalEntityId, currency),
                      LegalEntitySurvivalProbabilities.of(
                          legalEntityId,
                          IsdaCreditDiscountFactors.of(currency, valuationDate, tempCreditCurve))))
              .build();
      res[0] = calibrationCds.getProduct().getFixedRate();
      res[1] = tradePricer.price(calibrationCds, rates, PriceType.CLEAN, refData);
      if (computeJacobian) {
        CurrencyParameterSensitivities pufSensi =
            rates.parameterSensitivity(
                tradePricer.priceSensitivity(calibrationCds, rates, refData));
        CurrencyParameterSensitivities spSensi =
            rates.parameterSensitivity(
                tradePricer.parSpreadSensitivity(calibrationCds, rates, refData));
        res[2] =
            spSensi.getSensitivity(curveName, currency).getSensitivity().get(0)
                / pufSensi.getSensitivity(curveName, currency).getSensitivity().get(0);
      }
    } else if (marketQuote.getQuoteConvention().equals(CdsQuoteConvention.POINTS_UPFRONT)) {
      res[0] = calibrationCds.getProduct().getFixedRate();
      res[1] = marketQuote.getQuotedValue();
    } else {
      throw new IllegalArgumentException(
          "Unknown CDSQuoteConvention type " + marketQuote.getClass());
    }
    return res;
  }

  private Function<ResolvedCdsTrade, DoubleArray> getParSpreadSensitivityFunction(
      CreditRatesProvider ratesProvider,
      CurveName curveName,
      Currency currency,
      ReferenceData refData) {

    return trade -> {
      PointSensitivities point = tradePricer.parSpreadSensitivity(trade, ratesProvider, refData);
      return ratesProvider
          .parameterSensitivity(point)
          .getSensitivity(curveName, currency)
          .getSensitivity();
    };
  }

  @VisibleForTesting
  private Function<ResolvedCdsTrade, DoubleArray> getPointsUpfrontSensitivityFunction(
      CreditRatesProvider ratesProvider,
      CurveName curveName,
      Currency currency,
      ReferenceData refData) {

    return trade -> {
      PointSensitivities point = tradePricer.priceSensitivity(trade, ratesProvider, refData);
      return ratesProvider
          .parameterSensitivity(point)
          .getSensitivity(curveName, currency)
          .getSensitivity();
    };
  }
}
