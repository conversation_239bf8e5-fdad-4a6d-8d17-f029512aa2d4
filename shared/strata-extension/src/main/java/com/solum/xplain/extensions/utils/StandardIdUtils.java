package com.solum.xplain.extensions.utils;

import com.opengamma.strata.basics.StandardId;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class StandardIdUtils {

  private static final String CURVE_ID_SCHEME = "CurveId";
  private static final String TRADE_ID_SCHEME = "TradeId";
  private static final String DATA_SOURCE_SCHEME = "XPL";
  private static final String COUNTERPARTY_SCHEME = "Counterparty";

  public static StandardId curveIdStandardId(String curveId) {
    return StandardId.of(CURVE_ID_SCHEME, curveId);
  }

  public static StandardId marketDataStandardId(String mdk) {
    return StandardId.of(DATA_SOURCE_SCHEME, mdk);
  }

  public static StandardId tradeStandardId(String externalId) {
    return StandardId.of(TRADE_ID_SCHEME, externalId);
  }

  public static StandardId counterpartyStandardId(String counterparty) {
    return StandardId.of(COUNTERPARTY_SCHEME, counterparty);
  }
}
