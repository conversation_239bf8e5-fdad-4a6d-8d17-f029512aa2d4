package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.pricer.impl.rate.DispatchingRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardIborAveragedRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardIborInterpolatedRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardIborRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardInflationEndInterpolatedRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardInflationEndMonthRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardInflationInterpolatedRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardInflationMonthlyRateComputationFn;
import com.opengamma.strata.pricer.impl.rate.ForwardOvernightAveragedDailyRateComputationFn;
import com.opengamma.strata.pricer.impl.swap.DiscountingKnownAmountPaymentPeriodPricer;
import com.opengamma.strata.pricer.impl.swap.DispatchingSwapPaymentPeriodPricer;
import com.opengamma.strata.pricer.swap.DiscountingSwapLegPricer;
import com.opengamma.strata.pricer.swap.DiscountingSwapProductPricer;
import com.opengamma.strata.pricer.swap.DiscountingSwapTradePricer;
import com.opengamma.strata.pricer.swap.SwapPaymentEventPricer;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;

@Getter
@NullMarked
public final class XplainSwapTradePricers {
  private final DiscountingSwapProductPricer productPricer;
  private final DiscountingSwapTradePricer tradePricer;
  private final DiscountingSwapLegPricer legPricer;
  private final DispatchingSwapPaymentPeriodPricer periodPricer;

  public XplainSwapTradePricers(ReferenceData referenceData) {
    DispatchingRateComputationFn standardDispatcher =
        new DispatchingRateComputationFn(
            ForwardIborRateComputationFn.DEFAULT,
            ForwardIborInterpolatedRateComputationFn.DEFAULT,
            ForwardIborAveragedRateComputationFn.DEFAULT,
            new XplainForwardOvernightCompoundedRateComputation(referenceData),
            new XplainForwardOvernightCompoundedAnnualRateComputationFn(referenceData),
            new XplainApproxForwardOvernightAveragedRateComputationFn(referenceData),
            ForwardOvernightAveragedDailyRateComputationFn.DEFAULT,
            ForwardInflationMonthlyRateComputationFn.DEFAULT,
            ForwardInflationInterpolatedRateComputationFn.DEFAULT,
            ForwardInflationEndMonthRateComputationFn.DEFAULT,
            ForwardInflationEndInterpolatedRateComputationFn.DEFAULT);

    XplainDispatchingRateComputationFn defaultFns =
        new XplainDispatchingRateComputationFn(standardDispatcher, referenceData);

    periodPricer =
        new DispatchingSwapPaymentPeriodPricer(
            new XplainDiscountingRatePaymentPeriodPricer(defaultFns),
            DiscountingKnownAmountPaymentPeriodPricer.DEFAULT);
    legPricer = new DiscountingSwapLegPricer(periodPricer, SwapPaymentEventPricer.standard());
    productPricer = new DiscountingSwapProductPricer(legPricer);
    tradePricer = new DiscountingSwapTradePricer(productPricer);
  }
}
