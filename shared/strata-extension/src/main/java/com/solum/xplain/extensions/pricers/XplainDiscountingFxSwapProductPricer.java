/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import static com.solum.xplain.extensions.spotdate.FxSpotDateCalculator.spotDateFromBaseDate;

import com.google.common.collect.ImmutableSortedSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.*;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.market.sensitivity.PointSensitivities;
import com.opengamma.strata.pricer.ZeroRateSensitivity;
import com.opengamma.strata.pricer.fx.DiscountingFxSwapProductPricer;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.fx.ResolvedFxSwap;
import com.solum.xplain.extensions.product.ExtendedFxSwapConventions;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class XplainDiscountingFxSwapProductPricer {
  private final ReferenceData referenceData;

  public MultiCurrencyAmount presentValue(ResolvedFxSwap swap, RatesProvider provider) {
    return DiscountingFxSwapProductPricer.DEFAULT.presentValue(swap, provider);
  }

  public PointSensitivities presentValueSensitivity(ResolvedFxSwap swap, RatesProvider provider) {
    return DiscountingFxSwapProductPricer.DEFAULT.presentValueSensitivity(swap, provider);
  }

  public double parSpread(ResolvedFxSwap swap, RatesProvider provider) {
    Payment counterPaymentNear = swap.getNearLeg().getCounterCurrencyPayment();
    MultiCurrencyAmount pv = presentValue(swap, provider);
    double pvCounterCcy = convertedPv(swap, pv, provider);
    double dfEnd =
        provider.discountFactor(
            counterPaymentNear.getCurrency(), swap.getFarLeg().getPaymentDate());
    double notionalBaseCcy = swap.getNearLeg().getBaseCurrencyPayment().getAmount();
    return -pvCounterCcy / (notionalBaseCcy * dfEnd);
  }

  private double convertedPv(
      ResolvedFxSwap swap, MultiCurrencyAmount multiCurrencyPv, RatesProvider provider) {
    Currency resultCurrency = swap.getNearLeg().getCounterCurrencyPayment().getCurrency();
    ImmutableSortedSet<CurrencyAmount> amounts = multiCurrencyPv.getAmounts();
    if (amounts.size() == 1) {
      return amounts.first().convertedTo(resultCurrency, provider).getAmount();
    }
    LocalDate spotDate = spotDate(swap, provider);
    double total = 0d;
    for (CurrencyAmount amount : amounts) {
      double convertedAmount =
          provider.convert(amount.getAmount(), amount.getCurrency(), resultCurrency);
      convertedAmount =
          adjustedFxConvertedAmount(
              convertedAmount, amount.getCurrency(), resultCurrency, spotDate, provider);
      total += convertedAmount;
    }
    return CurrencyAmount.of(resultCurrency, total).getAmount();
  }

  private LocalDate spotDate(ResolvedFxSwap swap, RatesProvider provider) {
    CurrencyPair currencyPair = swap.getNearLeg().getCurrencyPair();
    LocalDate valuationDate = provider.getValuationDate();
    DaysAdjustment spotOffset = ExtendedFxSwapConventions.lookupSpotOffset(currencyPair);
    return spotDateFromBaseDate(valuationDate, currencyPair, spotOffset, referenceData);
  }

  public PointSensitivities parSpreadSensitivity(ResolvedFxSwap swap, RatesProvider provider) {
    Payment counterPaymentNear = swap.getNearLeg().getCounterCurrencyPayment();
    MultiCurrencyAmount pv = this.presentValue(swap, provider);
    double pvCounterCcy = convertedPv(swap, pv, provider);
    double dfEnd =
        provider.discountFactor(
            counterPaymentNear.getCurrency(), swap.getFarLeg().getPaymentDate());
    double notionalBaseCcy = swap.getNearLeg().getBaseCurrencyPayment().getAmount();
    double ps = -pvCounterCcy / (notionalBaseCcy * dfEnd);
    double psBar = 1.0;
    double pvCounterCcyBar = -1.0 / (notionalBaseCcy * dfEnd) * psBar;
    double dfEndBar = -ps / dfEnd * psBar;
    ZeroRateSensitivity ddfEnddr =
        provider
            .discountFactors(counterPaymentNear.getCurrency())
            .zeroRatePointSensitivity(swap.getFarLeg().getPaymentDate());
    PointSensitivities result = ddfEnddr.multipliedBy(dfEndBar).build();
    PointSensitivities dpvdr = this.presentValueSensitivity(swap, provider);
    PointSensitivities dpvdrConverted =
        dpvdr.convertedTo(counterPaymentNear.getCurrency(), provider);
    return result.combinedWith(dpvdrConverted.multipliedBy(pvCounterCcyBar));
  }

  private double adjustedFxConvertedAmount(
      double unconvertedAmount,
      Currency amountCurrency,
      Currency resultCurrency,
      LocalDate spotDate,
      RatesProvider provider) {
    return !amountCurrency.equals(resultCurrency)
        ? unconvertedAmount
            / provider.discountFactor(amountCurrency, spotDate)
            * provider.discountFactor(resultCurrency, spotDate)
        : unconvertedAmount;
  }

  public MultiCurrencyAmount currencyExposure(ResolvedFxSwap product, RatesProvider provider) {
    return presentValue(product, provider);
  }

  public MultiCurrencyAmount currentCash(ResolvedFxSwap swap, LocalDate valuationDate) {
    return DiscountingFxSwapProductPricer.DEFAULT.currentCash(swap, valuationDate);
  }
}
