package com.solum.xplain.extensions.calendar

import com.opengamma.strata.basics.date.HolidayCalendars
import java.time.LocalDate
import spock.lang.Specification

class ValuationDateHolidayCalendarTest extends Specification {

  def "should always return valuation date on calibration date # calibrationDate"() {
    setup:
    def calendar = new ValuationDateHolidayCalendar(HolidayCalendars.SAT_SUN, calibrationDate)

    expect:
    calendar.isBusinessDay(calibrationDate)

    where:
    calibrationDate << [LocalDate.now(), LocalDate.of(2023, 2, 4)]
  }

  def "should return next business date on #date"() {
    setup:
    def calendar = new ValuationDateHolidayCalendar(HolidayCalendars.SAT_SUN, date)

    expect:
    calendar.next(date) == nextDate

    where:
    date                     | nextDate
    LocalDate.of(2023, 2, 2) | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 3) | LocalDate.of(2023, 2, 6)
    LocalDate.of(2023, 2, 4) | LocalDate.of(2023, 2, 6)
    LocalDate.of(2023, 2, 5) | LocalDate.of(2023, 2, 6)
  }

  def "should return previous business date on #date"() {
    setup:
    def calendar = new ValuationDateHolidayCalendar(HolidayCalendars.SAT_SUN, date)

    expect:
    calendar.previous(date) == previousDate

    where:
    date                     | previousDate
    LocalDate.of(2023, 2, 6) | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 5) | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 4) | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 3) | LocalDate.of(2023, 2, 2)
  }

  def "should shift date #date with #shift days"() {
    setup:
    def calendar = new ValuationDateHolidayCalendar(HolidayCalendars.SAT_SUN, date)

    expect:
    calendar.shift(date, shift) == previousDate

    where:
    date                     | shift | previousDate
    LocalDate.of(2023, 2, 6) | -1    | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 5) | -1    | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 4) | -1    | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 3) | -1    | LocalDate.of(2023, 2, 2)
    LocalDate.of(2023, 2, 2) | 1     | LocalDate.of(2023, 2, 3)
    LocalDate.of(2023, 2, 3) | 1     | LocalDate.of(2023, 2, 6)
    LocalDate.of(2023, 2, 4) | 1     | LocalDate.of(2023, 2, 6)
    LocalDate.of(2023, 2, 5) | 1     | LocalDate.of(2023, 2, 6)
  }
}
