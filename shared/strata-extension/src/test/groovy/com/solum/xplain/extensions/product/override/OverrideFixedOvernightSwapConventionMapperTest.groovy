package com.solum.xplain.extensions.product.override

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.basics.date.DayCounts.ACT_360
import static com.opengamma.strata.basics.schedule.Frequency.TERM
import static com.solum.xplain.extensions.constants.OvernightIndexConstants.MXN_F_TIIE

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.BusinessDayConventions
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.HolidayCalendarId
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.basics.schedule.StubConvention
import com.solum.xplain.extensions.override.FixedOvernightSwapConventionOverride
import com.solum.xplain.extensions.override.OverrideFixedOvernightSwapConventionMapper
import com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions
import spock.lang.Specification

class OverrideFixedOvernightSwapConventionMapperTest extends Specification {

  def mapper = new OverrideFixedOvernightSwapConventionMapper()

  def "should merge MXN convention overrides"() {
    when:
    def conv = ExtendedFixedOvernightSwapConventions.makeConvention(
      "MXN-FIXED-TERM-F-TIIE-OIS", MXN_F_TIIE, ACT_360, TERM, 2, 1, MODIFIED_FOLLOWING)

    def result = mapper.mergeOverride(
      conv,
      override()
      )

    then:
    result.name == "MXN-FIXED-TERM-F-TIIE-OIS"
    result.spotDateOffset.days == 3

    def fixedLegConv = result.fixedLeg
    fixedLegConv.dayCount == DayCounts.ACT_360
    fixedLegConv.currency == Currency.of("MXN")
    fixedLegConv.paymentFrequency == Frequency.P6M
    fixedLegConv.stubConvention == StubConvention.SHORT_INITIAL
    fixedLegConv.paymentDateOffset.days == 3
    fixedLegConv.paymentDateOffset.calendar == HolidayCalendarId.of("MXMC")
    fixedLegConv.paymentDateOffset.adjustment.convention == BusinessDayConventions.FOLLOWING
    fixedLegConv.paymentDateOffset.adjustment.calendar == HolidayCalendarId.of("MXMC")
    fixedLegConv.rollConvention == RollConventions.SFE

    def floatLegConv = result.floatingLeg
    floatLegConv.dayCount == DayCounts.ACT_365F
    floatLegConv.currency == Currency.of("MXN")
    floatLegConv.paymentFrequency == Frequency.P6M
    floatLegConv.stubConvention == StubConvention.SHORT_INITIAL
    floatLegConv.paymentDateOffset.days == 2
    floatLegConv.paymentDateOffset.calendar == HolidayCalendarId.of("MXMC+USNY")
    floatLegConv.paymentDateOffset.adjustment.convention == BusinessDayConventions.FOLLOWING
    floatLegConv.paymentDateOffset.adjustment.calendar == HolidayCalendarId.of("MXMC+USNY")
    floatLegConv.rollConvention == RollConventions.SFE
    floatLegConv.rateCutOffDays == 1
  }

  def override() {
    return new FixedOvernightSwapConventionOverride(
      "convention-name",
      "P6M",
      "Act/360",
      "P6M",
      "Act/365F",
      3,
      null,
      2,
      "MXMC+USNY",
      "SFE",
      3,
      "MXMC",
      1)
  }

  def offset(int days) {
    return DaysAdjustment.ofBusinessDays(
      days,
      HolidayCalendarId.of("MXMC"),
      BusinessDayAdjustment.of(BusinessDayConventions.FOLLOWING, HolidayCalendarId.of("MXMC"))
      )
  }
}
