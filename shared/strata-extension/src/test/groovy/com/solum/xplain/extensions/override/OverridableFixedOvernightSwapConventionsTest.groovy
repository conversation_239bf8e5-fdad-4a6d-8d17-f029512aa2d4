package com.solum.xplain.extensions.override

import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention
import java.util.function.Predicate
import spock.lang.Specification

class OverridableFixedOvernightSwapConventionsTest extends Specification {

  def cleanup() {
    System.clearProperty("FIXED_LEG_FREQUENCY_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("_FIXED_LEG_FREQUENCY_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("FLOAT_LEG_FREQUENCY_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("FIXED_LEG_DAY_COUNT_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("FIXED_LEG_PAYMENT_DATE_OFFSET_DAYS_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("FIXED_LEG_PAYMENT_DATE_OFFSET_CALENDAR_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("FLOAT_LEG_PAYMENT_DATE_OFFSET_DAYS_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("ROLL_CONVENTION_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("SPOT_DATE_OFFSET_DAYS_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("SPOT_DATE_CALENDAR_DAYS_MXN_FIXED_TERM_F_TIIE_OIS")
    System.clearProperty("OVERNIGHT_RATE_CUTOFF_DAYS_MXN_FIXED_TERM_F_TIIE_OIS")
  }

  def "FixedOvernightSwapConventionOf(#conventionName) should apply overrides configParamName: #configParamValue"() {

    when:
    def indexSnake = conventionName.replace('-', '_')
    if (configParamValue != null) {
      System.setProperty(configParamName + indexSnake, configParamValue)
    }
    else {
      System.clearProperty(configParamName + indexSnake)
    }

    then:
    ((Predicate<FixedOvernightSwapConvention>) predicate).test(FixedOvernightSwapConvention.of(conventionName))

    where:
    conventionName                | configParamName                           || configParamValue | predicate
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_FREQUENCY_"                    || null             | { c -> c.fixedLeg.accrualFrequency == Frequency.TERM }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_FREQUENCY_"                    || "P1M"            | { c -> c.fixedLeg.accrualFrequency == Frequency.P1M }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "_FIXED_LEG_FREQUENCY_"                   || "P1M"            | { c -> c.fixedLeg.accrualFrequency == Frequency.TERM }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FLOAT_LEG_FREQUENCY_"                    || null             | { c -> c.floatingLeg.accrualFrequency == Frequency.TERM }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FLOAT_LEG_FREQUENCY_"                    || "P12M"           | { c -> c.floatingLeg.accrualFrequency == Frequency.P12M }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_DAY_COUNT_"                    || "Act/Act ISDA"   | { c -> c.fixedLeg.dayCount == DayCounts.ACT_ACT_ISDA }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_DAY_COUNT_"                    || null             | { c -> c.fixedLeg.dayCount == DayCounts.ACT_360 }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_PAYMENT_DATE_OFFSET_DAYS_"     || null             | { c -> c.fixedLeg.paymentDateOffset.days == 2 && c.fixedLeg.paymentDateOffset.calendar.name == "MXMC" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_PAYMENT_DATE_OFFSET_DAYS_"     || "5"              | { c -> c.fixedLeg.paymentDateOffset.days == 5 && c.fixedLeg.paymentDateOffset.calendar.name == "MXMC" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FIXED_LEG_PAYMENT_DATE_OFFSET_CALENDAR_" || "MXMC+USNY"      | { c -> c.fixedLeg.paymentDateOffset.days == 2 && c.fixedLeg.paymentDateOffset.calendar.name == "MXMC+USNY" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "FLOAT_LEG_PAYMENT_DATE_OFFSET_DAYS_"     || "4"              | { c -> c.floatingLeg.paymentDateOffset.days == 4 && c.floatingLeg.paymentDateOffset.calendar.name == "MXMC" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "ROLL_CONVENTION_"                        || null             | { c -> c.fixedLeg.rollConvention == RollConventions.EOM && c.floatingLeg.rollConvention == RollConventions.EOM }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "ROLL_CONVENTION_"                        || "IMM"            | { c -> c.fixedLeg.rollConvention == RollConventions.IMM && c.floatingLeg.rollConvention == RollConventions.IMM }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "SPOT_DATE_OFFSET_DAYS_"                  || null             | { c -> c.spotDateOffset.days == 2 && c.spotDateOffset.calendar.name == "MXMC" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "SPOT_DATE_OFFSET_DAYS_"                  || "5"              | { c -> c.spotDateOffset.days == 5 && c.spotDateOffset.calendar.name == "MXMC" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "SPOT_DATE_OFFSET_CALENDAR_"              || "MXMC+USNY"      | { c -> c.spotDateOffset.days == 2 && c.spotDateOffset.calendar.name == "MXMC+USNY" }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "OVERNIGHT_RATE_CUTOFF_DAYS_"             || null             | { c -> c.floatingLeg.rateCutOffDays == 1 }
    "MXN-FIXED-TERM-F-TIIE-OIS"   | "OVERNIGHT_RATE_CUTOFF_DAYS_"             || "2"              | { c -> c.floatingLeg.rateCutOffDays == 2 }
  }
}
