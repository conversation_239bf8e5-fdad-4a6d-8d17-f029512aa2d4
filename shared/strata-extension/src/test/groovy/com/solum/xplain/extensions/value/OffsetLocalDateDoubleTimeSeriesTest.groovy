package com.solum.xplain.extensions.value

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DateAdjuster
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import java.time.LocalDate
import spock.lang.Specification

class OffsetLocalDateDoubleTimeSeriesTest extends Specification {
  static LOCAL_DATE = LocalDate.parse("2023-01-25")

  def "should check if date is found"() {
    expect:
    offsetSeries.containsDate(date) == contains

    where:
    offsetSeries                                                    | date                    | contains
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE              | false
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(1))  | LOCAL_DATE.minusDays(1) | true
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE.plusDays(1)  | true
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), null)         | LOCAL_DATE              | true
  }

  def "should return fixing value"() {
    expect:
    offsetSeries.get(date) == value

    where:
    offsetSeries                                                    | date                    | value
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE              | OptionalDouble.empty()
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(1))  | LOCAL_DATE.minusDays(1) | OptionalDouble.of(0.01)
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE.plusDays(1)  | OptionalDouble.of(0.01)
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), null)         | LOCAL_DATE              | OptionalDouble.of(0.01)
  }

  def "should return adjusted date"() {
    expect:
    offsetSeries.adjustedDate(date) == adjusted

    where:
    offsetSeries                                                    | date                    | adjusted
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE              | LOCAL_DATE.minusDays(1)
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(1))  | LOCAL_DATE.minusDays(1) | LOCAL_DATE
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), adjuster(-1)) | LOCAL_DATE.plusDays(1)  | LOCAL_DATE
    new OffsetLocalDateDoubleTimeSeries(timeSeries(), null)         | LOCAL_DATE              | LOCAL_DATE
  }

  static LocalDateDoubleTimeSeries timeSeries() {
    def builder = LocalDateDoubleTimeSeries.builder()
    builder.put(LOCAL_DATE, 0.01)
    return builder.build()
  }

  private DateAdjuster adjuster(int days) {
    return DaysAdjustment.builder()
      .days(days)
      .calendar(HolidayCalendarIds.GBLO)
      .adjustment(BusinessDayAdjustment.NONE)
      .build()
      .normalized()
      .resolve(ReferenceData.standard())
  }
}
