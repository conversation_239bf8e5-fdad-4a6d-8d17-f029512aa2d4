package com.solum.xplain.extensions.product

import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.SEK_STIBOR_3M

import com.opengamma.strata.product.fra.type.FraConvention
import com.solum.xplain.extensions.immfra.ImmFraConvention
import spock.lang.Specification

class FraConventionTest extends Specification {

  def "should return IMM fra conventions"() {
    expect:
    def convention = ImmFraConvention.of(name)
    convention.getIndex() == index

    where:
    name                | index
    "DKK-CIBOR-6M-IMM-FRA"  | DKK_CIBOR_6M
    "NOK-NIBOR-6M-IMM-FRA"  | NOK_NIBOR_6M
    "NOK-NIBOR-3M-IMM-FRA"  | NOK_NIBOR_3M
    "SEK-STIBOR-3M-IMM-FRA" | SEK_STIBOR_3M
  }

  def "should return non IMM fra conventions"() {
    expect:
    def convention = FraConvention.of(index)
    convention.getName() == name

    where:
    index         | name
    DKK_CIBOR_3M  | "DKK-CIBOR-3M"
    NOK_NIBOR_6M  | "NOK-NIBOR-6M"
    NOK_NIBOR_3M  | "NOK-NIBOR-3M"
    SEK_STIBOR_3M | "SEK-STIBOR-3M"
  }
}
