package com.solum.xplain.extensions.immfra

import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_3M
import static com.opengamma.strata.product.common.BuySell.BUY

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.AdjustableDate
import com.opengamma.strata.basics.date.DateSequences
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.SequenceDate
import com.opengamma.strata.product.fra.Fra
import com.opengamma.strata.product.fra.FraTrade
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class ImmutableImmFraConventionTest extends Specification {

  static final REF_DATA = ReferenceData.standard()
  static final CONVENTION = new ImmutableImmFraConvention(DKK_CIBOR_3M, DateSequences.QUARTERLY_IMM)
  static final double NOTIONAL_2M = 2_000_000d

  def "should return correct values"() {
    expect:
    CONVENTION.name == "DKK-CIBOR-3M-IMM-FRA"
    CONVENTION.index == DKK_CIBOR_3M
    CONVENTION.dateSequence == DateSequences.QUARTERLY_IMM
  }

  def "should correctly convert to trade"() {
    setup:
    LocalDate tradeDate = LocalDate.of(2015, 5, 5)
    def sequenceDate = SequenceDate.base(Period.ofDays(2), 1)

    Fra expected = Fra.builder()
      .buySell(BUY)
      .notional(NOTIONAL_2M)
      .startDate(LocalDate.of(2015, 6, 17))
      .endDate(LocalDate.of(2015, 9, 17))
      .fixedRate(0.25d)
      .index(DKK_CIBOR_3M)
      .build()

    when:
    def trade = CONVENTION.createTrade(tradeDate, sequenceDate, BUY, NOTIONAL_2M, 0.25d, REF_DATA)

    then:
    trade.getProduct() == expected
  }

  def "should create trade from dates"() {
    setup:
    LocalDate tradeDate = LocalDate.of(2015, 5, 5)
    LocalDate startDate = LocalDate.of(2015, 8, 5)
    LocalDate endDate = LocalDate.of(2015, 11, 5)
    LocalDate paymentDate = LocalDate.of(2015, 8, 7)

    Fra expected = Fra.builder()
      .buySell(BUY)
      .notional(NOTIONAL_2M)
      .startDate(LocalDate.of(2015, 8, 5))
      .endDate(LocalDate.of(2015, 11, 5))
      .paymentDate(AdjustableDate.of(paymentDate, DaysAdjustment.NONE.getAdjustment()))
      .fixedRate(0.25d)
      .index(DKK_CIBOR_3M)
      .build()

    when:
    FraTrade test = CONVENTION.toTrade(tradeDate, startDate, endDate, paymentDate, BUY, NOTIONAL_2M, 0.25d)

    then:
    test.getInfo().getTradeDate() == Optional.of(tradeDate)
    test.getProduct() == expected
  }
}
