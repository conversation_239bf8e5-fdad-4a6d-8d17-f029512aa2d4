package com.solum.xplain.extensions.datesequence

import java.time.LocalDate
import spock.lang.Specification

class NzdFuturesDateSequenceTest extends Specification{

  def "should return correct next reference date for NzdFuturesDateSequence"() {
    setup:
    def convention = ExtendedDateSequences.QUARTERLY_NZD_IMM

    expect:
    convention.next(valuationDate) == date

    where:
    valuationDate             | date
    LocalDate.of(2020, 3, 7)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 3, 9)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 9, 8)  | LocalDate.of(2020, 9, 17)
    LocalDate.of(2020, 9, 9)  | LocalDate.of(2020, 9, 17)
    LocalDate.of(2020, 9, 16) | LocalDate.of(2020, 9, 17)
  }

  def "should return correct nextOrSame reference date for NzdFuturesDateSequence"() {
    setup:
    def convention = ExtendedDateSequences.QUARTERLY_NZD_IMM

    expect:
    convention.nextOrSame(valuationDate) == date

    where:
    valuationDate             | date
    LocalDate.of(2020, 3, 7)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 3, 9)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 3, 12)
    LocalDate.of(2020, 9, 8)  | LocalDate.of(2020, 9, 17)
    LocalDate.of(2020, 9, 9)  | LocalDate.of(2020, 9, 17)
    LocalDate.of(2020, 9, 16) | LocalDate.of(2020, 9, 17)
  }
}
