package com.solum.xplain.extensions.product

import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_JPTO

import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.product.fra.type.ImmutableFraConvention
import spock.lang.Specification

class ExtendedFraConventionsTest extends Specification {

  def "should correctly return JPY-LIBOR-3M FRA convention"() {
    setup:
    def convention = (ImmutableFraConvention) ExtendedFraConventions.INSTANCE.lookup("JPY-LIBOR-3M")

    expect:
    convention.getBusinessDayAdjustment().getCalendar() == GBLO_JPTO
    convention.getIndex() == IborIndex.of("JPY-LIBOR-3M")
  }

  def "should return EUR-EURIBOR-3M convention"() {
    setup:
    def convention = (ImmutableFraConvention) ExtendedFraConventions.INSTANCE.lookup("EUR-EURIBOR-3M")

    expect:
    convention.getBusinessDayAdjustment().getCalendar() == HolidayCalendarIds.EUTA
    convention.getIndex() == IborIndex.of("EUR-EURIBOR-3M")
  }

  def "should return all cached FRA conventions"() {
    setup:
    ExtendedFraConventions.INSTANCE.lookup("JPY-LIBOR-3M")
    ExtendedFraConventions.INSTANCE.lookup("EUR-EURIBOR-3M")

    expect:
    def conventions = ExtendedFraConventions.INSTANCE.lookupAll()
    conventions.get("JPY-LIBOR-3M") != null
    conventions.get("EUR-EURIBOR-3M") != null
  }
}
