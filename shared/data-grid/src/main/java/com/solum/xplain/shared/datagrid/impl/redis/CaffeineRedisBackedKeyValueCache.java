package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.KeyValueCache;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.impl.redis.nearcache.AbstractCaffeineRedisBackedCache;
import com.solum.xplain.shared.datagrid.impl.redis.nearcache.CachedValue;
import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.data.util.Predicates;

@NullMarked
class CaffeineRedisBackedKeyValueCache<K, V> extends AbstractCaffeineRedisBackedCache
    implements KeyValueCache<K, @Nullable V> {
  private final RedisKeyValueCache<K, @Nullable V> sharedCache;

  CaffeineRedisBackedKeyValueCache(
      LocalCacheReplica config,
      RedisKeyValueCache<K, V> sharedCache,
      RedisCacheMessagePublisher publisher) {
    super(config, publisher);
    this.sharedCache = sharedCache;
  }

  @Override
  @Nullable
  public V get(K key) {
    CachedValue cachedValue = layeredGet(key);
    return cachedValue == null ? null : cachedValue.raw();
  }

  @Override
  public void set(K key, V value) {
    layeredPut(key, value);
  }

  @Override
  public void set(K key, V value, Duration ttl) {
    layeredPut(key, value, ttl);
  }

  @Override
  public void merge(K key, V value, BiFunction<V, V, V> remappingFunction) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void merge(K key, V value, BiFunction<V, V, V> remappingFunction, Duration ttl) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void setAll(Map<K, V> values) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Collection<K> keySet() {
    return sharedCache.keySet();
  }

  @Override
  public Collection<@Nullable V> values() {
    return sharedCache.values();
  }

  @Override
  public Collection<Map.Entry<K, V>> entrySet() {
    return sharedCache.entrySet();
  }

  @Override
  public void delete(K key) {
    layeredEvict(key);
  }

  @Override
  public void removeAll(Predicate<V> predicate) {
    throw new UnsupportedOperationException();
  }

  @Override
  protected Optional<CachedValue> findInSharedCache(Object key) {
    V nullableValue = sharedCache.get((K) key);
    if (nullableValue == null) {
      // Could be null if the key does not exist or if the value is explicitly set to null, so let's
      // chck.
      if (!sharedCache.exists((K) key)) {
        return Optional.empty();
      }
    }
    return Optional.of(CachedValue.ofRaw(nullableValue));
  }

  @Override
  protected void putValueToSharedCache(Object key, CachedValue value) {
    sharedCache.set((K) key, value.raw());
  }

  @Override
  protected CachedValue putValueToSharedCacheIfAbsent(
      Object key, Callable<@Nullable Object> rawValueLoader) throws RuntimeException {
    throw new UnsupportedOperationException(); // not required to support KeyValueCache operations.
  }

  @Override
  protected void setSharedCacheTtl(Object key, Duration ttl) {
    sharedCache.setKeyExpiry((K) key, ttl);
  }

  @Override
  protected void evictFromSharedCache(Object key) {
    sharedCache.delete((K) key);
  }

  @Override
  protected void clearSharedCache() {
    sharedCache.removeAll(Predicates.isTrue());
  }
}
