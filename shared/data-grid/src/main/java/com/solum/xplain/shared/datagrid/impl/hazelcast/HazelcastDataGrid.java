package com.solum.xplain.shared.datagrid.impl.hazelcast;

import com.hazelcast.core.HazelcastInstance;
import com.solum.xplain.shared.datagrid.AtomicCounter;
import com.solum.xplain.shared.datagrid.ClusterLock;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.shared.datagrid.KeyValueCache;
import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.ValueSet;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class HazelcastDataGrid implements DataGrid {
  private final HazelcastInstance hazelcastInstance;
  private final ClassLoader classLoader = Thread.currentThread().getContextClassLoader();

  @PostConstruct
  void init() {
    log.debug("HazelcastDataGrid initialized");
  }

  public <K, V> KeyValueCache<K, V> getKeyValueCache(String name) {
    return new HazelcastKeyValueCache<>(hazelcastInstance.getMap(name));
  }

  @Override
  public <V> ValueSet<V> getValueSet(String name) {
    return new HazelcastValueSet<>(hazelcastInstance.getSet(name));
  }

  @Override
  public AtomicCounter getAtomicCounter(String name) {
    return new HazelcastAtomicCounter(hazelcastInstance.getCPSubsystem().getAtomicLong(name));
  }

  @Override
  public <T> PubSubTopic<T> getPubSubTopic(String name) {
    return new HazelcastPubSubTopic(hazelcastInstance.getReliableTopic(name), classLoader);
  }

  @Override
  public ClusterLock getClusterLock(String name) {
    return new HazelcastClusterLock(hazelcastInstance.getCPSubsystem().getLock(name));
  }
}
