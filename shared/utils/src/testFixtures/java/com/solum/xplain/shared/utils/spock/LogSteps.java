package com.solum.xplain.shared.utils.spock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.spockframework.runtime.extension.ExtensionAnnotation;

/**
 * Log each GIVEN/WHEN/THEN block including the related AND sections that are included in that step
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@ExtensionAnnotation(LoggingExtension.class)
public @interface LogSteps {}
