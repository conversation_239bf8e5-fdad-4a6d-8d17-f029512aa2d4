package com.solum.xplain.shared.utils.spock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Fire a {@link com.solum.xplain.shared.utils.event.CacheInvalidationEvent} after each test method
 * completes. This annotation is picked up by the {@link CacheInvalidationListener} which is
 * registered globally.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface InvalidateCaches {}
