package com.solum.xplain.core.utils

import java.lang.reflect.Field
import spock.lang.Specification

class ReflectionUtilsTest extends Specification {

  def "should join properties into path, skipping nulls"() {
    expect:
    ReflectionUtils.propertyName(parent, child) == path
    where:
    parent         | child              | path
    "parent"       | "child"            | "parent.child"
    "parent"       | null               | "parent"
    null           | "child"            | "child"
    null           | null               | ""
    "parent"       | "child.grandchild" | "parent.child.grandchild"
    "parent.child" | "grandchild"       | "parent.child.grandchild"
  }

  def "should join multiple into path, skipping nulls"() {
    expect:
    ReflectionUtils.propertyName(parent, child, grandchild) == path
    where:
    parent   | child   | grandchild | path
    "parent" | "child" | null       | "parent.child"
    "parent" | null    | null       | "parent"
    null     | "child" | null       | "child"
    null     | null    | null       | ""
    "parent" | "a.b"   | "c"        | "parent.a.b.c"
    "parent" | null    | "child"    | "parent.child"
  }

  def "should get Lombok field name constants for class and superclasses"() {
    expect:
    ReflectionUtils.lombokFieldNameConstantsStream(clazz).map(Field::getName).sorted().toList() == fields

    where:
    clazz          | fields
    ViewSubclass   | ["field1", "field2", "field3", "field4"]
    ViewSuperclass | ["field3", "field4"]
    NoConstants    | []
  }
}
