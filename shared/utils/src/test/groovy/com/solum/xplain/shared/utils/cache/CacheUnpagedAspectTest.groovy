package com.solum.xplain.shared.utils.cache

import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.Signature
import org.springframework.data.domain.Sort
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import spock.lang.Specification

class CacheUnpagedAspectTest extends Specification {

  def unpagedCachingService = Mock(UnpagedCachingService)
  def aspect = new CacheUnpagedAspect(unpagedCachingService)
  def pjp = Mock(ProceedingJoinPoint)
  def cacheUnpaged = Mock(CacheUnpaged)

  def setup() {
    // Set up security context with a test principal
    def authentication = new TestingAuthenticationToken("testUser", "password")
    SecurityContextHolder.getContext().setAuthentication(authentication)
  }

  def cleanup() {
    SecurityContextHolder.clearContext()
  }

  def "cacheUnpagedScrollableEntry should delegate to UnpagedCachingService with correct parameters"() {
    given:
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def data = [new TestData(id: "1")]
    def result = ScrollableEntry.of(data, scrollRequest)
    def methodSignature = "com.example.Service.getData(ScrollRequest)"
    def eventClasses = [TestEvent] as Class<?>[]

    pjp.getSignature() >> Mock(Signature, { toString() >> methodSignature })
    pjp.getArgs() >> [scrollRequest]
    cacheUnpaged.invalidateOnEvent() >> eventClasses
    unpagedCachingService.cacheUnpaged(_, _, _ as UnpagedCachingService.FetchWithScrollRequest, _ as Class[], _ as Object[]) >> result

    when:
    def actualResult = aspect.cacheUnpagedScrollableEntry(pjp, cacheUnpaged)

    then:
    actualResult == result
    1 * unpagedCachingService.cacheUnpaged(
      methodSignature,
      "testUser",
      { it instanceof UnpagedCachingService.FetchWithScrollRequest },
      eventClasses,
      [scrollRequest] as Object[]
      ) >> result
  }

  def "cacheUnpagedScrollableEntryEither should delegate to UnpagedCachingService with correct parameters"() {
    given:
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def data = [new TestData(id: "1")]
    def result = Either.right(ScrollableEntry.of(data, scrollRequest))
    def methodSignature = "com.example.Service.getDataEither(ScrollRequest)"
    def eventClasses = [TestEvent] as Class<?>[]

    pjp.getSignature() >> Mock(Signature, { toString() >> methodSignature })
    pjp.getArgs() >> [scrollRequest]
    cacheUnpaged.invalidateOnEvent() >> eventClasses
    unpagedCachingService.cacheUnpagedEither(_, _, _, _, _) >> result

    when:
    def actualResult = aspect.cacheUnpagedScrollableEntryEither(pjp, cacheUnpaged)

    then:
    actualResult == result
    1 * unpagedCachingService.cacheUnpagedEither(
      methodSignature,
      "testUser",
      { it instanceof UnpagedCachingService.FetchWithScrollRequestEither },
      eventClasses,
      [scrollRequest] as Object[]
      ) >> result
  }

  def "should extract principal from SecurityContext correctly"() {
    given:
    def customPrincipal = new CustomPrincipal(userId: "customUser123")
    def authentication = new TestingAuthenticationToken(customPrincipal, "password")
    SecurityContextHolder.getContext().setAuthentication(authentication)

    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def result = ScrollableEntry.of([], scrollRequest)

    pjp.getSignature() >> Mock(Signature, { toString() >> "test.method" })
    pjp.getArgs() >> [scrollRequest]
    cacheUnpaged.invalidateOnEvent() >> ([] as Class<?>[])
    unpagedCachingService.cacheUnpaged(_, _, _, _, _) >> result

    when:
    aspect.cacheUnpagedScrollableEntry(pjp, cacheUnpaged)

    then:
    1 * unpagedCachingService.cacheUnpaged(
      "test.method",
      customPrincipal,
      _,
      _ as Class<?>[],
      _
      )
  }

  def "should handle empty invalidateOnEvent array"() {
    given:
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def result = ScrollableEntry.of([], scrollRequest)

    pjp.getSignature() >> Mock(Signature, { toString() >> "test.method" })
    pjp.getArgs() >> [scrollRequest]
    cacheUnpaged.invalidateOnEvent() >> ([] as Class<?>[])
    unpagedCachingService.cacheUnpaged(_, _, _, _, _) >> result

    when:
    aspect.cacheUnpagedScrollableEntry(pjp, cacheUnpaged)

    then:
    1 * unpagedCachingService.cacheUnpaged(
      "test.method",
      "testUser",
      _,
      [] as Class<?>[],
      _
      )
  }

  def "should handle error results in Either version"() {
    given:
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def error = new ErrorItem(Error.OBJECT_NOT_FOUND, "Data not found")
    def result = Either.left(error)

    pjp.getSignature() >> Mock(Signature, { toString() >> "test.method" })
    pjp.getArgs() >> [scrollRequest]
    cacheUnpaged.invalidateOnEvent() >> ([] as Class<?>[])
    unpagedCachingService.cacheUnpagedEither(_, _, _, _, _) >> result

    when:
    def actualResult = aspect.cacheUnpagedScrollableEntryEither(pjp, cacheUnpaged)

    then:
    actualResult == result
    actualResult.isLeft()
    actualResult.left().get() == error
  }

  static class TestData {
    String id
  }

  static class TestEvent {
  }

  static class CustomPrincipal {
    String userId
  }
}
