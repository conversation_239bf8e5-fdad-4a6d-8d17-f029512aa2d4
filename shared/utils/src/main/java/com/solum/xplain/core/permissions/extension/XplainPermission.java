package com.solum.xplain.core.permissions.extension;

import com.solum.xplain.core.permissions.UserType;
import lombok.Data;

@Data
public class XplainPermission {

  private String name;
  private String authority;
  private UserType userType;
  private PermissionCategory permissionCategory;

  public XplainPermission() {}

  public XplainPermission(
      String name, String authority, UserType userType, PermissionCategory permissionCategory) {
    this.name = name;
    this.authority = authority;
    this.userType = userType;
    this.permissionCategory = permissionCategory;
  }
}
