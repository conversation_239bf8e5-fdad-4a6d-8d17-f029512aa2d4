package com.solum.xplain.core.authentication.value;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static io.atlassian.fugue.Eithers.cond;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.permissions.extension.XplainPermission;
import io.atlassian.fugue.Either;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.data.annotation.PersistenceCreator;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.util.Assert;

@Data
@AllArgsConstructor(
    staticName = "newOf",
    access = AccessLevel.PACKAGE,
    onConstructor_ = {@PersistenceCreator})
@NullMarked
public class XplainPrincipal implements OAuth2AuthenticatedPrincipal, Serializable {

  private final @Nullable String id;
  private final @Nullable String name;
  private final List<ObjectId> teams;
  private final Map<String, Object> attributes;
  private final List<GrantedAuthority> authorities;
  private final List<String> permissions;
  private final boolean trustedPrincipal;
  private final List<ObjectId> roles;

  public XplainPrincipal(
      String principalId,
      @Nullable Object principalName,
      List<ObjectId> teams,
      Map<String, Object> attributes,
      Collection<GrantedAuthority> authorities,
      List<XplainPermission> permissions,
      List<ObjectId> roles) {
    this(principalId, principalName, teams, attributes, authorities, permissions, false, roles);
  }

  public XplainPrincipal(
      String principalId,
      @Nullable Object principalName,
      List<ObjectId> teams,
      Map<String, Object> attributes,
      Collection<GrantedAuthority> authorities,
      List<XplainPermission> permissions,
      boolean trustedPrincipal,
      List<ObjectId> roles) {
    Assert.notEmpty(attributes, "attributes cannot be empty");
    this.attributes = ImmutableMap.copyOf(attributes);

    this.permissions = permissions.stream().map(XplainPermission::getName).toList();
    var permissionsAuthorities =
        permissions.stream()
            .map(p -> new SimpleGrantedAuthority(p.getAuthority()))
            .collect(toSet());
    this.authorities =
        ImmutableList.<GrantedAuthority>builder()
            .addAll(authorities)
            .addAll(permissionsAuthorities)
            .build();

    this.id = isNotEmpty(principalId) ? principalId : (String) this.attributes.get("sub");
    this.name =
        ofNullable(principalName)
            .map(Object::toString)
            .filter(StringUtils::isNotEmpty)
            .orElse(this.id);
    this.teams = teams;
    this.trustedPrincipal = trustedPrincipal;
    this.roles = roles;
  }

  public Either<ErrorItem, XplainPrincipal> allowedTeams(@Nullable List<String> teamIds) {
    return cond(userBelongsToTeam(teamIds), OPERATION_NOT_ALLOWED.entity("No team access"), this);
  }

  private boolean userBelongsToTeam(@Nullable List<String> teamIds) {
    return teamIds != null && teams.stream().map(Objects::toString).anyMatch(teamIds::contains);
  }
}
