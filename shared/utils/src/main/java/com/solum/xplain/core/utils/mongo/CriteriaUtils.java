package com.solum.xplain.core.utils.mongo;

import static java.util.stream.Collectors.collectingAndThen;

import jakarta.annotation.Nonnull;
import java.util.List;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.query.Criteria;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CriteriaUtils {
  /**
   * Return the simplest possible {@link Criteria} to represent the logical AND of the stream of
   * {@code Criteria}. If the stream is empty, an {@code Criteria} is returned. If the stream
   * contains only one {@link Criteria} element, then it is returned unchanged.
   *
   * <p>NB this collector uses an intermediate list to collect the stream of criteria before
   * creating the final criteria.
   *
   * @return reducing collector that returns a single criteria representing the logical AND of the
   *     stream of criteria
   */
  public static Collector<Criteria, ?, Criteria> toAndCriteria() {
    return collectingAndThen(Collectors.toList(), CriteriaUtils::toAndCriteria);
  }

  /**
   * Return the simplest possible {@link Criteria} to represent the logical OR of the stream of
   * {@code Criteria}. If the stream is empty, an {@code Criteria} is returned. If the stream
   * contains only one {@link Criteria} element, then it is returned unchanged.
   *
   * <p>NB this collector uses an intermediate list to collect the stream of criteria before
   * creating the final criteria.
   *
   * @return reducing collector that returns a single criteria representing the logical OR of the
   *     stream of criteria
   */
  public static Collector<Criteria, ?, Criteria> toOrCriteria() {
    return collectingAndThen(Collectors.<Criteria>toList(), CriteriaUtils::toOrCriteria);
  }

  /**
   * Return the simplest possible {@link Criteria} to represent the logical AND of the list of
   * {@code Criteria} provided. If the list is empty, an {@code Criteria} is returned. If the list
   * contains only one {@link Criteria} element, then it is returned unchanged.
   *
   * @param criteria list of criteria
   * @return criteria representing the logical AND of the list of criteria
   */
  @Nonnull
  public static Criteria toAndCriteria(@Nonnull List<Criteria> criteria) {
    return switch (criteria.size()) {
      case 0 -> new Criteria();
      case 1 -> criteria.getFirst();
      default -> new Criteria().andOperator(criteria);
    };
  }

  /**
   * Return the simplest possible {@link Criteria} to represent the logical OR of the list of {@code
   * Criteria} provided. If the list is empty, an {@code Criteria} is returned. If the list contains
   * only one {@link Criteria} element, then it is returned unchanged.
   *
   * @param criteria list of criteria
   * @return criteria representing the logical OR of the list of criteria
   */
  @Nonnull
  public static Criteria toOrCriteria(@Nonnull List<Criteria> criteria) {
    return switch (criteria.size()) {
      case 0 -> new Criteria();
      case 1 -> criteria.getFirst();
      default -> new Criteria().orOperator(criteria);
    };
  }
}
