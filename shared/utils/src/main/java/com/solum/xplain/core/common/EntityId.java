package com.solum.xplain.core.common;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.bson.types.ObjectId;

/**
 * Simply a 24 character hexadecimal representation of a BSON ObjectId. Not to be confused with the
 * entityId within our VersionedEntity
 */
@Data
@AllArgsConstructor
public class EntityId {
  @Schema(description = "Object ID", requiredMode = REQUIRED)
  private String id;

  public static EntityId entityId(String id) {
    return new EntityId(id);
  }

  public static EntityId entityId(ObjectId id) {
    return new EntityId(id.toString());
  }
}
