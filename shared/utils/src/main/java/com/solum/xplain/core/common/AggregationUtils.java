package com.solum.xplain.core.common;

import java.time.LocalDate;
import java.time.ZoneId;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.bson.Document;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AggregationUtils {
  public static final String AGGREGATION_VAR = "var";
  public static final String AGGREGATION_VAR_REF = "$$" + AGGREGATION_VAR;

  public static LocalDate getDate(Document document, String path) {
    return LocalDate.ofInstant(document.getDate(path).toInstant(), ZoneId.systemDefault());
  }
}
