package com.solum.xplain.shared.utils.cache;

import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Arrays;
import lombok.SneakyThrows;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * Abstract base class for fetch delegates used by {@link CacheUnpagedAspect} to intercept and
 * modify method calls for unpaged caching.
 *
 * <p>This class provides the core functionality for finding and replacing {@link ScrollRequest}
 * parameters in AspectJ-intercepted method calls. It supports two variants:
 *
 * <ul>
 *   <li>{@link ScrollRequestFetchDelegate} - for methods returning {@link ScrollableEntry}
 *   <li>{@link EitherFetchDelegate} - for methods returning {@link Either}&lt;{@link ErrorItem},
 *       {@link ScrollableEntry}&gt;
 * </ul>
 *
 * <p>The delegates work by:
 *
 * <ol>
 *   <li>Capturing the original method parameters when constructed
 *   <li>Finding the first {@link ScrollRequest} parameter in the method signature
 *   <li>Replacing it with an unpaged version (offset=0, pageSize=0) when fetching for cache
 *   <li>Proceeding with the original method call using the modified parameters
 * </ol>
 *
 * <p>This allows the caching system to always fetch complete result sets while preserving the
 * original method's pagination interface.
 *
 * @see CacheUnpagedAspect
 * @see UnpagedCachingService
 */
abstract class FetchDelegate {
  /** The AspectJ join point representing the intercepted method call. */
  protected final ProceedingJoinPoint pjp;

  /** A copy of the original method parameters, which will be modified for cache fetching. */
  protected final Object[] params;

  /**
   * Constructs a new FetchDelegate for the given join point.
   *
   * @param pjp the AspectJ proceeding join point representing the intercepted method call
   */
  protected FetchDelegate(ProceedingJoinPoint pjp) {
    this.pjp = pjp;
    this.params = Arrays.copyOf(pjp.getArgs(), pjp.getArgs().length);
  }

  /**
   * Creates a fetch delegate for methods that have the signature pattern:
   *
   * <pre>{@code
   * ScrollableEntry<SomeType> methodName(..., ScrollRequest scrollRequest, ...)
   * }</pre>
   *
   * @param <T> the type of data contained in the ScrollableEntry
   * @param pjp the AspectJ proceeding join point representing the intercepted method call
   * @return a fetch delegate that implements {@link UnpagedCachingService.FetchWithScrollRequest}
   */
  public static <T> UnpagedCachingService.FetchWithScrollRequest<T> forScrollableEntry(
      ProceedingJoinPoint pjp) {
    return new ScrollRequestFetchDelegate<>(pjp);
  }

  /**
   * Creates a fetch delegate for methods that have the signature pattern:
   *
   * <pre>{@code
   * Either<ErrorItem, ScrollableEntry<SomeType>> methodName(..., ScrollRequest scrollRequest, ...)
   * }</pre>
   *
   * @param <T> the type of data contained in the ScrollableEntry
   * @param pjp the AspectJ proceeding join point representing the intercepted method call
   * @return a fetch delegate that implements {@link
   *     UnpagedCachingService.FetchWithScrollRequestEither}
   */
  public static <T> UnpagedCachingService.FetchWithScrollRequestEither<T> forScrollableEntryEither(
      ProceedingJoinPoint pjp) {
    return new EitherFetchDelegate<>(pjp);
  }

  /**
   * Executes the original method with the specified ScrollRequest parameter.
   *
   * <p>This method finds the first {@link ScrollRequest} parameter in the method signature,
   * replaces it with the provided scrollRequest, and then proceeds with the method execution.
   *
   * @param scrollRequest the ScrollRequest to use for the method call (typically an unpaged
   *     request)
   * @return the result of the method execution
   * @throws IllegalArgumentException if no ScrollRequest parameter is found in the method signature
   */
  @SneakyThrows
  protected Object proceedWithScrollRequest(ScrollRequest scrollRequest) {
    int scrollRequestIndex = findScrollRequestIndex(params);
    params[scrollRequestIndex] = scrollRequest;
    return pjp.proceed(params);
  }

  /**
   * Finds the index of the first {@link ScrollRequest} parameter in the method parameters.
   *
   * @param params the method parameters to search
   * @return the index of the first ScrollRequest parameter
   * @throws IllegalArgumentException if no ScrollRequest parameter is found
   */
  protected int findScrollRequestIndex(Object[] params) {
    for (int i = 0; i < params.length; i++) {
      if (params[i] instanceof ScrollRequest) {
        return i;
      }
    }
    throw new IllegalArgumentException("No ScrollRequest found in parameters");
  }

  /**
   * Fetch delegate implementation for methods that return {@link ScrollableEntry} directly.
   *
   * <p>This delegate is used for methods annotated with {@code @CacheUnpaged} that have the
   * signature pattern:
   *
   * <pre>{@code
   * ScrollableEntry<SomeType> methodName(..., ScrollRequest scrollRequest, ...)
   * }</pre>
   *
   * <p>When caching is performed, this delegate will call the original method with an unpaged
   * ScrollRequest (offset=0, pageSize=0) to fetch the complete result set.
   *
   * @param <T> the type of data contained in the ScrollableEntry
   */
  static class ScrollRequestFetchDelegate<T> extends FetchDelegate
      implements UnpagedCachingService.FetchWithScrollRequest<T> {

    /**
     * Constructs a new ScrollRequestFetchDelegate.
     *
     * @param pjp the AspectJ proceeding join point representing the intercepted method call
     */
    public ScrollRequestFetchDelegate(ProceedingJoinPoint pjp) {
      super(pjp);
    }

    /**
     * Executes the original method with the provided ScrollRequest and returns the result.
     *
     * @param scrollRequest the ScrollRequest to use for the method call
     * @return the ScrollableEntry result from the original method
     */
    @SuppressWarnings("unchecked")
    @Override
    public ScrollableEntry<T> fetch(ScrollRequest scrollRequest) {
      return (ScrollableEntry<T>) proceedWithScrollRequest(scrollRequest);
    }
  }

  /**
   * Fetch delegate implementation for methods that return {@link Either}&lt;{@link ErrorItem},
   * {@link ScrollableEntry}&gt;.
   *
   * <p>This delegate is used for methods annotated with {@code @CacheUnpaged} that have the
   * signature pattern:
   *
   * <pre>{@code
   * Either<ErrorItem, ScrollableEntry<SomeType>> methodName(..., ScrollRequest scrollRequest, ...)
   * }</pre>
   *
   * <p>When caching is performed, this delegate will call the original method with an unpaged
   * ScrollRequest (offset=0, pageSize=0) to fetch the complete result set. If the method returns a
   * successful result (Right), it will be cached. If it returns an error (Left), the error will be
   * propagated without caching.
   *
   * @param <T> the type of data contained in the ScrollableEntry
   */
  static class EitherFetchDelegate<T> extends FetchDelegate
      implements UnpagedCachingService.FetchWithScrollRequestEither<T> {

    /**
     * Constructs a new EitherFetchDelegate.
     *
     * @param pjp the AspectJ proceeding join point representing the intercepted method call
     */
    public EitherFetchDelegate(ProceedingJoinPoint pjp) {
      super(pjp);
    }

    /**
     * Executes the original method with the provided ScrollRequest and returns the Either result.
     *
     * @param scrollRequest the ScrollRequest to use for the method call
     * @return the Either result from the original method, containing either an ErrorItem (Left) or
     *     ScrollableEntry (Right)
     */
    @SuppressWarnings("unchecked")
    @Override
    public Either<ErrorItem, ScrollableEntry<T>> fetch(ScrollRequest scrollRequest) {
      return (Either<ErrorItem, ScrollableEntry<T>>) proceedWithScrollRequest(scrollRequest);
    }
  }
}
