package com.solum.xplain.shared.utils.filter;

import static com.solum.xplain.shared.utils.filter.FilterClause.emptyFilter;
import static java.util.Collections.singletonList;
import static java.util.Objects.requireNonNull;

import com.google.common.base.Splitter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jspecify.annotations.Nullable;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.mongodb.core.query.Criteria;

@ToString
@EqualsAndHashCode
public class TableFilter {

  private static final Splitter splitter = Splitter.on(",").trimResults();
  private final List<FilterClause> filter;

  public TableFilter(List<FilterClause> filter) {
    this.filter = requireNonNull(filter);
  }

  public Stream<FilterClause> stream() {
    return filter.stream();
  }

  @Nullable
  public SimpleFilterClause getFilterClauseFor(String property) {
    return filter.stream()
        .filter(
            clause ->
                (clause instanceof SimpleFilterClause sfc && sfc.getProperty().equals(property)))
        .map(SimpleFilterClause.class::cast)
        .findFirst()
        .orElse(null);
  }

  public static TableFilter emptyTableFilter() {
    return new TableFilter(singletonList(emptyFilter()));
  }

  /**
   * Parse comma-separated filter parameters into a TableFilter.
   *
   * @param params List of comma-separated "field,operator,param1,param2,..."
   */
  public static TableFilter parseParameters(List<String> params) {
    return new TableFilter(params.stream().map(TableFilter::createClause).toList());
  }

  /**
   * Parse comma-separated filter parameters into a TableFilter.
   *
   * @param param Array of comma-separated "field,operator,param1,param2,..."
   */
  public static TableFilter parseParameters(String[] param) {
    return new TableFilter(Stream.of(param).map(TableFilter::createClause).toList());
  }

  private static FilterClause createClause(String param) {
    return Optional.of(splitter.splitToList(param))
        .filter(s -> s.size() >= 2)
        .flatMap(
            s -> {
              var dataValues = s.subList(2, s.size());
              return FilterOperation.parse(s.get(1))
                  .map(op -> (FilterClause) new SimpleFilterClause(s.getFirst(), op, dataValues));
            })
        .orElseGet(FilterClause::emptyFilter);
  }

  public Criteria criteria(Class<?> type, ConversionService conversionService) {
    return criteria(null, type, List.of(), conversionService);
  }

  public Criteria criteria(
      Class<?> type, ConversionService conversionService, @Nullable String filterPrefix) {
    return criteria(filterPrefix, type, List.of(), conversionService);
  }

  public Criteria criteria(
      Class<?> type, NestedCollectionMapping mappings, ConversionService conversionService) {
    return criteria(null, type, List.of(mappings), conversionService);
  }

  public Criteria criteria(
      @Nullable String filterPrefix,
      Class<?> type,
      List<NestedCollectionMapping> mappings,
      ConversionService conversionService) {
    Criteria[] criteria =
        filter.stream()
            .filter(FilterClause::isNotEmpty)
            .map(c -> c.criteria(filterPrefix, type, mappings, conversionService))
            .toArray(Criteria[]::new);
    if (criteria.length == 0) {
      return new Criteria();
    } else {
      return new Criteria().andOperator(criteria);
    }
  }
}
