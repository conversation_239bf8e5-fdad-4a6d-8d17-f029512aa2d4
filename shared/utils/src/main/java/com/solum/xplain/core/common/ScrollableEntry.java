package com.solum.xplain.core.common;

import java.util.List;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.Nullable;

@Data
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ScrollableEntry<T> {

  private final List<T> content;
  private final long startRow;
  private final long endRow;
  @Nullable private final Long lastRow;

  public ScrollableEntry(ScrollableEntry<T> page) {
    this.content = page.content;
    this.startRow = page.startRow;
    this.endRow = page.endRow;
    this.lastRow = page.lastRow;
  }

  public static <T> ScrollableEntry<T> of(List<T> items, ScrollRequest request, long total) {
    return new ScrollableEntry<>(items, request.getStartRow(), request.getEndRow(), total);
  }

  public static <T> ScrollableEntry<T> of(List<T> items, ScrollRequest request) {
    Long lastRow = null;
    if (items.size() < request.pageSize()) {
      lastRow = request.getStartRow() + items.size();
    }
    return new ScrollableEntry<>(items, request.getStartRow(), request.getEndRow(), lastRow);
  }

  public static <T> ScrollableEntry<T> limitByPageSize(List<T> items, ScrollRequest request) {
    if (items.size() == request.pageSize()) {
      return ScrollableEntry.of(items, request, request.getEndRow());
    }
    return ScrollableEntry.of(items.stream().limit(request.pageSize()).toList(), request);
  }

  public static <T> ScrollableEntry<T> empty() {
    return new ScrollableEntry<>(List.of(), 0, 0, null);
  }

  public <U> ScrollableEntry<U> map(Function<? super T, ? extends U> converter) {
    List<U> mapped = (List<U>) this.content.stream().map(converter).toList();
    return new ScrollableEntry<>(mapped, startRow, endRow, lastRow);
  }
}
