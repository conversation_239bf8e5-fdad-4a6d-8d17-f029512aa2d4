package com.solum.xplain.core.common.value;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class DateList {
  private final List<LocalDate> dates;

  public static DateList uniqueSorted(List<LocalDate> localDates) {
    return new DateList(Set.copyOf(localDates).stream().sorted().toList());
  }

  public boolean notEmpty() {
    return !dates.isEmpty();
  }
}
