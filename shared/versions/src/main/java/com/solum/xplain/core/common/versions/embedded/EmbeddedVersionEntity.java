package com.solum.xplain.core.common.versions.embedded;

import static com.solum.xplain.core.common.versions.State.DELETED;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.groupingBy;

import java.time.LocalDate;
import java.util.List;
import java.util.Map.Entry;
import java.util.Optional;
import lombok.NonNull;

public interface EmbeddedVersionEntity<V> {

  String ID_FIELD = "id";
  String SEMANTIC_ID_FIELD = "semanticId";
  String VERSIONS_FIELD = "versions";

  /**
   * Default usually autogenerated UUID used to uniquely identify record, primary key @Id.
   *
   * <p>Known ONLY after entity is created.
   *
   * @return id
   */
  String getId();

  /**
   * Semantic id that logically identifies entity uniquely.
   *
   * <p>Known ALWAYS as semanticId has nothing to do with an entity itself, semanticId is a part of
   * the business domain
   *
   * @return semanticId
   */
  String getSemanticId();

  @NonNull
  List<EmbeddedVersion<V>> getVersions();

  void setVersions(@NonNull List<EmbeddedVersion<V>> versions);

  default Optional<EmbeddedVersion<V>> versionAt(LocalDate stateDate) {
    var latestValidFrom =
        getVersions().stream()
            .collect(groupingBy(EmbeddedVersion::getValidFrom))
            .entrySet()
            .stream()
            .filter(
                e ->
                    e.getValue().stream()
                        .max(comparing(EmbeddedVersion::getRecordFrom))
                        .filter(v -> v.getState() != DELETED)
                        .isPresent())
            .map(Entry::getKey)
            .filter(validFrom -> !validFrom.isAfter(stateDate))
            .max(LocalDate::compareTo);

    return getVersions().stream()
        .filter(v -> latestValidFrom.filter(l -> v.getValidFrom().equals(l)).isPresent())
        .max(comparing(EmbeddedVersion::getRecordFrom));
  }

  default Optional<EmbeddedVersion<V>> exactVersionAt(LocalDate stateDate) {
    return getVersions().stream()
        .filter(v -> v.getValidFrom().equals(stateDate))
        .max(comparing(EmbeddedVersion::getRecordFrom))
        .filter(v -> v.getState() != DELETED);
  }

  default boolean hasFutureVersion(LocalDate stateDate) {
    return getVersions().stream()
        .collect(groupingBy(EmbeddedVersion::getValidFrom))
        .entrySet()
        .stream()
        .filter(
            e ->
                e.getValue().stream()
                    .max(comparing(EmbeddedVersion::getRecordFrom))
                    .filter(v -> v.getState() != DELETED)
                    .isPresent())
        .map(Entry::getKey)
        .anyMatch(validFrom -> validFrom.isAfter(stateDate));
  }
}
