package com.solum.xplain.core.common.versions.embedded.update;

import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;
import static com.solum.xplain.core.common.versions.State.ACTIVE;
import static com.solum.xplain.core.common.versions.State.ARCHIVED;
import static com.solum.xplain.core.common.versions.State.DELETED;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.value.FutureVersionsAction;
import com.solum.xplain.core.common.value.HasDetailsForArchive;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.AuditorAware;

@AllArgsConstructor
public class EntityUpdateOperations<V, T extends EmbeddedVersionEntity<V>> {

  private final AuditorAware<AuditUser> auditUserAuditorAware;
  private final Supplier<LocalDateTime> currentDateTimeSupplier;

  public EntityUpdateOperations(AuditorAware<AuditUser> auditUserAuditorAware) {
    this.auditUserAuditorAware = auditUserAuditorAware;
    this.currentDateTimeSupplier = LocalDateTime::now;
  }

  public EntityUpdateResult<V, T> createEntity(T entity, V value, NewVersionFormV2 options) {
    var now = currentDateTimeSupplier.get();
    var newVersions = ImmutableList.<EmbeddedVersion<V>>builder();
    var versionDate = options.getValidFrom();
    newVersions.add(newVersion(versionDate, now, ACTIVE, options.getComment(), value));

    if (!ROOT_DATE.equals(versionDate)) {
      newVersions.add(newVersion(ROOT_DATE, now, ARCHIVED, options.getComment(), value));
    }
    return new EntityUpdateResult<>(entity, newVersions.build());
  }

  public EntityUpdateResult<V, T> updateEntity(
      EntityForUpdate<V, T> entityForUpdate, V value, NewVersionFormV2 options) {
    var now = currentDateTimeSupplier.get();
    var newVersions = ImmutableList.<EmbeddedVersion<V>>builder();
    var entity = entityForUpdate.getEntity();
    var version = entityForUpdate.getVersion();
    var validFrom = defaultIfNull(options.getValidFrom(), version.getValidFrom());
    if (!value.equals(version.getValue()) || version.getState() != ACTIVE) {
      newVersions.add(newVersion(validFrom, now, ACTIVE, options.getComment(), value));
    }
    if (options.getFutureVersionsAction() == FutureVersionsAction.DELETE) {
      newVersions.addAll(deleteFuture(entity, validFrom, now));
    }
    return new EntityUpdateResult<>(entity, newVersions.build());
  }

  public EntityUpdateResult<V, T> archiveEntity(
      EntityForUpdate<V, T> entityForUpdate, HasDetailsForArchive options) {
    var now = currentDateTimeSupplier.get();
    var newVersions = ImmutableList.<EmbeddedVersion<V>>builder();
    var entity = entityForUpdate.getEntity();
    var version = entityForUpdate.getVersion();
    if (version.getState() == State.ACTIVE) {
      var archiveFrom = defaultIfNull(options.getValidFrom(), version.getValidFrom());
      newVersions.add(
          newVersion(archiveFrom, now, ARCHIVED, options.getComment(), version.getValue()));
      if (options.getFutureVersionsAction() == FutureVersionsAction.DELETE) {
        newVersions.addAll(deleteFuture(entity, archiveFrom, now));
      }
    }
    return new EntityUpdateResult<>(entity, newVersions.build());
  }

  public EntityUpdateResult<V, T> deleteEntity(
      EntityForUpdate<V, T> entityForUpdate, LocalDate validFrom) {
    var now = currentDateTimeSupplier.get();
    var newVersions = ImmutableList.<EmbeddedVersion<V>>builder();
    delete(entityForUpdate.getVersion(), validFrom, now).ifPresent(newVersions::add);
    return new EntityUpdateResult<>(entityForUpdate.getEntity(), newVersions.build());
  }

  private List<EmbeddedVersion<V>> deleteFuture(T entity, LocalDate stateDate, LocalDateTime now) {
    return entity.getVersions().stream()
        .map(EmbeddedVersion::getValidFrom)
        .filter(validFrom -> validFrom.isAfter(stateDate))
        .distinct()
        .flatMap(vf -> entity.exactVersionAt(vf).stream())
        .map(v -> delete(v, v.getValidFrom(), now))
        .flatMap(Optional::stream)
        .toList();
  }

  private Optional<EmbeddedVersion<V>> delete(
      EmbeddedVersion<V> version, LocalDate validFrom, LocalDateTime now) {
    if (version.getState() == DELETED || version.getValidFrom().equals(ROOT_DATE)) {
      return Optional.empty();
    }
    return Optional.of(newVersion(validFrom, now, DELETED, null, version.getValue()));
  }

  private EmbeddedVersion<V> newVersion(
      LocalDate validFrom, LocalDateTime now, State state, String comment, V value) {
    var auditUser = auditUserAuditorAware.getCurrentAuditor().orElse(null);
    return new EmbeddedVersion<>(validFrom, now, state, comment, auditUser, value);
  }
}
