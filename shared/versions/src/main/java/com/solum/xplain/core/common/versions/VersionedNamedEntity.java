package com.solum.xplain.core.common.versions;

import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VersionedNamedEntity extends VersionedEntity {
  private String name;

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  @Override
  public boolean valueEquals(Object object) {
    VersionedNamedEntity entity = (VersionedNamedEntity) object;
    return super.valueEquals(entity) && Objects.equals(this.name, entity.name);
  }
}
