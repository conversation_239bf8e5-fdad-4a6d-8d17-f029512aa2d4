package com.solum.xplain.core.common.value;

import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import org.springframework.lang.NonNull;

@Data
public class VersionedList<T> {
  private final LocalDate versionDate;
  @lombok.NonNull private final List<T> list;

  public static <T> VersionedList<T> atEpochStart(@NonNull List<T> list) {
    return new VersionedList<>(LocalDate.ofEpochDay(0), list);
  }

  public static <T> VersionedList<T> empty() {
    return new VersionedList<>(null, List.of());
  }
}
