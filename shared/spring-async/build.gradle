
dependencies {
  compileOnly "io.micrometer:micrometer-core"
  implementation "org.projectlombok:lombok:${lombokVersion}"
  implementation "org.springframework.boot:spring-boot-starter-web"
  implementation "com.github.ben-manes.caffeine:caffeine"

  testImplementation "org.spockframework:spock-core:${spockVersion}"

  integrationTestImplementation "io.micrometer:micrometer-core"
  integrationTestImplementation "org.spockframework:spock-core:${spockVersion}"
  integrationTestImplementation 'org.springframework:spring-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-log4j2'

  annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
}

configurations {
  all*.exclude module: 'spring-boot-starter-logging'
}

tasks.register('sourceJar', Jar) {
  from sourceSets.main.allJava
}
