<?xml version="1.0" encoding="UTF-8" ?>
<project name="MongoDb" id="Project_be1" database="MongoDb" >
	<schema name="solum-qa" >
		<table name="Collection" />
		<table name="auditEntry" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="description" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="collectionName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="referenceId" type="string" length="800" decimal="0" jt="12" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="createdAt" unique="NORMAL" >
				<column name="createdAt" />
			</index>
		</table>
		<table name="auditEntryItem" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="auditEntryId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="reason" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="description" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="auditEntryId" unique="NORMAL" >
				<column name="auditEntryId" />
			</index>
			<fk name="fk_auditentryitem_auditentry" virtual="y" to_schema="solum-qa" to_table="auditEntry" >
				<fk_column name="auditEntryId" pk="_id" />
			</fk>
		</table>
		<table name="breakTest" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="assetFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClasses" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="irInstruments" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
			</column>
			<column name="scope" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="overrideAssetFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="curveNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="surfaceNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="assetClasses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="irInstruments" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				</column>
				<column name="tenorFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="allowAllTenors" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tenorBuckets" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="operator1" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tenor1" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="operator2" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tenor2" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="threshold" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="measureType" type="string" length="800" decimal="0" jt="12" />
			<column name="operator" type="string" length="800" decimal="0" jt="12" />
			<column name="threshold" type="double" length="800" decimal="0" jt="8" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="zScoreObservationPeriod" type="string" length="800" decimal="0" jt="12" />
			<column name="parentTest" type="object" length="800" decimal="0" jt="4999544" >
				<column name="parentId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="calculationPortfolioItem" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="calculationResultId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="portfolioId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="tradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="productType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="tradeDetails" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="positionType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLeg" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
					<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="receiveLeg" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
					<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="info" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="externalTradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tradeDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				</column>
				<column name="cdsTradeDetails" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="corpTicker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="seniority" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="docClause" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="startDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="endDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="valuationStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			<column name="creditSector" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="currencies" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="metrics" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="presentValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="presentValuePayLegCurrency" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="cleanPresentValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="cleanPresentValueLocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="payLegPV" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="receiveLegPV" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="dv01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="dv01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="br01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="br01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="inf01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="inf01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="cs01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="cs01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="infcsbr01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="infcsbr01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="pv01" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="pv01LocalCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="cashFlowMetrics" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="tZeroPay" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="tZeroRec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="tZeroNet" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="tZeroNetReportingCcy" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="dv01TradeValues" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="curveName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="sensitivities" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="EUR" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
						<column name="JPY" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
						<column name="USD" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
					</column>
				</column>
				<column name="br01TradeValues" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="inf01TradeValues" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="cs01TradeValues" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="curveName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="sensitivities" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="EUR" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
						<column name="JPY" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
						<column name="USD" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="sensitivity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						</column>
					</column>
				</column>
				<column name="clientMetrics" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
				<column name="breakevenMetrics" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="parRate" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="calculation_portfolioItemId" unique="UNIQUE_KEY" >
				<column name="calculationResultId" />
				<column name="tradeId" />
			</index>
			<fk name="fk_calculationportfolioitem" virtual="y" to_schema="solum-qa" to_table="portfolioItem" >
				<fk_column name="portfolioId" pk="portfolioId" />
				<fk_column name="tradeId" pk="entityId" />
			</fk>
			<fk name="fk_calculationportfolioitem_cr" virtual="y" to_schema="solum-qa" to_table="calculationResult" >
				<fk_column name="calculationResultId" pk="_id" />
			</fk>
			<fk name="fk_calculationportfolioitem_crx" virtual="y" to_schema="solum-qa" to_table="calculationResultXva" >
				<fk_column name="calculationResultId" pk="_id" />
			</fk>
		</table>
		<table name="calculationResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="calculationResultStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="calculationType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="portfolioId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="marketData" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="configurationData" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="curveGroup" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="curveConfiguration" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="calculationResultChartData" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="indexName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="valueType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="chartPoints" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
						<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					</column>
				</column>
			</column>
			<column name="curveDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="stateDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="reportingCcy" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="discountingType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="strippingType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" />
			<column name="comments" type="string" length="800" decimal="0" jt="12" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="status_portfolio.portfolioId" unique="UNIQUE_KEY" >
				<column name="calculationResultStatus" />
				<column name="portfolioId" />
			</index>
			<fk name="fk_calculationresult_dashboard" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
			<fk name="fk_calculationresult" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="marketData._id" pk="_id" />
				<fk_column name="marketData.name" pk="name" />
			</fk>
			<fk name="fk_calculationresult_cc" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="configurationData.curveGroup.entityId" pk="curveGroupId" />
				<fk_column name="configurationData.curveConfiguration.entityId" pk="entityId" />
				<fk_column name="configurationData.curveConfiguration.name" pk="name" />
			</fk>
			<fk name="fk_calculationresult_cg" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="configurationData.curveGroup.name" pk="name" />
			</fk>
		</table>
		<table name="calculationResultXva" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="calculationResultId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="partyResults" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="partyExposures" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="cheapestToDeliverCurve" generator_rows="100" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="interpolator" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="extrapolatorLeft" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="extrapolatorRight" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="eligibleCurrencies" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="cheapestToDeliverCurveCalibrationResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="curveId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="calibratedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="curveDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="calibrationMarketDataGroup" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="sourceType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="chartPoints" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="CHF" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="AUD" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="JPY" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="MXN" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="EUR" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="GBP" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="CAD" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="USD" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="NZD" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="NOK" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="BRL" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="underlyingCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_cheapesttodelivercurvecalibrationresult" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="calibrationMarketDataGroup.marketDataGroupId" pk="_id" />
				<fk_column name="calibrationMarketDataGroup.marketDataGroupName" pk="name" />
			</fk>
			<fk name="fk_cheapesttodelivercurvecalibrationresult_cc" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="calibrationMarketDataGroup.curveConfigurationId" pk="entityId" />
			</fk>
			<fk name="fk_cheapesttodelivercurvecalibrationresult_ctd" virtual="y" to_schema="solum-qa" to_table="cheapestToDeliverCurve" >
				<fk_column name="curveId" pk="entityId" />
			</fk>
		</table>
		<table name="cleanMarketData" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="exceptionManagementId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="instrumentKey" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="providersValues" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="previousValue" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="curveConfigurationsValues" type="array[object]" length="800" decimal="0" jt="4999545" >
					<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
			</column>
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_cleanmarketdata" virtual="y" to_schema="solum-qa" to_table="instrumentResultOverlay" >
				<fk_column name="values.curveConfigurationsValues.curveConfigurationId" pk="curveConfigurationId" />
			</fk>
			<fk name="fk_cleanmarketdata_irp" virtual="y" to_schema="solum-qa" to_table="instrumentResultPreliminary" >
				<fk_column name="exceptionManagementId" pk="exceptionManagementResultId" />
				<fk_column name="values.providersValues" pk="providerData" />
				<fk_column name="values.instrumentKey" pk="instrument.key" />
			</fk>
		</table>
		<table name="clientPvComparisonResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="comparisonCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="correlationId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="calculationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="clientPvComparisonResultItem" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="correlationId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="view" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="valuationStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="metricsPresentValue" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPresentValuePayLegCurrency" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCleanPresentValue" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCleanPresentValueLocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPayLegPV" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsReceiveLegPV" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsDv01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsDv01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsBr01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsBr01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsInf01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsInf01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCs01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCs01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsInfcsbr01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsInfcsbr01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPv01" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPv01LocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsBreakevenParRate" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCFTZeroPay" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCFTZeroRec" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCFTZeroNet" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsCFTZeroNetReportingCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="tradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoExternalTradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoTradeType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoTradeDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="notional" type="double" length="800" decimal="0" jt="8" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoStartDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="tradeInfoEndDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="payLegCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLegNotionalValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="payLegRateMargin" type="double" length="800" decimal="0" jt="8" />
				<column name="payLegIndex" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLegFrequency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLegDayCount" type="string" length="800" decimal="0" jt="12" />
				<column name="receiveLegCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="receiveLegNotionalValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="receiveLegRateMargin" type="double" length="800" decimal="0" jt="8" />
				<column name="receiveLegIndex" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="receiveLegFrequency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="receiveLegDayCount" type="string" length="800" decimal="0" jt="12" />
				<column name="currencies" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="metricsPvDelta" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvDeltaLocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvGamma" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvGammaLocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvTheta" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvThetaLocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvVega" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsPvVegaLocalCcy" type="double" length="800" decimal="0" jt="8" />
				<column name="metricsBreakevenImpliedVol" type="double" length="800" decimal="0" jt="8" />
				<column name="tradeInfoPosition" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoExpiryDate" type="date" length="800" decimal="0" jt="91" />
				<column name="tradeInfoPremiumDate" type="date" length="800" decimal="0" jt="91" />
				<column name="tradeInfoPremiumValueAmount" type="double" length="800" decimal="0" jt="8" />
				<column name="tradeInfoStrike" type="double" length="800" decimal="0" jt="8" />
				<column name="valuationError" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_clientpvcomparisonresultitem" virtual="y" to_schema="solum-qa" to_table="comparisonResult" >
				<fk_column name="correlationId" pk="correlationId" />
			</fk>
		</table>
		<table name="company" generator_rows="150" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="allowAllTeams" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teamIds" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="numberOfPortfolios" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="description" type="string" length="800" decimal="0" jt="12" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_company_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teamIds" pk="_id" />
			</fk>
		</table>
		<table name="companyIpvSettings" generator_rows="337" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="products" type="object" length="800" decimal="0" jt="4999544" >
				<column name="IRS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="FXOPT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="XCCY" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CAP_FLOOR" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="INFLATION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="FXFWD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="SWAPTION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CREDIT_INDEX" type="object" length="800" decimal="0" jt="4999544" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_companyipvsettings_company" virtual="y" to_schema="solum-qa" to_table="company" >
				<fk_column name="entityId" pk="_id" />
			</fk>
			<fk name="fk_companyipvsettings" virtual="y" to_schema="solum-qa" to_table="ipvDataGroup" >
				<fk_column name="ipvDataGroup.entityId" pk="_id" />
				<fk_column name="ipvDataGroup.name" pk="name" />
			</fk>
			<fk name="fk_companyipvsettings_dp1" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="products.IRS.primary" pk="externalId" />
			</fk>
			<fk name="fk_companyipvsettings_dp2" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="products.IRS.secondary" pk="externalId" />
			</fk>
		</table>
		<table name="companyLegalEntity" generator_rows="150" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="externalId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="allowAllTeams" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teamIds" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="description" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_companylegalentity" virtual="y" to_schema="solum-qa" to_table="companyLegalEntity" >
				<fk_column name="companyId" pk="companyId" />
			</fk>
			<fk name="fk_companylegalentity_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teamIds" pk="_id" />
			</fk>
		</table>
		<table name="companyLegalEntityIpvSettings" generator_rows="337" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="products" type="object" length="800" decimal="0" jt="4999544" >
				<column name="IRS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CAP_FLOOR" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="FXOPT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="SWAPTION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="INFLATION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="XCCY" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="FXFWD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="CREDIT_INDEX" type="object" length="800" decimal="0" jt="4999544" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="CDX" type="object" length="800" decimal="0" jt="4999544" >
					<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
				</column>
			</column>
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_companylegalentityipvsettings" virtual="y" to_schema="solum-qa" to_table="companyLegalEntity" >
				<fk_column name="companyId" pk="companyId" />
				<fk_column name="entityId" pk="_id" />
			</fk>
			<fk name="fk_companylegalentityipvsettings_ivp" virtual="y" to_schema="solum-qa" to_table="ipvDataGroup" >
				<fk_column name="ipvDataGroup.entityId" pk="_id" />
				<fk_column name="ipvDataGroup.name" pk="name" />
			</fk>
			<fk name="fk_companylegalentityipvsettings_dp1" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="products.IRS.primary" pk="externalId" />
			</fk>
			<fk name="fk_companylegalentityipvsettings_dp2" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="products.IRS.secondary" pk="externalId" />
			</fk>
		</table>
		<table name="companyLegalEntityValuationSettings" generator_rows="225" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="marketDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="configurationType" type="string" length="800" decimal="0" jt="12" />
			<column name="curveConfiguration" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="curveStripping" type="string" length="800" decimal="0" jt="12" />
			<column name="discountingType" type="string" length="800" decimal="0" jt="12" />
			<column name="reportingCurrency" type="string" length="800" decimal="0" jt="12" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="nonFxCurveConfiguration" type="object" jt="4999544" >
				<column name="entityId" type="string" jt="12" />
				<column name="name" type="string" jt="12" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_companylegalentityvaluationsettings" virtual="y" to_schema="solum-qa" to_table="companyLegalEntity" >
				<fk_column name="companyId" pk="companyId" />
				<fk_column name="entityId" pk="_id" />
			</fk>
			<fk name="fk_companylegalentityvaluationsettings_mdg" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="marketDataGroup.marketDataGroupId" pk="_id" />
				<fk_column name="marketDataGroup.marketDataGroupName" pk="name" />
			</fk>
			<fk name="fk_companylegalentityvaluationsettings_cc" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="curveConfiguration.entityId" pk="entityId" />
				<fk_column name="curveConfiguration.name" pk="name" />
			</fk>
			<fk name="fk_companylegalentityvaluationsettings_cc2" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="nonFxCurveConfiguration.entityId" pk="entityId" />
				<fk_column name="nonFxCurveConfiguration.name" pk="name" />
			</fk>
		</table>
		<table name="companyValuationSettings" generator_rows="225" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="marketDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="configurationType" type="string" length="800" decimal="0" jt="12" />
			<column name="curveConfiguration" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="curveStripping" type="string" length="800" decimal="0" jt="12" />
			<column name="discountingType" type="string" length="800" decimal="0" jt="12" />
			<column name="reportingCurrency" type="string" length="800" decimal="0" jt="12" />
			<column name="nonFxCurveConfiguration" type="object" jt="4999544" >
				<column name="entityId" type="string" jt="12" />
				<column name="name" type="string" jt="12" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_companyvaluationsettings" virtual="y" to_schema="solum-qa" to_table="company" >
				<fk_column name="entityId" pk="_id" />
			</fk>
			<fk name="fk_companyvaluationsettings_mdg" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="marketDataGroup.marketDataGroupId" pk="_id" />
				<fk_column name="marketDataGroup.marketDataGroupName" pk="name" />
			</fk>
			<fk name="fk_companyvaluationsettings_cc" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="curveConfiguration.entityId" pk="_id" />
				<fk_column name="curveConfiguration.name" pk="name" />
			</fk>
			<fk name="Fk_companyValuationSettings_cc2" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="nonFxCurveConfiguration.entityId" pk="entityId" />
				<fk_column name="nonFxCurveConfiguration.name" pk="name" />
			</fk>
		</table>
		<table name="comparisonResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="comparisonCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="correlationId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="calculationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="against1CalculationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="curveConfigComparisonDifferenceChartData" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="indexName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="valueType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="chartPoints" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="calculatedValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="maturity" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
			</column>
			<column name="fxCurveConfigComparisonDifferenceChartData" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="comparisonResultCreatedAt" unique="UNIQUE_KEY" >
				<column name="createdAt" />
			</index>
			<fk name="fk_comparisonresult" virtual="y" to_schema="solum-qa" to_table="calculationResult" >
				<fk_column name="calculationId" pk="_id" />
			</fk>
			<fk name="fk_comparisonresult_against" virtual="y" to_schema="solum-qa" to_table="calculationResult" >
				<fk_column name="against1CalculationId" pk="_id" />
			</fk>
		</table>
		<table name="comparisonResultItem" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="correlationId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="view" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="comparable" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="metricsPresentValueN1" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="metricsDv01N1" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="valuationError" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="valuationStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="portfolioId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoExternalTradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoTradeType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoTradeDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeInfoStartDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="tradeInfoEndDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="payLegCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLegNotionalValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="payLegIndex" type="string" length="800" decimal="0" jt="12" />
				<column name="payLegFrequency" type="string" length="800" decimal="0" jt="12" />
				<column name="receiveLegCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="receiveLegNotionalValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="receiveLegRateMargin" type="double" length="800" decimal="0" jt="8" />
				<column name="receiveLegIndex" type="string" length="800" decimal="0" jt="12" />
				<column name="receiveLegFrequency" type="string" length="800" decimal="0" jt="12" />
				<column name="receiveLegDayCount" type="string" length="800" decimal="0" jt="12" />
				<column name="currencies" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="payLegRateMargin" type="double" length="800" decimal="0" jt="8" />
				<column name="payLegDayCount" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoPosition" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoCorpTicker" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoSeniority" type="string" length="800" decimal="0" jt="12" />
				<column name="tradeInfoDocClause" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="comparisonResultItemCorrelationId" unique="UNIQUE_KEY" >
				<column name="correlationId" />
			</index>
			<index name="comparisonResultItemCreatedAt" unique="UNIQUE_KEY" >
				<column name="createdAt" />
			</index>
			<fk name="fk_comparisonresultitem" virtual="y" to_schema="solum-qa" to_table="comparisonResult" >
				<fk_column name="correlationId" pk="correlationId" />
			</fk>
		</table>
		<table name="convexityAdjustmentsSettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="curveConvexityAdjustments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="USD 3M" type="object" length="800" decimal="0" jt="4999544" >
					<column name="mean" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="zeroTenorVol" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="volatilities" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="9M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="18M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="6M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="2Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					</column>
				</column>
				<column name="EUR 3M" type="object" length="800" decimal="0" jt="4999544" >
					<column name="mean" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="zeroTenorVol" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="volatilities" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="9M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="18M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="6M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="2Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					</column>
				</column>
				<column name="GBP 3M" type="object" length="800" decimal="0" jt="4999544" >
					<column name="mean" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="zeroTenorVol" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="volatilities" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="9M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="18M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="6M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="2Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1Y" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="3M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="1M" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					</column>
				</column>
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="correlationMatrix" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="domesticCcy" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="_id" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="key1" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="ccy" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="key2" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="ccy" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="valid_from_record_date" unique="UNIQUE_KEY" >
				<column name="validFrom" />
				<column name="recordDate" />
			</index>
		</table>
		<table name="creditCurve" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="corpTicker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="sector" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="seniority" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="docClause" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="legalEntityName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recoveryRate" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			<column name="cdsQuoteConvention" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="indexNodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="cdsNodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="fundingNodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_creditcurve_curvegroup" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="curveGroupId" pk="_id" />
			</fk>
		</table>
		<table name="csaEntity" generator_rows="225" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="externalCsaEntityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="description" type="string" length="800" decimal="0" jt="12" />
			<column name="ctdCurveKey" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="eligibleCurrencies" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
			</column>
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_csaentity_company" virtual="y" to_schema="solum-qa" to_table="company" >
				<fk_column name="companyId" pk="_id" />
			</fk>
			<fk name="fk_csaentity" virtual="y" to_schema="solum-qa" to_table="cheapestToDeliverCurve" >
				<fk_column name="ctdCurveKey.type" pk="type" />
				<fk_column name="ctdCurveKey.eligibleCurrencies" pk="eligibleCurrencies" />
			</fk>
		</table>
		<table name="curve" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="curveType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="explicitDiscount" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="interpolator" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="extrapolatorLeft" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="extrapolatorRight" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="convention" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="period" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="fraSettlement" type="string" length="800" decimal="0" jt="12" />
				<column name="serialFuture" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="chartPoints" type="array[object]" length="800" decimal="0" jt="4999545" />
			<column name="calibrationDiscountCurrency" type="string" length="800" decimal="0" jt="12" />
			<column name="discountFactorPoints" type="array[object]" length="800" decimal="0" jt="4999545" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curve_curvegroup" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="curveGroupId" pk="_id" />
			</fk>
		</table>
		<table name="curveConfiguration" generator_rows="150" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="CTD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FRA" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" />
					<column name="secondary" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="CREDIT_INDEX" type="object" length="800" decimal="0" jt="4999544" >
					<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="priority" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
				<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="CDS" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="FRA" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
					<column name="CREDIT_INDEX" type="object" length="800" decimal="0" jt="4999544" >
						<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					</column>
				</column>
				<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curveconfiguration" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="curveGroupId" pk="_id" />
			</fk>
			<fk name="fk_curveconfiguration_dp1" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="instruments.XCCY_IBOR_IBOR_SWAP.primary" pk="externalId" />
			</fk>
			<fk name="Fk_curveConfiguration_dp2" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="instruments.XCCY_IBOR_IBOR_SWAP.secondary" pk="externalId" />
			</fk>
			<fk name="Fk_curveConfiguration_dp3" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="instruments.CDS.primary" pk="externalId" />
			</fk>
			<fk name="fk_curveconfiguration_dp4" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="instruments.CDS.secondary" pk="externalId" />
			</fk>
		</table>
		<table name="curveGroup" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="updatedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="calibrationStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="numberOfCurves" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="numberOfVolatilitySurfaces" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="numberOfCreditCurves" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="numberOfFxRateNodes" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="numberOfFxVolatilities" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="numberOfInflationVolatilities" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="currency" type="string" length="800" decimal="0" jt="12" />
			<column name="calibratedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="calibrationDate" type="date" length="800" decimal="0" jt="91" />
			<column name="calibrationMarketDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="sourceType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curvegroup_marketdatagroup" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="calibrationMarketDataGroup.marketDataGroupId" pk="_id" />
				<fk_column name="calibrationMarketDataGroup.marketDataGroupName" pk="name" />
			</fk>
			<fk name="fk_curvegroup" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="calibrationMarketDataGroup.curveConfigurationId" pk="entityId" />
			</fk>
		</table>
		<table name="curveGroupFxRates" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="domesticCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="foreignCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curvegroupfxrates" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="entityId" pk="_id" />
			</fk>
		</table>
		<table name="curveGroupFxVolatility" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="expiry" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="domesticCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="foreignCurrency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="delta1" type="integer" length="800" decimal="0" jt="4" />
				<column name="delta2" type="integer" length="800" decimal="0" jt="4" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curvegroupfxvolatility" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="entityId" pk="_id" />
			</fk>
		</table>
		<table name="curveGroupInflationVolatility" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="expiry" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="priceIndex" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_curvegroupinflationvolatility" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="entityId" pk="_id" />
			</fk>
		</table>
		<table name="curveStrippingDiscountSettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="explicitEurIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitUsdIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitGbpIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="explicitAudIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitCadIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitChfIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitJpyIndices" type="array" length="800" decimal="0" jt="4999545" />
			<column name="explicitNzdIndices" type="array" length="800" decimal="0" jt="4999545" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="curveStrippingProductSettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="cdsCurvePriorities" type="array" length="800" decimal="0" jt="4999545" />
			<column name="fxCurvePriorities" type="array" length="800" decimal="0" jt="4999545" />
			<column name="fxCcyPriority" type="array" length="800" decimal="0" jt="4999545" />
			<column name="fxCcyFallback" type="string" length="800" decimal="0" jt="12" />
			<column name="inflationCurvePriorities" type="array" length="800" decimal="0" jt="4999545" />
			<column name="xccyCcy" type="array" length="800" decimal="0" jt="4999545" />
			<column name="xccyCcyFallback" type="string" length="800" decimal="0" jt="12" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="dashboard" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="stateDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="dateRange" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="startDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="endDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="saved" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="mdExceptionManagementSetup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="marketDataGroup" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="curveConfigurationResolvers" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="CTD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FRA" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
					</column>
					<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" >
						<column name="priority" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
						<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
								<column name="secondary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FRA" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
							<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="primary" type="string" length="800" decimal="0" jt="12" />
							</column>
							<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
								<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
								<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							</column>
						</column>
						<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="vdExceptionManagementSetup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="portfoliosFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="companies" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="entities" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
							<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="portfolioIds" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
					</column>
					<column name="companiesPortfoliosLabels" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
				</column>
				<column name="ipvValuationSettings" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="company" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="entity" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="portfolio" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="marketDataGroup" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="ipvDataGroup" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="hasXplainProvider" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="products" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="XCCY" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FXFWD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="IRS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="FXOPT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="SWAPTION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="CAP_FLOOR" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="INFLATION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
					</column>
				</column>
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_dashboard_marketdatagroup" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="mdExceptionManagementSetup.marketDataGroup.name" pk="name" />
				<fk_column name="mdExceptionManagementSetup.marketDataGroup.entityId" pk="_id" />
			</fk>
			<fk name="fk_dashboard" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="mdExceptionManagementSetup.curveConfigurationResolvers._id" pk="entityId" />
				<fk_column name="mdExceptionManagementSetup.curveConfigurationResolvers.name" pk="name" />
				<fk_column name="mdExceptionManagementSetup.curveConfigurationResolvers.curveGroupId" pk="curveGroupId" />
				<fk_column name="mdExceptionManagementSetup.curveConfigurationResolvers.instruments" pk="instruments" />
				<fk_column name="mdExceptionManagementSetup.curveConfigurationResolvers.overrides" pk="overrides" />
			</fk>
			<fk name="fk_dashboard_portfolio" virtual="y" to_schema="solum-qa" to_table="portfolio" >
				<fk_column name="vdExceptionManagementSetup.portfoliosFilter.companies.entities.portfolioIds" pk="_id" />
				<fk_column name="vdExceptionManagementSetup.portfoliosFilter.companies.entities.entityId" pk="entity.entityId" />
			</fk>
			<fk name="fk_dashboard_settings" virtual="y" to_schema="solum-qa" to_table="companyLegalEntityValuationSettings" >
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.marketDataGroup" pk="marketDataGroup" />
			</fk>
			<fk name="fk_dashboard_ipvs" virtual="y" to_schema="solum-qa" to_table="companyLegalEntityIpvSettings" >
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.products" pk="products" />
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup" pk="ipvDataGroup" />
			</fk>
			<fk name="fk_dashboard_portfolio2" virtual="y" to_schema="solum-qa" to_table="portfolio" >
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.portfolio.entityId" pk="_id" />
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.portfolio.name" pk="externalPortfolioId" />
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.company" pk="company" />
				<fk_column name="vdExceptionManagementSetup.ipvValuationSettings.entity" pk="entity" />
			</fk>
		</table>
		<table name="dashboardEntryMd" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="step" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="finishedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="startedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="resultId" type="string" length="800" decimal="0" jt="12" />
			<column name="breaksCount" type="long" length="800" decimal="0" jt="-1" />
			<column name="curveConfiguration" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_dashboardentrymd_dashboard" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
			<fk name="fk_dashboardentrymd" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
				<fk_column name="curveConfiguration.entityId" pk="mdExceptionManagementSetup.curveConfigurationResolvers._id" />
				<fk_column name="curveConfiguration.name" pk="mdExceptionManagementSetup.curveConfigurationResolvers.name" />
			</fk>
		</table>
		<table name="dashboardEntryMdBatch" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="step" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="finishedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="startedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="resultId" type="string" length="800" decimal="0" jt="12" />
			<column name="breaksCount" type="long" length="800" decimal="0" jt="-1" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="d" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
		</table>
		<table name="dashboardEntryVd" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="step" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="finishedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="status" type="string" length="800" decimal="0" jt="12" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="portfolios" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="startedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="resultId" type="string" length="800" decimal="0" jt="12" />
			<column name="ipvDataGroup" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="breaksCount" type="long" length="800" decimal="0" jt="-1" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_dashboardentryvd_dashboard" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="ipvDataGroup" pk="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup" />
				<fk_column name="portfolios" pk="vdExceptionManagementSetup.ipvValuationSettings.portfolio" />
			</fk>
			<fk name="fk_dashboardentryvd_dashboard2" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
		</table>
		<table name="dataProvider" generator_rows="100" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="externalId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="types" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="exceptionManagementResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="preliminaryStatus" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="curveDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="previousDate" type="date" length="800" decimal="0" jt="91" />
			<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="curveConfigurationResolvers" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="CTD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FRA" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" >
					<column name="priority" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
					<column name="instruments" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="IBOR_FIXING_DEPOSIT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="SWAPTION_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="OVERNIGHT_IBOR_BASIS_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="INFLATION_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="XCCY_IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FX_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FIXED_OVERNIGHT_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="SWAPTION_ATM" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FX_RATE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="FIXED_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="secondary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="FX_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FRA" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="FX_VOL_SKEW" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="CAP_FLOOR_VOL" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FUNDING" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="FIXED_INFLATION_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
						<column name="IBOR_FUTURE" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="primary" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="IBOR_IBOR_SWAP" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
							<column name="assetNames" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
							<column name="sectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
						</column>
					</column>
					<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="preliminaryApprovedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="preliminaryApprovedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="overlayApprovedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="overlayApprovedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_exceptionmanagementresult" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
			<fk name="fk_exceptionmanagementresult_dashboard2" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="marketDataGroupId" pk="mdExceptionManagementSetup.marketDataGroup.entityId" />
				<fk_column name="marketDataGroupName" pk="mdExceptionManagementSetup.marketDataGroup.name" />
			</fk>
			<fk name="fk_exceptionmanagementresult_d3" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="curveConfigurationResolvers" pk="mdExceptionManagementSetup.curveConfigurationResolvers" />
			</fk>
		</table>
		<table name="fixing" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="reference" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="reference_1_date_1" unique="UNIQUE_KEY" >
				<column name="reference" />
				<column name="date" />
			</index>
		</table>
		<table name="globalValuationSettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="forceIsdaInterpolatorsForCds" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="configurationType" type="string" length="800" decimal="0" jt="12" />
			<column name="curveConfiguration" type="object" length="800" decimal="0" jt="4999544" >
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="nonFxCurveConfiguration" prior="nonFxConfiguration" type="object" jt="4999544" >
				<column name="entityId" type="string" jt="12" />
				<column name="name" type="string" jt="12" />
			</column>
			<column name="curveStripping" type="string" length="800" decimal="0" jt="12" />
			<column name="discountingType" type="string" length="800" decimal="0" jt="12" />
			<column name="reportingCurrency" type="string" length="800" decimal="0" jt="12" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_globalvaluationsettings" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="curveConfiguration.entityId" pk="entityId" />
				<fk_column name="curveConfiguration.name" pk="name" />
			</fk>
			<fk name="fk_globalvaluationsettings_cc2" virtual="y" to_schema="solum-qa" to_table="curveConfiguration" >
				<fk_column name="nonFxCurveConfiguration.entityId" pk="entityId" />
				<fk_column name="nonFxCurveConfiguration.name" pk="name" />
			</fk>
		</table>
		<table name="inflationSeasonalitySettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="curveSeasonalities" type="object" length="800" decimal="0" jt="4999544" >
				<column name="EU AI CPI" type="object" length="800" decimal="0" jt="4999544" >
					<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="jan" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="feb" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="mar" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="apr" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="may" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jun" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jul" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="aug" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="sep" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="oct" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="nov" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="dec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="EU EXT CPI" type="object" length="800" decimal="0" jt="4999544" >
					<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="jan" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="feb" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="mar" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="apr" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="may" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jun" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jul" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="aug" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="sep" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="oct" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="nov" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="dec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="GB RPI" type="object" length="800" decimal="0" jt="4999544" >
					<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="jan" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="feb" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="mar" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="apr" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="may" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jun" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jul" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="aug" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="sep" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="oct" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="nov" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="dec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="US CPI U" type="object" length="800" decimal="0" jt="4999544" >
					<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="jan" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="feb" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="mar" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="apr" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="may" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jun" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jul" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="aug" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="sep" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="oct" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="nov" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="dec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="CH CPI" type="object" length="800" decimal="0" jt="4999544" >
					<column name="settingsType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="jan" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="feb" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="mar" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="apr" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="may" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jun" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="jul" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="aug" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="sep" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="oct" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="nov" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="dec" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
			</column>
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="instrumentResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementResultId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="instrument" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClassGroup" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="assetClass" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="currency" type="string" length="800" decimal="0" jt="12" />
				<column name="assetName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instrumentType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instrumentTypeSort" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
				<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="nodeInstrument" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="parsedTenor" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="mdkName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="fxPair" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="rawValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			<column name="breakTests" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_instrumentresult" virtual="y" to_schema="solum-qa" to_table="exceptionManagementResult" >
				<fk_column name="exceptionManagementResultId" pk="_id" />
				<fk_column name="dashboardId" pk="dashboardId" />
			</fk>
			<fk name="fk_instrumentresult_iro" virtual="y" to_schema="solum-qa" to_table="instrumentResultOverlay" >
				<fk_column name="curveConfigurationId" pk="curveConfigurationId" />
			</fk>
			<fk name="fk_instrumentresult_iro2" virtual="y" to_schema="solum-qa" to_table="instrumentResultOverlay" >
				<fk_column name="instrument" pk="instrument" />
			</fk>
		</table>
		<table name="instrumentResultOverlay" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementResultId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="instrument" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClassGroup" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="assetClass" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="currency" type="string" length="800" decimal="0" jt="12" />
				<column name="assetName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instrumentType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instrumentTypeSort" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
				<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="nodeInstrument" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="parsedTenor" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="mdkName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="fxPair" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="primaryProviderData" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="previousValue" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="secondaryProviderData" type="object" length="800" decimal="0" jt="4999544" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="previousValue" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="previousOverlayValue" type="double" length="800" decimal="0" jt="8" />
			<column name="breakTests" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="breakTestName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="breakTestType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="measureType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="operator" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="threshold" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="providerValue" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="triggered" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="calculationOnly" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="appliedTestsCount" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="hasBreaks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="taskId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_instrumentresultoverlay" virtual="y" to_schema="solum-qa" to_table="exceptionManagementResult" >
				<fk_column name="dashboardId" pk="dashboardId" />
				<fk_column name="exceptionManagementResultId" pk="_id" />
				<fk_column name="curveConfigurationId" pk="curveConfigurationResolvers._id" />
			</fk>
		</table>
		<table name="instrumentResultPreliminary" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementResultId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="instrument" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClassGroup" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="assetClass" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="currency" type="string" length="800" decimal="0" jt="12" />
				<column name="assetName" type="string" length="800" decimal="0" jt="12" />
				<column name="instrumentType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="instrumentTypeSort" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
				<column name="tenor" type="string" length="800" decimal="0" jt="12" />
				<column name="nodeInstrument" type="string" length="800" decimal="0" jt="12" />
				<column name="parsedTenor" type="double" length="800" decimal="0" jt="8" />
				<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="mdkName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="sector" type="string" length="800" decimal="0" jt="12" />
				<column name="fxPair" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="providerData" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="previousValue" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="allProvidersData" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="previousValue" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="breakTests" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="breakTestName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="breakTestType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="providerValue" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="triggered" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="calculationOnly" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="measureType" type="string" length="800" decimal="0" jt="12" />
				<column name="operator" type="string" length="800" decimal="0" jt="12" />
				<column name="threshold" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="appliedTestsCount" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="hasBreaks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="taskId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_instrumentresultpreliminary" virtual="y" to_schema="solum-qa" to_table="exceptionManagementResult" >
				<fk_column name="dashboardId" pk="dashboardId" />
				<fk_column name="exceptionManagementResultId" pk="_id" />
			</fk>
		</table>
		<table name="ipvBreakTest" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="providersTypes" type="array[object]" length="800" decimal="0" jt="4999545" />
			<column name="tradeFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="productTypes" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" />
				<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
			</column>
			<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="measureType" type="string" length="800" decimal="0" jt="12" />
			<column name="operator" type="string" length="800" decimal="0" jt="12" />
			<column name="threshold" type="double" length="800" decimal="0" jt="8" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="ipvData" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="vega" type="double" length="800" decimal="0" jt="8" />
				<column name="delta" type="double" length="800" decimal="0" jt="8" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvdata_ipvdatagroup" virtual="y" to_schema="solum-qa" to_table="ipvDataGroup" >
				<fk_column name="ipvGroupId" pk="_id" />
			</fk>
		</table>
		<table name="ipvDataGroup" generator_rows="225" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="company" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="allowAllTeams" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teamIds" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="object" length="800" decimal="0" jt="4999544" >
							<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="right" type="object" length="800" decimal="0" jt="4999544" >
							<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvdatagroup_company" virtual="y" to_schema="solum-qa" to_table="company" >
				<fk_column name="company.externalCompanyId" pk="externalCompanyId" />
				<fk_column name="company.entityId" pk="_id" />
				<fk_column name="company.name" pk="name" />
			</fk>
			<fk name="fk_ipvdatagroup_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teamIds" pk="_id" />
			</fk>
		</table>
		<table name="ipvExceptionManagementResult" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="previousDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="ipvDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvValuationSettingsData" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="products" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="SWAPTION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FXFWD" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="CDS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="CAP_FLOOR" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="IRS" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="XCCY" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="FXOPT" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="INFLATION" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="CDX" type="object" length="800" decimal="0" jt="4999544" >
						<column name="primary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="primaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="secondaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tertiaryWithGreeks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="companyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="portfolioId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="portfolioExternalId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="entity" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="externalEntityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="hasXplainProvider" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="approvedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="approvedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvexceptionmanagementresult" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="dashboardId" pk="_id" />
			</fk>
			<fk name="fk_ipvexceptionmanagementresult_d1" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="ipvDataGroupId" pk="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup.entityId" />
				<fk_column name="ipvDataGroupName" pk="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup.name" />
			</fk>
			<fk name="fk_ipvexceptionmanagementresult_d_ipvs" virtual="y" to_schema="solum-qa" to_table="dashboard" >
				<fk_column name="ipvValuationSettingsData.products" pk="vdExceptionManagementSetup.ipvValuationSettings.products" />
				<fk_column name="ipvValuationSettingsData.companyId" pk="vdExceptionManagementSetup.ipvValuationSettings.company.entityId" />
				<fk_column name="ipvValuationSettingsData.externalCompanyId" pk="vdExceptionManagementSetup.ipvValuationSettings.company.name" />
				<fk_column name="ipvValuationSettingsData.portfolioId" pk="vdExceptionManagementSetup.ipvValuationSettings.portfolio.entityId" />
				<fk_column name="ipvValuationSettingsData.portfolioExternalId" pk="vdExceptionManagementSetup.ipvValuationSettings.portfolio.name" />
				<fk_column name="ipvValuationSettingsData.entityId" pk="vdExceptionManagementSetup.ipvValuationSettings.entity.entityId" />
				<fk_column name="ipvValuationSettingsData.externalEntityId" pk="vdExceptionManagementSetup.ipvValuationSettings.entity.name" />
				<fk_column name="ipvValuationSettingsData.hasXplainProvider" pk="vdExceptionManagementSetup.ipvValuationSettings.hasXplainProvider" />
				<fk_column name="ipvValuationSettingsData.marketDataGroupId" pk="vdExceptionManagementSetup.ipvValuationSettings.marketDataGroup.entityId" />
			</fk>
		</table>
		<table name="ipvTaskDefaultTeams" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="defaultResolutionTeam" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="defaultApprovalTeam" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvtaskdefaultteams_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="defaultResolutionTeam._id" pk="_id" />
				<fk_column name="defaultResolutionTeam.name" pk="name" />
			</fk>
			<fk name="fk_ipvtaskdefaultteams_team2" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="defaultApprovalTeam._id" pk="_id" />
				<fk_column name="defaultApprovalTeam.name" pk="name" />
			</fk>
		</table>
		<table name="ipvTaskExecution" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="tradeFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="productTypes" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			</column>
			<column name="tradeFilterSummary" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="typesSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="rateCcysSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="fxPairsSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="sectorsSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="ipvDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ipvDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="revision" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="taskExceptionManagementType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="breaksCount" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="resolutionTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="approvalTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="performedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="approvedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="startedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="finishedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvtaskexecution" virtual="y" to_schema="solum-qa" to_table="ipvExceptionManagementResult" >
				<fk_column name="exceptionManagementId" pk="_id" />
				<fk_column name="dashboardId" pk="dashboardId" />
				<fk_column name="valuationDate" pk="valuationDate" />
				<fk_column name="ipvDataGroupId" pk="ipvDataGroupId" />
				<fk_column name="ipvDataGroupName" pk="ipvDataGroupName" />
			</fk>
		</table>
		<table name="ipvTasksDefinition" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="teams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="productType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="resolutionTeams" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="approvalTeams" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
			</column>
			<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="overrideTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="filter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="productTypes" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
					</column>
					<column name="resolutionTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
					<column name="approvalTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				</column>
			</column>
			<column name="granularityByTradeType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityByRate" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityBySector" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityByFxCcyPairType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvtasksdefinition_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teams.resolutionTeams" pk="_id" />
			</fk>
			<fk name="fk_ipvtasksdefinition_team2" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teams.approvalTeams" pk="_id" />
			</fk>
			<fk name="fk_ipvtasksdefinition_team3" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="overrides.overrideTeams.resolutionTeams" pk="_id" />
			</fk>
			<fk name="fk_ipvtasksdefinition_team4" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="overrides.overrideTeams.approvalTeams" pk="_id" />
			</fk>
		</table>
		<table name="ipvTradeResultOverlay" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementResultId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="trade" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="currency" type="string" length="800" decimal="0" jt="12" />
				<column name="underlying" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="productType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tradeDetails" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="payLeg" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="payReceive" type="string" length="800" decimal="0" jt="12" />
						<column name="type" type="string" length="800" decimal="0" jt="12" />
						<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="accrualFrequency" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentCompounding" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
						<column name="index" type="string" length="800" decimal="0" jt="12" />
						<column name="fixingDateOffsetDays" type="integer" length="800" decimal="0" jt="4" />
						<column name="dayCount" type="string" length="800" decimal="0" jt="12" />
						<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
						<column name="accrualMethod" type="string" length="800" decimal="0" jt="12" />
						<column name="overnightRateCutOffDays" type="integer" length="800" decimal="0" jt="4" />
					</column>
					<column name="receiveLeg" type="object" length="800" decimal="0" jt="4999544" >
						<column name="payReceive" type="string" length="800" decimal="0" jt="12" />
						<column name="type" type="string" length="800" decimal="0" jt="12" />
						<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="accrualFrequency" type="string" length="800" decimal="0" jt="12" />
						<column name="accrualMethod" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentCompounding" type="string" length="800" decimal="0" jt="12" />
						<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
						<column name="index" type="string" length="800" decimal="0" jt="12" />
						<column name="dayCount" type="string" length="800" decimal="0" jt="12" />
						<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
						<column name="fixingDateOffsetDays" type="integer" length="800" decimal="0" jt="4" />
						<column name="inflationLag" type="string" length="800" decimal="0" jt="12" />
						<column name="indexCalculationMethod" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="info" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="externalTradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="tradeDate" type="date" length="800" decimal="0" jt="91" />
					</column>
					<column name="paymentDateConvention" type="string" length="800" decimal="0" jt="12" />
					<column name="notionalScheduleInitialExchange" type="boolean" length="800" decimal="0" jt="12" />
					<column name="notionalScheduleFinalExchange" type="boolean" length="800" decimal="0" jt="12" />
					<column name="stubConvention" type="string" length="800" decimal="0" jt="12" />
					<column name="startDate" type="date" length="800" decimal="0" jt="91" />
					<column name="endDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="rollConvention" type="string" length="800" decimal="0" jt="12" />
					<column name="positionType" type="string" length="800" decimal="0" jt="12" />
					<column name="optionTradeDetails" type="object" length="800" decimal="0" jt="4999544" >
						<column name="expiryTime" type="date" length="800" decimal="0" jt="91" />
						<column name="expiryZone" type="string" length="800" decimal="0" jt="12" />
						<column name="expiryDate" type="date" length="800" decimal="0" jt="91" />
						<column name="expiryDateConvention" type="string" length="800" decimal="0" jt="12" />
						<column name="premiumDate" type="date" length="800" decimal="0" jt="91" />
						<column name="premiumDateConvention" type="string" length="800" decimal="0" jt="12" />
						<column name="premiumValue" type="double" length="800" decimal="0" jt="8" />
						<column name="swaptionSettlementType" type="string" length="800" decimal="0" jt="12" />
						<column name="strike" type="double" length="800" decimal="0" jt="8" mandatory="y" />
						<column name="capFloorType" type="string" length="800" decimal="0" jt="12" />
						<column name="premiumCurrency" type="string" length="800" decimal="0" jt="12" />
					</column>
					<column name="cdsTradeDetails" type="object" length="800" decimal="0" jt="4999544" >
						<column name="corpTicker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="seniority" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="docClause" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="portfolioId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="portfolioExternalId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="externalEntityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="currencyPair" type="string" length="800" decimal="0" jt="12" />
				<column name="creditSector" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="resolvedValue" type="double" length="800" decimal="0" jt="8" />
			<column name="primaryProviderData" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="pv" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="value" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="delta" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="value" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="vega" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
				<column name="parRate" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="value" type="double" length="800" decimal="0" jt="8" />
				</column>
				<column name="impliedVol" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
				<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="resolvedCreditSector" type="string" length="800" decimal="0" jt="12" />
			</column>
			<column name="secondaryProviderData" type="object" length="800" decimal="0" jt="4999544" >
				<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="pv" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				</column>
				<column name="delta" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
				<column name="vega" type="object" length="800" decimal="0" jt="4999544" mandatory="y" />
			</column>
			<column name="breakTests" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="breakTestName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="breakTestType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="providerType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="providerValue" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="triggered" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" />
					<column name="calculationOnly" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="appliedTestsCount" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="hasBreaks" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="resolution" type="object" length="800" decimal="0" jt="4999544" >
				<column name="providerName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="providerType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="taskId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_ipvtraderesultoverlay" virtual="y" to_schema="solum-qa" to_table="ipvExceptionManagementResult" >
				<fk_column name="exceptionManagementResultId" pk="_id" />
				<fk_column name="dashboardId" pk="dashboardId" />
			</fk>
		</table>
		<table name="marketDataForDate" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="groupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="dataId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="v" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="ticker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
					<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
						<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
					<column name="comment" type="string" length="800" decimal="0" jt="12" />
				</column>
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="marketDataGroup" generator_rows="150" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="allowAllCompanies" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="allowAllTeams" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teamIds" type="array[object]" length="800" decimal="0" jt="4999545" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_marketdatagroup_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teamIds" pk="_id" />
			</fk>
		</table>
		<table name="marketDataKey" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="key" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="assetGroup" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="instrumentType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="providerTickers" type="array[object]" jt="4999545" >
				<column name="code" type="string" jt="12" />
				<column name="ticker" type="string" jt="12" />
				<column name="factor" type="double" jt="8" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="valid_from_record_date" unique="UNIQUE_KEY" >
				<column name="validFrom" />
				<column name="recordDate" />
			</index>
			<fk name="fk_marketdatakey_dataprovider" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="providerTickers.code" pk="externalId" />
			</fk>
			<fk name="fk_marketdatakey" virtual="y" to_schema="solum-qa" to_table="marketDataValue" >
				<fk_column name="providerTickers.ticker" pk="ticker" />
			</fk>
		</table>
		<table name="marketDataValue" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="groupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="ticker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="date" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="provider" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="values" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="value" type="double" length="800" decimal="0" jt="8" mandatory="y" />
				<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="market_data_value_idx" unique="UNIQUE_KEY" >
				<column name="groupId" />
				<column name="date" />
				<column name="provider" />
				<column name="ticker" />
			</index>
			<fk name="fk_marketdatavalue" virtual="y" to_schema="solum-qa" to_table="marketDataGroup" >
				<fk_column name="groupId" pk="_id" />
			</fk>
			<fk name="fk_marketdatavalue_dp" virtual="y" to_schema="solum-qa" to_table="dataProvider" >
				<fk_column name="provider" pk="externalId" />
			</fk>
		</table>
		<table name="mdTaskDefaultTeams" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="defaultResolutionTeam" type="object" length="800" decimal="0" jt="4999544" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="defaultApprovalTeam" type="object" length="800" decimal="0" jt="4999544" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_mdtaskdefaultteams_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="defaultResolutionTeam._id" pk="_id" />
				<fk_column name="defaultResolutionTeam.name" pk="name" />
			</fk>
			<fk name="fk_mdtaskdefaultteams_team2" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="defaultApprovalTeam._id" pk="_id" />
				<fk_column name="defaultApprovalTeam.name" pk="name" />
			</fk>
		</table>
		<table name="mongockChangeLog" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="executionId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="changeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="author" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="timestamp" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="changeLogClass" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="changeSetMethod" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="executionMillis" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="executionHostname" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="executionId_1_author_1_changeId_1" unique="UNIQUE_KEY" >
				<column name="executionId" />
				<column name="author" />
				<column name="changeId" />
			</index>
		</table>
		<table name="mongockLock" />
		<table name="portfolio" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="calculatedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="calculatedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="allowAllTeams" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teamIds" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			<column name="company" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="entity" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="externalEntityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="externalPortfolioId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="object" length="800" decimal="0" jt="4999544" >
							<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" />
							<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="externalEntityId" type="string" length="800" decimal="0" jt="12" />
						</column>
						<column name="right" type="object" length="800" decimal="0" jt="4999544" >
							<column name="externalCompanyId" type="string" length="800" decimal="0" jt="12" />
							<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="externalEntityId" type="string" length="800" decimal="0" jt="12" />
						</column>
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="description" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_portfolio_company" virtual="y" to_schema="solum-qa" to_table="company" >
				<fk_column name="company.entityId" pk="_id" />
				<fk_column name="company.externalCompanyId" pk="externalCompanyId" />
				<fk_column name="company.name" pk="name" />
			</fk>
			<fk name="fk_portfolio" virtual="y" to_schema="solum-qa" to_table="companyLegalEntity" >
				<fk_column name="entity.entityId" pk="_id" />
				<fk_column name="entity.externalEntityId" pk="externalId" />
				<fk_column name="entity.name" pk="name" />
			</fk>
			<fk name="fk_portfolio_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teamIds" pk="_id" />
			</fk>
		</table>
		<table name="portfolioItem" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="portfolioId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="productType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="tradeDetails" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="payLeg" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="payReceive" type="string" length="800" decimal="0" jt="12" />
					<column name="type" type="string" length="800" decimal="0" jt="12" />
					<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="accrualFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentCompounding" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
					<column name="index" type="string" length="800" decimal="0" jt="12" />
					<column name="fixingDateOffsetDays" type="integer" length="800" decimal="0" jt="4" />
					<column name="dayCount" type="string" length="800" decimal="0" jt="12" />
					<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
					<column name="inflationLag" type="string" length="800" decimal="0" jt="12" />
					<column name="indexCalculationMethod" type="string" length="800" decimal="0" jt="12" />
					<column name="accrualMethod" type="string" length="800" decimal="0" jt="12" />
					<column name="overnightRateCutOffDays" type="integer" length="800" decimal="0" jt="4" />
				</column>
				<column name="receiveLeg" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="payReceive" type="string" length="800" decimal="0" jt="12" />
					<column name="type" type="string" length="800" decimal="0" jt="12" />
					<column name="notional" type="double" length="800" decimal="0" jt="8" mandatory="y" />
					<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="accrualFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="accrualMethod" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentFrequency" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentCompounding" type="string" length="800" decimal="0" jt="12" />
					<column name="paymentOffsetDays" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
					<column name="dayCount" type="string" length="800" decimal="0" jt="12" />
					<column name="initialValue" type="double" length="800" decimal="0" jt="8" />
					<column name="index" type="string" length="800" decimal="0" jt="12" />
					<column name="overnightRateCutOffDays" type="integer" length="800" decimal="0" jt="4" />
					<column name="inflationLag" type="string" length="800" decimal="0" jt="12" />
					<column name="indexCalculationMethod" type="string" length="800" decimal="0" jt="12" />
				</column>
				<column name="info" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="externalTradeId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="tradeDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				</column>
				<column name="paymentDateConvention" type="string" length="800" decimal="0" jt="12" />
				<column name="notionalScheduleInitialExchange" type="boolean" length="800" decimal="0" jt="12" />
				<column name="notionalScheduleFinalExchange" type="boolean" length="800" decimal="0" jt="12" />
				<column name="stubConvention" type="string" length="800" decimal="0" jt="12" />
				<column name="startDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="endDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
				<column name="rollConvention" type="string" length="800" decimal="0" jt="12" />
				<column name="positionType" type="string" length="800" decimal="0" jt="12" />
				<column name="cdsTradeDetails" type="object" length="800" decimal="0" jt="4999544" >
					<column name="corpTicker" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="seniority" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="docClause" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
			</column>
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="validTo" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordTo" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="deletedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="deletedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="clientMetrics" type="object" length="800" decimal="0" jt="4999544" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="portfolio_version_state_idx" unique="UNIQUE_KEY" >
				<column name="portfolioId" />
				<column name="validFrom" />
				<column name="validTo" />
				<column name="recordFrom" />
				<column name="recordTo" />
				<column name="state" />
				<column name="entityId" />
			</index>
			<index name="portfolio_external_id_idx" unique="UNIQUE_KEY" >
				<column name="portfolioId" />
				<column name="validFrom" />
				<column name="validTo" />
				<column name="recordFrom" />
				<column name="recordTo" />
				<column name="state" />
			</index>
			<index name="portfolio_external_id_idx" unique="UNIQUE_KEY" >
				<column name="portfolioId" />
				<column name="validFrom" />
				<column name="validTo" />
				<column name="recordFrom" />
				<column name="recordTo" />
				<column name="state" />
			</index>
			<fk name="fk_portfolioitem_portfolio" virtual="y" to_schema="solum-qa" to_table="portfolio" >
				<fk_column name="portfolioId" pk="_id" />
			</fk>
		</table>
		<table name="role" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="rolePermissions" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="userType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="subCategory" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="archived" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="updatedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
							<column name="userType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="subCategory" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="right" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
							<column name="userType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
							<column name="subCategory" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="storedXvaModel" />
		<table name="taskExecution" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="assetFilter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClasses" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="irInstruments" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" />
			</column>
			<column name="assetFilterSummary" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="assetClassSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="rateCcysSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="irInstrumentsSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="sectorsSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="fxPairsSummary" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="marketDataGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="marketDataGroupName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="revision" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="taskExceptionManagementType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="dashboardId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="exceptionManagementId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="valuationDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="breaksCount" type="long" length="800" decimal="0" jt="-1" mandatory="y" />
			<column name="resolutionTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="approvalTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="previousStatuses" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="status" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="performedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="approvedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="startedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="finishedAt" type="date" length="800" decimal="0" jt="91" />
			<column name="curveConfigurationId" type="string" length="800" decimal="0" jt="12" />
			<column name="curveConfigurationName" type="string" length="800" decimal="0" jt="12" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_taskexecution" virtual="y" to_schema="solum-qa" to_table="exceptionManagementResult" >
				<fk_column name="dashboardId" pk="dashboardId" />
				<fk_column name="exceptionManagementId" pk="_id" />
				<fk_column name="valuationDate" pk="curveDate" />
				<fk_column name="marketDataGroupId" pk="marketDataGroupId" />
				<fk_column name="marketDataGroupName" pk="marketDataGroupName" />
			</fk>
		</table>
		<table name="tasksDefinition" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="type" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="assetClass" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="resolutionTeams" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
				<column name="approvalTeams" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
			</column>
			<column name="overrides" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="overrideTeams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
					<column name="filter" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
						<column name="assetClasses" type="array" length="800" decimal="0" jt="4999545" mandatory="y" />
						<column name="irInstruments" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="rateCcys" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="creditSectors" type="array[object]" length="800" decimal="0" jt="4999545" />
						<column name="fxPairs" type="array[object]" length="800" decimal="0" jt="4999545" />
					</column>
					<column name="resolutionTeams" type="array[object]" length="800" decimal="0" jt="4999545" />
					<column name="approvalTeams" type="array[object]" length="800" decimal="0" jt="4999545" />
				</column>
			</column>
			<column name="granularityByAssetClassType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityByRate" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityBySector" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityByInstrument" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="granularityByFxCcyPair" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_tasksdefinition_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teams.resolutionTeams" pk="_id" />
			</fk>
			<fk name="fk_tasksdefinition_team2" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teams.approvalTeams" pk="_id" />
			</fk>
			<fk name="fk_tasksdefinition_team3" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="overrides.overrideTeams.resolutionTeams" pk="_id" />
			</fk>
			<fk name="fk_tasksdefinition_team4" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="overrides.overrideTeams.approvalTeams" pk="_id" />
			</fk>
		</table>
		<table name="team" generator_rows="100" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="lastModifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="right" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="name_1" unique="UNIQUE_KEY" >
				<column name="name" />
			</index>
		</table>
		<table name="user" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="lastModifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="updatedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="password" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="customerRoles" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="userRoleId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="enabled" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="teams" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="teamId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="auditLogs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="diff" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="diffs" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
						<column name="fieldName" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						<column name="left" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="teamId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
						<column name="right" type="array[object]" length="800" decimal="0" jt="4999545" >
							<column name="teamId" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
							<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
						</column>
					</column>
				</column>
				<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
					<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
					<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				</column>
				<column name="createdAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			</column>
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="createdBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<index name="username_1" unique="UNIQUE_KEY" >
				<column name="username" />
			</index>
			<fk name="fk_user_team" virtual="y" to_schema="solum-qa" to_table="team" >
				<fk_column name="teams.teamId" pk="_id" />
				<fk_column name="teams.name" pk="name" />
			</fk>
			<fk name="fk_user_role" virtual="y" to_schema="solum-qa" to_table="role" >
				<fk_column name="customerRoles.userRoleId" pk="_id" />
				<fk_column name="customerRoles.name" pk="name" />
			</fk>
		</table>
		<table name="volatilitySurface" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="curveGroupId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="entityId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="skewType" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="xinterpolator" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="yinterpolator" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="sabr" type="boolean" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="capletValuationModel" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="nodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="expiry" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="capletVolatilities" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="tenor" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="strike" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			</column>
			<column name="skewNodes" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="surfaceSkewId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="skewValue" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" mandatory="y" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
			<fk name="fk_volatilitysurface" virtual="y" to_schema="solum-qa" to_table="curveGroup" >
				<fk_column name="curveGroupId" pk="_id" />
			</fk>
		</table>
		<table name="xvaLiborIndices" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="liborIndices" type="array[object]" length="800" decimal="0" jt="4999545" mandatory="y" >
				<column name="currency" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="index" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
		<table name="xvaSettings" >
			<column name="_id" type="oid" length="800" decimal="0" jt="-8" mandatory="y" />
			<column name="hullWhiteMeanReversion" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			<column name="timeEnd" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="simulationsForXva" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="simulationsForPfe" type="integer" length="800" decimal="0" jt="4" mandatory="y" />
			<column name="pfePercentile" type="double" length="800" decimal="0" jt="8" mandatory="y" />
			<column name="validFrom" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="recordDate" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="state" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="modifiedAt" type="date" length="800" decimal="0" jt="91" mandatory="y" />
			<column name="_class" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			<column name="comment" type="string" length="800" decimal="0" jt="12" />
			<column name="modifiedBy" type="object" length="800" decimal="0" jt="4999544" >
				<column name="userId" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="username" type="string" length="800" decimal="0" jt="12" mandatory="y" />
				<column name="name" type="string" length="800" decimal="0" jt="12" mandatory="y" />
			</column>
			<index name="_id_" unique="PRIMARY_KEY" >
				<column name="_id" />
			</index>
		</table>
	</schema>
	<connector name="MongoDb" database="MongoDb" url="mongodb://solum-qa:<EMAIL>:27017,cluster-solum-shard-00-01.wwd4p.mongodb.net:27017,cluster-solum-shard-00-02.wwd4p.mongodb.net:27017/solum-qa?ssl=true&amp;replicaSet=atlas-ksvb83-shard-0&amp;authSource=admin&amp;retryWrites=true&amp;w=majority" host="localhost" port="27017" user="admin" />
	<layout name="Audit Entry" id="Layout_26ac" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER Diagram - Audit Entry]]></comment>
		<entity schema="solum-qa" name="auditEntry" color="BED3F4" x="48" y="96" />
		<entity schema="solum-qa" name="auditEntryItem" color="C7F4BE" x="400" y="80" />
		<group name="auditEntry" color="ECF0F7" >
			<entity schema="solum-qa" name="auditEntry" />
			<entity schema="solum-qa" name="auditEntryItem" />
		</group>
	</layout>
	<layout name="Company" id="Layout_1278" confirmed="y" joined_routing="y" show_relation="columns" >
		<comment><![CDATA[MongoDb]]></comment>
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="products.IRS" color="BEBEF4" x="2320" y="384" />
		<entity schema="solum-qa" name="companyIpvSettings" column="products.IRS" color="F4DDBE" x="2208" y="848" />
		<entity schema="solum-qa" name="company" column="auditLogs" color="BED3F4" x="1008" y="944" />
		<entity schema="solum-qa" name="companyLegalEntity" column="auditLogs" color="C7F4BE" x="1408" y="432" />
		<entity schema="solum-qa" name="portfolio" column="auditLogs" color="3986C1" x="960" y="1520" />
		<entity schema="solum-qa" name="cheapestToDeliverCurve" color="F4DDBE" x="2176" y="1104" >
			<column name="_id" />
			<column name="entityId" />
		</entity>
		<entity schema="solum-qa" name="company" color="BED3F4" x="1216" y="720" />
		<entity schema="solum-qa" name="portfolio" column="company" color="3986C1" x="960" y="1296" />
		<entity schema="solum-qa" name="companyIpvSettings" color="F4DDBE" x="1696" y="656" />
		<entity schema="solum-qa" name="companyLegalEntity" color="C7F4BE" x="1232" y="224" />
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" color="BEBEF4" x="1696" y="176" />
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" color="D1BEF4" x="336" y="176" />
		<entity schema="solum-qa" name="companyValuationSettings" color="C7F4BE" x="384" y="688" />
		<entity schema="solum-qa" name="csaEntity" color="BED3F4" x="1696" y="1104" />
		<entity schema="solum-qa" name="csaEntity" column="ctdCurveKey" color="BED3F4" x="1952" y="1184" />
		<entity schema="solum-qa" name="curveConfiguration" color="C7F4BE" x="848" y="112" >
			<column name="_id" />
			<column name="entityId" />
			<column name="curveGroupId" />
		</entity>
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" column="curveConfiguration" color="D1BEF4" x="96" y="384" />
		<entity schema="solum-qa" name="companyValuationSettings" column="curveConfiguration" color="C7F4BE" x="96" y="896" />
		<entity schema="solum-qa" name="dataProvider" color="BEBEF4" x="1264" y="80" >
			<column name="_id" />
			<column name="externalId" />
		</entity>
		<entity schema="solum-qa" name="portfolio" column="entity" color="3986C1" x="960" y="1408" />
		<entity schema="solum-qa" name="companyIpvSettings" column="ipvDataGroup" color="F4DDBE" x="1952" y="720" />
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="ipvDataGroup" color="BEBEF4" x="2064" y="240" />
		<entity schema="solum-qa" name="ipvDataGroup" color="D1BEF4" x="1472" y="656" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="marketDataGroup" color="BED3F4" x="688" y="320" >
			<column name="_id" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="companyValuationSettings" column="marketDataGroup" color="C7F4BE" x="48" y="784" />
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" column="marketDataGroup" color="D1BEF4" x="48" y="288" />
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" column="nonFxCurveConfiguration" color="3986C1" x="80" y="544" />
		<entity schema="solum-qa" name="companyValuationSettings" column="nonFxCurveConfiguration" color="3986C1" x="48" y="1040" />
		<entity schema="solum-qa" name="portfolio" color="3986C1" x="736" y="1200" />
		<entity schema="solum-qa" name="companyIpvSettings" column="products" color="F4DDBE" x="1952" y="832" />
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="products" color="BEBEF4" x="2064" y="368" />
		<entity schema="solum-qa" name="team" color="F4DDBE" x="896" y="304" >
			<column name="_id" />
		</entity>
		<group name="cheapestToDeliverCurve" color="F7F2EC" >
			<entity schema="solum-qa" name="cheapestToDeliverCurve" />
		</group>
		<group name="dataProvider" color="ECECF7" >
			<entity schema="solum-qa" name="dataProvider" />
		</group>
		<group name="csaEntity" color="ECF0F7" >
			<entity schema="solum-qa" name="csaEntity" column="ctdCurveKey" />
			<entity schema="solum-qa" name="csaEntity" />
		</group>
		<group name="ipvDataGroup" color="F0ECF7" >
			<entity schema="solum-qa" name="ipvDataGroup" />
		</group>
		<group name="companyValuationSettings" color="EEF7EC" >
			<entity schema="solum-qa" name="companyValuationSettings" column="curveConfiguration" />
			<entity schema="solum-qa" name="companyValuationSettings" column="marketDataGroup" />
			<entity schema="solum-qa" name="companyValuationSettings" />
		</group>
		<group name="marketDataGroup" color="ECF0F7" >
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="companyLegalEntity" color="EEF7EC" >
			<entity schema="solum-qa" name="companyLegalEntity" column="auditLogs" />
			<entity schema="solum-qa" name="companyLegalEntity" />
		</group>
		<group name="companyLegalEntityValuationSettings" color="F0ECF7" >
			<entity schema="solum-qa" name="companyLegalEntityValuationSettings" column="curveConfiguration" />
			<entity schema="solum-qa" name="companyLegalEntityValuationSettings" column="marketDataGroup" />
			<entity schema="solum-qa" name="companyLegalEntityValuationSettings" />
		</group>
		<group name="curveConfiguration" color="EEF7EC" >
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
		<group name="company" color="ECF0F7" >
			<entity schema="solum-qa" name="company" column="auditLogs" />
			<entity schema="solum-qa" name="company" />
		</group>
		<group name="team" color="F7F2EC" >
			<entity schema="solum-qa" name="team" />
		</group>
		<group name="companyIpvSettings" color="F7F2EC" >
			<entity schema="solum-qa" name="companyIpvSettings" column="ipvDataGroup" />
			<entity schema="solum-qa" name="companyIpvSettings" column="products.IRS" />
			<entity schema="solum-qa" name="companyIpvSettings" column="products" />
			<entity schema="solum-qa" name="companyIpvSettings" />
		</group>
		<group name="companyLegalEntityIpvSettings" color="ECECF7" >
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="products.IRS" />
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="products" />
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" column="ipvDataGroup" />
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" />
		</group>
		<group name="portfolio" color="C4E0F9" >
			<entity schema="solum-qa" name="portfolio" />
			<entity schema="solum-qa" name="portfolio" column="entity" />
			<entity schema="solum-qa" name="portfolio" column="auditLogs" />
			<entity schema="solum-qa" name="portfolio" column="company" />
		</group>
		<query id="Query_13d2" name="cheapestToDeliverCurve" confirmed="y" >
			<query_table schema="solum-qa" name="cheapestToDeliverCurve" alias="cheapestToDeliverCurve" x="48" y="48" >
				<column name="_id" />
				<column name="entityId" />
				<column name="name" />
				<column name="type" />
				<column name="interpolator" />
				<column name="extrapolatorLeft" />
				<column name="extrapolatorRight" />
				<column name="eligibleCurrencies" />
				<column name="nodes" />
				<column name="validFrom" />
				<column name="state" />
				<column name="comment" />
				<column name="recordDate" />
				<column name="modifiedBy" />
				<column name="modifiedAt" />
				<column name="_class" />
				<query_table schema="solum-qa" name="cheapestToDeliverCurve" column="nodes" x="480" y="48" fk="JSon" type="Inner Join" >
					<column name="tenor" />
				</query_table>
				<query_table schema="solum-qa" name="cheapestToDeliverCurve" column="modifiedBy" x="480" y="144" fk="JSon" type="Inner Join" >
					<column name="userId" />
					<column name="username" />
					<column name="name" />
				</query_table>
			</query_table>
		</query>
	</layout>
	<layout name="Curves" id="Layout_1caa" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER Diagram - Curves]]></comment>
		<entity schema="solum-qa" name="curveConfiguration" column="overrides.instruments.CDS" color="F4DDBE" x="48" y="1024" />
		<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="chartPoints.CHF" color="BEBEF4" x="1664" y="1264" />
		<entity schema="solum-qa" name="curveConfiguration" column="instruments.XCCY_IBOR_IBOR_SWAP" color="F4DDBE" x="64" y="576" />
		<entity schema="solum-qa" name="curveGroup" column="auditLogs" color="BED3F4" x="1424" y="832" />
		<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="calibrationMarketDataGroup" color="BEBEF4" x="1408" y="1120" />
		<entity schema="solum-qa" name="curveGroup" column="calibrationMarketDataGroup" color="BED3F4" x="1424" y="688" />
		<entity schema="solum-qa" name="volatilitySurface" column="capletVolatilities" color="C7F4BE" x="2400" y="1184" />
		<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="chartPoints" color="BEBEF4" x="1408" y="1248" />
		<entity schema="solum-qa" name="cheapestToDeliverCurve" color="BEBEF4" x="1024" y="1552" />
		<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" color="BEBEF4" x="1024" y="1024" />
		<entity schema="solum-qa" name="creditCurve" color="BED3F4" x="2048" y="512" />
		<entity schema="solum-qa" name="curve" color="D1BEF4" x="2048" y="80" />
		<entity schema="solum-qa" name="curveConfiguration" color="F4DDBE" x="688" y="496" />
		<entity schema="solum-qa" name="curveGroup" color="BED3F4" x="1040" y="528" />
		<entity schema="solum-qa" name="curveGroupFxRates" color="D1BEF4" x="2048" y="1728" />
		<entity schema="solum-qa" name="curveGroupFxVolatility" color="C7F4BE" x="2048" y="1440" />
		<entity schema="solum-qa" name="curveGroupInflationVolatility" color="F4DDBE" x="2032" y="2016" />
		<entity schema="solum-qa" name="dataProvider" color="C7F4BE" x="736" y="336" >
			<column name="_id" />
			<column name="externalId" />
		</entity>
		<entity schema="solum-qa" name="curveConfiguration" column="instruments" color="F4DDBE" x="304" y="560" />
		<entity schema="solum-qa" name="curveConfiguration" column="overrides.instruments" color="F4DDBE" x="304" y="992" />
		<entity schema="solum-qa" name="marketDataGroup" color="BED3F4" x="1168" y="400" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="curveGroupInflationVolatility" column="nodes" color="F4DDBE" x="2320" y="2064" />
		<entity schema="solum-qa" name="curveGroupFxRates" column="nodes" color="D1BEF4" x="2304" y="1776" />
		<entity schema="solum-qa" name="curve" column="nodes" color="D1BEF4" x="2384" y="240" />
		<entity schema="solum-qa" name="cheapestToDeliverCurve" column="nodes" color="BEBEF4" x="1280" y="1696" />
		<entity schema="solum-qa" name="curveGroupFxVolatility" column="nodes" color="C7F4BE" x="2336" y="1488" />
		<entity schema="solum-qa" name="volatilitySurface" column="nodes" color="C7F4BE" x="2400" y="1088" />
		<entity schema="solum-qa" name="curveConfiguration" column="overrides" color="F4DDBE" x="720" y="960" />
		<entity schema="solum-qa" name="volatilitySurface" column="skewNodes" color="C7F4BE" x="2400" y="1280" />
		<entity schema="solum-qa" name="volatilitySurface" color="C7F4BE" x="2048" y="1008" />
		<group name="creditCurve" color="ECF0F7" >
			<entity schema="solum-qa" name="creditCurve" />
		</group>
		<group name="cheapestToDeliverCurve" color="ECECF7" >
			<entity schema="solum-qa" name="cheapestToDeliverCurve" />
			<entity schema="solum-qa" name="cheapestToDeliverCurve" column="nodes" />
		</group>
		<group name="curve" color="F0ECF7" >
			<entity schema="solum-qa" name="curve" column="nodes" />
			<entity schema="solum-qa" name="curve" />
		</group>
		<group name="curveGroupFxVolatitiy" color="EEF7EC" >
			<entity schema="solum-qa" name="curveGroupFxVolatility" column="nodes" />
			<entity schema="solum-qa" name="curveGroupFxVolatility" />
		</group>
		<group name="curveGroupInflationVolatility" color="F7F2EC" >
			<entity schema="solum-qa" name="curveGroupInflationVolatility" column="nodes" />
			<entity schema="solum-qa" name="curveGroupInflationVolatility" />
		</group>
		<group name="curveGroupFxRates" color="F0ECF7" >
			<entity schema="solum-qa" name="curveGroupFxRates" column="nodes" />
			<entity schema="solum-qa" name="curveGroupFxRates" />
		</group>
		<group name="marketDataGroup" color="ECF0F7" >
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="dataProvider" color="EEF7EC" >
			<entity schema="solum-qa" name="dataProvider" />
		</group>
		<group name="volatilitySurface" color="EEF7EC" >
			<entity schema="solum-qa" name="volatilitySurface" column="capletVolatilities" />
			<entity schema="solum-qa" name="volatilitySurface" />
			<entity schema="solum-qa" name="volatilitySurface" column="skewNodes" />
			<entity schema="solum-qa" name="volatilitySurface" column="nodes" />
		</group>
		<group name="curveGroup" color="ECF0F7" >
			<entity schema="solum-qa" name="curveGroup" column="calibrationMarketDataGroup" />
			<entity schema="solum-qa" name="curveGroup" column="auditLogs" />
			<entity schema="solum-qa" name="curveGroup" />
		</group>
		<group name="cheapestToDeliverCurveCalibrationResult" color="ECECF7" >
			<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="chartPoints.CHF" />
			<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="chartPoints" />
			<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" column="calibrationMarketDataGroup" />
			<entity schema="solum-qa" name="cheapestToDeliverCurveCalibrationResult" />
		</group>
		<group name="curveConfiguration" color="F7F2EC" >
			<entity schema="solum-qa" name="curveConfiguration" column="overrides.instruments.CDS" />
			<entity schema="solum-qa" name="curveConfiguration" column="overrides.instruments" />
			<entity schema="solum-qa" name="curveConfiguration" column="overrides" />
			<entity schema="solum-qa" name="curveConfiguration" column="instruments.XCCY_IBOR_IBOR_SWAP" />
			<entity schema="solum-qa" name="curveConfiguration" column="instruments" />
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
	</layout>
	<layout name="Data" id="Layout_c15" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[MongoDb]]></comment>
		<entity schema="solum-qa" name="correlationMatrix" column="values._id" color="C7F4BE" x="896" y="1200" />
		<entity schema="solum-qa" name="ipvDataGroup" column="auditLogs" color="BEBEF4" x="1392" y="736" />
		<entity schema="solum-qa" name="marketDataGroup" column="auditLogs" color="C7F4BE" x="48" y="416" />
		<entity schema="solum-qa" name="company" color="BEBEF4" x="816" y="512" >
			<column name="_id" />
			<column name="teamIds" />
		</entity>
		<entity schema="solum-qa" name="ipvDataGroup" column="company" color="BEBEF4" x="1392" y="608" />
		<entity schema="solum-qa" name="correlationMatrix" color="C7F4BE" x="480" y="992" />
		<entity schema="solum-qa" name="dataProvider" color="D1BEF4" x="1152" y="256" >
			<column name="_id" />
			<column name="externalId" />
		</entity>
		<entity schema="solum-qa" name="fixing" color="BED3F4" x="48" y="992" />
		<entity schema="solum-qa" name="ipvData" color="D1BEF4" x="1712" y="480" />
		<entity schema="solum-qa" name="ipvDataGroup" color="BEBEF4" x="1104" y="512" />
		<entity schema="solum-qa" name="correlationMatrix" column="values._id.key1" color="C7F4BE" x="1088" y="1216" />
		<entity schema="solum-qa" name="correlationMatrix" column="values._id.key2" color="C7F4BE" x="1088" y="1312" />
		<entity schema="solum-qa" name="marketDataGroup" color="C7F4BE" x="288" y="224" />
		<entity schema="solum-qa" name="marketDataKey" color="F4DDBE" x="1712" y="80" />
		<entity schema="solum-qa" name="marketDataValue" color="BED3F4" x="832" y="192" />
		<entity schema="solum-qa" name="marketDataKey" column="providerTickers" color="F4DDBE" x="2000" y="304" />
		<entity schema="solum-qa" name="team" color="F4DDBE" x="608" y="528" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="ipvData" column="values" color="D1BEF4" x="1968" y="592" />
		<entity schema="solum-qa" name="marketDataValue" column="values" color="BED3F4" x="608" y="304" />
		<entity schema="solum-qa" name="fixing" column="values" color="BED3F4" x="272" y="1056" />
		<entity schema="solum-qa" name="correlationMatrix" column="values" color="C7F4BE" x="736" y="1184" />
		<group name="fixing" color="ECF0F7" >
			<entity schema="solum-qa" name="fixing" />
			<entity schema="solum-qa" name="fixing" column="values" />
		</group>
		<group name="marketDataKey" color="F7F2EC" >
			<entity schema="solum-qa" name="marketDataKey" />
			<entity schema="solum-qa" name="marketDataKey" column="providerTickers" />
		</group>
		<group name="ipvData" color="F0ECF7" >
			<entity schema="solum-qa" name="ipvData" />
			<entity schema="solum-qa" name="ipvData" column="values" />
		</group>
		<group name="marketDataValue" color="ECF0F7" >
			<entity schema="solum-qa" name="marketDataValue" column="values" />
			<entity schema="solum-qa" name="marketDataValue" />
		</group>
		<group name="team" color="F7F2EC" >
			<entity schema="solum-qa" name="team" />
		</group>
		<group name="correlationMatrix" color="EEF7EC" >
			<entity schema="solum-qa" name="correlationMatrix" />
			<entity schema="solum-qa" name="correlationMatrix" column="values._id.key1" />
			<entity schema="solum-qa" name="correlationMatrix" column="values._id" />
			<entity schema="solum-qa" name="correlationMatrix" column="values._id.key2" />
			<entity schema="solum-qa" name="correlationMatrix" column="values" />
		</group>
		<group name="company" color="ECECF7" >
			<entity schema="solum-qa" name="company" />
		</group>
		<group name="dataProvider" color="F0ECF7" >
			<entity schema="solum-qa" name="dataProvider" />
		</group>
		<group name="marketDataGroup" color="EEF7EC" >
			<entity schema="solum-qa" name="marketDataGroup" column="auditLogs" />
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="ipvDataGroup" color="ECECF7" >
			<entity schema="solum-qa" name="ipvDataGroup" column="company" />
			<entity schema="solum-qa" name="ipvDataGroup" column="auditLogs" />
			<entity schema="solum-qa" name="ipvDataGroup" />
		</group>
	</layout>
	<layout name="Global Settings" id="Layout_235e" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[MongoDb]]></comment>
		<entity schema="solum-qa" name="inflationSeasonalitySettings" column="curveSeasonalities.EU AI CPI" color="BED3F4" x="656" y="1056" />
		<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments.USD 3M" color="BEBEF4" x="704" y="608" />
		<entity schema="solum-qa" name="convexityAdjustmentsSettings" color="BEBEF4" x="48" y="560" />
		<entity schema="solum-qa" name="globalValuationSettings" column="curveConfiguration" color="F4DDBE" x="448" y="304" />
		<entity schema="solum-qa" name="curveConfiguration" color="F4DDBE" x="832" y="80" >
			<column name="_id" />
			<column name="entityId" />
			<column name="curveGroupId" />
			<column name="instruments" />
			<column name="name" />
			<column name="overrides" />
		</entity>
		<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments" color="BEBEF4" x="416" y="592" />
		<entity schema="solum-qa" name="inflationSeasonalitySettings" column="curveSeasonalities" color="BED3F4" x="368" y="1040" />
		<entity schema="solum-qa" name="curveStrippingDiscountSettings" color="BED3F4" x="368" y="1728" />
		<entity schema="solum-qa" name="curveStrippingProductSettings" color="C7F4BE" x="48" y="1728" />
		<entity schema="solum-qa" name="globalValuationSettings" color="F4DDBE" x="48" y="160" />
		<entity schema="solum-qa" name="inflationSeasonalitySettings" color="BED3F4" x="48" y="928" />
		<entity schema="solum-qa" name="xvaLiborIndices" column="liborIndices" color="C7F4BE" x="624" y="1424" />
		<entity schema="solum-qa" name="globalValuationSettings" column="nonFxCurveConfiguration" color="F4DDBE" x="448" y="400" />
		<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments.USD 3M.volatilities" color="BEBEF4" x="992" y="656" />
		<entity schema="solum-qa" name="xvaLiborIndices" color="C7F4BE" x="368" y="1392" />
		<entity schema="solum-qa" name="xvaSettings" color="D1BEF4" x="48" y="1392" />
		<group name="xvaSettings" color="F0ECF7" >
			<entity schema="solum-qa" name="xvaSettings" />
		</group>
		<group name="curveStrippingDiscountSettings" color="ECF0F7" >
			<entity schema="solum-qa" name="curveStrippingDiscountSettings" />
		</group>
		<group name="curveStrippingProductSettings" color="EEF7EC" >
			<entity schema="solum-qa" name="curveStrippingProductSettings" />
		</group>
		<group name="xvaLiborIndices" color="EEF7EC" >
			<entity schema="solum-qa" name="xvaLiborIndices" />
			<entity schema="solum-qa" name="xvaLiborIndices" column="liborIndices" />
		</group>
		<group name="globalValuationSettings" color="F7F2EC" >
			<entity schema="solum-qa" name="globalValuationSettings" />
			<entity schema="solum-qa" name="globalValuationSettings" column="nonFxCurveConfiguration" />
			<entity schema="solum-qa" name="globalValuationSettings" column="curveConfiguration" />
		</group>
		<group name="convexityAdjustmentsSettings" color="ECECF7" >
			<entity schema="solum-qa" name="convexityAdjustmentsSettings" />
			<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments.USD 3M.volatilities" />
			<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments.USD 3M" />
			<entity schema="solum-qa" name="convexityAdjustmentsSettings" column="curveConvexityAdjustments" />
		</group>
		<group name="inflationSeasonalitySettings" color="ECF0F7" >
			<entity schema="solum-qa" name="inflationSeasonalitySettings" />
			<entity schema="solum-qa" name="inflationSeasonalitySettings" column="curveSeasonalities.EU AI CPI" />
			<entity schema="solum-qa" name="inflationSeasonalitySettings" column="curveSeasonalities" />
		</group>
		<group name="curveConfiguration" color="F7F2EC" >
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
	</layout>
	<layout name="Portfolio and Valuations" id="Layout_222a" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[MongoDb]]></comment>
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues.sensitivities.EUR" color="BED3F4" x="48" y="1520" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues.sensitivities.EUR" color="BED3F4" x="48" y="1632" />
		<entity schema="solum-qa" name="portfolio" column="auditLogs" color="F4DDBE" x="1824" y="2144" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.breakevenMetrics" color="BED3F4" x="1280" y="1616" />
		<entity schema="solum-qa" name="calculationPortfolioItem" color="BED3F4" x="1344" y="512" />
		<entity schema="solum-qa" name="calculationResult" color="C7F4BE" x="1808" y="528" />
		<entity schema="solum-qa" name="calculationResult" column="configurationData.calculationResultChartData" color="C7F4BE" x="2576" y="880" />
		<entity schema="solum-qa" name="calculationResultXva" color="3986C1" x="1328" y="272" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cashFlowMetrics" color="BED3F4" x="512" y="1328" />
		<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.cdsTradeDetails" color="C7F4BE" x="416" y="2640" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.cdsTradeDetails" color="BED3F4" x="576" y="1072" />
		<entity schema="solum-qa" name="calculationResult" column="configurationData.calculationResultChartData.chartPoints" color="C7F4BE" x="2848" y="928" />
		<entity schema="solum-qa" name="comparisonResult" column="curveConfigComparisonDifferenceChartData.chartPoints" color="D1BEF4" x="2800" y="368" />
		<entity schema="solum-qa" name="clientPvComparisonResult" color="3986C1" x="1760" y="2336" />
		<entity schema="solum-qa" name="clientPvComparisonResultItem" color="BEBEF4" x="3248" y="880" />
		<entity schema="solum-qa" name="company" color="BED3F4" x="2656" y="1840" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="portfolio" column="company" color="F4DDBE" x="1760" y="2032" />
		<entity schema="solum-qa" name="companyLegalEntity" color="BEBEF4" x="2848" y="1776" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="comparisonResult" color="D1BEF4" x="1808" y="80" />
		<entity schema="solum-qa" name="comparisonResultItem" color="BED3F4" x="3248" y="192" />
		<entity schema="solum-qa" name="calculationResult" column="configurationData" color="C7F4BE" x="2128" y="736" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues" color="BED3F4" x="512" y="1584" />
		<entity schema="solum-qa" name="comparisonResult" column="curveConfigComparisonDifferenceChartData" color="D1BEF4" x="2384" y="320" />
		<entity schema="solum-qa" name="calculationResult" column="configurationData.curveConfiguration" color="C7F4BE" x="2576" y="768" />
		<entity schema="solum-qa" name="curveConfiguration" color="C7F4BE" x="1808" y="1136" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="calculationResult" column="configurationData.curveGroup" color="C7F4BE" x="2576" y="656" />
		<entity schema="solum-qa" name="curveGroup" color="BEBEF4" x="2048" y="1136" >
			<column name="_id" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="dashboard" color="F4DDBE" x="2592" y="1392" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues" color="BED3F4" x="512" y="1472" />
		<entity schema="solum-qa" name="portfolio" column="entity" color="F4DDBE" x="2368" y="1632" />
		<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.info" color="C7F4BE" x="416" y="2544" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.info" color="BED3F4" x="544" y="960" />
		<entity schema="solum-qa" name="calculationResult" column="marketData" color="C7F4BE" x="2128" y="608" />
		<entity schema="solum-qa" name="marketDataGroup" color="D1BEF4" x="2240" y="1376" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics" color="BED3F4" x="912" y="1216" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.payLeg" color="BED3F4" x="512" y="656" />
		<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.payLeg" color="C7F4BE" x="416" y="1904" />
		<entity schema="solum-qa" name="portfolio" color="F4DDBE" x="2080" y="1840" />
		<entity schema="solum-qa" name="portfolioItem" color="C7F4BE" x="1376" y="1808" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.receiveLeg" color="BED3F4" x="512" y="800" />
		<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.receiveLeg" color="C7F4BE" x="416" y="2240" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues.sensitivities" color="BED3F4" x="272" y="1504" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues.sensitivities" color="BED3F4" x="272" y="1616" />
		<entity schema="solum-qa" name="team" color="F4DDBE" x="2912" y="2176" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="portfolioItem" column="tradeDetails" color="C7F4BE" x="1024" y="1888" />
		<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails" color="BED3F4" x="944" y="624" />
		<entity schema="solum-qa" name="comparisonResultItem" column="view" color="BED3F4" x="3504" y="256" />
		<entity schema="solum-qa" name="clientPvComparisonResultItem" column="view" color="BEBEF4" x="3520" y="944" />
		<group name="clientPvComparisonResultItem" color="ECECF7" >
			<entity schema="solum-qa" name="clientPvComparisonResultItem" />
			<entity schema="solum-qa" name="clientPvComparisonResultItem" column="view" />
		</group>
		<group name="comparisonResultItem" color="ECF0F7" >
			<entity schema="solum-qa" name="comparisonResultItem" />
			<entity schema="solum-qa" name="comparisonResultItem" column="view" />
		</group>
		<group name="curveConfiguration" color="EEF7EC" >
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
		<group name="marketDataGroup" color="F0ECF7" >
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="team" color="F7F2EC" >
			<entity schema="solum-qa" name="team" />
		</group>
		<group name="comparisonResult" color="F0ECF7" >
			<entity schema="solum-qa" name="comparisonResult" />
			<entity schema="solum-qa" name="comparisonResult" column="curveConfigComparisonDifferenceChartData" />
			<entity schema="solum-qa" name="comparisonResult" column="curveConfigComparisonDifferenceChartData.chartPoints" />
		</group>
		<group name="companyLegalEntity" color="ECECF7" >
			<entity schema="solum-qa" name="companyLegalEntity" />
		</group>
		<group name="company" color="ECF0F7" >
			<entity schema="solum-qa" name="company" />
		</group>
		<group name="dashboard" color="F7F2EC" >
			<entity schema="solum-qa" name="dashboard" />
		</group>
		<group name="curveGroup" color="ECECF7" >
			<entity schema="solum-qa" name="curveGroup" />
		</group>
		<group name="portfolioItem" color="EEF7EC" >
			<entity schema="solum-qa" name="portfolioItem" />
			<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.cdsTradeDetails" />
			<entity schema="solum-qa" name="portfolioItem" column="tradeDetails" />
			<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.info" />
			<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.payLeg" />
			<entity schema="solum-qa" name="portfolioItem" column="tradeDetails.receiveLeg" />
		</group>
		<group name="portfolio" color="F7F2EC" >
			<entity schema="solum-qa" name="portfolio" column="company" />
			<entity schema="solum-qa" name="portfolio" column="entity" />
			<entity schema="solum-qa" name="portfolio" column="auditLogs" />
			<entity schema="solum-qa" name="portfolio" />
		</group>
		<group name="calculationResult" color="EEF7EC" >
			<entity schema="solum-qa" name="calculationResult" column="configurationData.calculationResultChartData.chartPoints" />
			<entity schema="solum-qa" name="calculationResult" column="configurationData.calculationResultChartData" />
			<entity schema="solum-qa" name="calculationResult" column="configurationData.curveConfiguration" />
			<entity schema="solum-qa" name="calculationResult" column="configurationData" />
			<entity schema="solum-qa" name="calculationResult" column="marketData" />
			<entity schema="solum-qa" name="calculationResult" column="configurationData.curveGroup" />
			<entity schema="solum-qa" name="calculationResult" />
		</group>
		<group name="calculationPortfolioItem" color="ECF0F7" >
			<entity schema="solum-qa" name="calculationPortfolioItem" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.breakevenMetrics" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cashFlowMetrics" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues.sensitivities.EUR" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues.sensitivities" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.dv01TradeValues" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues.sensitivities.EUR" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues.sensitivities" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="metrics.cs01TradeValues" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.cdsTradeDetails" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.info" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.payLeg" />
			<entity schema="solum-qa" name="calculationPortfolioItem" column="tradeDetails.receiveLeg" />
		</group>
	</layout>
	<layout name="User" id="Layout_1cd0" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER diagram - User]]></comment>
		<entity schema="solum-qa" name="role" column="auditLogs" color="C7F4BE" x="1904" y="304" />
		<entity schema="solum-qa" name="team" column="auditLogs" color="F4DDBE" x="48" y="208" />
		<entity schema="solum-qa" name="user" column="auditLogs" color="BED3F4" x="1296" y="400" />
		<entity schema="solum-qa" name="user" column="customerRoles" color="BED3F4" x="1296" y="288" />
		<entity schema="solum-qa" name="role" color="C7F4BE" x="1536" y="128" />
		<entity schema="solum-qa" name="role" column="rolePermissions" color="C7F4BE" x="1904" y="176" />
		<entity schema="solum-qa" name="team" color="F4DDBE" x="272" y="80" />
		<entity schema="solum-qa" name="user" column="teams" color="BED3F4" x="544" y="320" />
		<entity schema="solum-qa" name="user" color="BED3F4" x="784" y="160" />
		<group name="role" color="EEF7EC" >
			<entity schema="solum-qa" name="role" />
			<entity schema="solum-qa" name="role" column="rolePermissions" />
			<entity schema="solum-qa" name="role" column="auditLogs" />
		</group>
		<group name="user" color="ECF0F7" >
			<entity schema="solum-qa" name="user" column="customerRoles" />
			<entity schema="solum-qa" name="user" column="teams" />
			<entity schema="solum-qa" name="user" column="auditLogs" />
			<entity schema="solum-qa" name="user" />
		</group>
		<group name="team" color="F7F2EC" >
			<entity schema="solum-qa" name="team" />
			<entity schema="solum-qa" name="team" column="auditLogs" />
		</group>
	</layout>
	<layout name="XM" id="Layout_2107" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER diagram - XM]]></comment>
		<entity schema="solum-qa" name="instrumentResultPreliminary" column="allProvidersData" color="BED3F4" x="768" y="960" />
		<entity schema="solum-qa" name="ipvTaskExecution" column="approvalTeams" color="BEBEF4" x="2768" y="2128" />
		<entity schema="solum-qa" name="taskExecution" column="approvalTeams" color="C7F4BE" x="2832" y="1472" />
		<entity schema="solum-qa" name="taskExecution" column="assetFilter" color="C7F4BE" x="2832" y="992" />
		<entity schema="solum-qa" name="taskExecution" column="assetFilterSummary" color="C7F4BE" x="2832" y="1152" />
		<entity schema="solum-qa" name="instrumentResultOverlay" column="breakTests" color="BED3F4" x="2816" y="656" />
		<entity schema="solum-qa" name="instrumentResultPreliminary" column="breakTests" color="BED3F4" x="768" y="1072" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="breakTests" color="F4DDBE" x="1824" y="2768" />
		<entity schema="solum-qa" name="cleanMarketData" color="D1BEF4" x="1088" y="176" />
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" color="BEBEF4" x="448" y="1904" >
			<column name="_id" />
			<column name="companyId" />
			<column name="ipvDataGroup" />
			<column name="products" />
		</entity>
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" color="C7F4BE" x="304" y="1536" >
			<column name="_id" />
			<column name="companyId" />
			<column name="marketDataGroup" />
			<column name="nonFxCurveConfiguration" />
		</entity>
		<entity schema="solum-qa" name="curveConfiguration" color="D1BEF4" x="48" y="1472" >
			<column name="_id" />
			<column name="entityId" />
			<column name="curveGroupId" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="cleanMarketData" column="values.curveConfigurationsValues" color="D1BEF4" x="432" y="400" />
		<entity schema="solum-qa" name="dashboard" color="C7F4BE" x="1552" y="1296" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.delta" color="F4DDBE" x="2112" y="2496" />
		<entity schema="solum-qa" name="exceptionManagementResult" color="BED3F4" x="1520" y="848" />
		<entity schema="solum-qa" name="instrumentResultOverlay" column="instrument" color="BED3F4" x="2816" y="80" />
		<entity schema="solum-qa" name="instrumentResultPreliminary" column="instrument" color="BED3F4" x="720" y="560" />
		<entity schema="solum-qa" name="instrumentResult" column="instrument" color="C7F4BE" x="1520" y="464" />
		<entity schema="solum-qa" name="instrumentResult" color="C7F4BE" x="1856" y="400" />
		<entity schema="solum-qa" name="instrumentResultOverlay" color="BED3F4" x="2368" y="400" />
		<entity schema="solum-qa" name="instrumentResultPreliminary" color="BED3F4" x="1056" y="816" />
		<entity schema="solum-qa" name="ipvDataGroup" color="BED3F4" x="944" y="1504" >
			<column name="_id" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="ipvExceptionManagementResult" color="F4DDBE" x="1488" y="1632" />
		<entity schema="solum-qa" name="ipvTaskExecution" color="BEBEF4" x="2352" y="1728" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" color="F4DDBE" x="1472" y="2192" />
		<entity schema="solum-qa" name="ipvExceptionManagementResult" column="ipvValuationSettingsData" color="F4DDBE" x="1872" y="1872" />
		<entity schema="solum-qa" name="marketDataGroup" color="F4DDBE" x="672" y="1504" >
			<column name="_id" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.parRate" color="F4DDBE" x="2112" y="2576" />
		<entity schema="solum-qa" name="portfolio" color="F4DDBE" x="1120" y="1312" >
			<column name="_id" />
			<column name="teamIds" />
			<column name="company" />
			<column name="entity" />
			<column name="externalPortfolioId" />
		</entity>
		<entity schema="solum-qa" name="instrumentResultOverlay" column="primaryProviderData" color="BED3F4" x="2816" y="368" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData" color="F4DDBE" x="1824" y="2448" />
		<entity schema="solum-qa" name="instrumentResultPreliminary" column="providerData" color="BED3F4" x="768" y="848" />
		<entity schema="solum-qa" name="instrumentResultOverlay" column="breakTests.providerValue" color="BED3F4" x="3136" y="752" />
		<entity schema="solum-qa" name="instrumentResultPreliminary" column="breakTests.providerValue" color="BED3F4" x="528" y="1120" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="breakTests.providerValue" color="F4DDBE" x="2112" y="2832" />
		<entity schema="solum-qa" name="cleanMarketData" column="values.providersValues" color="D1BEF4" x="464" y="272" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="secondaryProviderData.pv" color="F4DDBE" x="2112" y="2672" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.pv" color="F4DDBE" x="2112" y="2416" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="resolution" color="F4DDBE" x="1824" y="2896" />
		<entity schema="solum-qa" name="ipvTaskExecution" column="resolutionTeams" color="BEBEF4" x="2768" y="2032" />
		<entity schema="solum-qa" name="taskExecution" column="resolutionTeams" color="C7F4BE" x="2832" y="1344" />
		<entity schema="solum-qa" name="instrumentResultOverlay" column="secondaryProviderData" color="BED3F4" x="2816" y="528" />
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="secondaryProviderData" color="F4DDBE" x="1824" y="2640" />
		<entity schema="solum-qa" name="taskExecution" color="C7F4BE" x="2352" y="960" />
		<entity schema="solum-qa" name="team" color="D1BEF4" x="960" y="1328" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="ipvTradeResultOverlay" column="trade" color="F4DDBE" x="1824" y="2192" />
		<entity schema="solum-qa" name="ipvTaskExecution" column="tradeFilter" color="BEBEF4" x="2768" y="1760" />
		<entity schema="solum-qa" name="ipvTaskExecution" column="tradeFilterSummary" color="BEBEF4" x="2768" y="1888" />
		<entity schema="solum-qa" name="cleanMarketData" column="values" color="D1BEF4" x="720" y="256" />
		<group name="instrument" color="EEF7EC" >
			<entity schema="solum-qa" name="instrumentResult" column="instrument" />
			<entity schema="solum-qa" name="instrumentResult" />
		</group>
		<group name="companyLegalEntityIpvSettings" color="ECECF7" >
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" />
		</group>
		<group name="team" color="F0ECF7" >
			<entity schema="solum-qa" name="team" />
		</group>
		<group name="ipvDataGroup" color="ECF0F7" >
			<entity schema="solum-qa" name="ipvDataGroup" />
		</group>
		<group name="companyLegalEntityValuationSettings" color="EEF7EC" >
			<entity schema="solum-qa" name="companyLegalEntityValuationSettings" />
		</group>
		<group name="cleanMarketData" color="F0ECF7" >
			<entity schema="solum-qa" name="cleanMarketData" column="values.providersValues" />
			<entity schema="solum-qa" name="cleanMarketData" column="values" />
			<entity schema="solum-qa" name="cleanMarketData" column="values.curveConfigurationsValues" />
			<entity schema="solum-qa" name="cleanMarketData" />
		</group>
		<group name="marketDataGroup" color="F7F2EC" >
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="portfolio" color="ECECF7" >
			<entity schema="solum-qa" name="portfolio" />
		</group>
		<group name="approvalTeams" color="EEF7EC" >
			<entity schema="solum-qa" name="taskExecution" column="approvalTeams" />
			<entity schema="solum-qa" name="taskExecution" />
			<entity schema="solum-qa" name="taskExecution" column="assetFilter" />
			<entity schema="solum-qa" name="taskExecution" column="assetFilterSummary" />
			<entity schema="solum-qa" name="taskExecution" column="resolutionTeams" />
		</group>
		<group name="ipvExceptionManagementResult" color="F7F2EC" >
			<entity schema="solum-qa" name="ipvExceptionManagementResult" column="ipvValuationSettingsData" />
			<entity schema="solum-qa" name="ipvExceptionManagementResult" />
		</group>
		<group name="tradeFilter" color="ECECF7" >
			<entity schema="solum-qa" name="ipvTaskExecution" column="tradeFilter" />
			<entity schema="solum-qa" name="ipvTaskExecution" />
			<entity schema="solum-qa" name="ipvTaskExecution" column="tradeFilterSummary" />
			<entity schema="solum-qa" name="ipvTaskExecution" column="resolutionTeams" />
			<entity schema="solum-qa" name="ipvTaskExecution" column="approvalTeams" />
		</group>
		<group name="intraumentResultPreliminary" color="ECF0F7" >
			<entity schema="solum-qa" name="instrumentResultPreliminary" column="allProvidersData" />
			<entity schema="solum-qa" name="instrumentResultPreliminary" />
			<entity schema="solum-qa" name="instrumentResultPreliminary" column="instrument" />
			<entity schema="solum-qa" name="instrumentResultPreliminary" column="providerData" />
			<entity schema="solum-qa" name="instrumentResultPreliminary" column="breakTests.providerValue" />
			<entity schema="solum-qa" name="instrumentResultPreliminary" column="breakTests" />
		</group>
		<group name="instrument" color="ECF0F7" >
			<entity schema="solum-qa" name="instrumentResultOverlay" column="instrument" />
			<entity schema="solum-qa" name="instrumentResultOverlay" />
			<entity schema="solum-qa" name="instrumentResultOverlay" column="primaryProviderData" />
			<entity schema="solum-qa" name="instrumentResultOverlay" column="breakTests.providerValue" />
			<entity schema="solum-qa" name="instrumentResultOverlay" column="breakTests" />
			<entity schema="solum-qa" name="instrumentResultOverlay" column="secondaryProviderData" />
		</group>
		<group name="curveConfiguration" color="F0ECF7" >
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
		<group name="exceptionManagementResult" color="ECF0F7" >
			<entity schema="solum-qa" name="exceptionManagementResult" />
		</group>
		<group name="ipvTradeResultOverlay" color="F7F2EC" >
			<entity schema="solum-qa" name="ipvTradeResultOverlay" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.delta" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.parRate" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="primaryProviderData.pv" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="secondaryProviderData.pv" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="secondaryProviderData" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="resolution" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="breakTests.providerValue" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="breakTests" />
			<entity schema="solum-qa" name="ipvTradeResultOverlay" column="trade" />
		</group>
		<group name="dashboard" color="EEF7EC" >
			<entity schema="solum-qa" name="dashboard" />
		</group>
		<script name="JSon_001" id="Editor_189b" language="SQL" >
			<string><![CDATA[// REPLACE <database> and <collection> WITH AN EXISTING DATABASE AND COLLECTION
<database>.<collection>.find()]]></string>
		</script>
		<browser id="Browse_2e4" name="JSon_002" confirm_updates="y" >
			<browse_table schema="solum-qa" entity="instrumentResultOverlay" x="20" y="20" width="400" height="300" record_view="y" >
				<browse_table schema="solum-qa" entity="instrumentResultOverlay" column="secondaryProviderData" fk="JSon" x="460" y="20" width="500" height="350" record_view="y" />
			</browse_table>
		</browser>
		<query id="Query_b10" name="JSon" >
			<query_table schema="solum-qa" name="instrumentResultOverlay" alias="instrumentResultOverlay" x="48" y="48" >
				<column name="_id" />
				<column name="dashboardId" />
				<column name="exceptionManagementResultId" />
				<column name="instrument" />
				<column name="curveConfigurationId" />
				<column name="status" />
				<column name="primaryProviderData" />
				<column name="secondaryProviderData" />
				<column name="previousOverlayValue" />
				<column name="breakTests" />
				<column name="appliedTestsCount" />
				<column name="hasBreaks" />
				<column name="modifiedBy" />
				<column name="modifiedAt" />
				<column name="previousStatuses" />
				<column name="_class" />
				<column name="taskId" />
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="instrument" x="448" y="48" fk="JSon" type="Inner Join" >
					<column name="assetClassGroup" />
					<column name="assetClass" />
					<column name="currency" />
					<column name="assetName" />
					<column name="instrumentType" />
					<column name="instrumentTypeSort" />
					<column name="tenor" />
					<column name="nodeInstrument" />
					<column name="parsedTenor" />
					<column name="key" />
					<column name="mdkName" />
					<column name="underlying" />
					<column name="fxPair" />
				</query_table>
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="primaryProviderData" x="448" y="336" fk="JSon" type="Inner Join" >
					<column name="provider" />
					<column name="value" />
					<column name="previousValue" />
				</query_table>
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="secondaryProviderData" x="448" y="464" fk="JSon" type="Inner Join" >
					<column name="provider" />
					<column name="value" />
					<column name="previousValue" />
				</query_table>
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="breakTests" x="448" y="592" fk="JSon" type="Inner Join" >
					<column name="breakTestName" />
					<column name="breakTestType" />
					<column name="measureType" />
					<column name="operator" />
					<column name="threshold" />
					<column name="providerValue" />
					<query_table schema="solum-qa" name="instrumentResultOverlay" column="breakTests.providerValue" x="656" y="592" fk="JSon" type="Inner Join" >
						<column name="triggered" />
						<column name="value" />
						<column name="calculationOnly" />
					</query_table>
				</query_table>
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="modifiedBy" x="448" y="768" fk="JSon" type="Inner Join" >
					<column name="userId" />
					<column name="username" />
					<column name="name" />
				</query_table>
				<query_table schema="solum-qa" name="instrumentResultOverlay" column="secondaryProviderData" x="416" y="48" fk="JSon" type="Inner Join" />
			</query_table>
		</query>
		<form name="Report" id="Report_cab" template="Page" css="container-fluid" view="frame" >
			<body name="Body" template="Grid" pos="0,0,0,0,f,f" layout="{{-2},{-2}}" scroll="n" html="n" />
		</form>
	</layout>
	<layout name="XM Dashboard" id="Layout_79c" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER Diagram - XM  Dashboard]]></comment>
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter.companies" color="C7F4BE" x="1808" y="416" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.company" color="C7F4BE" x="1808" y="560" />
		<entity schema="solum-qa" name="companyLegalEntityIpvSettings" color="D1BEF4" x="1056" y="1408" >
			<column name="_id" />
			<column name="companyId" />
			<column name="entityId" />
			<column name="ipvDataGroup" />
			<column name="products" />
		</entity>
		<entity schema="solum-qa" name="companyLegalEntityValuationSettings" color="BED3F4" x="1152" y="1200" >
			<column name="_id" />
			<column name="companyId" />
			<column name="entityId" />
			<column name="marketDataGroup" />
		</entity>
		<entity schema="solum-qa" name="dashboardEntryMd" column="curveConfiguration" color="BED3F4" x="128" y="288" />
		<entity schema="solum-qa" name="curveConfiguration" color="BEBEF4" x="736" y="1136" >
			<column name="_id" />
			<column name="entityId" />
			<column name="curveGroupId" />
			<column name="name" />
		</entity>
		<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup.curveConfigurationResolvers" color="C7F4BE" x="1504" y="240" />
		<entity schema="solum-qa" name="dashboard" color="C7F4BE" x="784" y="80" />
		<entity schema="solum-qa" name="dashboardEntryMd" color="BED3F4" x="352" y="80" />
		<entity schema="solum-qa" name="dashboardEntryMdBatch" color="BEBEF4" x="368" y="912" />
		<entity schema="solum-qa" name="dashboardEntryVd" color="F4DDBE" x="352" y="464" />
		<entity schema="solum-qa" name="dashboard" column="dateRange" color="C7F4BE" x="1136" y="80" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter.companies.entities" color="C7F4BE" x="2064" y="448" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.entity" color="C7F4BE" x="1808" y="656" />
		<entity schema="solum-qa" name="dashboardEntryVd" column="ipvDataGroup" color="F4DDBE" x="48" y="752" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup" color="C7F4BE" x="1808" y="976" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings" color="C7F4BE" x="1504" y="544" />
		<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup.marketDataGroup" color="C7F4BE" x="1504" y="144" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.marketDataGroup" color="C7F4BE" x="1808" y="880" />
		<entity schema="solum-qa" name="marketDataGroup" color="C7F4BE" x="1744" y="1168" >
			<column name="_id" />
			<column name="name" />
			<column name="teamIds" />
		</entity>
		<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup" color="C7F4BE" x="1136" y="208" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.portfolio" color="C7F4BE" x="1808" y="768" />
		<entity schema="solum-qa" name="portfolio" color="F4DDBE" x="720" y="1376" >
			<column name="_id" />
			<column name="teamIds" />
			<column name="company" />
			<column name="entity" />
			<column name="externalPortfolioId" />
		</entity>
		<entity schema="solum-qa" name="dashboardEntryVd" column="portfolios" color="F4DDBE" x="48" y="624" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter" color="C7F4BE" x="1504" y="400" />
		<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup" color="C7F4BE" x="1136" y="384" />
		<group name="dashboardEntryMdBatch" color="ECECF7" >
			<entity schema="solum-qa" name="dashboardEntryMdBatch" />
		</group>
		<group name="dashboardEntryMd" color="ECF0F7" >
			<entity schema="solum-qa" name="dashboardEntryMd" column="curveConfiguration" />
			<entity schema="solum-qa" name="dashboardEntryMd" />
		</group>
		<group name="companyLegalEntityIpvSettings" color="F0ECF7" >
			<entity schema="solum-qa" name="companyLegalEntityIpvSettings" />
		</group>
		<group name="dashboardEntryVd" color="F7F2EC" >
			<entity schema="solum-qa" name="dashboardEntryVd" column="portfolios" />
			<entity schema="solum-qa" name="dashboardEntryVd" column="ipvDataGroup" />
			<entity schema="solum-qa" name="dashboardEntryVd" />
		</group>
		<group name="companyLegalEntityValuationSettings" color="ECF0F7" >
			<entity schema="solum-qa" name="companyLegalEntityValuationSettings" />
		</group>
		<group name="marketDataGroup" color="EEF7EC" >
			<entity schema="solum-qa" name="marketDataGroup" />
		</group>
		<group name="portfolio" color="F7F2EC" >
			<entity schema="solum-qa" name="portfolio" />
		</group>
		<group name="curveConfiguration" color="ECECF7" >
			<entity schema="solum-qa" name="curveConfiguration" />
		</group>
		<group name="dashboard" color="EEF7EC" >
			<entity schema="solum-qa" name="dashboard" column="dateRange" />
			<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup.marketDataGroup" />
			<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter.companies.entities" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter.companies" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.portfoliosFilter" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.ipvDataGroup" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.portfolio" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.entity" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.company" />
			<entity schema="solum-qa" name="dashboard" column="vdExceptionManagementSetup.ipvValuationSettings.marketDataGroup" />
			<entity schema="solum-qa" name="dashboard" column="mdExceptionManagementSetup.curveConfigurationResolvers" />
			<entity schema="solum-qa" name="dashboard" />
		</group>
	</layout>
	<layout name="XM Setup" id="Layout_1f4b" confirmed="y" joined_routing="y" show_column_type="y" show_relation="columns" >
		<comment><![CDATA[Xplain ER Diagram - XM  Setup]]></comment>
		<entity schema="solum-qa" name="breakTest" column="assetFilter" color="BED3F4" x="464" y="448" />
		<entity schema="solum-qa" name="breakTest" color="BED3F4" x="144" y="464" />
		<entity schema="solum-qa" name="ipvTaskDefaultTeams" column="defaultApprovalTeam" color="C7F4BE" x="2096" y="1024" />
		<entity schema="solum-qa" name="mdTaskDefaultTeams" column="defaultApprovalTeam" color="D1BEF4" x="2080" y="736" />
		<entity schema="solum-qa" name="ipvTaskDefaultTeams" column="defaultResolutionTeam" color="C7F4BE" x="2096" y="928" />
		<entity schema="solum-qa" name="mdTaskDefaultTeams" column="defaultResolutionTeam" color="D1BEF4" x="2080" y="624" />
		<entity schema="solum-qa" name="tasksDefinition" column="overrides.overrideTeams.filter" color="BED3F4" x="2640" y="192" />
		<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides.overrideTeams.filter" color="F4DDBE" x="48" y="256" />
		<entity schema="solum-qa" name="ipvBreakTest" color="BEBEF4" x="144" y="1024" />
		<entity schema="solum-qa" name="ipvTaskDefaultTeams" color="C7F4BE" x="1776" y="896" />
		<entity schema="solum-qa" name="ipvTasksDefinition" color="F4DDBE" x="1008" y="80" />
		<entity schema="solum-qa" name="mdTaskDefaultTeams" color="D1BEF4" x="1776" y="544" />
		<entity schema="solum-qa" name="breakTest" column="overrides.overrideAssetFilter" color="BED3F4" x="752" y="624" />
		<entity schema="solum-qa" name="tasksDefinition" column="overrides.overrideTeams" color="BED3F4" x="2336" y="192" />
		<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides.overrideTeams" color="F4DDBE" x="336" y="240" />
		<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides" color="F4DDBE" x="624" y="224" />
		<entity schema="solum-qa" name="tasksDefinition" column="overrides" color="BED3F4" x="2048" y="240" />
		<entity schema="solum-qa" name="breakTest" column="overrides" color="BED3F4" x="464" y="592" />
		<entity schema="solum-qa" name="breakTest" column="parentTest" color="BED3F4" x="464" y="784" />
		<entity schema="solum-qa" name="tasksDefinition" color="BED3F4" x="1712" y="80" />
		<entity schema="solum-qa" name="team" color="C7F4BE" x="1488" y="400" >
			<column name="_id" />
		</entity>
		<entity schema="solum-qa" name="ipvTasksDefinition" column="teams" color="F4DDBE" x="656" y="112" />
		<entity schema="solum-qa" name="tasksDefinition" column="teams" color="BED3F4" x="2048" y="128" />
		<entity schema="solum-qa" name="breakTest" column="overrides.tenorFilter.tenorBuckets" color="BED3F4" x="1024" y="832" />
		<entity schema="solum-qa" name="breakTest" column="overrides.tenorFilter" color="BED3F4" x="752" y="800" />
		<entity schema="solum-qa" name="ipvBreakTest" column="tradeFilter" color="BEBEF4" x="416" y="1120" />
		<group name="ipvBreakTest" color="ECECF7" >
			<entity schema="solum-qa" name="ipvBreakTest" />
			<entity schema="solum-qa" name="ipvBreakTest" column="tradeFilter" />
		</group>
		<group name="ipvTaskDefaultTeams" color="EEF7EC" >
			<entity schema="solum-qa" name="ipvTaskDefaultTeams" column="defaultApprovalTeam" />
			<entity schema="solum-qa" name="ipvTaskDefaultTeams" column="defaultResolutionTeam" />
			<entity schema="solum-qa" name="ipvTaskDefaultTeams" />
		</group>
		<group name="mdTaskDefaultTeams" color="F0ECF7" >
			<entity schema="solum-qa" name="mdTaskDefaultTeams" column="defaultResolutionTeam" />
			<entity schema="solum-qa" name="mdTaskDefaultTeams" column="defaultApprovalTeam" />
			<entity schema="solum-qa" name="mdTaskDefaultTeams" />
		</group>
		<group name="ipvTasksDefinition" color="F7F2EC" >
			<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides.overrideTeams.filter" />
			<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides.overrideTeams" />
			<entity schema="solum-qa" name="ipvTasksDefinition" column="overrides" />
			<entity schema="solum-qa" name="ipvTasksDefinition" column="teams" />
			<entity schema="solum-qa" name="ipvTasksDefinition" />
		</group>
		<group name="breakTest" color="ECF0F7" >
			<entity schema="solum-qa" name="breakTest" />
			<entity schema="solum-qa" name="breakTest" column="assetFilter" />
			<entity schema="solum-qa" name="breakTest" column="overrides.overrideAssetFilter" />
			<entity schema="solum-qa" name="breakTest" column="overrides" />
			<entity schema="solum-qa" name="breakTest" column="parentTest" />
			<entity schema="solum-qa" name="breakTest" column="overrides.tenorFilter.tenorBuckets" />
			<entity schema="solum-qa" name="breakTest" column="overrides.tenorFilter" />
		</group>
		<group name="tasksDefinition" color="ECF0F7" >
			<entity schema="solum-qa" name="tasksDefinition" column="overrides.overrideTeams.filter" />
			<entity schema="solum-qa" name="tasksDefinition" column="overrides.overrideTeams" />
			<entity schema="solum-qa" name="tasksDefinition" column="overrides" />
			<entity schema="solum-qa" name="tasksDefinition" column="teams" />
			<entity schema="solum-qa" name="tasksDefinition" />
		</group>
		<group name="team" color="EEF7EC" >
			<entity schema="solum-qa" name="team" />
		</group>
	</layout>
</project>