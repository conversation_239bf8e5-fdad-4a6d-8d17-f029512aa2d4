db.getCollection("mongockChangeLog").insertMany([
  {
    "author" : "solum",
    "changeId" : "24_before",
    "executionId" : "skip_changeunit_024",
    "changeLogClass" : "com.solum.xplain.support.migration.changeunits.ChangeUnit024",
    "changeSetMethod" : "beforeExecution",
    "errorTrace" : null,
    "executionHostname" : "manual",
    "executionMillis" : NumberLong(1),
    "metadata" : null,
    "state" : "EXECUTED",
    "systemChange" : false,
    "timestamp" : ISODate(),
    "type" : "BEFORE_EXECUTION"
  },
  {
    "author" : "solum",
    "changeId" : "24",
    "executionId" : "skip_changeunit_024",
    "changeLogClass" : "com.solum.xplain.support.migration.changeunits.ChangeUnit024",
    "changeSetMethod" : "execution",
    "errorTrace" : null,
    "executionHostname" : "manual",
    "executionMillis" : Number<PERSON><PERSON>(1),
    "metadata" : null,
    "state" : "EXECUTED",
    "systemChange" : false,
    "timestamp" : ISODate(),
    "type" : "EXECUTION"
  }
]);
