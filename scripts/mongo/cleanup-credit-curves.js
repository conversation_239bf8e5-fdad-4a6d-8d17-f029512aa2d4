// BNYM data from incident is in XPL - Documents/Clients/BNYM/Documents Received/P1 - 11 Oct - Credit Curve Sort
// load the dump with mongoimport -d <db> -c <collection> --jsonArray creditCurve.json

// Take a backup of the creditCurve collection before starting:
db.creditCurve.aggregate([{$out: "creditCurveBackup"}]);
// Restore instructions - if necessary:
// db.getCollection("creditCurve").renameCollection("creditCurveBackout");
// db.getCollection("creditCurveBackup").renameCollection("creditCurve");

// First of all clear the redundant no-node records. Halves the data.
db.creditCurve.deleteMany({cdsNodes: [], fundingNodes: [], indexNodes: []})
db.creditCurve.deleteMany({cdsNodes: null, fundingNodes: null, indexNodes: []})

// Find the object IDs of records where there is another record with the same entityId and validFrom and just keep the most recent minor version
// Takes a long time to run (5-10mins) but halves the data again.
db.creditCurve.aggregate([
    // Find all the minor revisions (and the most recent) for each major revision
    {
        $group: {
            _id: {
                entityId: "$entityId",
                validFrom: "$validFrom"
            },
            count: { $sum: 1 },
            mostRecent: {$max: "$recordDate"},
            recordDates: {$addToSet: "$recordDate"}
        }
    },
    // Deal with the cases where there is more than one minor revision
    {
        $match: {
            count: {
                $gt: 1
            }
        }
    },
    // Return the relevant data
    {
        $project: {
            _id: 0,
            entityId: "$_id.entityId",
            validFrom: "$_id.validFrom",
            mostRecent: "$mostRecent",
            recordDates: "$recordDates"
        }
    }
], { allowDiskUse: true }).forEach(function(dupes) {
    print("Deleting " + (dupes.recordDates.length - 1) + " records with entityId: " + dupes.entityId + ", validFrom: " + JSON.stringify(dupes.validFrom) + " except for record date: " + JSON.stringify(dupes.mostRecent) + "...");
    let query = {entityId: dupes.entityId, validFrom: {"$eq": dupes.validFrom}, recordDate: {"$ne": dupes.mostRecent}};
    print(".... delete for " + JSON.stringify(query));
    var res = db.creditCurve.deleteMany(query);
    print(".... deleted " + JSON.stringify(res));
})

// Now delete the records where nothing ever changed and just keep the first version
// Quick to run but the one above MUST be run first. Removes about a third of the remaining data.
db.creditCurve.aggregate([
    // Find distinct data values, with the list of valid dates for each
    {
        $group: {
            _id: { // this needs to match CreditCurve.valueEquals() with entityId added
                entityId: "$entityId",
                name: "$name",
                state: "$state",
                curveGroupId: "$curveGroupId",
                curveType: "$curveType",
                creditIndexFactor: "$creditIndexFactor",
                creditIndexSeries: "$creditIndexSeries",
                creditIndexVersion: "$creditIndexVersion",
                creditIndexStartDate: "$creditIndexStartDate",
                reference: "$reference",
                corpTicker: "$corpTicker",
                entityLongName: "$entityLongName",
                seniority: "$seniority",
                recoveryRate: "$recoveryRate",
                currency: "$currency",
                quoteConvention: "$quoteConvention",
                fixedCoupon: "$fixedCoupon",
                docClause: "$docClause",
                sector: "$sector",
                cdsNodes: "$cdsNodes",
                indexNodes: "$indexNodes",
                fundingNodes: "$fundingNodes",
                chartPoints: "$chartPoints"
            },
            count: { $sum: 1 },
            firstOccurence: {$min: "$validFrom"},
            matchingOccurences: {$addToSet: "$validFrom"}
        }
    },
    // Count how many unique records there are for each entity ID
    {
        $group: {
            _id: "$_id.entityId",
            count: { $sum: 1 },
            firstOccurence: {$min: "$firstOccurence"},
            matchingOccurences: {$addToSet: "$matchingOccurences"}
        }
    },
    // Return the entity IDs where there is only one unique data value across all dates
    {
        $match: {
            count: 1
        }
    },
], { allowDiskUse: true }).forEach(function(unchanged) {
    print("Deleting records with entityId: " + unchanged._id + " except for validFrom: " + JSON.stringify(unchanged.firstOccurence) + "...");
    let query = {entityId: unchanged._id, validFrom: {"$ne": unchanged.firstOccurence}};
    print(".... delete for " + JSON.stringify(query));
    var res = db.creditCurve.deleteMany(query);
    print(".... deleted " + JSON.stringify(res));
});
