(function (window) {
  window.__env = window.__env || {};
  window.__env.authConfig = {
    // https://manfredsteyer.github.io/angular-oauth2-oidc/docs/classes/AuthConfig.html#source
    clientId: 'DEBUGGER',
    issuer: 'http://oauth2:8081/default',
    logoutUrl: 'http://oauth2:8081/default',
    requireHttps: false,
    customQueryParams: {audience: 'api'},
    scope: 'openid profile offline_access',
    redirectUri: window.location.origin + '/xplain/',
    responseType: 'code',
    silentRefreshTimeout: 0,
    revocationEndpoint: 'http://oauth2:8081/revoke',
    disablePKCE: true,
    oidc: false,
    disableOAuth2StateCheck: true,
  };
})(this);

const env = window.location.hostname.split('.')[0];
if (env === 'localhost') {
  window.document.title = "api docker - " + window.document.title;
}
