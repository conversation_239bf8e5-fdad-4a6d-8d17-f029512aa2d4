package com.solum.xplain.xva.proxy.config;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@Validated
@ConfigurationProperties(prefix = "app.xva-proxy.s3")
public class S3Properties {
  @NotEmpty private String bucket;
  private String region;
}
