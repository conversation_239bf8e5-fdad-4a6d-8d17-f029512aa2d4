package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
public class XvaResultsFile {

  private final XvaPartyResultsFile partyResults;

  private final Map<String, XvaPartyExposureFile> xvaPartyExposures;

  public XvaResultsFile(
      @JsonProperty("PartyResults") XvaPartyResultsFile partyResults,
      @JsonProperty("PartyExposures") Map<String, XvaPartyExposureFile> xvaPartyExposures) {
    this.partyResults = partyResults;
    this.xvaPartyExposures = xvaPartyExposures;
  }
}
