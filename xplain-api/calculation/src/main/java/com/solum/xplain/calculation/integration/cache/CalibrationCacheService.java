package com.solum.xplain.calculation.integration.cache;

import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.BOND_CURVES;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.CAP_FLOOR;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.CURVES;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.CURVES_FX;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.MD_FX_RATES;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.RATES;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.RATES_CREDIT;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.RATES_FX;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.REF_DATA;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.VOLS;
import static com.solum.xplain.valuation.messages.calibration.CalibrationCacheType.VOL_FX;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.opengamma.strata.basics.ImmutableReferenceData;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataFxRateProvider;
import com.opengamma.strata.market.curve.InterpolatedNodalCurve;
import com.opengamma.strata.pricer.fxopt.BlackFxOptionVolatilities;
import com.solum.xplain.calculation.PortfolioCalculationData;
import com.solum.xplain.calculation.curvegroup.CalculationCreditCalibrationResult;
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData;
import com.solum.xplain.calibration.capfloor.CapletFloorletVolatilityCalibrationResults;
import com.solum.xplain.calibration.rates.RatesCalibrationResult;
import com.solum.xplain.calibration.volatility.VolatilitiesCalibrationData;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.valuation.messages.calibration.CalibrationCacheType;
import com.solum.xplain.valuation.messages.calibration.credits.FxShiftedValuationCreditCurveRates;
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurves;
import com.solum.xplain.valuation.messages.calibration.rates.FxShiftedValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceForCalculation;
import com.solum.xplain.valuation.messages.calibration.vols.ValuationCalibratedCaplets;
import com.solum.xplain.valuation.messages.calibration.vols.ValuationFxShiftedCaplets;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Service
@Profile("!test")
@RequiredArgsConstructor
public class CalibrationCacheService {
  private static final String CAPLET_CACHE_TEMPLATE = "%s_%s_%s";
  private static final String COMMON_CACHE_TEMPLATE = "%s_%s";

  private final DataGrid dataGrid;
  private final CacheMapper mapper;

  public void cleanCalculationCache(String calculationId) {
    clearMap(RATES, calculationId);
    clearMap(RATES_FX, calculationId);
    clearMap(RATES_CREDIT, calculationId);
    clearMap(VOL_FX, calculationId);
    clearMap(VOLS, calculationId);
    clearMap(CAP_FLOOR, calculationId);
    clearMap(CURVES, calculationId);
    clearMap(CURVES_FX, calculationId);
    clearMap(BOND_CURVES, calculationId);
    clearMap(MD_FX_RATES, calculationId);
    clearMap(REF_DATA, calculationId);
  }

  private void clearMap(CalibrationCacheType type, String calculationId) {
    var map = dataGrid.getKeyValueCache(type.name());
    map.keySet().stream()
        .filter(String.class::isInstance)
        .map(String.class::cast)
        .filter(c -> c.startsWith(calculationId))
        .forEach(map::delete);
  }

  public void cacheResults(String calculationId, PortfolioCalculationData result) {
    cacheReferenceData(calculationId, result.getReferenceData());
    cacheNonFxCalibrationResults(calculationId, result.getCalibrationResult());
    result.fxCalibrationResult().ifPresent(r -> cacheFxCalibrationResults(calculationId, r));
  }

  private void cacheReferenceData(String calculationId, ImmutableReferenceData referenceData) {
    dataGrid.getKeyValueCache(REF_DATA.toString()).set(calculationId, referenceData.getValues());
  }

  private void cacheNonFxCalibrationResults(
      String calculationId, CalculationCurveGroupData result) {
    cacheRatesResults(calculationId, RATES, result.getRates());
    cacheBondCurves(calculationId, result.getBondCurves());
    cacheCreditRates(calculationId, result.getCreditRates());
    cacheCaplets(calculationId, result.getCaplets());
    cacheVolatilities(calculationId, result.getVolatilities());
    cacheFxVolatilities(calculationId, result.getFxOptionVols());
    cacheCurves(calculationId, CURVES, result.getCurves(), result.getCreditCurves());
    cacheFxRatesProvider(calculationId, result.getMarketData());
  }

  private void cacheFxCalibrationResults(String calculationId, CalculationCurveGroupData result) {
    cacheRatesResults(calculationId, RATES_FX, result.getRates());
    cacheFxVolatilities(calculationId, result.getFxOptionVols());
    cacheCurves(calculationId, CURVES_FX, result.getCurves(), result.getCreditCurves());
  }

  private void cacheBondCurves(
      String calculationId, Map<String, InterpolatedNodalCurve> bondCurves) {
    var cacheMap =
        bondCurves.entrySet().stream()
            .collect(toMap(v -> commonCacheKey(calculationId, v.getKey()), Entry::getValue));

    dataGrid
        .<String, InterpolatedNodalCurve>getKeyValueCache(BOND_CURVES.toString())
        .setAll(cacheMap);
  }

  private void cacheFxRatesProvider(String calculationId, MarketData marketData) {
    dataGrid
        .getKeyValueCache(MD_FX_RATES.toString())
        .set(calculationId, MarketDataFxRateProvider.of(marketData));
  }

  private void cacheCurves(
      String calculationId,
      CalibrationCacheType cacheType,
      List<Curve> curves,
      List<CreditCurve> creditCurves) {
    var mappedCurves = convertCollectionTo(curves, mapper::toCalibrationCurve);
    var mappedCreditCurves = convertCollectionTo(creditCurves, mapper::toCalibrationCurve);
    var joinedCurves = Iterables.concat(mappedCurves, mappedCreditCurves);
    var calibrationCurves = new CalibrationCurves();
    calibrationCurves.setCurves(ImmutableList.copyOf(joinedCurves));
    dataGrid.getKeyValueCache(cacheType.toString()).set(calculationId, calibrationCurves);
  }

  private void cacheRatesResults(
      String calculationId, CalibrationCacheType type, Map<String, RatesCalibrationResult> rates) {
    var cacheMap =
        rates.entrySet().stream()
            .collect(
                toMap(
                    k -> commonCacheKey(calculationId, k.getKey()),
                    e -> toValuationRates(e.getValue())));
    dataGrid.<String, ValuationCurveRates>getKeyValueCache(type.name()).setAll(cacheMap);
  }

  private ValuationCurveRates toValuationRates(RatesCalibrationResult result) {
    return new ValuationCurveRates(
        result.getRatesProvider(),
        result.getShiftedRatesProviders().stream()
            .map(
                r ->
                    new FxShiftedValuationCurveRates(
                        r.getCurrencyPair(),
                        r.getRatesProvider().getDiscountCurves(),
                        r.getRatesProvider().getIndexCurves(),
                        r.getRatesProvider().getFxRateProvider()))
            .toList());
  }

  private void cacheCreditRates(
      String calculationId, Map<String, CalculationCreditCalibrationResult> rates) {
    var cacheMap =
        rates.entrySet().stream()
            .collect(
                toMap(
                    k -> commonCacheKey(calculationId, k.getKey()),
                    e -> toCalibrationCreditRates(e.getValue())));
    dataGrid
        .<String, ValuationCreditRates>getKeyValueCache(CalibrationCacheType.RATES_CREDIT.name())
        .setAll(cacheMap);
  }

  private ValuationCreditRates toCalibrationCreditRates(CalculationCreditCalibrationResult result) {
    return new ValuationCreditRates(
        result.getCreditRates(),
        result.getFxShiftedCreditRates().stream()
            .map(r -> new FxShiftedValuationCreditCurveRates(r.getCurrencyPair(), r.getResult()))
            .toList());
  }

  private void cacheCaplets(
      String calculationId, Map<String, CapletFloorletVolatilityCalibrationResults> caplets) {
    var cacheMap =
        caplets.entrySet().stream()
            .map(k -> toVols(calculationId, k))
            .flatMap(Collection::stream)
            .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
    dataGrid
        .<String, ValuationCalibratedCaplets>getKeyValueCache(CalibrationCacheType.CAP_FLOOR.name())
        .setAll(cacheMap);
  }

  private List<Map.Entry<String, ValuationCalibratedCaplets>> toVols(
      String calculationId, Map.Entry<String, CapletFloorletVolatilityCalibrationResults> entry) {
    return toValuationCaplets(entry.getValue()).entrySet().stream()
        .map(v -> Map.entry(capletsKey(calculationId, entry.getKey(), v.getKey()), v.getValue()))
        .toList();
  }

  private Map<IborIndex, ValuationCalibratedCaplets> toValuationCaplets(
      CapletFloorletVolatilityCalibrationResults result) {
    return result.getResults().stream()
        .collect(
            Collectors.toMap(
                v -> v.getDefinition().getIndex(),
                v -> {
                  var shifted =
                      v.getFxShiftedResults().stream()
                          .map(
                              r ->
                                  new ValuationFxShiftedCaplets(
                                      r.getCurrencyPair(), r.getResult().getVolatilities()))
                          .toList();
                  return new ValuationCalibratedCaplets(v.getResult().getVolatilities(), shifted);
                }));
  }

  private void cacheFxVolatilities(
      String calculationId, Map<CurrencyPair, BlackFxOptionVolatilities> fxVols) {
    var cacheMap =
        fxVols.entrySet().stream()
            .collect(toMap(k -> currencyPairKey(calculationId, k.getKey()), Map.Entry::getValue));
    dataGrid
        .<String, BlackFxOptionVolatilities>getKeyValueCache(CalibrationCacheType.VOL_FX.name())
        .setAll(cacheMap);
  }

  private void cacheVolatilities(
      String calculationId, Map<RateIndex, VolatilitiesCalibrationData> vols) {
    var cacheMap =
        vols.entrySet().stream()
            .collect(
                toMap(
                    k -> rateIndexKey(calculationId, k.getKey()),
                    v -> mapper.fromData(v.getValue())));
    dataGrid
        .<String, SurfaceForCalculation>getKeyValueCache(CalibrationCacheType.VOLS.name())
        .setAll(cacheMap);
  }

  private String rateIndexKey(String calculationId, RateIndex index) {
    return commonCacheKey(calculationId, index.toString());
  }

  private String currencyPairKey(String calculationId, CurrencyPair currencyPair) {
    return commonCacheKey(calculationId, currencyPair.toString());
  }

  private String commonCacheKey(String calculationId, String identifier) {
    return String.format(COMMON_CACHE_TEMPLATE, calculationId, identifier);
  }

  private String capletsKey(String calculationId, String discounting, IborIndex index) {
    return String.format(CAPLET_CACHE_TEMPLATE, calculationId, discounting, index);
  }
}
