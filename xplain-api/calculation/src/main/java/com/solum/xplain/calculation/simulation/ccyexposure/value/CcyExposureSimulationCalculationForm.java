package com.solum.xplain.calculation.simulation.ccyexposure.value;

import com.solum.xplain.calculation.form.BaseCalculationForm;
import com.solum.xplain.calculation.simulation.ccyexposure.validation.ValidSimulationCalculationCurrencies;
import com.solum.xplain.calculation.simulation.daterange.validation.UniqueSimulationName;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.extensions.simulations.SimulationType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ValidSimulationCalculationCurrencies
public class CcyExposureSimulationCalculationForm extends BaseCalculationForm {

  @NotNull
  @Schema(description = "Curve date (market data date)", example = "2023-01-31")
  private LocalDate curveDate;

  @NotEmpty
  @UniqueSimulationName(type = SimulationType.CCY_EXPOSURE)
  @Schema(description = "Unique ccy exposure simulation name")
  private String name;

  @NotNull
  @Schema(description = "Valuation date", example = "2023-01-31")
  private LocalDate valuationDate;

  @NotNull @Valid ShiftForm spotShift;

  @NotNull @Valid ShiftForm volatilityShift;

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  private String baseCurrency;

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  private String counterCurrency;
}
