package com.solum.xplain.calculation.simulation.daterange.entity;

import com.solum.xplain.core.error.Error;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = DateRangeSimulationErrorItem.COLLECTION_NAME)
@NoArgsConstructor
@FieldNameConstants
public class DateRangeSimulationErrorItem {

  public static final String COLLECTION_NAME = "simulationErrorItem";

  @Id private String id;
  private LocalDate valuationDate;
  private String simulationId;
  private Error reason;
  private String description;

  public static DateRangeSimulationErrorItem ofCalculationError(
      String simulationId, LocalDate valuationDate, String description) {
    return new DateRangeSimulationErrorItem(
        simulationId, valuationDate, description, Error.CALCULATION_ERROR);
  }

  public static DateRangeSimulationErrorItem ofCalibrationError(
      String simulationId, LocalDate valuationDate, String description) {
    return new DateRangeSimulationErrorItem(
        simulationId, valuationDate, description, Error.CALIBRATION_ERROR);
  }

  public DateRangeSimulationErrorItem(
      String simulationId, LocalDate valuationDate, String description, Error reason) {
    this.simulationId = simulationId;
    this.description = description;
    this.valuationDate = valuationDate;
    this.reason = reason;
  }
}
