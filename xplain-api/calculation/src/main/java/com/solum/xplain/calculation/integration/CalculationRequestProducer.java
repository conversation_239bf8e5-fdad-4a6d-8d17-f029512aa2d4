package com.solum.xplain.calculation.integration;

import com.solum.xplain.calculation.value.TradeCalculationRequest;
import com.solum.xplain.core.config.properties.CalculationProperties;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@Profile("!test")
public class CalculationRequestProducer {
  private final KafkaTemplate<String, ValuationRequest> kafkaTemplate;
  private final TradeRequestMapper requestMapper;
  private final CalculationProperties calculationProperties;

  public CalculationRequestProducer(
      KafkaTemplate<String, ValuationRequest> kafkaTemplate,
      TradeRequestMapper requestMapper,
      CalculationProperties calculationProperties) {
    this.kafkaTemplate = kafkaTemplate;
    this.requestMapper = requestMapper;
    this.calculationProperties = calculationProperties;
  }

  public void sendRequest(TradeCalculationRequest request) {
    var valuationRequest = requestMapper.valuationRequest(request);
    kafkaTemplate.send(calculationProperties.getValuationsTopic(), valuationRequest);
  }
}
