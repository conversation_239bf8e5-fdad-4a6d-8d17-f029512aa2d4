package com.solum.xplain.calculation.pnlexplain.value;

public enum PnlExplainDeltaValueMetricType {
  DV01(PnlExplainTrade01MetricType.DV01),
  BR01(PnlExplainTrade01MetricType.BR01),
  CS01(PnlExplainTrade01MetricType.CS01),
  INF01(PnlExplainTrade01MetricType.INF01),
  SPOT01(null);

  private final PnlExplainTrade01MetricType metricType;

  PnlExplainDeltaValueMetricType(PnlExplainTrade01MetricType metricType) {
    this.metricType = metricType;
  }

  public PnlExplainTrade01MetricType metricType() {
    return this.metricType;
  }
}
