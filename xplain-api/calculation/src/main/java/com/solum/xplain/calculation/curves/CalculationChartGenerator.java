package com.solum.xplain.calculation.curves;

import static org.apache.commons.lang3.StringUtils.isNoneBlank;

import com.opengamma.strata.market.ValueType;
import com.solum.xplain.calculation.curves.value.CalculationConfigurationType;
import com.solum.xplain.calculation.curves.value.CalculationResultChartDataView;
import com.solum.xplain.calculation.curves.value.CalculationResultsChartsView;
import com.solum.xplain.calculation.repository.CalculationResultRepository;
import com.solum.xplain.calibration.curve.charts.ChartDateType;
import com.solum.xplain.calibration.curve.charts.ChartPointGenerator;
import com.solum.xplain.calibration.curve.charts.value.CurveChartGeneratorData;
import com.solum.xplain.calibration.curve.charts.value.CurveDetails;
import com.solum.xplain.calibration.rates.charts.CalibratedCurve;
import com.solum.xplain.core.curvegroup.curve.entity.CurvePoints;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CalculationChartGenerator {
  private final ChartPointGenerator chartPointGenerator;
  private final CalculationResultRepository repository;

  public CalculationResultsChartsView generateAllCharts(
      String calculationResultId, ChartDateType dateType) {
    var calculationCurves = repository.calculationCurves(calculationResultId);
    var chartData =
        generateCurveCharts(
            calculationCurves.getValuationDate(),
            calculationCurves.getCurveConfigurationCurves(),
            dateType);
    var chartsView = new CalculationResultsChartsView();
    chartsView.setCurveConfigurationChartData(chartData);

    if (CollectionUtils.isNotEmpty(calculationCurves.getFxCurveConfigurationCurves())) {
      chartsView.setFxCurveConfigurationChartDataView(
          generateCurveCharts(
              calculationCurves.getValuationDate(),
              calculationCurves.getFxCurveConfigurationCurves(),
              dateType));
    }
    return chartsView;
  }

  private List<CalculationResultChartDataView> generateCurveCharts(
      LocalDate valuationDate, List<CalibratedCurve> curves, ChartDateType dateType) {
    return curves.stream()
        .map(
            c -> {
              var points = generateCurvePoints(valuationDate, c, dateType);
              return new CalculationResultChartDataView(
                  c.getName(),
                  c.getDiscountingKey(),
                  c.isPrimary(),
                  c.getYValueType(),
                  points.getPoints());
            })
        .toList();
  }

  public Either<ErrorItem, List<CurvePoints>> generateSingleCurvePoints(
      String resultId,
      String curveName,
      CalculationConfigurationType configurationType,
      ChartDateType chartDateType) {
    var calculationCurves = repository.calculationCurves(resultId);
    var vd = calculationCurves.getValuationDate();
    return calculationCurves
        .curveData(configurationType, curveName)
        .map(cs -> cs.stream().map(c -> generateCurvePoints(vd, c, chartDateType)).toList());
  }

  private CurvePoints generateCurvePoints(
      @Nullable LocalDate valuationDate, CalibratedCurve curve, ChartDateType chartDateType) {
    if (!hasValidGeneratorData(curve) || valuationDate == null) {
      var yValueType = ValueType.of(curve.getYValueType());
      return CurvePoints.newOf(
          curve.getName(), curve.getDiscountingKey(), yValueType, curve.getChartPoints());
    }

    var generatorData = buildGeneratorData(valuationDate, curve);
    return chartPointGenerator.generateChartPoints(generatorData, chartDateType);
  }

  /*
   * Old data was not migrated so and interpolation/extrapolation is not supported.*/
  private boolean hasValidGeneratorData(CalibratedCurve curve) {
    return isNoneBlank(
        curve.getInterpolator(), curve.getExtrapolatorLeft(), curve.getExtrapolatorRight());
  }

  private CurveChartGeneratorData buildGeneratorData(
      LocalDate valuationDate, CalibratedCurve curve) {
    var curveDetails =
        new CurveDetails(
            curve.getName(),
            curve.getDiscountingKey(),
            ValueType.of(curve.getXValueType()),
            ValueType.of(curve.getYValueType()),
            curve.getInterpolator(),
            curve.getExtrapolatorLeft(),
            curve.getExtrapolatorRight());

    return new CurveChartGeneratorData(
        curveDetails,
        curve.getInflationAdjustmentType(),
        curve.getInflationSeasonalityAdjustment(),
        curve.getChartPoints(),
        valuationDate);
  }
}
