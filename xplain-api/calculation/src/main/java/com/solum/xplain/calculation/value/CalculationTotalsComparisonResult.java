package com.solum.xplain.calculation.value;

import static com.solum.xplain.core.utils.ComparisonUtils.safeDiv;
import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsLast;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.math.NumberUtils.DOUBLE_ZERO;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CalculationTotalsComparisonResult {
  private final String id;
  private final Integer tradesCount;
  private final Double metricsPresentValue;
  private final Double metricsPresentValueN1;
  private final Double metricsPVN1DiffPv;
  private final Double metricsAbsPVN1DiffPv;
  private Double metricsAbsDiffCoefficientN1;
  private List<CalculationTotalsComparisonResult> groupedValues;

  public CalculationTotalsComparisonResult(CalculationTotalsComparisonView view) {
    this.id = view.getId();
    this.metricsAbsDiffCoefficientN1 = null;
    this.tradesCount = view.getTradesCount();
    this.metricsPresentValue = view.getMetricsPresentValue();
    this.metricsPresentValueN1 = view.getMetricsPresentValueN1();
    this.metricsPVN1DiffPv = view.getMetricsPVN1DiffPv();
    this.metricsAbsPVN1DiffPv = view.getMetricsAbsPVN1DiffPv();
    if (view.getGroupedValues() != null) {
      this.groupedValues =
          Stream.of(view.getGroupedValues())
              .flatMap(Collection::stream)
              .map(CalculationTotalsComparisonResult::new)
              .toList();
    } else {
      this.groupedValues = null;
    }
  }

  public static CalculationTotalsComparisonResult fromView(CalculationTotalsComparisonView view) {
    return new CalculationTotalsComparisonResult(view);
  }

  public CalculationTotalsComparisonResult withDiff(Double totalMetricsAbsPresentValueDiffN1) {

    if (groupedValues == null) {
      return withDiff(totalMetricsAbsPresentValueDiffN1, null);
    }

    return groupedValues.stream()
        .map(n -> n.withDiff(totalMetricsAbsPresentValueDiffN1))
        .collect(
            collectingAndThen(toList(), list -> withDiff(totalMetricsAbsPresentValueDiffN1, list)));
  }

  private CalculationTotalsComparisonResult withDiff(
      Double totalMetricsAbsPresentValueDiffN1, List<CalculationTotalsComparisonResult> totals) {
    return new CalculationTotalsComparisonResult(
        id,
        tradesCount,
        metricsPresentValue,
        metricsPresentValueN1,
        metricsPVN1DiffPv,
        metricsAbsPVN1DiffPv,
        difference(metricsAbsPVN1DiffPv, totalMetricsAbsPresentValueDiffN1),
        totals);
  }

  public CalculationTotalsComparisonResult withTotals(
      List<CalculationTotalsComparisonResult> totals) {
    return new CalculationTotalsComparisonResult(
        id,
        tradesCount,
        metricsPresentValue,
        metricsPresentValueN1,
        metricsPVN1DiffPv,
        metricsAbsPVN1DiffPv,
        metricsAbsDiffCoefficientN1,
        totals);
  }

  // We need to sort, since mongodb can't sort inner documents
  public CalculationTotalsComparisonResult sorted() {
    if (groupedValues == null) {
      return this;
    }

    return groupedValues.stream()
        .map(CalculationTotalsComparisonResult::sorted)
        .sorted(comparing(CalculationTotalsComparisonResult::getId, nullsLast(naturalOrder())))
        .collect(collectingAndThen(toList(), this::withTotals));
  }

  private Double difference(Double part, Double total) {
    if (DOUBLE_ZERO.equals(total)) {
      return DOUBLE_ZERO;
    }
    return safeDiv(part, total);
  }
}
