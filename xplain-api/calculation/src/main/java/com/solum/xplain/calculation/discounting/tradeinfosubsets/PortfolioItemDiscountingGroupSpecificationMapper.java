package com.solum.xplain.calculation.discounting.tradeinfosubsets;

import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.PortfolioItem;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Mapper for creating {@link PortfolioItemDiscountingGroupSpecification} instances from {@link
 * GenericPortfolioItemDiscountingCriteria} or {@link PortfolioItem} instances.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PortfolioItemDiscountingGroupSpecificationMapper {

  public static PortfolioItemDiscountingGroupSpecification fromGenericPortfolioItemCriteria(
      GenericPortfolioItemDiscountingCriteria criteria) {
    return switch ((CoreProductType) criteria.getProductType()) {
      case CAP_FLOOR -> capfloor(criteria);
      case CDS -> cds(criteria);
      case CREDIT_INDEX -> cdx(criteria);
      case CREDIT_INDEX_TRANCHE -> cdxTranche(criteria);
      case FXFWD -> fxForward(criteria);
      case FXOPT -> fxOption(criteria);
      case FXSWAP -> fxForward(criteria); // NOTE: same as FxFwd
      case FXCOLLAR -> fxOption(criteria); // NOTE: same as FxOption
      case INFLATION -> inflation(criteria);
      case IRS -> irs(criteria);
      case SWAPTION -> swaption(criteria);
      case XCCY -> xccy(criteria);
      case FRA -> fra(criteria);
      case LOAN_NOTE -> // This method is currently not called for LOAN_NOTE
          throw new IllegalStateException("Unexpected product type: " + criteria.getProductType());
    };
  }

  public static PortfolioItemDiscountingGroupSpecification fromPortfolioItem(
      PortfolioItem portfolioItem) {
    return switch ((CoreProductType) portfolioItem.getProductType()) {
      case CAP_FLOOR -> capfloor(portfolioItem);
      case CDS -> cds(portfolioItem);
      case CREDIT_INDEX -> cdx(portfolioItem);
      case CREDIT_INDEX_TRANCHE -> cdxTranche(portfolioItem);
      case FXFWD -> fxForward(portfolioItem);
      case FXOPT -> fxOption(portfolioItem);
      case FXSWAP -> fxForward(portfolioItem); // NOTE: same as FxFwd
      case FXCOLLAR -> fxOption(portfolioItem);
      case INFLATION -> inflation(portfolioItem);
      case IRS -> irs(portfolioItem);
      case SWAPTION -> swaption(portfolioItem);
      case XCCY -> xccy(portfolioItem);
      case FRA -> fra(portfolioItem);
      case LOAN_NOTE -> // This method is currently not called for LOAN_NOTE
          throw new IllegalStateException(
              "Unexpected product type: " + portfolioItem.getProductType());
    };
  }

  private static CapfloorPortfolioItemDiscountingGroupSpecification capfloor(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    var payLegIsPositionLeg = genericPortfolioItemDiscountingCriteria.getPayLegIndex() != null;

    if (payLegIsPositionLeg) {
      return new CapfloorPortfolioItemDiscountingGroupSpecification(
          genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
          genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
          genericPortfolioItemDiscountingCriteria.getPayLegType(),
          genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
          genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
    }

    return new CapfloorPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static CapfloorPortfolioItemDiscountingGroupSpecification capfloor(
      PortfolioItem portfolioItem) {
    var positionLeg = portfolioItem.getTradeDetails().tradePositionLeg().orElseThrow();

    return new CapfloorPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        positionLeg.getIndex(),
        positionLeg.getType(),
        orFalse(positionLeg.getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static CdsPortfolioItemDiscountingGroupSpecification cds(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new CdsPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getCreditReference(),
        genericPortfolioItemDiscountingCriteria.getCdsSeniority(),
        genericPortfolioItemDiscountingCriteria.getCreditDocClause(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static CdsPortfolioItemDiscountingGroupSpecification cds(PortfolioItem portfolioItem) {
    return new CdsPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getReference(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getSeniority(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getDocClause(),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static CdxPortfolioItemDiscountingGroupSpecification cdx(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new CdxPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getCreditReference(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static CdxPortfolioItemDiscountingGroupSpecification cdx(PortfolioItem portfolioItem) {
    return new CdxPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getReference(),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static CdxTranchePortfolioItemDiscountingGroupSpecification cdxTranche(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new CdxTranchePortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getCreditReference(),
        genericPortfolioItemDiscountingCriteria.getCreditIndexTranche(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static CdxTranchePortfolioItemDiscountingGroupSpecification cdxTranche(
      PortfolioItem portfolioItem) {
    return new CdxTranchePortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getReference(),
        portfolioItem.getTradeDetails().getCreditTradeDetails().getCreditIndexTranche(),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static FxFwdPortfolioItemDiscountingGroupSpecification fxForward(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new FxFwdPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegCurrency(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegCurrency(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static FxFwdPortfolioItemDiscountingGroupSpecification fxForward(
      PortfolioItem portfolioItem) {
    return new FxFwdPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getCurrency(),
        portfolioItem.getTradeDetails().getReceiveLeg().getCurrency(),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static FxOptPortfolioItemDiscountingGroupSpecification fxOption(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new FxOptPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegCurrency(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegCurrency(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static FxOptPortfolioItemDiscountingGroupSpecification fxOption(
      PortfolioItem portfolioItem) {
    return new FxOptPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getCurrency(),
        portfolioItem.getTradeDetails().getReceiveLeg().getCurrency(),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static InflationPortfolioItemDiscountingGroupSpecification inflation(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new InflationPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
        genericPortfolioItemDiscountingCriteria.getPayLegType(),
        genericPortfolioItemDiscountingCriteria.getPayLegSpread(),
        genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegSpread(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup(),
        genericPortfolioItemDiscountingCriteria.getCounterpartyType());
  }

  private static InflationPortfolioItemDiscountingGroupSpecification inflation(
      PortfolioItem portfolioItem) {
    return new InflationPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getIndex(),
        portfolioItem.getTradeDetails().getPayLeg().getType(),
        portfolioItem.getTradeDetails().getPayLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getPayLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getReceiveLeg().getIndex(),
        portfolioItem.getTradeDetails().getReceiveLeg().getType(),
        portfolioItem.getTradeDetails().getReceiveLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getReceiveLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup(),
        portfolioItem.getTradeDetails().getInfo().getCounterPartyType());
  }

  private static IrsPortfolioItemDiscountingGroupSpecification irs(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new IrsPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
        genericPortfolioItemDiscountingCriteria.getPayLegType(),
        genericPortfolioItemDiscountingCriteria.getPayLegSpread(),
        genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegSpread(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static IrsPortfolioItemDiscountingGroupSpecification irs(PortfolioItem portfolioItem) {
    return new IrsPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getIndex(),
        portfolioItem.getTradeDetails().getPayLeg().getType(),
        portfolioItem.getTradeDetails().getPayLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getPayLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getReceiveLeg().getIndex(),
        portfolioItem.getTradeDetails().getReceiveLeg().getType(),
        portfolioItem.getTradeDetails().getReceiveLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getReceiveLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static SwaptionPortfolioItemDiscountingGroupSpecification swaption(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new SwaptionPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
        genericPortfolioItemDiscountingCriteria.getPayLegType(),
        genericPortfolioItemDiscountingCriteria.getPayLegSpread(),
        genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegSpread(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static SwaptionPortfolioItemDiscountingGroupSpecification swaption(
      PortfolioItem portfolioItem) {
    return new SwaptionPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getIndex(),
        portfolioItem.getTradeDetails().getPayLeg().getType(),
        portfolioItem.getTradeDetails().getPayLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getPayLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getReceiveLeg().getIndex(),
        portfolioItem.getTradeDetails().getReceiveLeg().getType(),
        portfolioItem.getTradeDetails().getReceiveLeg().getInitialValue(),
        orFalse(portfolioItem.getTradeDetails().getReceiveLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static XccyPortfolioItemDiscountingGroupSpecification xccy(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new XccyPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
        genericPortfolioItemDiscountingCriteria.getPayLegType(),
        genericPortfolioItemDiscountingCriteria.getPayLegSpread(),
        genericPortfolioItemDiscountingCriteria.getPayLegCurrency(),
        genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegSpread(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegCurrency(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static XccyPortfolioItemDiscountingGroupSpecification xccy(PortfolioItem portfolioItem) {
    return new XccyPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getIndex(),
        portfolioItem.getTradeDetails().getPayLeg().getType(),
        portfolioItem.getTradeDetails().getPayLeg().getInitialValue(),
        portfolioItem.getTradeDetails().getPayLeg().getCurrency(),
        orFalse(portfolioItem.getTradeDetails().getPayLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getReceiveLeg().getIndex(),
        portfolioItem.getTradeDetails().getReceiveLeg().getType(),
        portfolioItem.getTradeDetails().getReceiveLeg().getInitialValue(),
        portfolioItem.getTradeDetails().getReceiveLeg().getCurrency(),
        orFalse(portfolioItem.getTradeDetails().getReceiveLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static FraPortfolioItemDiscountingGroupSpecification fra(
      GenericPortfolioItemDiscountingCriteria genericPortfolioItemDiscountingCriteria) {
    return new FraPortfolioItemDiscountingGroupSpecification(
        genericPortfolioItemDiscountingCriteria.getTradeCurrency(),
        genericPortfolioItemDiscountingCriteria.getPayLegIndex(),
        genericPortfolioItemDiscountingCriteria.getPayLegType(),
        genericPortfolioItemDiscountingCriteria.isPayLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegIndex(),
        genericPortfolioItemDiscountingCriteria.getReceiveLegType(),
        genericPortfolioItemDiscountingCriteria.isReceiveLegIsOffshore(),
        genericPortfolioItemDiscountingCriteria.getCsaDiscountingGroup());
  }

  private static FraPortfolioItemDiscountingGroupSpecification fra(PortfolioItem portfolioItem) {
    return new FraPortfolioItemDiscountingGroupSpecification(
        portfolioItem.getTradeDetails().getInfo().getTradeCurrency(),
        portfolioItem.getTradeDetails().getPayLeg().getIndex(),
        portfolioItem.getTradeDetails().getPayLeg().getType(),
        orFalse(portfolioItem.getTradeDetails().getPayLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getReceiveLeg().getIndex(),
        portfolioItem.getTradeDetails().getReceiveLeg().getType(),
        orFalse(portfolioItem.getTradeDetails().getReceiveLeg().getIsOffshore()),
        portfolioItem.getTradeDetails().getInfo().getCsaDiscountingGroup());
  }

  private static boolean orFalse(Boolean value) {
    return value != null && value;
  }
}
