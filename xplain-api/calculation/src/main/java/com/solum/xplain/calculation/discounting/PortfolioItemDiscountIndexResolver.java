package com.solum.xplain.calculation.discounting;

import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecification;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.calibration.discounting.OisConfigurations;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemIndexResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import io.atlassian.fugue.Either;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@AllArgsConstructor
@EqualsAndHashCode
public class PortfolioItemDiscountIndexResolver
    implements DiscountableItemIndexResolver<PortfolioItemDiscountingGroupSpecification> {
  private final ProductSettingsResolver productSettingsResolver;
  private final DiscountingIndexResolver discountingIndexResolver;

  public static PortfolioItemDiscountIndexResolver fromDiscountSettings(
      OisConfigurations oisConfigurations, ProductSettingsResolver productSettingsResolver) {
    return new PortfolioItemDiscountIndexResolver(
        productSettingsResolver, new DiscountingIndexResolver(oisConfigurations));
  }

  @Override
  public Either<ErrorItem, FloatingRateIndex> resolveIndex(
      DiscountableItem<PortfolioItemDiscountingGroupSpecification> discountableItem,
      Set<FloatingRateIndex> availableIndices) {
    var item = discountableItem.originalItem();
    return item.resolveIndex(availableIndices, productSettingsResolver, discountingIndexResolver);
  }
}
