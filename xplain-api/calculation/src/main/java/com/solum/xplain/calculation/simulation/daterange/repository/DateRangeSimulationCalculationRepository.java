package com.solum.xplain.calculation.simulation.daterange.repository;

import static com.solum.xplain.core.portfolio.Portfolio.PORTFOLIO_COLLECTION;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationCalculation;
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem;
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationView;
import com.solum.xplain.calculation.simulation.entity.GenericSimulationCalculation;
import com.solum.xplain.calculation.simulation.repository.GenericSimulationCalculationRepository;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.util.List;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.mongodb.core.BulkOperations.BulkMode;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Repository;

@Repository
public class DateRangeSimulationCalculationRepository
    extends GenericSimulationCalculationRepository<DateRangeSimulationCalculation> {

  public DateRangeSimulationCalculationRepository(
      MongoOperations mongoOperations, ConversionService conversionService) {
    super(mongoOperations, conversionService);
  }

  public void saveSimulationErrors(String simulationId, List<DateRangeSimulationErrorItem> errors) {
    if (errors.isEmpty()) {
      return;
    }
    incrementFailedCount(simulationId, errors.size());
    mongoOperations
        .bulkOps(BulkMode.UNORDERED, DateRangeSimulationErrorItem.class)
        .insert(errors)
        .execute();
  }

  public ScrollableEntry<DateRangeSimulationCalculationView> simulationsScrollable(
      ScrollRequest scrollRequest, TableFilter tableFilter, EntityTeamFilter portfolioFilter) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                portfolioLookup(),
                unwind(PORTFOLIO_COLLECTION),
                match(portfolioFilter.criteria(PORTFOLIO_COLLECTION)),
                match(
                    tableFilter.criteria(
                        DateRangeSimulationCalculationView.class, conversionService)));
    operations.addAll(
        new ScrollSortOperations(scrollRequest, GenericSimulationCalculation.Fields.id)
            .withDefaultSort(DEFAULT_SIMULATION_RESULT_SORT)
            .withLimitPlusOne()
            .build());
    var entries =
        mongoOperations
            .aggregateAndReturn(DateRangeSimulationCalculationView.class)
            .by(
                Aggregation.newAggregation(
                    DateRangeSimulationCalculation.class, operations.build()))
            .all()
            .getMappedResults();
    return ScrollableEntry.limitByPageSize(entries, scrollRequest);
  }
}
