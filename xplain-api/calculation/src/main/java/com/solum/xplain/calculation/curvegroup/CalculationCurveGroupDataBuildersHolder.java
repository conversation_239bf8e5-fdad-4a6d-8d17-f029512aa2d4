package com.solum.xplain.calculation.curvegroup;

import com.opengamma.strata.data.MarketData;
import com.solum.xplain.calculation.value.CalculationCalibrationSettings;
import com.solum.xplain.calculation.value.CalculationConfigurationData;
import com.solum.xplain.core.error.ErrorItem;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.function.UnaryOperator;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;

@AllArgsConstructor
public class CalculationCurveGroupDataBuildersHolder {
  private final CalculationCurveGroupDataBuilder curveGroupDataBuilder;
  private final CalculationCurveGroupDataBuilder fxCurveGroupDataBuilder;
  private final CalculationCalibrationSettings settings;
  private final Set<ErrorItem> warnings = new CopyOnWriteArraySet<>();

  public List<CalculationCurveGroupDataBuilder> builders() {
    if (fxCurveGroupDataBuilder != null) {
      return List.of(curveGroupDataBuilder, fxCurveGroupDataBuilder);
    } else {
      return List.of(curveGroupDataBuilder);
    }
  }

  public CalculationCurveGroupData calculate(
      MarketData marketData, UnaryOperator<ErrorItem> errorConsumer) {
    return curveGroupDataBuilder.calculate(settings, marketData, errorConsumer);
  }

  public CalculationCurveGroupData calculateFx(
      @Nullable MarketData marketData, UnaryOperator<ErrorItem> errorConsumer) {
    if (fxCurveGroupDataBuilder == null) {
      return null;
    }
    return fxCurveGroupDataBuilder.calculate(settings, marketData, errorConsumer);
  }

  public CalculationConfigurationData instrumentResolver() {
    return curveGroupDataBuilder.calculationConfigurationData();
  }

  public CalculationConfigurationData fxInstrumentResolver() {
    return fxCurveGroupDataBuilder == null
        ? null
        : fxCurveGroupDataBuilder.calculationConfigurationData();
  }

  public List<String> discountCurves() {
    return curveGroupDataBuilder.discountCurves();
  }

  public List<String> fxDiscountCurves() {
    return fxCurveGroupDataBuilder == null ? List.of() : fxCurveGroupDataBuilder.discountCurves();
  }

  public List<ErrorItem> warnings() {
    return List.copyOf(warnings);
  }

  public void addWarnings(List<ErrorItem> errorItems) {
    warnings.addAll(errorItems);
  }

  public ErrorItem addWarning(ErrorItem errorItem) {
    warnings.add(errorItem);
    return errorItem;
  }
}
