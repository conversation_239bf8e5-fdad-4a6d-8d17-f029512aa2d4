package com.solum.xplain.calculation.simulation.daterange.entity;

import static com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationCalculation.AUDIT_COLLECTION;

import com.solum.xplain.calculation.simulation.entity.GenericSimulationCalculation;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@FieldNameConstants
@Document(collection = AUDIT_COLLECTION)
public class DateRangeSimulationCalculation extends GenericSimulationCalculation {
  public static final String AUDIT_COLLECTION = "simulationCalculation";
  private LocalDate valuationStartDate;
  private LocalDate valuationEndDate;
}
