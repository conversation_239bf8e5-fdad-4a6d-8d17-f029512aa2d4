package com.solum.xplain.calculation.discounting.tradeinfosubsets;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import io.atlassian.fugue.Either;
import java.util.Set;

/**
 * Interface for resolving currency and index information based on a group of portfolio items, as
 * well as holding other relevant data for discounting purposes.
 */
public interface PortfolioItemDiscountingGroupSpecification {

  CoreProductType productType();

  ClearingHouse clearingHouse();

  boolean isOffshore();

  String getCsaDiscountingGroup();

  Currency resolveCurrency(ProductSettingsResolver settingsResolver);

  Currency tradeCurrency();

  CurrencyPair currencyPair();

  Either<ErrorItem, FloatingRateIndex> resolveIndex(
      Set<FloatingRateIndex> availableIndices,
      ProductSettingsResolver productSettingsResolver,
      DiscountingIndexResolver discountingIndexResolver);

  Set<FloatingRateIndex> tradeRateIndices();
}
