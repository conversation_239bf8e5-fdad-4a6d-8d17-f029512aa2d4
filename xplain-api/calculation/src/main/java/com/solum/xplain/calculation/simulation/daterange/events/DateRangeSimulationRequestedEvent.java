package com.solum.xplain.calculation.simulation.daterange.events;

import com.solum.xplain.calculation.simulation.daterange.data.DateRangeSimulationCalculationData;
import com.solum.xplain.calculation.simulation.events.SimulationRequestedEvent;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import lombok.Getter;

@Getter
public class DateRangeSimulationRequestedEvent extends SimulationRequestedEvent {

  private final LocalDate valuationStartDate;
  private final LocalDate valuationEndDate;

  public DateRangeSimulationRequestedEvent(
      String name,
      LocalDate valuationStartDate,
      LocalDate valuationEndDate,
      BitemporalDate stateDate,
      DateRangeSimulationCalculationData simulationData,
      AuditUser currentUser) {
    super(name, stateDate, simulationData, currentUser);
    this.valuationStartDate = valuationStartDate;
    this.valuationEndDate = valuationEndDate;
  }

  @Override
  public DateRangeSimulationCalculationData getSimulationData() {
    return (DateRangeSimulationCalculationData) this.simulationData;
  }
}
