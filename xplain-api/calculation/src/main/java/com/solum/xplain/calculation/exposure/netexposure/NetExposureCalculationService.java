package com.solum.xplain.calculation.exposure.netexposure;

import com.solum.xplain.calculation.exposure.CcyExposureCalculationOptions;
import com.solum.xplain.calculation.exposure.netexposure.value.CcyExposureCalculatedCashFlow;
import com.solum.xplain.calculation.exposure.schedule.CombinedCashflowSchedule;
import com.solum.xplain.calculation.exposure.schedule.CombinedCashflowSchedule.CombinedCashflowBucket;
import com.solum.xplain.calculation.value.CalculationResultTotalsForm;
import com.solum.xplain.core.ccyexposure.CcyExposureControllerService;
import com.solum.xplain.core.ccyexposure.value.CcyExposureWithCashflows;
import com.solum.xplain.core.ccyexposure.value.HasCashflow;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class NetExposureCalculationService {

  private NetExposureCalculationDataService netExposureCalculationDataService;
  private CcyExposureControllerService ccyExposureControllerService;

  public List<NetExposureBucketCalculationMetrics> calculate(
      CcyExposureCalculationOptions options,
      TableFilter tableFilter,
      CalculationResultTotalsForm totalsForm) {

    var externalCcyExposureWithCashflows = fetchExternalCcyExposures(options);

    CombinedCashflowSchedule combinedCashflowSchedule =
        netExposureCalculationDataService.generateCombinedCashflowSchedule(
            externalCcyExposureWithCashflows, options, tableFilter, totalsForm);

    return combinedCashflowSchedule.buckets().stream()
        .map(
            bucket -> {
              LocalDate scheduleDate = bucket.scheduleDate();
              Double exposure = calculateExposure(bucket.externalCashflows());
              Double forwardsExposure = calculateExposure(bucket.forwards());
              Double optionExposure = calculateExposure(bucket.options());
              Double netExposure = exposure + forwardsExposure + optionExposure;
              Double hedgeRatio =
                  exposure != 0.0 ? Math.abs((forwardsExposure + optionExposure) / exposure) : 0.0;
              Double deltaOptions = calculateExposure(bucket.deltaOptions());
              Double deltaNetExposure = exposure + forwardsExposure + deltaOptions;
              Double deltaHedgeRatio =
                  exposure != 0.0 ? Math.abs((forwardsExposure + deltaOptions) / exposure) : 0.0;
              Double presentValue = calculatePresentValue(bucket);

              var results =
                  NetExposureBucketCalculationMetrics.builder()
                      .scheduleDate(scheduleDate)
                      .exposure(exposure)
                      .forwardsExposure(forwardsExposure)
                      .optionExposure(optionExposure)
                      .netExposure(netExposure)
                      .hedgeRatio(hedgeRatio)
                      .deltaOptions(deltaOptions)
                      .deltaNetExposure(deltaNetExposure)
                      .deltaHedgeRatio(deltaHedgeRatio)
                      .presentValue(presentValue)
                      .build();
              return results;
            })
        .toList();
  }

  private Double calculateExposure(List<HasCashflow> cashflows) {
    if (cashflows.isEmpty()) {
      return 0.0;
    }
    return cashflows.stream().mapToDouble(HasCashflow::getAmount).sum();
  }

  private Double calculatePresentValue(CombinedCashflowBucket combinedCashflowBucket) {
    return Stream.concat(
            combinedCashflowBucket.forwards().stream(), combinedCashflowBucket.options().stream())
        .map(
            c ->
                c instanceof CcyExposureCalculatedCashFlow calculatedCashFlow
                    ? calculatedCashFlow.getPresentValue()
                    : 0.0d)
        .reduce(Double::sum)
        .orElse(0.0d);
  }

  private List<CcyExposureWithCashflows> fetchExternalCcyExposures(
      CcyExposureCalculationOptions options) {
    return ccyExposureControllerService.ccyExposuresWithCashflows(
        options.getExposureCurrency(), options.getStateDate(), options.getExposureIds());
  }
}
