package com.solum.xplain.calculation.pnlexplain.entity;

import com.solum.xplain.calculation.pnlexplain.value.result.PortfolioItemCalculatedPnlExplainResult;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@FieldNameConstants
@AllArgsConstructor
@Document
public class PnlExplainCalculationResult {

  private String pnlExplainCalculationId;
  private LocalDateTime recordDate;
  private LocalDate valuationDate;
  private LocalDate secondValuationDate;

  private List<PortfolioItemCalculatedPnlExplainResult> tradePnlExplainResults;
}
