package com.solum.xplain.calculation.simulation.ccyexposure.events;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.calculation.simulation.ccyexposure.data.CcyExposureSimulationCalculationData;
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftForm;
import com.solum.xplain.calculation.simulation.events.SimulationRequestedEvent;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import lombok.Getter;

@Getter
public class CcyExposureSimulationRequestedEvent extends SimulationRequestedEvent {

  private final LocalDate valuationDate;
  private final ShiftForm spotShift;
  private final ShiftForm volatilityShift;
  private final CurrencyPair currencyPair;

  public CcyExposureSimulationRequestedEvent(
      String name,
      LocalDate valuationDate,
      ShiftForm spotShift,
      ShiftForm volatilityShift,
      CurrencyPair currencyPair,
      BitemporalDate stateDate,
      CcyExposureSimulationCalculationData simulationData,
      AuditUser currentUser) {
    super(name, stateDate, simulationData, currentUser);
    this.valuationDate = valuationDate;
    this.spotShift = spotShift;
    this.volatilityShift = volatilityShift;
    this.currencyPair = currencyPair;
  }

  @Override
  public CcyExposureSimulationCalculationData getSimulationData() {
    return (CcyExposureSimulationCalculationData) this.simulationData;
  }
}
