package com.solum.xplain.calculation.simulation.data;

import static io.atlassian.fugue.extensions.step.Steps.begin;

import com.opengamma.strata.basics.ImmutableReferenceData;
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuildersHolder;
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataService;
import com.solum.xplain.calculation.form.CalculationForm;
import com.solum.xplain.calculation.market.CalculationMarketData;
import com.solum.xplain.calculation.market.CalculationMarketDataService;
import com.solum.xplain.calculation.simulation.ccyexposure.data.CcyExposureSimulationCalculationData;
import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureSimulationCalculationForm;
import com.solum.xplain.calculation.simulation.daterange.data.DateRangeSimulationCalculationData;
import com.solum.xplain.calculation.simulation.daterange.value.DateRangeSimulationCalculationForm;
import com.solum.xplain.calculation.trades.CalculationTrades;
import com.solum.xplain.calculation.trades.CalculationTradesFactory;
import com.solum.xplain.calculation.value.CalculationOptions;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.refdata.VersionedReferenceData;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class SimulationDataFactory {

  private final CalculationMarketDataService marketDataService;
  private final AuditEntryService auditEntryService;
  private final CalculationCurveGroupDataService curvesDataService;
  private final CalculationTradesFactory calculationTradesFactory;
  private final VersionedReferenceData versionedReferenceData;

  public Either<ErrorItem, DateRangeSimulationCalculationData> simulationCalculationData(
      PortfolioCondensedView portfolio,
      LocalDate valuationDate,
      LocalDate curveDate,
      BitemporalDate stateDate,
      DateRangeSimulationCalculationForm form) {
    var optionsFn = dateRangeCalculationOptions(curveDate, stateDate, form);
    ImmutableReferenceData refData = versionedReferenceData.asAt(stateDate.getRecordDate());
    return begin(calculationTradesFactory.calculationTrades(portfolio.getId(), form, stateDate))
        .then(
            trades ->
                curvesDataService.resolveCalculationData(form, trades, valuationDate, stateDate))
        .then(
            (calculationTrades, curveGroupDataBuildersHolder) ->
                marketDataService.marketData(
                    form, curveDate, valuationDate, stateDate, curveGroupDataBuildersHolder))
        .yield(
            (trades, curves, md) ->
                dateRangeMapper(curves, md, optionsFn, portfolio, trades, refData));
  }

  public Either<ErrorItem, CcyExposureSimulationCalculationData> simulationCalculationData(
      PortfolioCondensedView portfolio,
      LocalDate valuationDate,
      LocalDate curveDate,
      BitemporalDate stateDate,
      CcyExposureSimulationCalculationForm form) {
    var optionsFn = ccyExposureCalculationOptions(curveDate, valuationDate, stateDate, form);
    ImmutableReferenceData refData = versionedReferenceData.asAt(stateDate.getRecordDate());
    return begin(calculationTradesFactory.calculationTrades(portfolio.getId(), form, stateDate))
        .then(
            trades ->
                curvesDataService.resolveCalculationData(form, trades, valuationDate, stateDate))
        .then(
            (calculationTrades, curveGroupDataBuildersHolder) ->
                marketDataService.marketData(
                    form, curveDate, valuationDate, stateDate, curveGroupDataBuildersHolder))
        .yield(
            (trades, curves, md) ->
                ccyExposureMapper(curves, md, optionsFn, portfolio, trades, refData));
  }

  private DateRangeSimulationCalculationData dateRangeMapper(
      CalculationCurveGroupDataBuildersHolder calculationCurveDataHolder,
      CalculationMarketData marketData,
      Function<LocalDate, CalculationOptions> optionsProvider,
      PortfolioCondensedView portfolio,
      CalculationTrades trades,
      ImmutableReferenceData refData) {
    var simulationData =
        new DateRangeSimulationCalculationData(
            calculationCurveDataHolder, marketData, portfolio, optionsProvider, trades, refData);
    logErrors(simulationData);

    return simulationData;
  }

  private CcyExposureSimulationCalculationData ccyExposureMapper(
      CalculationCurveGroupDataBuildersHolder calculationCurveDataHolder,
      CalculationMarketData marketData,
      CalculationOptions options,
      PortfolioCondensedView portfolio,
      CalculationTrades trades,
      ImmutableReferenceData refData) {
    var simulationData =
        new CcyExposureSimulationCalculationData(
            calculationCurveDataHolder, marketData, portfolio, options, trades, refData);
    logErrors(simulationData);

    return simulationData;
  }

  private CalculationOptions ccyExposureCalculationOptions(
      LocalDate curveDate,
      LocalDate valuationDate,
      BitemporalDate stateDate,
      CalculationForm form) {
    return CalculationOptions.newOf(
        valuationDate,
        curveDate,
        stateDate,
        form.reportingCurrency(),
        form.calculationType(),
        form.calculationDiscounting(),
        form.priceRequirements());
  }

  private Function<LocalDate, CalculationOptions> dateRangeCalculationOptions(
      LocalDate curveDate, BitemporalDate stateDate, CalculationForm form) {
    return vd ->
        CalculationOptions.newOf(
            vd,
            curveDate,
            stateDate,
            form.reportingCurrency(),
            form.calculationType(),
            form.calculationDiscounting(),
            form.priceRequirements());
  }

  private void logErrors(SimulationCalculationData data) {
    String simulationName =
        data instanceof DateRangeSimulationCalculationData ? "date-range" : "ccy exposure";

    var auditEntry =
        AuditEntry.of(
            data.auditCollectionName(),
            String.format(
                "Portfolio %s %s simulation started",
                data.getPortfolio().getExternalPortfolioId(), simulationName),
            data.getSimulationId().toString());
    auditEntryService.newEntry(auditEntry);
  }
}
