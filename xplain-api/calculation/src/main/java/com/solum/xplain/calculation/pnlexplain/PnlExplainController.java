package com.solum.xplain.calculation.pnlexplain;

import static com.solum.xplain.calculation.pnlexplain.permissions.PnlExplainAuthorities.AUTHORITY_RUN_PNL_EXPLAIN_CALCULATION;
import static com.solum.xplain.calculation.pnlexplain.permissions.PnlExplainAuthorities.AUTHORITY_VIEW_PNL_EXPLAIN_CALCULATION_RESULTS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.RequireLock.Type.PATH_VARIABLE;
import static com.solum.xplain.core.lock.XplainLock.VALUATION_LOCK_ID;

import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculation;
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculationResult;
import com.solum.xplain.calculation.pnlexplain.value.PnlExplainCalculationForm;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.lock.RequireLock;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/portfolio")
@AllArgsConstructor
@PnlExplainEnabled
public class PnlExplainController {

  private final PnlExplainControllerService service;

  @Operation(summary = "Get PnL Explain Calculations")
  @GetMapping("/pnl")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_PNL_EXPLAIN_CALCULATION_RESULTS)
  public List<PnlExplainCalculation> getCalculations() {
    return service.getPnlExplainCalculations();
  }

  @Operation(summary = "Perform PnL Explain Calculations")
  @PostMapping("/{portfolioId}/pnl")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_PNL_EXPLAIN_CALCULATION)
  @RequireLock(type = PATH_VARIABLE, name = "portfolioId", prefix = VALUATION_LOCK_ID)
  public ResponseEntity<EntityId> calculate(
      @PathVariable("portfolioId") String id,
      @RequestBody @Valid PnlExplainCalculationForm form,
      Authentication user) {
    return eitherErrorItemResponse(
        service.calculateResults(user, id, form, EntityId.entityId(ObjectId.get())));
  }

  @Operation(summary = "Get PnL Explain Calculation by Id")
  @GetMapping("/pnl/{pnlExplainCalculationId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_PNL_EXPLAIN_CALCULATION_RESULTS)
  public PnlExplainCalculation getPnlExplainCalculation(
      @PathVariable("pnlExplainCalculationId") String pnlExplainCalculationId) {
    return service.getPnlExplainCalculation(pnlExplainCalculationId);
  }

  @Operation(summary = "Delete PnL Explain Calculation")
  @DeleteMapping("/pnl/{pnlExplainCalculationId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_RUN_PNL_EXPLAIN_CALCULATION)
  public ResponseEntity<EntityId> deletePnlExplainCalculation(
      @PathVariable("pnlExplainCalculationId") String pnlExplainCalculationId) {
    return eitherErrorItemResponse(service.deletePnlExplainCalculation(pnlExplainCalculationId));
  }

  @Operation(summary = "Get PnL Calculation Result for PnL Explain Calculation")
  @GetMapping("/pnl/{pnlExplainCalculationId}/result")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_PNL_EXPLAIN_CALCULATION_RESULTS)
  public ResponseEntity<PnlExplainCalculationResult> getPnlExplainCalculationResults(
      @PathVariable("pnlExplainCalculationId") String pnlExplainCalculationId) {
    return ResponseEntity.ok(service.getResultForPnlCalculation(pnlExplainCalculationId));
  }

  @Operation(summary = "Get PnL Explain Calculations for Portfolio")
  @GetMapping("/{portfolioId}/pnl")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_PNL_EXPLAIN_CALCULATION_RESULTS)
  public List<PnlExplainCalculation> getPnlExplainCalculationsForPortfolio(
      @PathVariable("portfolioId") String portfolioId) {
    return service.getPnlExplainCalculationsForPortfolio(portfolioId);
  }
}
