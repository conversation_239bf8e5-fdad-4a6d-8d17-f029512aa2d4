package com.solum.xplain.calculation.pnlexplain.calculation;

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;
import static java.math.MathContext.DECIMAL64;
import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.calculation.pnlexplain.value.result.PnlExplainCarryResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlPortfolioItemCalculationResult;
import com.solum.xplain.calculation.pnlexplain.value.result.PnlTradeValuationResults;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;

/**
 * Performs the calculation of the "carry" component of PnL Explain for each trade, which measures
 * the effect of time on the PV of a trade.
 */
@AllArgsConstructor
public class PnlExplainCarryCalculation {

  private static final Logger LOG = getLogger(PnlExplainCarryCalculation.class);

  // trade results for calculation using firstValuationDate and firstCurveDate
  private final PnlTradeValuationResults pnlTradeValuationResults;

  /**
   * Performs carry calculation for all trades
   *
   * @return PnlExplainCarryResult which holds carry values for all trades
   */
  public PnlExplainCarryResult calculate() {

    List<String> tradeIds =
        pnlTradeValuationResults.firstValDateFirstCurveDateResults().keySet().stream().toList();

    // TODO pass through error consumer for audit entries for failed trades

    Map<String, BigDecimal> tradeCarryResults = new HashMap<>();
    tradeIds.forEach(
        tradeId -> {
          var firstOrderCarryEither = getTradeFirstOrderCarry(tradeId);

          if (firstOrderCarryEither.isRight()) {
            var firstOrderCarry = firstOrderCarryEither.right().get();

            var secondOrderCarryEither = getTradeSecondOrderCarry(tradeId, firstOrderCarry);

            if (secondOrderCarryEither.isRight()) {
              // carry A + carry B
              var tradeCarry = firstOrderCarry.add(secondOrderCarryEither.right().get());
              tradeCarryResults.put(tradeId, tradeCarry);
            }
          }
        });

    return new PnlExplainCarryResult(tradeCarryResults);
  }

  /**
   * Calculates the "first order carry", which is component of carry which measures the impact of
   * time when holding the market levels at the first date constant (=> PV(valuationDate2,
   * curveDate1) - PV(valuationDate1, curveDate1)). This is equivalent to "carry A".
   *
   * @param tradeId the id of the trade for which to calculate the first order carry
   * @return first order carry, carry A
   */
  private Either<ErrorItem, BigDecimal> getTradeFirstOrderCarry(String tradeId) {
    // pv at second valuation date using market data from first valuation date
    var pvVd2Cd1 =
        getTradePv(pnlTradeValuationResults.secondValDateFirstCurveDateResults(), tradeId);

    if (pvVd2Cd1.isLeft()) {
      return pvVd2Cd1;
    }

    // pv at first valuation date using market data from first valuation date
    var pvVd1Cd1 =
        getTradePv(pnlTradeValuationResults.firstValDateFirstCurveDateResults(), tradeId);

    return pvVd1Cd1.fold(Either::left, r -> right(pvVd2Cd1.right().get().subtract(r)));
  }

  /**
   * Calculates second order carry = average of "carry A" and "carry B"
   *
   * @param tradeId the id of the trade for which to calculate the first order carry
   * @param carryA first order carry
   * @return second order carry
   */
  private Either<ErrorItem, BigDecimal> getTradeSecondOrderCarry(
      String tradeId, BigDecimal carryA) {

    var carryBEither = getTradeCarryB(tradeId);

    return carryBEither.fold(
        Either::left,
        carryB -> {
          var carryAverage = carryB.subtract(carryA).divide(BigDecimal.valueOf(2.0d), DECIMAL64);
          return right(carryAverage);
        });
  }

  /**
   * Calculates "carry B", which measures impact of time when holding the market levels at the
   * second date constant (=> PV(valuationDate2, curveDate2) - PV(valuationDate1, curveDate2))
   *
   * @param tradeId the id of the trade for which to calculate "carry B"
   * @return "carry B"
   */
  private Either<ErrorItem, BigDecimal> getTradeCarryB(String tradeId) {

    // pv at second valuation date using market data from first second valuation date
    var pvVd2Cd2 =
        getTradePv(pnlTradeValuationResults.secondValDateSecondCurveDateResults(), tradeId);

    if (pvVd2Cd2.isLeft()) {
      return pvVd2Cd2;
    }

    // pv at first valuation date using market data from second valuation date
    var pvVd1Cd2 =
        getTradePv(pnlTradeValuationResults.firstValDateSecondCurveDateResults(), tradeId);

    return pvVd1Cd2.fold(Either::left, r -> right(pvVd2Cd2.right().get().subtract(r)));
  }

  private Either<ErrorItem, BigDecimal> getTradePv(
      Map<String, PnlPortfolioItemCalculationResult> resultsCollection, String tradeId) {

    var tradeResult = resultsCollection.get(tradeId);

    if (tradeResult != null) {
      var pv = tradeResult.getMetricsPresentValuePayLegCurrency();
      if (pv == null) {
        String nullPvErrorMessage = format("Trade %s pv is null", tradeId);
        LOG.debug(nullPvErrorMessage);
        return left(CALCULATION_ERROR.entity(nullPvErrorMessage));
      }
      return right(BigDecimal.valueOf(pv));
    }

    String errorMessage = format("Missing calculation for trade %s", tradeId);
    LOG.info(errorMessage);
    return left(CALCULATION_ERROR.entity(errorMessage));
  }
}
