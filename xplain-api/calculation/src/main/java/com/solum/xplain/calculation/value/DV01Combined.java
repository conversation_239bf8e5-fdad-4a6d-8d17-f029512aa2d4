package com.solum.xplain.calculation.value;

import static java.util.List.of;

import java.util.List;
import lombok.Data;

@Data
public class DV01Combined {

  public static final DV01Combined EMPTY = new DV01Combined(of(), of(), of(), of(), of(), of());

  private final List<DV01TradeValue> dv01;
  private final List<DV01TradeValue> br01;
  private final List<DV01TradeValue> inf01;
  private final List<DV01TradeValue> cs01;
  private final List<DV01TradeValue> deltaFwd;
  private final List<DV01TradeValue> deltaSpot;
}
