package com.solum.xplain.calculation;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.MongoVariables.VALUE_PREFIX;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.Fields.field;

import com.solum.xplain.calculation.value.CalculationTotalsComparisonView;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation;
import org.springframework.data.mongodb.core.aggregation.Field;
import org.springframework.data.mongodb.core.aggregation.Fields;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CalculationAggregationUtils {

  public static AddFieldsOperation mapGroupedFields(Fields groupIdFields) {
    var addFields = addFields();
    var groupIdFieldsList = groupIdFields.asList();
    if (groupIdFieldsList.size() == 1) {
      addFields
          .addField(groupIdFieldsList.getFirst().getTarget())
          .withValueOf(field(UNDERSCORE_ID));
    } else {
      for (Field field : groupIdFieldsList) {
        addFields
            .addField(field.getTarget())
            .withValueOf(field(UNDERSCORE_ID + "." + field.getTarget()));
      }
    }
    return addFields.build();
  }

  public static String toGroupedValuesField(String path) {
    return propertyName(
        VALUE_PREFIX.concat(CalculationTotalsComparisonView.Fields.groupedValues), path);
  }
}
