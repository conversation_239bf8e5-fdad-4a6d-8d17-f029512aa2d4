package com.solum.xplain.calculation.discounting.tradeinfosubsets;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import io.atlassian.fugue.Either;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

@Data
public class FraPortfolioItemDiscountingGroupSpecification
    implements PortfolioItemDiscountingGroupSpecification {

  private final String tradeCurrency;
  private final String receiveLegIndex;
  private final CalculationType receiveLegType;
  private final Boolean receiveLegIsOffshore;
  private final String payLegIndex;
  private final CalculationType payLegType;
  private final Boolean payLegIsOffshore;

  private final String csaDiscountingGroup;

  public FraPortfolioItemDiscountingGroupSpecification(
      String tradeCurrency,
      String payLeg,
      CalculationType payLegType,
      Boolean payLegOffshore,
      String receiveLeg,
      CalculationType receiveLegType,
      Boolean receiveLegOffshore,
      String csaDiscountingGroup) {
    this.tradeCurrency = tradeCurrency;
    this.payLegIndex = payLeg;
    this.payLegType = payLegType;
    this.payLegIsOffshore = BooleanUtils.isTrue(payLegOffshore);
    this.receiveLegIndex = receiveLeg;
    this.receiveLegType = receiveLegType;
    this.receiveLegIsOffshore = BooleanUtils.isTrue(receiveLegOffshore);
    this.csaDiscountingGroup = csaDiscountingGroup;
  }

  @Override
  public CoreProductType productType() {
    return CoreProductType.FRA;
  }

  @Override
  public ClearingHouse clearingHouse() {
    return ClearingHouse.NONE;
  }

  @Override
  public boolean isOffshore() {
    return payLegIsOffshore;
  }

  @Override
  public Currency resolveCurrency(ProductSettingsResolver settingsResolver) {
    return Currency.of(tradeCurrency);
  }

  @Override
  public Currency tradeCurrency() {
    return Currency.of(tradeCurrency);
  }

  @Override
  public CurrencyPair currencyPair() {
    return null;
  }

  @Override
  public Either<ErrorItem, FloatingRateIndex> resolveIndex(
      Set<FloatingRateIndex> availableIndices,
      ProductSettingsResolver productSettingsResolver,
      DiscountingIndexResolver discountingIndexResolver) {

    // Resolve the correct floating rate index based on the position
    FloatingRateIndex index;
    if (payLegType.equals(CalculationType.FIXED)) {
      // For a BUY position, use the ibor leg index (floating rate)
      index =
          PortfolioItemDiscountingSpecificationUtils.rateIndex(
                  receiveLegType, receiveLegIndex, receiveLegIsOffshore)
              .orElseThrow(() -> new IllegalArgumentException("No Ibor leg for BUY position"));
    } else {
      // For a SELL position, use the pay leg index (floating rate)
      index =
          PortfolioItemDiscountingSpecificationUtils.rateIndex(
                  payLegType, payLegIndex, payLegIsOffshore)
              .orElseThrow(() -> new IllegalArgumentException("No Ibor leg for SELL position"));
    }

    return discountingIndexResolver.resolveDiscountingIndex(index, availableIndices);
  }

  @Override
  public Set<FloatingRateIndex> tradeRateIndices() {
    return Stream.of(
            PortfolioItemDiscountingSpecificationUtils.rateIndex(
                payLegType, payLegIndex, payLegIsOffshore),
            PortfolioItemDiscountingSpecificationUtils.rateIndex(
                receiveLegType, receiveLegIndex, receiveLegIsOffshore))
        .flatMap(Optional::stream)
        .collect(Collectors.toSet());
  }
}
