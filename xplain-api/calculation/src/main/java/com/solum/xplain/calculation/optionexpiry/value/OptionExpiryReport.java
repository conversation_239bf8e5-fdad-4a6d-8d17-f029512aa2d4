package com.solum.xplain.calculation.optionexpiry.value;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.util.Assert;

@Data
public class OptionExpiryReport {

  @Schema(description = "Sorted array of expiries (year fractions)")
  @NotNull
  private final List<BigDecimal> expiry;

  @Schema(description = "Sorted array of ITMs")
  @NotNull
  private final List<BigDecimal> itm;

  @Schema(
      description =
          "2D array of values per type, first level ITM, second level expiry. E.g. values[0][1] corresponds to itm[0] and expiry[1]")
  @NotNull
  private final Map<OptionExpiryReportType, BigDecimal[][]> values;

  public OptionExpiryReport(
      @NotNull List<BigDecimal> expiry,
      @NotNull List<BigDecimal> itm,
      @NotNull Map<OptionExpiryReportType, BigDecimal[][]> values) {
    this.expiry = expiry;
    this.itm = itm;
    if (!expiry.isEmpty() && !itm.isEmpty()) {
      for (var v : values.values()) {
        Assert.isTrue(v.length == itm.size(), "Values array must have the same size as itm array");
        for (int i = 0; i < itm.size(); i++) {
          Assert.isTrue(
              v[i].length == expiry.size(),
              "Each values sub-array must have the same size as expiry array");
        }
      }
    }
    this.values = values;
  }
}
