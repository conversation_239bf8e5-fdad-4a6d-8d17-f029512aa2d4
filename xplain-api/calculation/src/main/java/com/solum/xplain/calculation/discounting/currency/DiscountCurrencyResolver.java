package com.solum.xplain.calculation.discounting.currency;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import java.util.List;

public interface DiscountCurrencyResolver {

  List<ProductType> productTypes();

  Currency resolveCurrency(TradeDetails tradeDetails, ProductSettingsResolver settingsResolver);
}
