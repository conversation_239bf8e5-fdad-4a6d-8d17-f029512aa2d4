package com.solum.xplain.calculation.validation;

import com.solum.xplain.calculation.form.CalculationForm;
import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.market.validation.CompanyMarketDataGroupValidator;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ValidCalculationMarketDataGroupValidator
    implements ConstraintValidator<ValidCalculationMarketDataGroup, CalculationForm> {
  private final PortfolioRepository portfolioRepository;
  private final CompanyMarketDataGroupValidator mdValidator;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  public ValidCalculationMarketDataGroupValidator(
      PortfolioRepository portfolioRepository,
      CompanyMarketDataGroupValidator mdValidator,
      RequestPathVariablesSupport requestPathVariablesSupport) {
    this.portfolioRepository = portfolioRepository;
    this.mdValidator = mdValidator;
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  @Override
  public boolean isValid(CalculationForm value, ConstraintValidatorContext context) {
    String portfolioId = requestPathVariablesSupport.getPathVariable("portfolioId");
    if (value != null
        && StringUtils.isNotEmpty(value.getMarketDataGroupId())
        && StringUtils.isNotEmpty(portfolioId)) {
      return portfolioRepository
          .portfolio(portfolioId)
          .map(PortfolioCondensedView::getCompanyId)
          .filter(cId -> mdValidator.isMdAllowedForCompany(value.getMarketDataGroupId(), cId))
          .isPresent();
    }
    return true;
  }
}
