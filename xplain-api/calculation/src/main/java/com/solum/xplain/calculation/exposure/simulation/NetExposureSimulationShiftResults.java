package com.solum.xplain.calculation.exposure.simulation;

import com.solum.xplain.calculation.exposure.netexposure.NetExposureBucketCalculationMetrics;
import java.math.BigDecimal;
import java.util.List;

public record NetExposureSimulationShiftResults(
    BigDecimal shiftSize, List<NetExposureBucketSimulationCalculationMetrics> metricsList) {

  public static NetExposureSimulationShiftResults empty() {
    return new NetExposureSimulationShiftResults(null, List.of());
  }

  public static NetExposureSimulationShiftResults of(
      BigDecimal shiftSize, List<NetExposureBucketCalculationMetrics> metricsList) {

    List<NetExposureBucketSimulationCalculationMetrics> converted =
        metricsList.stream().map(NetExposureBucketSimulationCalculationMetrics::of).toList();

    return new NetExposureSimulationShiftResults(shiftSize, converted);
  }
}
