package com.solum.xplain.calculation.simulation.daterange.events

import static com.solum.xplain.core.sockets.events.EventType.SIMULATIONS_UPDATED

import com.solum.xplain.calculation.simulation.events.SimulationsSocketEventsListener
import com.solum.xplain.core.sockets.constants.CoreSocketEvents
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class SimulationsSocketEventsListenerTest extends Specification {
  ApplicationEventPublisher eventPublisher = Mock()

  SimulationsSocketEventsListener listener = new SimulationsSocketEventsListener(eventPublisher)

  def "should handle start simulation event"() {
    setup:
    def event = Mock(DateRangeSimulationRequestedEvent)
    event.simulationId() >> "id"

    when:
    listener.handleSimulationEvent(event)

    then:
    1 * eventPublisher.publishEvent(CoreSocketEvents.global(SIMULATIONS_UPDATED, "id"))
  }

  def "should handle simulation end event"() {
    setup:
    def event = Mock(DateRangeSimulationFinishedEvent)
    event.getSimulationId() >> "id"

    when:
    listener.handleSimulationEvent(event)

    then:
    1 * eventPublisher.publishEvent(CoreSocketEvents.global(SIMULATIONS_UPDATED, "id"))
  }
}
