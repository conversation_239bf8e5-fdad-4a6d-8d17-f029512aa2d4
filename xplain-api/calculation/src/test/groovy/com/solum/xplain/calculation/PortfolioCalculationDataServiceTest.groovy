package com.solum.xplain.calculation

import com.opengamma.strata.basics.ImmutableReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.data.MarketData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuildersHolder
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataService
import com.solum.xplain.calculation.form.PortfolioCalculationForm
import com.solum.xplain.calculation.market.CalculationMarketData
import com.solum.xplain.calculation.market.CalculationMarketDataService
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.trades.CalculationTradesFactory
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.calculation.value.CalculationOptions
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.refdata.VersionedReferenceData
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import spock.lang.Specification

class PortfolioCalculationDataServiceTest extends Specification {

  def static CURVE_DATE = LocalDate.of(2021, 01, 01)
  def static VALUATION_DATE = LocalDate.of(2021, 01, 02)
  def static STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 03))
  def static PORTFOLIO = new PortfolioCondensedView(id: ObjectId.get().toHexString())

  CalculationMarketDataService marketDataService = Mock()
  AuditEntryService auditEntryService = Mock()
  CalculationCurveGroupDataService calculationCurveGroupDataService = Mock()
  VersionedReferenceData versionedReferenceData = Mock()
  CalculationTradesFactory calculationTradesFactory = Mock()

  PortfolioCalculationDataService service = new PortfolioCalculationDataService(
  marketDataService, auditEntryService, calculationCurveGroupDataService, versionedReferenceData, calculationTradesFactory)

  def "should create portfolio calculation data"() {
    setup:
    def mdId = "mdId"
    def calculationMd = new CalculationMarketData(
      new CalculationResultMarketData(id: mdId, name: "mdName", type: MarketDataSourceType.OVERLAY),
      MarketData.empty(VALUATION_DATE),
      MarketData.empty(VALUATION_DATE)
      )
    def discounting = Mock(CalculationDiscounting)

    def priceRequirements = Mock(InstrumentPriceRequirements)
    def form = Mock(PortfolioCalculationForm)
    form.reportingCurrency() >> Currency.EUR
    form.calculationType() >> PortfolioCalculationType.TRADES
    form.calculationDiscounting() >> discounting
    form.marketDataSource() >> MarketDataSourceType.OVERLAY
    form.getMarketDataGroupId() >> mdId
    form.tradeId() >> Optional.empty()
    form.productTypes() >> Optional.empty()
    form.priceRequirements() >> priceRequirements
    def calculationTrade = Mock(CalculationTrades)

    def holder = Mock(CalculationCurveGroupDataBuildersHolder)
    1 * holder.warnings() >> [Error.CALCULATION_WARNING.entity()]
    def curveData = Mock(CalculationCurveGroupData)
    1 * holder.calculate(MarketData.empty(VALUATION_DATE), _) >> curveData
    def curveDataFx = Mock(CalculationCurveGroupData)
    1 * holder.calculateFx(MarketData.empty(VALUATION_DATE), _) >> curveDataFx

    1 * marketDataService.marketData(form, CURVE_DATE, VALUATION_DATE, STATE_DATE, holder) >> Either.right(calculationMd)

    1 * calculationTradesFactory.calculationTrades(PORTFOLIO.getId(), form, STATE_DATE) >> Either.right(calculationTrade)

    1 * calculationCurveGroupDataService.resolveCalculationData(form, calculationTrade, VALUATION_DATE, STATE_DATE) >> Either.right(holder)

    1 * versionedReferenceData.asAt(STATE_DATE.getRecordDate()) >> ImmutableReferenceData.empty()

    when:
    def result = service.portfolioCalculationData(PORTFOLIO, VALUATION_DATE, CURVE_DATE, STATE_DATE, form)

    then:
    result.isRight()
    result.getOrNull().getPortfolio() == PORTFOLIO
    result.getOrNull().getOptions() == CalculationOptions.newOf(
      VALUATION_DATE,
      CURVE_DATE,
      STATE_DATE,
      Currency.EUR,
      PortfolioCalculationType.TRADES,
      discounting,
      priceRequirements
      )
    result.getOrNull().getMarketData() == CalculationResultMarketData.newOf(mdId, "mdName", MarketDataSourceType.OVERLAY)
    result.getOrNull().getCalibrationResult() == curveData
    result.getOrNull().getFxCalibrationResult() == curveDataFx
    result.getOrNull().getReferenceData() == ImmutableReferenceData.empty()

    1 * auditEntryService.newEntryWithLogs(_, [Error.CALCULATION_WARNING.entity()])
  }
}
