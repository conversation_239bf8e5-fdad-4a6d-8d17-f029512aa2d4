package com.solum.xplain.calculation.curvegroup

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuilder.builder
import static com.solum.xplain.calculation.curvegroup.CurveGroupDataType.MULTI_FX
import static com.solum.xplain.calculation.curvegroup.CurveGroupDataType.MULTI_NON_FX
import static com.solum.xplain.calculation.curvegroup.CurveGroupDataType.SINGLE
import static com.solum.xplain.core.error.Error.CALCULATION_WARNING
import static io.atlassian.fugue.Either.left

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.calculation.discounting.DiscountingGroupsBuilderFn
import com.solum.xplain.calculation.discounting.ResolvedDiscountingCalibrationBundles
import com.solum.xplain.calculation.repository.GenericPortfolioItemDiscountingCriteriaBuilder
import com.solum.xplain.calibration.rates.group.DiscountingGroup
import com.solum.xplain.calibration.rates.group.DiscountingGroups
import com.solum.xplain.calibration.rates.group.DiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.DiscountingGroupsResults
import com.solum.xplain.calibration.rates.set.CalibrationBundle
import com.solum.xplain.calibration.rates.set.CalibrationSubGroupsResolver
import com.solum.xplain.calibration.value.CalculationShifts
import com.solum.xplain.core.common.AuditUserMapperImpl
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapperImpl
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [CurveGroupFxVolatilityMapperImpl.class, AuditUserMapperImpl.class])
class CalculationCurveGroupDataBuilderTest extends Specification {
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()
  @Autowired
  CurveGroupFxVolatilityMapper curveGroupFxVolatilityMapper

  def "should resolve product type for SINGLE"() {
    setup:
    def builder = builder(SINGLE, referenceData, curveGroupFxVolatilityMapper)
    def result = builder.resolveProductTypes([CoreProductType.IRS] as Set<CoreProductType>)

    expect:
    result == Either.right(builder)
  }

  @Unroll
  def "should not resolve product types for #type with result #expected"() {
    setup:
    def builder = builder(type, referenceData, curveGroupFxVolatilityMapper)
    def result = builder.resolveProductTypes(products as Set<CoreProductType>)

    expect:
    result == expected

    where:
    type         | products                | expected
    MULTI_FX     | [CoreProductType.IRS]   | left(calculationError("FX discounting group has no trades"))
    MULTI_NON_FX | [CoreProductType.FXFWD] | left(calculationError("Non FX discounting group has no trades"))
    SINGLE       | []                      | left(calculationError("Discounting group has no trades"))
  }

  def "should not resolve product dsc when LOAN trade"() {
    setup:
    def dscBuilder = Mock(DiscountingGroupsBuilder)
    def builderFn = Mock(DiscountingGroupsBuilderFn)
    1 * builderFn.builder(_) >> dscBuilder
    def builder = builder(SINGLE, referenceData, curveGroupFxVolatilityMapper)
    builder.withCurveConfigurationId("cid")
    builder.withMarketDataSource(MarketDataSourceType.RAW_PRIMARY)
    builder.withStateDate(BitemporalDate.newOfNow())
    builder.resolveDiscountingBuilder(builderFn)
    builder.resolveProductTypes(Set.of(CoreProductType.LOAN_NOTE))

    when:
    builder.withTrade(new GenericPortfolioItemDiscountingCriteriaBuilder().build())

    then:
    0 * dscBuilder.withItem(_)
  }

  def "should resolve bundles"() {
    setup:
    def errors = new ArrayList<ErrorItem>()
    def valuationDate = LocalDate.of(2023, 1, 1)

    def baseDsc = Mock(DiscountingGroup)
    def groupsResolver = Mock(CalibrationSubGroupsResolver)
    1 * groupsResolver.description() >> "description"
    1 * groupsResolver.calibrationCurrency() >> EUR
    1 * groupsResolver.calibrationSubGroups(_, _, _, _) >> Either.right([])
    def dscResults = Mock(DiscountingGroupsResults)
    1 * dscResults.calibrationSubGroupsResolvers() >> [(baseDsc): groupsResolver]
    def discountings = Mock(DiscountingGroups)
    1 * discountings.results() >> dscResults
    def dscBuilder = Mock(DiscountingGroupsBuilder)
    1 * dscBuilder.build {
      errors.add(it)
    } >> discountings
    def builderFn = Mock(DiscountingGroupsBuilderFn)
    1 * builderFn.builder(_) >> dscBuilder
    def builder = builder(SINGLE, referenceData, curveGroupFxVolatilityMapper)
    builder.resolveDiscountingBuilder(builderFn)
    builder.withCurveConfigurationInstrumentResolver(Mock(CurveConfigurationInstrumentResolver))
    builder.resolveCurves((a, b) -> [])
    builder.withCalculationShifts(Mock(CalculationShifts))
    builder.withTriangulationCcy(EUR)


    when:
    builder.resolveCalibrationBundles(valuationDate, {
      errors.add(it)
    })

    then:
    builder.calibrationBundles == new ResolvedDiscountingCalibrationBundles(discountings, [(baseDsc): new CalibrationBundle("description", EUR, [])])
  }

  static ErrorItem calculationError(String msg) {
    return CALCULATION_WARNING.entity(msg)
  }
}
