package com.solum.xplain.calculation.it

import static com.solum.xplain.core.portfolio.CoreProductType.CDS
import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.cdsTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxOptionTradeDetails
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.fxSingle
import static com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder.swaptionTradeDetails

import com.solum.xplain.calibration.CalibrationIntegrationSampleData
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import org.bson.types.ObjectId
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query

trait CalculationIntegrationSampleData extends CalibrationIntegrationSampleData {
  String COMPANY_ID = ObjectId.get().toHexString()
  String LEGAL_ENTITY_ID = ObjectId.get().toHexString()
  String PORTFOLIO_ID = ObjectId.get().toHexString()

  abstract MongoOperations operations()

  def setupCalculationPortfolio() {

    def company = new Company(id: COMPANY_ID, allowAllTeams: true, name: "Company", externalCompanyId: "EXT_COMPANY")
    operations().insert(company)

    def entity = new CompanyLegalEntity(id: LEGAL_ENTITY_ID, allowAllTeams: true, name: "Entity", externalId: "EXT_ENTITY", companyId: company.id)
    operations().insert(entity)

    def portfolio = new Portfolio(
      id: PORTFOLIO_ID,
      allowAllTeams: true,
      name: "Entity",
      externalPortfolioId: "EXT_PORTFOLIO",
      entity: new CompanyLegalEntityReference(entityId: entity.id, name: entity.name, externalEntityId: entity.externalId),
      company: new CompanyReference(entityId: company.id, name: company.name, externalCompanyId: company.externalCompanyId)
      )
    operations().insert(portfolio)
  }

  def cleanupCalculationData() {
    operations().remove(new Query(), Portfolio)
    operations().remove(new Query(), CompanyLegalEntity)
    operations().remove(new Query(), Company)
    operations().remove(new Query(), PortfolioItem)

    operations().remove(new Query(), VolatilitySurface)
    operations().remove(new Query(), CurveGroupFxVolatility)
  }

  def mockIrsPortfolioItem() {
    operations().insert(PortfolioItemBuilder.irsPortfolioItem(PORTFOLIO_ID))
  }

  def mockFxFwdPortfolioItem() {
    operations().insert(PortfolioItemBuilder.trade(FXFWD, fxSingle(DATE), PORTFOLIO_ID))
  }

  def mockCreditPortfolioItem() {
    operations().insert(PortfolioItemBuilder.trade(CDS, cdsTradeDetails(), PORTFOLIO_ID))
  }

  def mockSwaptionPortfolioItem() {
    operations().insert(PortfolioItemBuilder.trade(SWAPTION, swaptionTradeDetails(), PORTFOLIO_ID))
  }

  def mockVolatilitySurface() {
    def surface = VolatilitySurfaceBuilder.surfaceWithNodes()
    surface.curveGroupId = CURVE_GROUP_ID
    operations().insert(surface)

    surface.allInstruments()
      .forEach { i -> mockMdk(i.getKey(), i.getInstrument()) }
  }

  def mockFxOptionPortfolioItem() {
    operations().insert(PortfolioItemBuilder.trade(FXOPT, fxOptionTradeDetails(DATE), PORTFOLIO_ID))
  }

  def mockFxVolatility() {
    def nodes = [
      new CurveGroupFxVolatilityNode(expiry: "6M", domesticCurrency: "EUR", foreignCurrency: "USD", delta1: 10, delta2: 25),
      new CurveGroupFxVolatilityNode(expiry: "1Y", domesticCurrency: "EUR", foreignCurrency: "USD", delta1: 10, delta2: 25),
    ] as Set

    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .entityId(CURVE_GROUP_ID)
      .nodes(nodes)
      .build()
    operations().insert(fxVolatility)
    fxVolatility.allInstruments()
      .forEach { i -> mockMdk(i.getKey(), i.getInstrument()) }
  }
}
