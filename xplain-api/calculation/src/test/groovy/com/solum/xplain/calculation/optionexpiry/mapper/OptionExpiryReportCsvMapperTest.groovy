package com.solum.xplain.calculation.optionexpiry.mapper

import static com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReportType.SWAPTION_PAY_BUY
import static java.math.BigDecimal.valueOf

import com.solum.xplain.calculation.optionexpiry.csv.OptionExpiryReportCsvMapper
import com.solum.xplain.calculation.optionexpiry.value.OptionExpiryReport
import spock.lang.Specification

class OptionExpiryReportCsvMapperTest extends Specification {

  def "should generate csv"() {
    setup:
    def expiryReport = new OptionExpiryReport(
      [valueOf(0.1d), valueOf(0.2d)],
      [valueOf(0.3d)],
      [(SWAPTION_PAY_BUY): new BigDecimal[][]{
          new BigDecimal[]{
            1d, 2d
        }
      }]
    )

    when:
    def csv = new OptionExpiryReportCsvMapper(expiryReport, SWAPTION_PAY_BUY).toCsv()
    def result = csv.write()

    then:
    result == """ITM\\EXP,0.1,0.2
0.3,1.0,2.0
"""
  }
}
