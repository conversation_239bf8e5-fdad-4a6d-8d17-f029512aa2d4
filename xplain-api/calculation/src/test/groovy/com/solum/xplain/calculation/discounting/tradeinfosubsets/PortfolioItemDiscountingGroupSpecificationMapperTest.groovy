package com.solum.xplain.calculation.discounting.tradeinfosubsets

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndex
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.trade.CreditTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSeniority
import com.solum.xplain.extensions.enums.PositionType
import spock.lang.Specification

class PortfolioItemDiscountingGroupSpecificationMapperTest extends Specification {

  def "should correctly map objects to CapfloorPortfolioItemDiscountingGroupSpecification"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.CAP_FLOOR,
      tradeCurrency: "GBP",
      receiveLegIndex: "GBP-LIBOR-3M",
      receiveLegType: CalculationType.IBOR,
      receiveLegIsOffshore: false,
      csaDiscountingGroup: "Group1"
      )
    def payCriteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.CAP_FLOOR,
      tradeCurrency: "GBP",
      payLegIndex: "GBP-LIBOR-3M",
      payLegType: CalculationType.IBOR,
      payLegIsOffshore: false,
      csaDiscountingGroup: "Group1"
      )
    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.CAP_FLOOR,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("GBP"),
      csaDiscountingGroup: "Group1",
      counterPartyType: CounterpartyType.BILATERAL
      ),
      payLeg: new TradeLegDetails(
      index: "GBP-LIBOR-3M",
      type: CalculationType.IBOR,
      initialValue: 0.01,
      isOffshore: false,
      currency: Currency.parse("GBP")
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)
    def resultFromPayGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(payCriteria)

    then:
    resultFromGenericCriteria instanceof CapfloorPortfolioItemDiscountingGroupSpecification
    def capfloorResult = resultFromGenericCriteria as CapfloorPortfolioItemDiscountingGroupSpecification
    capfloorResult.tradeCurrency() == Currency.parse("GBP")
    capfloorResult.productType() == CoreProductType.CAP_FLOOR
    capfloorResult.currencyPair() == null
    capfloorResult.getPositionLegIndex() == "GBP-LIBOR-3M"
    capfloorResult.capletIndex() == IborIndex.of("GBP-LIBOR-3M")
    capfloorResult.getPositionLegType() == CalculationType.IBOR
    !capfloorResult.isOffshore()
    capfloorResult.getCsaDiscountingGroup() == "Group1"
    capfloorResult.resolveCurrency(Mock(ProductSettingsResolver)) == Currency.parse("GBP")

    resultFromPayGenericCriteria == resultFromGenericCriteria

    when:
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromPortfolioItem == resultFromGenericCriteria
  }

  def "should correctly map objects to CdsPortfolioItemDiscountingGroupSpecification"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.CDS,
      tradeCurrency: "USD",
      creditReference: "Reference1",
      cdsSeniority: CreditSeniority.SNRFOR,
      creditDocClause: CreditDocClause.CR,
      csaDiscountingGroup: "Group1"
      )
    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.CDS,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("USD"),
      csaDiscountingGroup: "Group1",
      counterPartyType: CounterpartyType.BILATERAL
      ),
      creditTradeDetails: new CreditTradeDetails(
      reference: "Reference1",
      seniority: CreditSeniority.SNRFOR,
      docClause: CreditDocClause.CR
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)

    then:
    resultFromGenericCriteria instanceof CdsPortfolioItemDiscountingGroupSpecification
    def cdsResult = resultFromGenericCriteria as CdsPortfolioItemDiscountingGroupSpecification
    cdsResult.tradeCurrency() == Currency.parse("USD")
    cdsResult.getCreditReference() == "Reference1"
    cdsResult.getCdsSeniority() == CreditSeniority.SNRFOR
    cdsResult.getDocClause() == CreditDocClause.CR
    cdsResult.getCsaDiscountingGroup() == "Group1"

    when:
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromPortfolioItem == resultFromGenericCriteria
  }

  def "should correctly map objects to IrsPortfolioItemDiscountingGroupSpecification"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.IRS,
      tradeCurrency: "EUR",
      payLegIndex: "EUR-EURIBOR-6M",
      payLegType: CalculationType.IBOR,
      payLegSpread: 0.002,
      payLegIsOffshore: false,
      receiveLegIndex: "EUR-EURIBOR-6M",
      receiveLegType: CalculationType.IBOR,
      receiveLegSpread: 0.001,
      csaDiscountingGroup: "Group1"
      )
    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.IRS,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("EUR"),
      csaDiscountingGroup: "Group1",
      counterPartyType: CounterpartyType.BILATERAL
      ),
      payLeg: new TradeLegDetails(
      index: "EUR-EURIBOR-6M",
      type: CalculationType.IBOR,
      initialValue: 0.002,
      isOffshore: false,
      currency: Currency.parse("EUR")
      ),
      receiveLeg: new TradeLegDetails(
      index: "EUR-EURIBOR-6M",
      type: CalculationType.IBOR,
      initialValue: 0.001,
      isOffshore: false,
      currency: Currency.parse("EUR")
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)

    then:
    resultFromGenericCriteria instanceof IrsPortfolioItemDiscountingGroupSpecification
    def irsResult = resultFromGenericCriteria as IrsPortfolioItemDiscountingGroupSpecification
    irsResult.tradeCurrency() == Currency.parse("EUR")
    irsResult.getPayLegIndex() == "EUR-EURIBOR-6M"
    irsResult.getPayLegType() == CalculationType.IBOR
    irsResult.getPayLegSpread() == 0.002
    !irsResult.isOffshore()
    irsResult.getReceiveLegIndex() == "EUR-EURIBOR-6M"
    irsResult.getReceiveLegType() == CalculationType.IBOR
    irsResult.getReceiveLegSpread() == 0.001
    irsResult.getCsaDiscountingGroup() == "Group1"

    when:
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromPortfolioItem == resultFromGenericCriteria
  }

  def "should correctly map objects to XccyPortfolioItemDiscountingGroupSpecification"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.XCCY,
      tradeCurrency: "USD",
      payLegIndex: "USD-LIBOR-3M",
      payLegCurrency: "USD",
      payLegType: CalculationType.IBOR,
      payLegSpread: 0.002,
      payLegIsOffshore: false,
      receiveLegCurrency: "EUR",
      receiveLegIndex: "EUR-EURIBOR-6M",
      receiveLegType: CalculationType.IBOR,
      receiveLegSpread: 0.001,
      receiveLegIsOffshore: false,
      csaDiscountingGroup: "Group1"
      )
    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.XCCY,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("USD"),
      csaDiscountingGroup: "Group1",
      counterPartyType: CounterpartyType.BILATERAL
      ),
      payLeg: new TradeLegDetails(
      index: "USD-LIBOR-3M",
      type: CalculationType.IBOR,
      initialValue: 0.002,
      isOffshore: false,
      currency: Currency.parse("USD")
      ),
      receiveLeg: new TradeLegDetails(
      index: "EUR-EURIBOR-6M",
      type: CalculationType.IBOR,
      initialValue: 0.001,
      isOffshore: false,
      currency: Currency.parse("EUR")
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)

    then:
    resultFromGenericCriteria instanceof XccyPortfolioItemDiscountingGroupSpecification
    def xccyResult = resultFromGenericCriteria as XccyPortfolioItemDiscountingGroupSpecification
    xccyResult.tradeCurrency() == Currency.parse("USD")
    xccyResult.getPayLegIndex() == "USD-LIBOR-3M"
    xccyResult.getPayLegType() == CalculationType.IBOR
    xccyResult.getPayLegSpread() == 0.002
    !xccyResult.isOffshore()
    xccyResult.getReceiveLegIndex() == "EUR-EURIBOR-6M"
    xccyResult.getReceiveLegType() == CalculationType.IBOR
    xccyResult.getReceiveLegSpread() == 0.001
    xccyResult.getCsaDiscountingGroup() == "Group1"

    when:
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromPortfolioItem == resultFromGenericCriteria
  }

  def "should correctly map #productType objects to FxfwdPortfolioItemDiscountingGroupSpecification"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: productType,
      tradeCurrency: "USD",
      payLegCurrency: Currency.parse("USD"),
      receiveLegCurrency: Currency.parse("EUR"),
      csaDiscountingGroup: "Group1"
      )
    def portfolioItem = new PortfolioItem(
      productType: productType,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("USD"),
      csaDiscountingGroup: "Group1",
      counterPartyType: CounterpartyType.BILATERAL
      ),
      payLeg: new TradeLegDetails(
      currency: Currency.parse("USD")
      ),
      receiveLeg: new TradeLegDetails(
      currency: Currency.parse("EUR")
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)

    then:
    resultFromGenericCriteria instanceof FxFwdPortfolioItemDiscountingGroupSpecification
    def fxfwdResult = resultFromGenericCriteria as FxFwdPortfolioItemDiscountingGroupSpecification
    fxfwdResult.tradeCurrency() == Currency.parse("USD")
    fxfwdResult.getPayLegCurrency() == "USD"
    fxfwdResult.getReceiveLegCurrency() == "EUR"
    fxfwdResult.currencyPair() == CurrencyPair.of(Currency.parse("EUR"), Currency.parse("USD"))
    fxfwdResult.getCsaDiscountingGroup() == "Group1"

    when:
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromPortfolioItem == resultFromGenericCriteria

    where:
    productType << [CoreProductType.FXFWD, CoreProductType.FXSWAP]
  }

  def "should correctly map objects to FraPortfolioItemDiscountingGroupSpecification for BUY position"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.FRA,
      tradeCurrency: "NZD",
      payLegType: CalculationType.FIXED,  // The buyer pays the fixed rate
      payLegIsOffshore: false,
      csaDiscountingGroup: "Group1",
      receiveLegCurrency: "NZD",
      receiveLegIndex: "NZD-LIBOR-3M",
      receiveLegType: CalculationType.IBOR,  // The buyer receives the floating rate
      )

    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.FRA,
      tradeDetails: new TradeDetails(
      positionType: PositionType.BUY,  // BUY position (buyer pays fixed, receives floating)
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("NZD"),
      csaDiscountingGroup: "Group1"
      ),
      payLeg: new TradeLegDetails(
      type: CalculationType.FIXED,
      isOffshore: false,
      currency: Currency.parse("NZD"),
      notional: 1000000,
      initialValue: 0.05,  // Fixed rate
      dayCount: "Act/360"
      ),
      receiveLeg: new TradeLegDetails(
      type: CalculationType.IBOR,
      isOffshore: false,
      currency: Currency.parse("NZD"),
      notional: 1000000,
      initialValue: 0.0,  // Floating rate
      index: "NZD-LIBOR-3M",
      dayCount: "Act/360"
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromGenericCriteria instanceof FraPortfolioItemDiscountingGroupSpecification
    def fraResult = resultFromGenericCriteria as FraPortfolioItemDiscountingGroupSpecification
    fraResult.productType() == CoreProductType.FRA
    fraResult.tradeCurrency() == Currency.parse("NZD")
    fraResult.getReceiveLegType() == CalculationType.IBOR  // Buyer receives floating rate
    !fraResult.isOffshore()
    fraResult.getCsaDiscountingGroup() == "Group1"
    fraResult.currencyPair() == null

    resultFromPortfolioItem == resultFromGenericCriteria
  }

  def "should correctly map objects to FraPortfolioItemDiscountingGroupSpecification for SELL position"() {
    given:
    def criteria = new GenericPortfolioItemDiscountingCriteria(
      productType: CoreProductType.FRA,
      tradeCurrency: "NZD",
      payLegIndex: "NZD-LIBOR-3M",
      payLegType: CalculationType.IBOR,  // The seller pays the floating rate
      payLegIsOffshore: false,
      csaDiscountingGroup: "Group1",
      receiveLegCurrency: "NZD",
      receiveLegIndex: null,  // Fixed leg does not have an index
      receiveLegType: CalculationType.FIXED,  // The seller receives the fixed rate
      )

    def portfolioItem = new PortfolioItem(
      productType: CoreProductType.FRA,
      tradeDetails: new TradeDetails(
      positionType: PositionType.SELL,  // SELL position (seller pays floating, receives fixed)
      info: new TradeInfoDetails(
      tradeCurrency: Currency.parse("NZD"),
      csaDiscountingGroup: "Group1"
      ),
      payLeg: new TradeLegDetails(
      type: CalculationType.IBOR,
      isOffshore: false,
      currency: Currency.parse("NZD"),
      notional: 1000000,
      initialValue: 0.0,  // Floating rate (seller pays floating)
      index: "NZD-LIBOR-3M",  // Floating rate index
      dayCount: "Act/360"
      ),
      receiveLeg: new TradeLegDetails(
      type: CalculationType.FIXED,
      isOffshore: false,
      currency: Currency.parse("NZD"),
      notional: 1000000,
      initialValue: 0.05,  // Fixed rate (seller receives fixed)
      dayCount: "Act/360"
      )
      )
      )

    when:
    def resultFromGenericCriteria = PortfolioItemDiscountingGroupSpecificationMapper.fromGenericPortfolioItemCriteria(criteria)
    def resultFromPortfolioItem = PortfolioItemDiscountingGroupSpecificationMapper.fromPortfolioItem(portfolioItem)

    then:
    resultFromGenericCriteria instanceof FraPortfolioItemDiscountingGroupSpecification
    def fraResult = resultFromGenericCriteria as FraPortfolioItemDiscountingGroupSpecification
    fraResult.productType() == CoreProductType.FRA
    fraResult.tradeCurrency() == Currency.parse("NZD")
    fraResult.getReceiveLegType() == CalculationType.FIXED  // Seller receives fixed rate
    !fraResult.isOffshore()
    fraResult.getCsaDiscountingGroup() == "Group1"
    fraResult.currencyPair() == null

    resultFromPortfolioItem == resultFromGenericCriteria
  }
}
