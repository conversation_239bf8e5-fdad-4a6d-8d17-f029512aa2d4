package com.solum.xplain.calculation.discounting.currency.type

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import java.time.LocalDate
import spock.lang.Specification

class SwapCurrencyResolverTest extends Specification {
  def RESOLVER = new SwapCurrencyResolver()

  def "should return correct product type"() {
    expect:
    RESOLVER.productTypes() == [CoreProductType.IRS, CoreProductType.INFLATION, CoreProductType.SWAPTION]
  }

  def "should resolve INFLATION currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.inflationSwap(LocalDate.now()), null) == Currency.EUR
  }

  def "should resolve Swaption currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.swaptionTradeDetails(LocalDate.now()), null) == Currency.EUR
  }

  def "should resolve IRS currency"() {
    expect:
    RESOLVER.resolveCurrency(TradeDetailsBuilder.overnightFixedSwap(LocalDate.now()), null) == Currency.USD
  }
}
