package com.solum.xplain.calculation.discounting.tradeinfosubsets

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import spock.lang.Specification

class FxFwdPortfolioItemDiscountingGroupSpecificationTest extends Specification {
  def "test resolveCurrency"() {
    given:
    def payLegCurrency = "EUR"
    def receiveLegCurrency = "USD"
    def tradeCurrency = "USD"
    def csaDiscountingGroup = "CSA_GROUP"
    def specification = new FxFwdPortfolioItemDiscountingGroupSpecification(
      tradeCurrency, payLegCurrency, receiveLegCurrency, csaDiscountingGroup)
    def productSettingsResolver = Mock(ProductSettingsResolver)
    def currencyPair = CurrencyPair.of(Currency.EUR, Currency.USD)

    when:
    productSettingsResolver.resolveFxCcy(currencyPair) >> Currency.USD
    def result = specification.resolveCurrency(productSettingsResolver)

    then:
    result == Currency.USD
  }
}
