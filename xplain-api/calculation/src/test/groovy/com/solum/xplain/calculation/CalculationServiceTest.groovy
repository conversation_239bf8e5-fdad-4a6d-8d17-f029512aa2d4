package com.solum.xplain.calculation

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calculation.events.TradesCalculationRequestedEvent
import com.solum.xplain.calculation.form.PortfolioCalculationForm
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.calculation.value.CalculationOptions
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.users.AuditUser
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.domain.AuditorAware
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class CalculationServiceTest extends Specification {
  private static def CURVE_DATE = LocalDate.of(2021, 01, 01)
  private static def VALUATION_DATE = LocalDate.of(2021, 01, 01)
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 02))

  PortfolioRepository portfolioRepository = Mock()
  AuthenticationContext userRepository = Mock()
  AuditEntryService auditEntryService = Mock()
  PortfolioCalculationDataService calculationDataService = Mock()
  ApplicationEventPublisher eventPublisher = Mock()
  CalculationPortfolioValidator calculationPortfolioValidator = Mock()
  AuditorAware<AuditUser> auditorAware = Mock()
  CalculationService service = new CalculationService(
  portfolioRepository,
  userRepository,
  auditEntryService,
  calculationDataService,
  calculationPortfolioValidator,
  eventPublisher,
  auditorAware
  )

  def "should calculate portfolio"() {
    setup:
    def objectId = ObjectId.get()
    def calculationId = ObjectId.get()
    def currentAuditUser = new AuditUser()
    CalculationOptions options = Mock(CalculationOptions)
    options.getStateDate() >> STATE_DATE
    options.getReportingCcy() >> Currency.EUR
    options.getDiscounting() >> new CalculationDiscounting(LOCAL_CURRENCY, OIS, USD, false)
    PortfolioCalculationForm form = Mock(PortfolioCalculationForm)
    form.getStateDate() >> STATE_DATE.actualDate
    form.getCurveDate() >> CURVE_DATE
    form.getValuationDate() >> VALUATION_DATE
    def portfolioView = new PortfolioView(id: objectId.toHexString(), allowAllTeams: true)
    def portfolioCalculationData = Mock(PortfolioCalculationData)
    1 * portfolioCalculationData.getCalculationId() >> calculationId
    1 * auditorAware.getCurrentAuditor() >> Optional.of(currentAuditUser)
    1 * userRepository.userEither(_) >> right(user("id"))
    1 * portfolioRepository.getEither(objectId.toHexString()) >> right(portfolioView)
    1 * calculationDataService.portfolioCalculationData(
      portfolioView,
      VALUATION_DATE,
      CURVE_DATE,
      STATE_DATE,
      form) >> right(portfolioCalculationData)
    1 * calculationPortfolioValidator.validatePortfolio(portfolioView, STATE_DATE) >> right(portfolioView)
    1 * eventPublisher.publishEvent({
      it instanceof TradesCalculationRequestedEvent &&
        ((TradesCalculationRequestedEvent) it).getCalculationId() == calculationId &&
        ((TradesCalculationRequestedEvent) it).getCalculationData() == portfolioCalculationData &&
        ((TradesCalculationRequestedEvent) it).getCurrentUser() == currentAuditUser
    })

    def auth = new TestingAuthenticationToken(user(), null)

    when:
    def result = service.calculateResults(
      auth,
      objectId.toHexString(),
      form,
      STATE_DATE
      )

    then:
    result.isRight()
    result.getOrNull().id == calculationId.toHexString()
  }
}
