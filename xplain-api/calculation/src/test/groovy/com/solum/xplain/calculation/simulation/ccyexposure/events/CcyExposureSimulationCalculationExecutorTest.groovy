package com.solum.xplain.calculation.simulation.ccyexposure.events

import static com.opengamma.strata.basics.currency.Currency.EUR

import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.market.ShiftType
import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.PortfolioCalculationData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuildersHolder
import com.solum.xplain.calculation.integration.CalculationRequestProducer
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.simulation.ccyexposure.data.CcyExposureSimulationCalculationData
import com.solum.xplain.calculation.simulation.ccyexposure.entity.CcyExposureSimulationCalculation
import com.solum.xplain.calculation.simulation.ccyexposure.repository.CcyExposureSimulationCalculationRepository
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftForm
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.value.TradeCalculationRequest
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.simulations.SimulationType
import java.time.LocalDate
import java.util.function.UnaryOperator
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.springframework.data.domain.AuditorAware
import reactor.core.publisher.Flux
import spock.lang.Specification

class CcyExposureSimulationCalculationExecutorTest extends Specification {
  def VALUATION_DATE = LocalDate.now()

  CalculationTradeCountHolder calculationTradeCountHolder = Mock()
  CcyExposureSimulationCalculationRepository simulationCalculationRepository = Mock()
  CalibrationCacheService cacheService = Mock()
  CalculationRequestProducer requestProducer = Mock()
  AuditEntryService auditEntryService = Mock()
  AuditorAware<AuditUser> auditUserAuditorAware = Mock()
  CalculationResultRepository calculationResultRepository = Mock()

  CcyExposureSimulationCalculationExecutor executor = new CcyExposureSimulationCalculationExecutor(
  calculationTradeCountHolder,
  simulationCalculationRepository,
  cacheService,
  requestProducer,
  auditEntryService,
  auditUserAuditorAware,
  calculationResultRepository
  )

  def "should process simulation requested event"() {
    setup:
    def calculationData = Mock(CalculationCurveGroupData)
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> { Stream.of(new TradeLegDetails(currency: "EUR")) }

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def simulationId = ObjectId.get()
    def user = AuditUser.of(UserBuilder.user())
    def spotShift = spotShift()
    def volShift = volShift()
    def ccyPair = CurrencyPair.parse("EUR/USD")

    auditUserAuditorAware.getCurrentAuditor() >> Optional.of(user)

    def event = Mock(CcyExposureSimulationRequestedEvent)
    event.getName() >> "name"
    event.getValuationDate() >> LocalDate.now()
    event.getCurrentUser() >> user
    event.getSpotShift() >> spotShift
    event.getVolatilityShift() >> volShift
    event.getCurrencyPair() >> ccyPair

    def calculationTrades = Mock(CalculationTrades)
    calculationTrades.tradesCount() >> 1
    calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def curveGroupBuildersHolder = Mock(CalculationCurveGroupDataBuildersHolder)
    curveGroupBuildersHolder.warnings() >> []

    def portfolio = Mock(PortfolioCondensedView)
    portfolio.getExternalPortfolioId() >> "portfolioId"

    def simulationData = Mock(CcyExposureSimulationCalculationData)
    simulationData.getSimulationId() >> simulationId
    simulationData.getCalculationTrades() >> calculationTrades
    simulationData.getCalculationCurveDataHolder() >> curveGroupBuildersHolder
    simulationData.getPortfolio() >> portfolio

    def simulationCalculation = Mock(CcyExposureSimulationCalculation)
    simulationData.performed("name", 9, user, VALUATION_DATE, spotShift, volShift, ccyPair) >> simulationCalculation

    def baseCalcId = ObjectId.get()
    def baseCalculationData = pfCalcData(calculationData, calculationTrades, baseCalcId, portfolio)
    simulationData.baseCalculationData(_ as UnaryOperator<ErrorItem>) >> baseCalculationData
    def request1 = TradeCalculationRequest.newOfSimulation(simulationId.toHexString(), SimulationType.CCY_EXPOSURE, baseCalcId.toHexString(), VALUATION_DATE, EUR, portfolioItem, calculationData)

    // spot
    def spotShift1CalcId = ObjectId.get()
    def spotShift1CalcData = pfCalcData(calculationData, calculationTrades, spotShift1CalcId, portfolio)
    def spotShift2CalcId = ObjectId.get()
    def spotShift2CalcData = pfCalcData(calculationData, calculationTrades, spotShift2CalcId, portfolio)
    def spotShift3CalcId = ObjectId.get()
    def spotShift3CalcData = pfCalcData(calculationData, calculationTrades, spotShift3CalcId, portfolio)
    def spotShift4CalcId = ObjectId.get()
    def spotShift4CalcData = pfCalcData(calculationData, calculationTrades, spotShift4CalcId, portfolio)

    mockSimulationCalculationData(simulationData, ShiftType.ABSOLUTE, ShiftDataType.SPOT, 0.1d, ccyPair, spotShift1CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.ABSOLUTE, ShiftDataType.SPOT, -0.1d, ccyPair, spotShift2CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.ABSOLUTE, ShiftDataType.SPOT, 0.2d, ccyPair, spotShift3CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.ABSOLUTE, ShiftDataType.SPOT, -0.2d, ccyPair, spotShift4CalcData)

    def spotShiftRequest1 = calcReq(simulationId, spotShift1CalcId, portfolioItem, calculationData)
    def spotShiftRequest2 = calcReq(simulationId, spotShift2CalcId, portfolioItem, calculationData)
    def spotShiftRequest3 = calcReq(simulationId, spotShift3CalcId, portfolioItem, calculationData)
    def spotShiftRequest4 = calcReq(simulationId, spotShift4CalcId, portfolioItem, calculationData)

    // vols
    def volShift1CalcId = ObjectId.get()
    def volShift1CalcData = pfCalcData(calculationData, calculationTrades, volShift1CalcId, portfolio)
    def volShift2CalcId = ObjectId.get()
    def volShift2CalcData = pfCalcData(calculationData, calculationTrades, volShift2CalcId, portfolio)
    def volShift3CalcId = ObjectId.get()
    def volShift3CalcData = pfCalcData(calculationData, calculationTrades, volShift3CalcId, portfolio)
    def volShift4CalcId = ObjectId.get()
    def volShift4CalcData = pfCalcData(calculationData, calculationTrades, volShift4CalcId, portfolio)

    mockSimulationCalculationData(simulationData, ShiftType.RELATIVE, ShiftDataType.VOLATILITY, 0.5d, ccyPair, volShift1CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.RELATIVE, ShiftDataType.VOLATILITY, -0.5d, ccyPair, volShift2CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.RELATIVE, ShiftDataType.VOLATILITY, 1.0d, ccyPair, volShift3CalcData)
    mockSimulationCalculationData(simulationData, ShiftType.RELATIVE, ShiftDataType.VOLATILITY, -1.0d, ccyPair, volShift4CalcData)

    def volShiftRequest1 = calcReq(simulationId, volShift1CalcId, portfolioItem, calculationData)
    def volShiftRequest2 = calcReq(simulationId, volShift2CalcId, portfolioItem, calculationData)
    def volShiftRequest3 = calcReq(simulationId, volShift3CalcId, portfolioItem, calculationData)
    def volShiftRequest4 = calcReq(simulationId, volShift4CalcId, portfolioItem, calculationData)

    event.getSimulationData() >> simulationData

    when:
    executor.process(event)

    then:
    9 * calculationResultRepository.saveCalculation(_, _)

    // Simulation request
    1 * calculationTradeCountHolder.registerCalculation(simulationId.toHexString(), 9)
    1 * simulationCalculationRepository.saveSimulation(simulationCalculation)

    // Base calculation
    1 * calculationTradeCountHolder.registerCalculation(baseCalcId.toHexString(), 1)
    1 * cacheService.cacheResults(baseCalcId.toHexString(), baseCalculationData)
    1 * requestProducer.sendRequest(request1)

    // Spot shift calculations
    1 * calculationTradeCountHolder.registerCalculation(spotShift1CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(spotShift1CalcId.toHexString(), spotShift1CalcData)
    1 * requestProducer.sendRequest(spotShiftRequest1)
    1 * calculationTradeCountHolder.registerCalculation(spotShift2CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(spotShift2CalcId.toHexString(), spotShift2CalcData)
    1 * requestProducer.sendRequest(spotShiftRequest2)
    1 * calculationTradeCountHolder.registerCalculation(spotShift3CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(spotShift3CalcId.toHexString(), spotShift3CalcData)
    1 * requestProducer.sendRequest(spotShiftRequest3)
    1 * calculationTradeCountHolder.registerCalculation(spotShift4CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(spotShift4CalcId.toHexString(), spotShift4CalcData)
    1 * requestProducer.sendRequest(spotShiftRequest4)

    // Vol shift calculations
    1 * calculationTradeCountHolder.registerCalculation(volShift1CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(volShift1CalcId.toHexString(), volShift1CalcData)
    1 * requestProducer.sendRequest(volShiftRequest1)
    1 * calculationTradeCountHolder.registerCalculation(volShift2CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(volShift2CalcId.toHexString(), volShift2CalcData)
    1 * requestProducer.sendRequest(volShiftRequest2)
    1 * calculationTradeCountHolder.registerCalculation(volShift3CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(volShift3CalcId.toHexString(), volShift3CalcData)
    1 * requestProducer.sendRequest(volShiftRequest3)
    1 * calculationTradeCountHolder.registerCalculation(volShift4CalcId.toHexString(), 1)
    1 * cacheService.cacheResults(volShift4CalcId.toHexString(), volShift4CalcData)
    1 * requestProducer.sendRequest(volShiftRequest4)
  }

  def "should process simulation with calibration errors"() {
    setup:
    def calculationData = Mock(CalculationCurveGroupData)
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> { Stream.of(new TradeLegDetails(currency: "EUR")) }

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def simulationId = ObjectId.get()
    def user = AuditUser.of(UserBuilder.user())
    def spotShift = spotShift(0)
    def volShift = volShift(0)
    def ccyPair = CurrencyPair.parse("EUR/USD")

    auditUserAuditorAware.getCurrentAuditor() >> Optional.of(user)

    def event = Mock(CcyExposureSimulationRequestedEvent)
    event.getName() >> "name"
    event.getValuationDate() >> LocalDate.now()
    event.getCurrentUser() >> user
    event.getSpotShift() >> spotShift
    event.getVolatilityShift() >> volShift
    event.getCurrencyPair() >> ccyPair

    def calculationTrades = Mock(CalculationTrades)
    calculationTrades.tradesCount() >> 1
    calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def curveGroupBuildersHolder = Mock(CalculationCurveGroupDataBuildersHolder)
    def warnings = [com.solum.xplain.core.error.Error.CALIBRATION_WARNING.entity("WARNING")]
    curveGroupBuildersHolder.warnings() >> warnings

    def portfolio = Mock(PortfolioCondensedView)
    portfolio.getExternalPortfolioId() >> "portfolioId"

    def simulationData = Mock(CcyExposureSimulationCalculationData)
    simulationData.getSimulationId() >> simulationId
    simulationData.getCalculationTrades() >> calculationTrades
    simulationData.getCalculationCurveDataHolder() >> curveGroupBuildersHolder
    simulationData.getPortfolio() >> portfolio

    def simulationCalculation = Mock(CcyExposureSimulationCalculation)
    simulationData.performed("name", 1, user, VALUATION_DATE, spotShift, volShift, ccyPair) >> simulationCalculation

    def baseCalcId = ObjectId.get()
    def baseCalculationData = pfCalcData(calculationData, calculationTrades, baseCalcId, portfolio)
    simulationData.baseCalculationData(_ as UnaryOperator<ErrorItem>) >> baseCalculationData
    def request1 = TradeCalculationRequest.newOfSimulation(simulationId.toHexString(), SimulationType.CCY_EXPOSURE, baseCalcId.toHexString(), VALUATION_DATE, EUR, portfolioItem, calculationData)

    event.getSimulationData() >> simulationData

    when:
    executor.process(event)

    then:
    // Simulation request
    1 * calculationTradeCountHolder.registerCalculation(simulationId.toHexString(), 1)
    1 * simulationCalculationRepository.saveSimulation(simulationCalculation)

    // Base calculation
    1 * calculationTradeCountHolder.registerCalculation(baseCalcId.toHexString(), 1)
    1 * cacheService.cacheResults(baseCalcId.toHexString(), baseCalculationData)
    1 * requestProducer.sendRequest(request1)
    1 * auditEntryService.newEntryWithLogs(_, _) >> { List<?> Args ->
      def entry = (AuditEntry) Args[0]
      def logs = (List<ErrorItem>) Args[1]

      assert entry.description == "Portfolio portfolioId simulation calculation completed"
      assert entry.collectionName == "calculationResult"

      assert logs.size() == 1
      assert logs[0].description == "WARNING"
      assert logs[0].reason == com.solum.xplain.core.error.Error.CALIBRATION_WARNING
    }
  }

  def spotShift(int numberShifts = 2) {
    def f = new ShiftForm()
    f.numberShifts = numberShifts
    f.shiftSize = 0.1d
    f.shiftType = ShiftType.ABSOLUTE
    return f
  }

  def volShift(int numberShifts = 2) {
    def f = new ShiftForm()
    f.numberShifts = numberShifts
    f.shiftSize = 0.5d
    f.shiftType = ShiftType.RELATIVE
    return f
  }

  def pfCalcData(CalculationCurveGroupData calculationData, CalculationTrades calculationTrades, ObjectId calcId, PortfolioCondensedView portfolioCondensedView){
    def portfolioCalcData = Mock(PortfolioCalculationData)
    portfolioCalcData.reportingCcy() >> EUR
    portfolioCalcData.getCalculationId() >> calcId
    portfolioCalcData.result(CoreProductType.IRS) >> calculationData
    portfolioCalcData.getCalculationTrades() >> calculationTrades
    portfolioCalcData.valuationDate() >> VALUATION_DATE
    portfolioCalcData.performed(_ as AuditUser, 1) >> new CalculationResult()
    portfolioCalcData.getPortfolio() >> portfolioCondensedView
    return portfolioCalcData
  }

  def mockSimulationCalculationData(CcyExposureSimulationCalculationData simulationData,
    ShiftType shiftType, ShiftDataType shiftDataType, Double shiftSize, CurrencyPair currencyPair, PortfolioCalculationData result
  ) {
    simulationData.calculationData(
      _ as UnaryOperator<ErrorItem>,
      shiftType,
      shiftDataType,
      shiftSize,
      currencyPair
      ) >> result
  }

  def calcReq(ObjectId simulationId, ObjectId spotShiftCalcId, PortfolioItem portfolioItem, CalculationCurveGroupData calculationData) {
    TradeCalculationRequest.newOfSimulation(
      simulationId.toHexString(),
      SimulationType.CCY_EXPOSURE,
      spotShiftCalcId.toHexString(),
      VALUATION_DATE,
      EUR,
      portfolioItem,
      calculationData
      )
  }
}
