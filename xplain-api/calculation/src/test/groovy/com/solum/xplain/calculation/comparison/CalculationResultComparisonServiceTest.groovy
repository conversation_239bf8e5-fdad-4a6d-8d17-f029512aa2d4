package com.solum.xplain.calculation.comparison


import static com.solum.xplain.calibration.curve.charts.ChartDateType.ACTUAL_DATE
import static com.solum.xplain.core.error.Error.COMPARISON_ERROR
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.FxRateProvider
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.solum.xplain.calculation.CalculationPortfolioItemViewBuilder
import com.solum.xplain.calculation.comparison.entity.ComparisonResult
import com.solum.xplain.calculation.comparison.entity.ComparisonResultItem
import com.solum.xplain.calculation.comparison.repository.CalculationComparisonRepository
import com.solum.xplain.calculation.curves.CalculationChartGenerator
import com.solum.xplain.calculation.curves.value.CalculationResultChartDataView
import com.solum.xplain.calculation.curves.value.CalculationResultsChartsView
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.value.CalculationResultView
import com.solum.xplain.core.common.value.ChartPoint
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import spock.lang.Specification
import spock.lang.Unroll

class CalculationResultComparisonServiceTest extends Specification {

  CalculationResultRepository resultRepository = Mock()
  ComparisonFxRateProvider fxRateProvider = Mock()
  CalculationComparisonRepository comparisonRepository = Mock()
  CalculationChartGenerator calculationChartGenerator = Mock()
  CalculationResultComparisonService service = new CalculationResultComparisonService(resultRepository, comparisonRepository, fxRateProvider, calculationChartGenerator)

  def "should compare items"() {
    setup:
    def calculationResultId = ObjectId.get()
    var resultItem = CalculationPortfolioItemViewBuilder.ofMetrics()
    1 * resultRepository.calculationItems(calculationResultId.toString()) >> Flux.fromIterable([resultItem])
    1 * resultRepository.calculationItems(calculationResultId.toString(), [resultItem.getTradeId()]) >> Flux
    .fromIterable([resultItem])
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.right(new FxRateProvider() {
        @Override
        double fxRate(Currency baseCurrency, Currency counterCurrency) {
          return 10
        }
      }
      )
    calculationChartGenerator.generateAllCharts(calculationResultId.toString(), ACTUAL_DATE) >> new CalculationResultsChartsView(
      curveConfigurationChartData: [],
      fxCurveConfigurationChartDataView: []
      )
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compare(Currency.of("EUR"), view, view)
    result.isRight()
    then:
    1 * comparisonRepository.saveComparison(_ as List<ComparisonResultItem>) >> { List<?> items ->
      def results = items[0] as List<ComparisonResultItem>
      assert results.size() == 1
      assert results[0].view.metricsPVN1DiffPv == 0
      assert results[0].view.metricsPVN1DiffPv == 0
      return Mono.empty()
    }

    1 * comparisonRepository.saveComparisonResult(_ as ComparisonResult) >> { ComparisonResult r ->
      r.comparisonCurrency == "EUR"
      r.calculationId == calculationResultId.toString()
      r.against1CalculationId == calculationResultId.toString()
    }
  }

  def "should compare items with same currency"() {
    setup:
    def calculationResultId = ObjectId.get()
    var resultItem = CalculationPortfolioItemViewBuilder.ofMetrics()
    1 * resultRepository.calculationItems(calculationResultId.toString()) >> Flux.fromIterable([resultItem])
    1 * resultRepository.calculationItems(calculationResultId.toString(), [resultItem.getTradeId()]) >> Flux
    .fromIterable([resultItem])
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.right(FxRateProvider.minimal())
    calculationChartGenerator.generateAllCharts(calculationResultId.toString(), ACTUAL_DATE) >> new CalculationResultsChartsView(
      curveConfigurationChartData: [],
      fxCurveConfigurationChartDataView: []
      )
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compare(Currency.of("USD"), view, view)
    result.isRight()
    then:
    1 * comparisonRepository.saveComparison(_ as List<ComparisonResultItem>) >> { List<?> items ->
      def results = items[0] as List<ComparisonResultItem>
      assert results.size() == 1
      assert results[0].view.metricsPVN1DiffPv == 0
      assert results[0].view.metricsPVN1DiffPv == 0
      return Mono.empty()
    }

    1 * comparisonRepository.saveComparisonResult(_ as ComparisonResult) >> { ComparisonResult r ->
      r.comparisonCurrency == "USD"
      r.calculationId == calculationResultId.toString()
      r.against1CalculationId == calculationResultId.toString()
    }
  }

  @Unroll
  def "should compare items with difference chart where maturity #maturity1 and #maturity2"() {
    setup:
    def calculationResultId = ObjectId.get()
    var resultItem = CalculationPortfolioItemViewBuilder.ofMetrics()
    1 * resultRepository.calculationItems(calculationResultId.toString()) >> Flux.fromIterable([resultItem])
    1 * resultRepository.calculationItems(calculationResultId.toString(), [resultItem.getTradeId()]) >> Flux
    .fromIterable([resultItem])
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.right(FxRateProvider.minimal())

    calculationChartGenerator.generateAllCharts(calculationResultId.toString(), ACTUAL_DATE) >> new CalculationResultsChartsView(
      curveConfigurationChartData: [
        new CalculationResultChartDataView(
        indexName: "test",
        chartPoints: [
          new ChartPoint(LocalDate.now(), 1d, maturity1),
          new ChartPoint(LocalDate.now(), 1d, maturity2)
        ]
        )
      ],
      )
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compare(Currency.of("USD"), view, view)
    result.isRight()
    then:
    1 * comparisonRepository.saveComparison(_ as List<ComparisonResultItem>) >> { List<?> items ->
      def results = items[0] as List<ComparisonResultItem>
      assert results.size() == 1
      assert results[0].view.metricsPVN1DiffPv == 0
      assert results[0].view.metricsPVN1DiffPv == 0
      return Mono.empty()
    }

    1 * comparisonRepository.saveComparisonResult(_ as ComparisonResult) >> { ComparisonResult r ->
      r.comparisonCurrency == "USD"
      r.calculationId == calculationResultId.toString()
      r.against1CalculationId == calculationResultId.toString()
      r.curveConfigComparisonDifferenceChartData.size() == 1
      r.curveConfigComparisonDifferenceChartData[0].chartPoints.size() == pointsCount
    }
    where:
    maturity1 | maturity2 | pointsCount
    null      | null      | 0
    0.1       | null      | 1
    0.1       | 0.2       | 2
    0.1       | 0.1       | 1
  }

  def "should throw error when missing FX rate"() {
    setup:
    def calculationResultId = ObjectId.get()
    var resultItem = CalculationPortfolioItemViewBuilder.ofMetrics()
    1 * resultRepository.calculationItems(calculationResultId.toString()) >> Flux.fromIterable([resultItem])
    1 * resultRepository.calculationItems(calculationResultId.toString(), [resultItem.getTradeId()]) >> Flux
    .fromIterable([resultItem])
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.right(MarketDataFxRateProvider.of(MarketData.empty(LocalDate.now())))
    calculationChartGenerator.generateAllCharts(calculationResultId.toString(), ACTUAL_DATE) >> new CalculationResultsChartsView(curveConfigurationChartData: [],)
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compare(Currency.of("EUR"), view, view)
    result.isRight()
    then:
    result.isLeft()
    def errorItem = result.left().get() as ErrorItem
    errorItem.reason == COMPARISON_ERROR
    errorItem.description == "No FX rate market data for USD/EUR"
  }


  def "should throw error when missing FX rate provider"() {
    setup:
    def calculationResultId = ObjectId.get()
    1 * fxRateProvider.fxRateProvider(_, _) >> Either.left(OBJECT_NOT_FOUND.entity("ERR"))
    def view = new CalculationResultView(
      id: calculationResultId.toString(),
      curveConfigurationId: "confId",
      marketDataGroupId: "marketId",
      marketDataSourceType: MarketDataSourceType.OVERLAY,
      valuationDate: LocalDate.now(),
      stateDate: LocalDate.now(),
      curveDate: LocalDate.now(),
      currency: Currency.of("USD")
      )
    when:
    def result = service.compare(Currency.of("EUR"), view, view)
    result.isRight()
    then:
    result.isLeft()
    def errorItem = result.left().get() as ErrorItem
    errorItem.reason == OBJECT_NOT_FOUND
    errorItem.description == "ERR"
  }
}
