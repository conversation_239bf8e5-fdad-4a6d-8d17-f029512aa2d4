package com.solum.xplain.calculation.integration


import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.core.portfolio.value.ValuationStatus
import com.solum.xplain.valuation.messages.metrics.CashFlowValueDto
import com.solum.xplain.valuation.messages.metrics.ValuationMetricsDto
import java.time.LocalDate
import org.bson.BsonBinaryWriter
import org.bson.BsonDateTime
import org.bson.BsonObjectId
import org.bson.BsonWriter
import org.bson.codecs.Encoder
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.conversions.Bson
import org.bson.io.BasicOutputBuffer
import org.bson.types.ObjectId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ValuationResponseMapperTest extends IntegrationSpecification {
  @Autowired
  ValuationResponseMapper mapper

  def "should correctly map response"() {
    setup:
    def mapped = mapper.fromValuationResponse(ValuationResponseBuilder.valuationResponse())

    expect:
    mapped.tradeDetails.payLeg.type == CalculationType.FIXED
    mapped.tradeDetails.receiveLeg.type == CalculationType.IBOR
    mapped.tradeDetails.receiveLeg.isOffshore == null
    mapped.productType == CoreProductType.IRS
    mapped.valuationError == "error"
    mapped.tradeInfoExternalIdentifiers.size() == 1
    mapped.tradeInfoExternalIdentifiers.get(0).identifier == "ExtIdentifier"
    mapped.tradeInfoExternalIdentifiers.get(0).externalSourceId == "ExtSource"
    mapped.tradeInfoCustomFields.size() == 1
    mapped.tradeInfoCustomFields.get(0).externalFieldId == "ExtField"
    mapped.tradeInfoCustomFields.get(0).value == "value"

    when:
    def offshoreResult = ValuationResponseBuilder.valuationResponse()
    offshoreResult.tradeDetails.receiveLeg.isOffshore = true
    mapped = mapper.fromValuationResponse(offshoreResult)

    then:
    mapped.tradeDetails.payLeg.type == CalculationType.FIXED
    mapped.tradeDetails.receiveLeg.type == CalculationType.IBOR
    mapped.tradeDetails.receiveLeg.isOffshore
  }

  def "should correctly map response to Document"() {
    setup:
    def vr = ValuationResponseBuilder.valuationResponse()
    def portfolioId = ObjectId.get().toHexString()
    vr.portfolioId = portfolioId
    def externalTradeId = "tradeId"
    vr.externalTradeId = externalTradeId

    def metricsBytes = metricsBytes()
    vr.setValuationMetricsBytes(metricsBytes)

    when:
    def mapped = mapper.fromValuationResponseToBasicDBObject(vr)

    then:
    def tradeDetails = (TradeDetails) mapped.get("tradeDetails")
    tradeDetails.payLeg.type == CalculationType.FIXED
    tradeDetails.receiveLeg.type == CalculationType.IBOR
    tradeDetails.receiveLeg.isOffshore == null

    tradeDetails.startDate instanceof LocalDate
    tradeDetails.startDate.equals(LocalDate.parse("2018-04-16"))

    mapped.get("calculationResultId") instanceof BsonObjectId
    mapped.get("portfolioId") == portfolioId
    mapped.get("tradeId") == "entityId"
    mapped.get("externalTradeId") == externalTradeId
    mapped.get("productType") == CoreProductType.IRS
    mapped.get("valuationStatus") == ValuationStatus.ERROR
    mapped.get("valuationError") == "error"
    mapped.get("underlying") == "EUR 3M"
    mapped.get("notional") == 1.0
    mapped.get("currencies") == "EUR"
    mapped.get("discountingCcy") == null
    mapped.get("description") == null

    mapped.get("metrics").get("cashFlows").size() == 2
    mapped.get("metrics").get("cashFlows").get(0).get("paymentDate") instanceof BsonDateTime
    mapped.get("metrics").get("cashFlows").get(1).get("paymentDate") instanceof BsonDateTime
  }

  def cashflow(LocalDate paymentDate) {
    def cfv = new CashFlowValueDto()
    cfv.setPaymentDate(paymentDate)
    return cfv
  }

  def metricsBytes() {
    def metrics = new ValuationMetricsDto()
    metrics.setCashFlows([cashflow(LocalDate.parse("2018-04-16")), cashflow(LocalDate.parse("2018-04-17"))])

    def codecRegistry = CodecRegistries.fromRegistries(
      Bson.DEFAULT_CODEC_REGISTRY,
      CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()))

    BasicOutputBuffer bsonOutput = new BasicOutputBuffer()
    BsonWriter writer = new BsonBinaryWriter(bsonOutput)
    Encoder<ValuationMetricsDto> encoder = codecRegistry.get(ValuationMetricsDto.class)
    encoder.encode(writer, metrics, EncoderContext.builder().build())
    return bsonOutput.toByteArray()
  }
}
