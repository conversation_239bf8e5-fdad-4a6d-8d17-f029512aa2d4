package com.solum.xplain.calculation.simulation.daterange.events

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem.ofCalibrationError

import com.solum.xplain.calculation.PortfolioCalculationData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.integration.CalculationRequestProducer
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService
import com.solum.xplain.calculation.simulation.daterange.data.DateRangeSimulationCalculationData
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationCalculation
import com.solum.xplain.calculation.simulation.daterange.repository.DateRangeSimulationCalculationRepository
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.value.TradeCalculationRequest
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeLegDetails
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.simulations.SimulationType
import java.time.LocalDate
import java.util.function.UnaryOperator
import java.util.stream.Stream
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import spock.lang.Specification

class DateRangeSimulationCalculationExecutorTest extends Specification {
  def VALUATION_START_DATE = LocalDate.now()
  def VALUATION_END_DATE = LocalDate.now().plusDays(1)
  def CALCULATION_ID_START = ObjectId.get()
  def CALCULATION_ID_END = ObjectId.get()

  CalculationTradeCountHolder calculationTradeCountHolder = Mock()
  DateRangeSimulationCalculationRepository simulationCalculationRepository = Mock()
  CalibrationCacheService cacheService = Mock()
  CalculationRequestProducer requestProducer = Mock()

  DateRangeSimulationCalculationExecutor executor = new DateRangeSimulationCalculationExecutor(calculationTradeCountHolder, simulationCalculationRepository, cacheService, requestProducer)

  def "should process simulation requested event"() {
    setup:
    def calculationData = Mock(CalculationCurveGroupData)
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> { Stream.of(new TradeLegDetails(currency: "EUR")) }

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def simulationId = ObjectId.get()
    def user = AuditUser.of(UserBuilder.user())

    def event = Mock(DateRangeSimulationRequestedEvent)
    event.getName() >> "name"
    event.getValuationStartDate() >> LocalDate.now()
    event.getValuationEndDate() >> LocalDate.now().plusDays(1)
    event.getCurrentUser() >> user

    def calculationTrades = Mock(CalculationTrades)
    calculationTrades.tradesCount() >> 1
    calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def simulationData = Mock(DateRangeSimulationCalculationData)
    simulationData.getSimulationId() >> simulationId
    simulationData.getCalculationTrades() >> calculationTrades

    def simulationCalculation = Mock(DateRangeSimulationCalculation)
    simulationData.performed("name", 2, user, VALUATION_START_DATE, VALUATION_END_DATE) >> simulationCalculation

    def portfolioStartData = Mock(PortfolioCalculationData)
    portfolioStartData.reportingCcy() >> EUR
    portfolioStartData.getCalculationId() >> CALCULATION_ID_START
    portfolioStartData.result(CoreProductType.IRS) >> calculationData
    portfolioStartData.getCalculationTrades() >> calculationTrades
    portfolioStartData.valuationDate() >> VALUATION_START_DATE
    simulationData.calculationData(VALUATION_START_DATE, _ as UnaryOperator<ErrorItem>) >> portfolioStartData
    def request1 = TradeCalculationRequest.newOfSimulation(simulationId.toHexString(), SimulationType.DATE_RANGE, CALCULATION_ID_START.toHexString(), VALUATION_START_DATE, EUR, portfolioItem, calculationData)

    def portfolioEndData = Mock(PortfolioCalculationData)
    portfolioEndData.reportingCcy() >> EUR
    portfolioEndData.getCalculationId() >> CALCULATION_ID_END
    portfolioEndData.result(CoreProductType.IRS) >> calculationData
    portfolioEndData.getCalculationTrades() >> calculationTrades
    portfolioEndData.valuationDate() >> VALUATION_END_DATE

    simulationData.calculationData(VALUATION_END_DATE, _ as UnaryOperator<ErrorItem>) >> portfolioEndData
    def request2 = TradeCalculationRequest.newOfSimulation(simulationId.toHexString(), SimulationType.DATE_RANGE, CALCULATION_ID_END.toHexString(), VALUATION_END_DATE, EUR, portfolioItem, calculationData)

    event.getSimulationData() >> simulationData

    when:
    executor.process(event)

    then:
    // Simulation request
    1 * calculationTradeCountHolder.registerCalculation(simulationId.toHexString(), 2)
    1 * simulationCalculationRepository.saveSimulation(simulationCalculation)

    // Daily calculations
    1 * calculationTradeCountHolder.registerCalculation(CALCULATION_ID_START.toHexString(), 1)
    1 * cacheService.cacheResults(CALCULATION_ID_START.toHexString(), portfolioStartData)
    1 * requestProducer.sendRequest(request1)
    1 * simulationCalculationRepository.saveSimulationErrors(simulationId.toHexString(), [])

    1 * calculationTradeCountHolder.registerCalculation(CALCULATION_ID_END.toHexString(), 1)
    1 * cacheService.cacheResults(CALCULATION_ID_END.toHexString(), portfolioEndData)
    1 * requestProducer.sendRequest(request2)
    1 * simulationCalculationRepository.saveSimulationErrors(simulationId.toHexString(), [])
  }

  def "should process simulation with calibration errors"() {
    setup:
    def calculationData = Mock(CalculationCurveGroupData)
    def tradeDetails = Mock(TradeDetails)
    tradeDetails.legsStream() >> { Stream.of(new TradeLegDetails(currency: "EUR")) }

    def portfolioItem = Mock(PortfolioItem)
    portfolioItem.productType >> CoreProductType.IRS
    portfolioItem.tradeDetails >> tradeDetails

    def simulationId = ObjectId.get()
    def user = AuditUser.of(UserBuilder.user())

    def event = Mock(DateRangeSimulationRequestedEvent)
    event.getName() >> "name"
    event.getValuationStartDate() >> VALUATION_START_DATE
    event.getValuationEndDate() >> VALUATION_START_DATE
    event.getCurrentUser() >> user

    def calculationTrades = Mock(CalculationTrades)
    calculationTrades.tradesCount() >> 1
    calculationTrades.tradesFlux() >> Flux.just(portfolioItem)

    def simulationData = Mock(DateRangeSimulationCalculationData)
    simulationData.getSimulationId() >> simulationId
    simulationData.getCalculationTrades() >> calculationTrades

    def simulationCalculation = Mock(DateRangeSimulationCalculation)
    simulationData.performed("name", 1, user, VALUATION_START_DATE, VALUATION_START_DATE) >> simulationCalculation

    def portfolioStartData = Mock(PortfolioCalculationData)
    portfolioStartData.reportingCcy() >> EUR
    portfolioStartData.getCalculationId() >> CALCULATION_ID_START
    portfolioStartData.result(CoreProductType.IRS) >> calculationData
    portfolioStartData.getCalculationTrades() >> calculationTrades
    portfolioStartData.valuationDate() >> VALUATION_START_DATE
    simulationData.calculationData(VALUATION_START_DATE, _ as UnaryOperator<ErrorItem>) >> { LocalDate date, UnaryOperator<ErrorItem> errorsConsumer ->
      errorsConsumer.apply(Error.CALIBRATION_WARNING.entity("WARNING"))
      errorsConsumer.apply(Error.CALIBRATION_ERROR.entity("ERROR"))
      return portfolioStartData
    }
    def request1 = TradeCalculationRequest.newOfSimulation(simulationId.toHexString(), SimulationType.DATE_RANGE, CALCULATION_ID_START.toHexString(), VALUATION_START_DATE, EUR, portfolioItem, calculationData)


    event.getSimulationData() >> simulationData

    when:
    executor.process(event)

    then:
    // Simulation request
    1 * calculationTradeCountHolder.registerCalculation(simulationId.toHexString(), 1)
    1 * simulationCalculationRepository.saveSimulation(simulationCalculation)

    // Daily calculations
    1 * calculationTradeCountHolder.registerCalculation(CALCULATION_ID_START.toHexString(), 1)
    1 * cacheService.cacheResults(CALCULATION_ID_START.toHexString(), portfolioStartData)
    1 * requestProducer.sendRequest(request1)
    1 * simulationCalculationRepository.saveSimulationErrors(simulationId.toHexString(), [ofCalibrationError(simulationId.toHexString(), VALUATION_START_DATE, "ERROR")])
  }
}
