package com.solum.xplain.xva.integration.csv

import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.trade

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.extensions.enums.CallPutType
import java.time.LocalDate
import spock.lang.Specification

class XvaTradesCsvMapperTest extends Specification {
  def mapper = new XvaTradesCsvMapper()


  def "should return csv row for fra  trade"() {
    setup:
    def item = trade(CoreProductType.FRA, TradeDetailsBuilder.fraDetails(LocalDate.parse("2017-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write().trim()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FRA,IRS_COUNTER_PARTY,2018-06-22,2018-09-22,,EUR,EUR,,10000000,-10000000,,,,0.0,IBOR,0.025,Fixed,,,,,,A/360,A/360,,,Mod Foll,Mod Foll,,,,,,,,,,,,,,,ISDA,,,,"""
  }

  def "should return csv row for irs swap trade"() {
    setup:
    def item = trade(CoreProductType.IRS, TradeDetailsBuilder.irsDetails(LocalDate.parse("2017-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,InterestRateSwap,IRS_COUNTER_PARTY,2018-06-22,2026-06-22,,EUR,EUR,,10000000,-10000000,,,,0.0,IBOR,0.025,Fixed,,4,1,,,A/360,30/360,,,Mod Foll,Mod Foll,,,,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row for xccy swap trade"() {
    setup:
    def item = trade(CoreProductType.XCCY, TradeDetailsBuilder.xccySwap(LocalDate.parse("2017-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,CrossCurrencySwap,,2017-12-22,2025-12-22,,USD,EUR,,11000000,-10000000,,,,0.0,IBOR,0.0,IBOR,,4,4,,,A/360,A/360,,,Mod Foll,Mod Foll,,,,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row for fxforward trade"() {
    setup:
    def item = trade(CoreProductType.FXFWD, TradeDetailsBuilder.fxSingle(LocalDate.parse("2018-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FxForward,FXFWD_COUNTERPARTY,,2018-12-20,,EUR,USD,,10000,-11000,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row for swaption trade"() {
    setup:
    def item = trade(CoreProductType.SWAPTION, TradeDetailsBuilder.swaptionTradeDetails(LocalDate.parse("2017-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,Swaption,,2018-06-22,2026-06-22,EUR,,,-10000000,,,,,,,,,,0.025,,,1,4,,,30/360,A/360,,,Mod Foll,Mod Foll,TRUE,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row for cap floor trade"() {
    setup:
    def item = trade(CoreProductType.CAP_FLOOR, TradeDetailsBuilder.capFloor(LocalDate.parse("2017-12-20")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,CapFloor,CAPFLOOR_COUNTER_PARTY,2017-12-20,2018-12-20,EUR,,,20,,,,,,,,,,1,,,,4,,,,A/360,,,,None,TRUE,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row fx option trade for CALL"() {
    //BUY
    setup:
    def item = trade(CoreProductType.FXOPT, TradeDetailsBuilder.fxOptionTradeDetails(LocalDate.parse("2020-01-01")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FxOption,FXOPT_COUNTERPARTY,,2021-01-01,,,,10000,,,,,,,,,,1.1,,,,,,,,,,,,,true,,,,,,,,,,EUR,USD,,,,,
"""
  }

  def "should return csv row fx option trade for PUT"() {
    setup:
    def item = trade(CoreProductType.FXOPT, TradeDetailsBuilder.fxOptionTradeDetails(LocalDate.parse("2020-01-01"), CallPutType.PUT))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FxOption,FXOPT_COUNTERPARTY,,2021-01-01,,,,11000,,,,,,,,,,0.9090909090909091,,,,,,,,,,,,,false,,,,,,,,,,EUR,USD,,,,,
"""
  }

  def "should return csv row for fx collar trade for CALL"() {
    setup:
    def item = trade(CoreProductType.FXCOLLAR, TradeDetailsBuilder.fxCollarTradeDetails(LocalDate.parse("2020-01-01")))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FxCollar,FXOPT_COUNTERPARTY,,2021-01-01,,,,10000,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,EUR,USD,,1.2,1.1,TRUE,FALSE
"""
  }

  def "should return csv row for fx collar trade for PUT"() {
    //BUY
    setup:
    def item = trade(CoreProductType.FXCOLLAR, TradeDetailsBuilder.fxCollarTradeDetails(LocalDate.parse("2020-01-01"),CallPutType.PUT))

    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,FxCollar,FXOPT_COUNTERPARTY,,2021-01-01,,,,12000,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,EUR,USD,,0.8333333333333334,0.9166666666666666,FALSE,TRUE
"""
  }

  def "should return csv row for irs trade with 1D freq"() {
    setup:
    def item = trade(CoreProductType.IRS, TradeDetailsBuilder.irsDetails(LocalDate.parse("2017-12-20")))
    item.getTradeDetails().getReceiveLeg().accrualFrequency = "1D"
    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,InterestRateSwap,IRS_COUNTER_PARTY,2018-06-22,2026-06-22,,EUR,EUR,,10000000,-10000000,,,,0.0,IBOR,0.025,Fixed,,0,1,,,A/360,30/360,,,Mod Foll,Mod Foll,,,,,,,,,,,,,,,,,,,
"""
  }

  def "should return csv row for irs trade with overnight leg"() {
    setup:
    def item = trade(CoreProductType.IRS, TradeDetailsBuilder.overnightFixedSwap(LocalDate.parse("2017-12-20")))
    when:
    def result = mapper.toCsvFields(item)
    def csv = new CsvOutputFile(XvaTradesCsvMapper.FIELDS, [result])
    def resultStr = csv.write()

    then:
    resultStr == """TradeID,ValuationFunction,Counterparty,StartDate,EndDate,Currency,ReceiveCurrency,PayCurrency,Notional,ReceiveNotional,PayNotional,ReceiveAmortNotionals,PayAmortNotionals,Coupon,ReceiveCoupon,ReceiveIndex,PayCoupon,PayIndex,Strike,ReceiveFrequency,PayFrequency,FixedFrequency,FloatingFrequency,ReceiveDCT,PayDCT,FixedDCT,FloatingDCT,ReceiveBDC,PayBDC,FixedBDC,FloatingBDC,IsCall,CashflowDates,CashflowAmounts,Dates,Notionals,Strikes,ReceiveNotionals,PayNotionals,ReceiveLegType,PayLegType,BaseCurrency,QuoteCurrency,FRADiscountingMethod,Strike1,Strike2,IsCall1,IsCall2
externalTradeId,InterestRateSwap,IRS_COUNTER_PARTY,2017-12-22,2025-12-22,,USD,USD,,10000000,-10000000,,,,0.025,Fixed,0.025,RFR,,1,1,,,A/360,A/360,,,Mod Foll,Mod Foll,,,,,,,,,,,,,,,,,,,
"""
  }
}
