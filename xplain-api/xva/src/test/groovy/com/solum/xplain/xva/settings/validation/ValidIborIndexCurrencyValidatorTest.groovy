package com.solum.xplain.xva.settings.validation

import com.solum.xplain.xva.settings.entity.XvaLiborIndex
import spock.lang.Specification

class ValidIborIndexCurrencyValidatorTest extends Specification {

  def "should validate currency and ibor index pair in XvaLiborIndex"() {
    when:
    def validator = new ValidIborIndexCurrencyValidator()
    def form = new XvaLiborIndex(currency: currency, index: index)

    then:
    isValid == validator.isValid(form, null)

    where:
    currency  | index           | isValid
    null      | null            | true
    "USD"     | null            | true
    null      | "USD-LIBOR-3M"  | true
    "USD"     | "USD-LIBOR-3M"  | true
    "GBP"     | "USD-LIBOR-3M"  | false
    "USD"     | "GBP-LIBOR-3M"  | false
  }
}
