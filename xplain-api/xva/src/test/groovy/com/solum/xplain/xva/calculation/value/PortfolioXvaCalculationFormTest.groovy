package com.solum.xplain.xva.calculation.value

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import spock.lang.Specification
import spock.lang.Unroll

class PortfolioXvaCalculationFormTest extends Specification {

  @Unroll
  def "should construct correct calculation builder with calculation type #type"() {
    setup:
    def form = new PortfolioXvaCalculationForm()
    form.setCalculationCurrency("EUR")
    form.setMarketDataSource("RAW_PRIMARY")
    form.setCalculationType(type)
    form.setDiscountingType(DISCOUNT_EUR.name())
    form.setTriangulationCcy(USD.getCode())
    form.setCurveConfiguration(new CalculationConfigForm("curveId", null))

    expect:
    form.calculationType() == PortfolioCalculationType.valueOf(type)
    form.configurationType() == CurveConfigurationType.SINGLE
    form.reportingCurrency() == Currency.EUR
    form.calculationConfiguration() == new CalculationConfigForm("curveId", null)
    form.fxCalculationConfiguration() == null
    form.marketDataSource() == MarketDataSourceType.RAW_PRIMARY
    form.calculationDiscounting() == new CalculationDiscounting(DISCOUNT_EUR, OIS, USD, false)

    where:
    type           | _
    "XVA"          | _
    "XVA_EXPOSURE" | _
    "TRADES"       | _
  }

  @Unroll
  def "should return single trade id"() {
    setup:
    def form = new PortfolioXvaCalculationForm()
    form.tradeId = "tradeId"

    expect:
    form.tradeId() == Optional.of("tradeId")
  }
}
