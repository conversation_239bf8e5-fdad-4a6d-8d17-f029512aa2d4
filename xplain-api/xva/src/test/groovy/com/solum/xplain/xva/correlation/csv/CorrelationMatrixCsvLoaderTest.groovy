package com.solum.xplain.xva.correlation.csv

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static org.hamcrest.Matchers.startsWith
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.xva.correlation.value.CorrelationType
import spock.lang.Specification

class CorrelationMatrixCsvLoaderTest extends Specification {
  def loader = new CorrelationMatrixCsvLoader()

  def "should throw error if missing header"() {
    setup:
    def csvString = """\
        EUR,FX,USD,FX,1
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def result = loader.parse(csvBytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    that errors[0].description, startsWith("Missing header:")
  }

  def "should throw error if missing data"() {
    setup:
    def csvString = """\
        ccy1,type1,ccy2,type2,value
        EUR,FX,USD,FX,
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def result = loader.parse(csvBytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().getOrNull() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    that errors[0].description, startsWith("Error parsing line 2. Field value is invalid: No value was found for 'value'")
  }

  def "should validate ccy, type"() {
    setup:
    def csvString = """\
        ccy1,type1,ccy2,type2,value
        QQQ,FX,USD,FX,0
        EUR,QQQ,USD,FX,0
        EUR,FX,USD,FX,2
        222,FX,USD,FX,0
        EUR,FX,EUR,FX,0.1
        EUR,FX,AAA,FX,0
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def result = loader.parse(csvBytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().getOrNull() as List<ErrorItem>
    errors.size() == 6
    errors.reason == [PARSING_ERROR, PARSING_ERROR, PARSING_ERROR, PARSING_ERROR, PARSING_ERROR, PARSING_ERROR]
    errors[0].description == "Error parsing line 2. Field ccy1 is invalid: Unknown currency QQQ"
    errors[1].description == "Error parsing line 3. Field type1 is invalid: Unsupported value: QQQ. Supported values: [IR, FX]"
    errors[2].description == "Error parsing line 4. Field value is invalid: Unsupported value: 2.0. Supported values are between: -1.0 and 1.0"
    errors[3].description == "Error parsing line 5. Field ccy1 is invalid: Unknown currency 222"
    errors[4].description == "Error parsing line 6, value should be 1 for same currency and type, got 0.100000"
    errors[5].description == "Error parsing line 7. Field ccy2 is invalid: Unknown currency AAA"
  }

  def "should return error on duplicate"() {
    setup:
    def csvString = """\
        ccy1,type1,ccy2,type2,value
        USD,FX,EUR,FX,0.1
        EUR,FX,USD,FX,0.2
        USD,FX,EUR,FX,0.1
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def result = loader.parse(csvBytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().getOrNull() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    errors[0].description == "Error parsing lines [2,3,4]: Duplicate entry found: Ccy1: EUR, Type1: FX, Ccy2: USD, Type2: FX"
  }

  def "should limit matrix size to 100 * 100"() {
    setup:
    def csvString = "ccy1,type1,ccy2,type2,value\n"
    for (int i = 0; i < 10004; i++) {
      csvString += "EUR,FX,USD,FX,0.1\n"
    }
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def result = loader.parse(csvBytes, STRICT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].description == "Correlation matrix cannot have more than 100 rows and columns"
  }

  def "should import file"() {
    setup:
    def csvString = """\
        ccy1,type1,ccy2,type2,value
        EUR,IR,EUR,IR,1
        USD, IR, EUR, IR, 0.58154
        USD, FX, EUR, IR, -0.29532
        USD, IR, USD, IR, 1
        USD, FX, USD, IR, -0.01096
        USD, FX, USD, FX, 1
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def parsedData = loader.parse(csvBytes, STRICT)

    then:
    parsedData.isRight()
    def values = parsedData.getOrNull().getParsedLines()
    values.size() == 6
  }

  def "should return correctly parsed CSV rows"() {
    setup:
    def csvString = """\
        ccy1, type1, ccy2, type2, value
        USD, IR, EUR, IR, 0.58154
        USD, FX, EUR, IR, -0.29532
        """.stripIndent()
    def csvBytes = csvString.getBytes("UTF-8")

    when:
    def parsedData = loader.parse(csvBytes, STRICT)

    then:
    parsedData.isRight()
    def result = parsedData.getOrNull().getParsedLines()
    result[0].id.key1.toString() == "USD IR"
    result[0].id.key1.ccy == "USD"
    result[0].id.key1.type == CorrelationType.IR
    result[0].id.key2.ccy == "EUR"
    result[0].id.key2.type == CorrelationType.IR
    result[0].value == 0.58154d

    result[1].id.key2.toString() == "EUR IR"
    result[1].id.key1.ccy == "USD"
    result[1].id.key1.type == CorrelationType.FX
    result[1].id.key2.ccy == "EUR"
    result[1].id.key2.type == CorrelationType.IR
    result[1].value == -0.29532d
  }
}
