package com.solum.xplain.xva.calculation.entity

import com.solum.xplain.xva.proxy.messages.XvaPartyExposureFile
import java.time.LocalDate
import spock.lang.Specification

class XvaPartyExposureTest extends Specification {
  def "should convert from file values"() {
    setup:
    def file = new XvaPartyExposureFile(
      pfeStatus: ["OK"],
      numTrades: [1],
      numWhatIfTrades: [1],
      epe: [10D, 20D],
      anchorDate: [LocalDate.parse("2019-01-01")]
      )

    when:
    def result1 = XvaPartyExposure.from("Party Name", "Party Full Name", file)
    def result2 = XvaPartyExposure.from("Party Name", null, file)

    then:
    result1.pfeStatus == "OK"
    result1.partyName == "Party Name"
    result1.partyFullName == "Party Full Name"
    result1.exposureItems.size() == 2

    result2.partyName == "Party Name"
    result2.partyFullName == "Party Name"
  }
}
