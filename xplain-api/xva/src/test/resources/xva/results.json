{"Trades": {"TradeID": ["qqq"], "ValuationFunction": ["InterestRateSwap"], "Counterparty": ["sss"], "StartDate": ["2018-06-22"], "EndDate": ["2026-06-22"], "Currency": ["EUR"], "ReceiveCurrency": ["EUR"], "PayCurrency": ["EUR"], "Notional": [10000000], "ReceiveNotional": [10000000], "PayNotional": [10000000], "ReceiveAmortNotionals": [null], "PayAmortNotionals": [null], "FixedAmortNotionals": [null], "FloatingAmortNotionals": [null], "Coupon": [0.025], "Margin": [null], "ReceiveCoupon": [null], "PayCoupon": [null], "Strike": [null], "ReceiveFrequency": [4], "PayFrequency": [1], "FixedFrequency": [1], "FloatingFrequency": [4], "ReceiveDCT": [null], "PayDCT": [null], "FixedDCT": ["30U/360"], "FloatingDCT": [null], "ReceiveBDC": ["<PERSON><PERSON>"], "PayBDC": ["<PERSON><PERSON>"], "FixedBDC": [null], "FloatingBDC": [null], "IsCall": [null], "ReceiveIsFixed": [null], "PayIsFixed": [null], "CashflowDates": [null], "CashflowAmounts": [null], "Dates": [null], "Notionals": [null], "Strikes": [null], "ReceiveNotionals": [null], "PayNotionals": [null], "ReceiveLegType": [null], "PayLegType": [null], "_row": ["qqq"]}, "TradeResults": {"TradeID": ["qqq"], "PV": [0], "FundingPV": [0], "CVA": [0], "DVA": [0], "PVStatus": ["#(qqq) #InterestRateSwap (Instruments.R):#FixedLeg (Instruments.R):#DateSchedule (UtilsDate.R):#AdjustDate (UtilsDate.R):missing value where TRUE/FALSE needed!!!!!"], "FundingPVStatus": ["#(qqq) #InterestRateSwap (Instruments.R):#FixedLeg (Instruments.R):#DateSchedule (UtilsDate.R):#AdjustDate (UtilsDate.R):missing value where TRUE/FALSE needed!!!!!"], "CVADVAStatus": ["Not Calculated"], "PFEStatus": ["Not Calculated"], "_row": ["qqq"]}, "PartyResults": {"PartyName": ["BARC_GB_LON"], "PV": [0], "FundingPV": [0], "PVWhatIf": [0], "FundingPVWhatIf": [0], "CVA": [0], "DVA": [0], "FCA_CP": [1], "FBA_CP": [2], "FCA_Self": [3], "FBA_Self": [4], "CVAWhatIf": [0], "DVAWhatIf": [0], "FCAWhatIf_CP": [5], "FBAWhatIf_CP": [6], "FCAWhatIf_Self": [7], "FBAWhatIf_Self": [8], "PVStatus": ["OK"], "FundingPVStatus": ["OK"], "PVWhatIfStatus": ["OK"], "FundingPVWhatIfStatus": ["OK"], "CVADVAStatus": ["Not calculated"], "_row": ["BARC_GB_LON"]}, "TradeExposures": {}, "PartyExposures": {"BARC_GB_LON": {"PFEStatus": ["OK"], "NumTrades": [2], "NumWhatIfTrades": [0], "EPE": [4353163.37624546, 4354147.57789525, 4375], "ENE": [0, 0, -20499.8669155012, -27209.08018478], "PFE": [4353163.37624546, 1737626.68049865, 6223], "EE": [4353163.37624546, 4354147.57789525, 43551], "Time": [0, 0.0833333333333333, 0.16666666666666], "AnchorDate": ["2018-01-16"], "Paths": {}}}, "Model": {"BtPtT": ["function (state = NULL, Model, id, T, funding, FundingSpread, ", "    ZeroForPast) ", "{", "    out <- tryCatch({", "        lambda = Model$lambda[[id]]", "        if (!is.null(state$t)) ", "            t = state$t", "        else t = 0", "        if (is.null(state)) {", "            w = 0", "        }", "        else {", "            if (is.matrix(state$w)) {", "                i = which(id == findIDs(Model))", "                w = state$w[, i]", "            }", "            else {", "                w = state$w", "            }", "        }", "        m = length(T)", "        n = Nstates.xccyHW(state)", "        if (is.null(state)) ", "            out = BtPtT.default(state, Model, id, T, funding, ", "                FundingSpread, ZeroForPast)", "        else {", "            l = (exp(-lambda * T) - exp(-lambda * Model$TStar))/lambda", "            if (id == Model$Numeraire) ", "                compensator = array(0.5 * qvar.xccyHW(Model, ", "                  id, t), n) %o% (l * l)", "            else compensator = array(0.5 * qvar.xccyHW(Model, ", "                id, t), n) %o% (l * l) + array(qirfxcovar.xccyHW(Model, ", "                id, id, t), n) %o% l", "            out = BtPtT.default(state, Model, id, T, funding, ", "                FundingSpread, ZeroForPast) * exp(w %o% l - compensator)", "        }", "        out", "    }, error = function(e) {", "        if (grepl(\"subscript out of bounds\", e$message, TRUE, ", "            TRUE)) {", "            if (!(id %in% names(Model$curve))) {", "                e = list(message = paste0(\"Curve not found: \", ", "                  id, \". Try rebuilding the Hull-White model with that curve included\"))", "            }", "        }", "        stop(AddContext(\"BtPtT.xccyHW\", e))", "    }, warning = function(e) {", "        stop(AddContext(\"BtPtT.xccyHW\", e, \"Warning\"))", "    })", "    return(out)", "}"], "BtStDt": ["function (state = NULL, Model, id) ", "{", "    out <- tryCatch({", "        if (!is.null(state$t)) ", "            t = state$t", "        else t = 0", "        if (id == Model$Numeraire) ", "            out = 1", "        else {", "            spot = Model$spot[[id]]", "            if (NCOL(spot) > 1) {", "                spot = spot[NROW(spot), 2]", "            }", "            if (is.null(state)) {", "                out = spot", "            }", "            else {", "                w3 = extractFXstate(state, Model, id)", "                n = Nstates.xccyHW(state)", "                compensator = array(0.5 * qfxvar.xccyHW(Model, ", "                  id, t), n)", "                out = spot * matrix(exp(w3 - compensator), n, ", "                  1)", "            }", "        }", "        out", "    }, error = function(e) {", "        stop(AddContext(\"BtStDt.xccyHW\", e))", "    }, warning = function(e) {", "        stop(AddContext(\"BtStDt.xccyHW\", e, \"Warning\"))", "    })", "    return(out)", "}"], "Volt": ["function (state = NULL, Model, id, t1, strike = NULL) ", "{", "    out <- tryCatch({", "        if (!is.null(state$t)) ", "            t = state$t", "        else t = 0", "        lambda0 = Model$lambda[[Model$Numeraire]]", "        lambda = Model$lambda[[id]]", "        l1 = -(exp(-lambda0 * t1) - exp(-lambda0 * Model$TStar))/lambda0", "        l2 = (exp(-lambda * t1) - exp(-lambda * Model$TStar))/lambda", "        l3 = 1", "        var2 = l1 * l1 * qvar.xccyHW(Model, Model$Numeraire, ", "            t1) + l2 * l2 * qvar.xccyHW(Model, id, t1) + l3 * ", "            l3 * qfxvar.xccyHW(Model, id, t1) + 2 * l1 * l2 * ", "            qirircovar.xccyHW(Model, Model$Numeraire, id, t1) + ", "            2 * l1 * l3 * qirfxcovar.xccyHW(Model, Model$Numeraire, ", "                id, t1) + 2 * l2 * l3 * qirfxcovar.xccyHW(Model, ", "            id, id, t1)", "        var1 = l1 * l1 * qvar.xccyHW(Model, Model$Numeraire, ", "            t) + l2 * l2 * qvar.xccyHW(Model, id, t) + l3 * l3 * ", "            qfxvar.xccyHW(Model, id, t) + 2 * l1 * l2 * qirircovar.xccyHW(Model, ", "            Model$Numeraire, id, t) + 2 * l1 * l3 * qirfxcovar.xccyHW(Model, ", "            Model$Numeraire, id, t) + 2 * l2 * l3 * qirfxcovar.xccyHW(Model, ", "            id, id, t)", "        sqrt(pmax(var2 - var1, 1e-09))", "    }, error = function(e) {", "        stop(AddContext(\"Volt.xccyHW\", e))", "    }, warning = function(e) {", "        stop(AddContext(\"Volt.xccyHW\", e, \"Warning\"))", "    })", "    return(out)", "}"], "VoltT": ["function (state = NULL, Model, Currency, StartDate, EndDate, ", "    FixedFrequency, FixedDCT, FloatingFrequency, FloatingDCT, ", "    BDC, FixedSchedule = NULL, FloatingSchedule = NULL) ", "{", "    out <- tryCatch({", "        if (is.null(FixedSchedule)) {", "            t1 = DateToTime(AdjustDate(StartDate, BDC), Model$AnchorDate)", "        }", "        else {", "            t1 = FixedSchedule$Times[1]", "        }", "        if (!is.null(state$t)) ", "            t = state$t", "        else t = 0", "        Model$Numeraire = Currency", "        eps = 1e-05 * sqrt(qvar.xccyHW(Model, Currency, t1))", "        var1 = qvar.xccyHW(Model, Currency, t)", "        var2 = qvar.xccyHW(Model, Currency, t1)", "        g = (SwapRate(list(w = rep(eps, length(t)), t = t), Model, ", "            Currency, 1, StartDate, EndDate, FixedFrequency, ", "            FixedDCT, BDC, FloatingFrequency, FloatingDCT, BDC, ", "            FixedSchedule, FloatingSchedule) - SwapRate(list(w = rep(-eps, ", "            length(t)), t = t), Model, Currency, 1, StartDate, ", "            EndDate, FixedFrequency, FixedDCT, BDC, FloatingFrequency, ", "            FloatingDCT, BDC, FixedSchedule, FloatingSchedule))/2/eps", "        sqrt(g * g * pmax(var2 - var1, 1e-09))", "    }, error = function(e) {", "        stop(AddContext(\"VoltT.xccyHW\", e))", "    }, warning = function(e) {", "        stop(AddContext(\"VoltT.xccyHW\", e, \"Warning\"))", "    })", "    return(out)", "}"], "Nstates": ["function (state) ", "{", "    max(length(state$t), NROW(state$w), 1)", "}"], "Survival": ["function (state = NULL, Model, id, Times) ", "{", "    n = length(Model$CreditCurve[[id]]$t)", "    m = length(Times)", "    tbar = matrix(c(0, Model$CreditCurve[[id]]$t, 999), m, n + ", "        2, byrow = TRUE)", "    zbar = matrix(c(Model$CreditCurve[[id]]$z, Model$CreditCurve[[id]]$z[n]), ", "        m, n + 1, byrow = TRUE)", "    matrix(exp(-rowSums(zbar * diff2(pmin(tbar, Times)))), Nstates.xccyHW(state), ", "        m, byrow = TRUE)", "}"], "AnchorDate": ["2018-03-16"], "TStar": [15], "Numeraire": ["EUR"], "CollateralCcy": ["EUR"], "Currencies": ["EUR", "USD"], "spot": {"EUR": [1], "USD": [0.813603449678627]}, "expiries": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "rho": [[1, 0.581539970161759, -0.295323325458099], [0.581539970161759, 1, -0.0109605433616978], [-0.295323325458099, -0.0109605433616978, 1]], "lambda": [0.03, 0.03], "curve": {"EUR": {"t1": [0.506502395619439, 1.00479123887748, 2.00136892539357, 3.00068446269678, 4, 4.99931553730322, 7.00342231348391, 10.0013689253936, 15.0006844626968, 20, 30.0013689253936], "t2": [0.506502395619439, 1.00479123887748, 2.00136892539357, 3.00068446269678, 4, 4.99931553730322, 7.00342231348391, 10.0013689253936, 15.0006844626968, 20, 30.0013689253936], "z1": [0.00138028853175226, 0.00139141174122501, 0.00138940476647339, 0.00139003845489151, 0.00139035550616077, 0.00351022404727052, 0.00614843393470454, 0.00969355141822697, 0.0129228250650785, 0.0147620855023646, 0.0154398996297794], "z2": [-0.000606718848963349, -0.000617991092337374, -0.00061598289420497, -0.000616616439658712, -0.00061693344101183, -0.000904400089799517, -0.00115071378786383, -0.00125424496736522, -0.001092360305394, -0.000862875820580276, -0.001329810775908]}, "USD": {"t1": [0.506502395619439, 1.00479123887748, 2.00136892539357, 3.00068446269678, 4, 4.99931553730322, 7.00342231348391, 10.0013689253936, 15.0006844626968, 20, 30.0013689253936], "t2": [0.506502395619439, 1.00479123887748, 2.00136892539357, 3.00068446269678, 4, 4.99931553730322, 7.00342231348391, 10.0013689253936, 15.0006844626968, 20, 30.0013689253936], "z1": [0.0227930904202214, 0.0238614828598176, 0.0257314446696007, 0.0268986356044778, 0.0274444690849021, 0.0278817419797849, 0.0279686482241625, 0.0286867462183848, 0.0293882115567727, 0.0295876857456079, 0.0291504333882631], "z2": [0.00194630813074763, 0.00258102232279445, 0.00243173889630401, 0.00231604954614381, 0.0022432195200386, 0.00187500745311176, 0.00160288160983384, 0.00135018806715043, 0.0010175815436282, 0.000440343624729176, -0.00149739803623763]}}, "sigma": {"EUR": {"t": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "v": [0.00583052645184225, 0.00719109670071426, 0.00780981545587563, 0.00818704301628374, 0.0085781108445168, 0.00828254121639522, 0.00777680791512163, 0.00753529715696722, 0.00762456919845323, 0.00760680043941509, 0.00703650913106424, 0.00678987399525136, 0.00639529229331081, 0.00603423700843249]}, "USD": {"t": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "v": [0.00815897476800594, 0.00879807783752212, 0.00886527696218842, 0.00890885509996248, 0.00862756665142014, 0.00831302805233282, 0.00805059605723286, 0.00791054979416825, 0.00788991964703685, 0.00784818982976033, 0.00791580572123477, 0.00790721869080363, 0.00630110176370685, 0.00703893385995795]}}, "fxsigma": {"USD": {"t": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "v": [-0.0181046785749563, 0.00925556098477375, 0.0377691530504854, 0.0562470833103708, 0.0697289579639448, 0.0845230529904092, 0.0974934192626111, 0.100129108657326, 0.105937394897907, 0.111299470607714, 0.108741600375164, 0.110906281499828, 0.112351665523454, 0.112337043350135]}}, "CreditCurve": {"BARC_GB_LON": {"t": [0.5, 1, 2, 3, 4, 5, 7, 10], "z": [0.00300301270889407, 0.00521299509478032, 0.00618327743837583, 0.0073975895259321, 0.0100769871536234, 0.0110140972362698, 0.0164503927370375, 0.0164759646836521], "xlabels": ["6M", "1Y", "2Y", "3Y", "4Y", "5Y", "7Y", "10Y"], "data": [0.001802484, 0.002465336, 0.003086995, 0.003535645, 0.004157078, 0.004637581, 0.006072907, 0.007127836], "freq": [4], "recovery": [0.4]}, "SELF": {"t": [0.5, 1, 2, 3, 4, 5, 7, 10], "z": [0.000954414467038764, 0.00095441435936824, 0.00231636209052193, 0.00222367453413034, 0.00658778924503931, 0.00589052578625831, 0.0116198125866791, 0.0101965161526449], "xlabels": ["6M", "1Y", "2Y", "3Y", "4Y", "5Y", "7Y", "10Y"], "data": [0.000572717, 0.000572717, 0.0009809413, 0.00109848, 0.001808242, 0.002148803, 0.003484939, 0.004225537], "freq": [4], "recovery": [0.4]}}, "FundingSpreads": {"Name": ["SELF", "WPAC_AU_SYD", "WELL_US_SFO", "UBSW_CH_ZRH", "TDOM_CA_TOR", "SOGE_FR_PAR", "SMFG_JP_TYO", "SMBC_JP_TYO", "SCGP_GB_LON", "SBOS_US_NYC", "RVSA_AT_SZG", "ROYC_CA_TOR", "RBOS_GB_LON", "NOSC_CA_TOR", "NOMA_JP_TYO", "NOLA_DE_HAJ", "NBAD_AE_AUH", "NATA_AU_MEL", "MSCS_US_NYC", "MHCB_JP_TYO", "MACQ_AU_SYD", "LOYD_GB_LON", "LBBW_DE_STR", "INGB_NL_AMS", "ICBK_CN_BJS", "HYVE_DE_MUC", "HSBC_GB_LON", "HELA_DE_FRA", "GSGI_US_NYC", "GIBA_AT_VIE", "GENO_DE_FRA", "ESSE_SE_STO", "DNBA_NO_OSL", "DEUT_DE_FRA", "DBSS_SG_SIN", "DABA_DK_CPH", "CTBA_AU_SYD", "CRES_CH_ZRH", "COBA_DE_FRA", "CITI_US_NYC", "CIBC_CA_TOR", "CHAS_US_NYC", "BYLA_DE_MUC", "BSUI_FR_PAR", "BSCH_ES_MAD", "BPCE_FR_PAR", "BOTK_JP_TYO", "BOFA_US_NYC", "BNPA_FR_PAR", "BKCH_CN_BJS", "BFCM_FR_PAR", "BBVA_ES_MAD", "BARC_GB_LON", "ANZB_AU_MEL", "AGRI_FR_PAR"], "Spread": [0, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.009124287, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.0156344, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.0101062, 0.007674736, 0.01075215, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.02289877, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.007674736, 0.009116559, 0.007674736, 0.006672369, 0.007674736, 0.007674736, 0.0172002, 0.007674736, 0.007674736, 0.009164555, 0.009176637, 0.007674736, 0.007674736, 0.0160586, 0.01436115, 0.007674736, 0.007674736], "_row": ["SELF", "WPAC_AU_SYD", "WELL_US_SFO", "UBSW_CH_ZRH", "TDOM_CA_TOR", "SOGE_FR_PAR", "SMFG_JP_TYO", "SMBC_JP_TYO", "SCGP_GB_LON", "SBOS_US_NYC", "RVSA_AT_SZG", "ROYC_CA_TOR", "RBOS_GB_LON", "NOSC_CA_TOR", "NOMA_JP_TYO", "NOLA_DE_HAJ", "NBAD_AE_AUH", "NATA_AU_MEL", "MSCS_US_NYC", "MHCB_JP_TYO", "MACQ_AU_SYD", "LOYD_GB_LON", "LBBW_DE_STR", "INGB_NL_AMS", "ICBK_CN_BJS", "HYVE_DE_MUC", "HSBC_GB_LON", "HELA_DE_FRA", "GSGI_US_NYC", "GIBA_AT_VIE", "GENO_DE_FRA", "ESSE_SE_STO", "DNBA_NO_OSL", "DEUT_DE_FRA", "DBSS_SG_SIN", "DABA_DK_CPH", "CTBA_AU_SYD", "CRES_CH_ZRH", "COBA_DE_FRA", "CITI_US_NYC", "CIBC_CA_TOR", "CHAS_US_NYC", "BYLA_DE_MUC", "BSUI_FR_PAR", "BSCH_ES_MAD", "BPCE_FR_PAR", "BOTK_JP_TYO", "BOFA_US_NYC", "BNPA_FR_PAR", "BKCH_CN_BJS", "BFCM_FR_PAR", "BBVA_ES_MAD", "BARC_GB_LON", "ANZB_AU_MEL", "AGRI_FR_PAR"]}}, "Control": {"DoLogging": [false], "BuildCurvesFromRates": [true], "CounterpartiesToProcess": ["BARC_GB_LON"], "UseCachedModel": [true], "DoPV": [true], "DoCVA": [true], "DoPFE": [true], "PartitionByNetSet": [true], "PartitionByTrade": [false], "UseSobol": [true], "TimeGap": [0.0833333333333333], "PFEPercentile": [0.05], "IsShortfall": [false], "NumSims": [255], "NumSimsCVA": [2047], "SelfPartyName": ["SELF"], "WhatIfPartyName": ["WHATIF"], "InterpolateCovariance": [true], "SavePaths": [false], "OnValuationErrors": ["Continue"], "TradeFile": ["/home/<USER>/projects/solumn-xplain/xva/xplain.csv"], "MarketFile": ["/home/<USER>/projects/solumn-xplain/xva/SCRiPTMarket.json"], "ResultsFile": ["/home/<USER>/projects/solumn-xplain/xva/SCRiPTResults.json"], "ThrowOnError": [false]}, "About": {"ComputerName": [""], "ProcessingTimeInSeconds": ["34.815"], "TimeFinished": ["2018-06-18 16:41:53.863"], "NumberOfTrades": [1], "NumberOfTasks": [2], "InParallel": [true]}}