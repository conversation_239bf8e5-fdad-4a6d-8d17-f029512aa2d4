package com.solum.xplain.xva.calculation;

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;

import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xva.calculation.entity.CalculationResultXva;
import com.solum.xplain.xva.calculation.event.XvaCalculationRequestedEvent;
import com.solum.xplain.xva.calculation.value.XvaDataStoreResult;
import com.solum.xplain.xva.integration.XvaCalculationRequestProducer;
import com.solum.xplain.xva.integration.data.XvaRemoteDataService;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class XvaValuationsExecutor {

  private final CalculationResultXvaRepository calculationResultXvaRepository;
  private final AuditEntryService auditEntryService;
  private final XvaCalculationDataService xvaDataService;
  private final XvaRemoteDataService xvaRemoteDataService;
  private final XvaCalculationRequestProducer requestProducer;

  @Async
  @EventListener
  public void calculateXVA(XvaCalculationRequestedEvent event) {
    validateConfigurationType(event)
        .flatMap(xvaDataService::resolveRequiredData)
        .flatMap(xvaRemoteDataService::storeData)
        .map(requestProducer::sendRequest)
        .fold(
            err -> logError(event.getCalculationId(), List.of(err)),
            result -> saveResult(result, event.getCurrentUser()));
  }

  private Either<ErrorItem, XvaCalculationRequestedEvent> validateConfigurationType(
      XvaCalculationRequestedEvent event) {
    var data = event.getCalculationData();
    if (data.getFxCalibrationResult() != null) {
      return Either.left(
          CALCULATION_ERROR.entity(
              "XVA calculation does not support non SINGLE curve configuration"));
    }
    return Either.right(event);
  }

  private EntityId saveResult(XvaDataStoreResult storeResult, AuditUser auditUser) {
    var data = storeResult.getCalculationData();
    var result =
        CalculationResultXva.of(
            data.getCalculationId(),
            data.getCalculationPerspective(),
            data.getCalculationLevel(),
            auditUser);
    logError(data.getCalculationId(), storeResult.getLog());
    calculationResultXvaRepository.saveXvaCalculation(result);
    return EntityId.entityId(result.getId());
  }

  private EntityId logError(ObjectId calculationId, List<ErrorItem> errorItem) {
    auditEntryService
        .entryByReference(
            CalculationResult.CALCULATION_RESULT_COLLECTION, calculationId.toHexString())
        .toOptional()
        .ifPresent(e -> auditEntryService.addLogs(e.getId(), errorItem));
    return EntityId.entityId(calculationId);
  }
}
