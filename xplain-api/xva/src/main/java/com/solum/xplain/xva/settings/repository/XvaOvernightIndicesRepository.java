package com.solum.xplain.xva.settings.repository;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.settings.GenericVersionedSettingsRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.settings.XvaOvernightIndicesMapper;
import com.solum.xplain.xva.settings.entity.XvaOvernightIndices;
import com.solum.xplain.xva.settings.value.XvaOvernightIndexForm;
import com.solum.xplain.xva.settings.value.XvaOvernightIndicesView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

@Repository
public class XvaOvernightIndicesRepository
    extends GenericVersionedSettingsRepository<XvaOvernightIndices, XvaOvernightIndicesView> {

  private final XvaOvernightIndicesMapper mapper;

  public XvaOvernightIndicesRepository(
      XvaOvernightIndicesMapper mapper, MongoOperations mongoOperations) {
    super(mongoOperations, mapper, XvaOvernightIndices::empty);
    this.mapper = mapper;
  }

  public XvaOvernightIndices getXvaOvernightIndices(BitemporalDate stateDate) {
    return activeEntityOrEmpty(stateDate);
  }

  public Either<ErrorItem, EntityId> saveOvernightIndices(
      LocalDate stateDate, XvaOvernightIndexForm form) {
    return getExactSettings(stateDate)
        .flatMap(settings -> update(settings, form.getVersionForm(), s -> mapper.from(form, s)));
  }

  public Either<ErrorItem, EntityId> deleteOvernightIndiciesVersion(LocalDate stateDate) {
    return getExactSettings(stateDate).flatMap(this::checkFirstVersion).flatMap(this::delete);
  }
}
