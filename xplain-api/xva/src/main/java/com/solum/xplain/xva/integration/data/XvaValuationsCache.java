package com.solum.xplain.xva.integration.data;

import com.solum.xplain.shared.datagrid.DataGrid;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class XvaValuationsCache {
  private static final String XVA_CURVES_CACHE = "XVA_CURVES";

  private final DataGrid dataGrid;

  public void cacheCurveNamesMapping(String calculationId, Map<String, String> creditCurveNames) {
    dataGrid
        .getKeyValueCache(XVA_CURVES_CACHE)
        .set(calculationId, new XvaCreditCurveNames(creditCurveNames));
  }

  public Map<String, String> getCurveNamesMapping(String calculationId) {
    var mapping =
        (XvaCreditCurveNames) dataGrid.getKeyValueCache(XVA_CURVES_CACHE).get(calculationId);

    return mapping == null ? Map.of() : mapping.getCurveNamesMapping();
  }

  public void clearCache(String calculationId) {
    dataGrid.getKeyValueCache(XVA_CURVES_CACHE).delete(calculationId);
  }
}
