package com.solum.xplain.xva;

import com.solum.xplain.core.CoreConfig;
import com.solum.xplain.xva.calculation.config.S3Properties;
import com.solum.xplain.xva.settings.value.XvaSettingsProperties;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@Configuration
@Import(CoreConfig.class)
@EnableConfigurationProperties({S3Properties.class, XvaSettingsProperties.class})
@ComponentScan(
    excludeFilters = {
      @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
      @Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class)
    })
@EntityScan(basePackages = {"com.solum.xplain.xva"})
public class XvaConfig {}
