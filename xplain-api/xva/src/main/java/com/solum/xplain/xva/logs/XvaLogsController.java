package com.solum.xplain.xva.logs;

import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_VIEW_STORED_XVA_CALCULATION;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/xva-logs")
public class XvaLogsController {

  private final XvaLogsControllerService logsControllerService;

  public XvaLogsController(XvaLogsControllerService logsControllerService) {
    this.logsControllerService = logsControllerService;
  }

  @GetMapping
  @Operation(summary = "Get stored XVA calculations")
  @PreAuthorize(AUTHORITY_VIEW_STORED_XVA_CALCULATION)
  public List<String> getFiles() {
    return logsControllerService.listStoredCalculations();
  }

  @GetMapping("/download")
  @Operation(summary = "Download stored xva calculation zip file")
  @PreAuthorize(AUTHORITY_VIEW_STORED_XVA_CALCULATION)
  public void downloadZip(@RequestParam String prefix, HttpServletResponse response)
      throws IOException {
    response.setStatus(HttpServletResponse.SC_OK);
    String zipName = logsControllerService.getFileNameFromKey(prefix);
    response.addHeader("Content-Disposition", "attachment; filename=\"" + zipName + ".zip\"");
    logsControllerService.getStoredFiles(prefix, response.getOutputStream());
  }
}
