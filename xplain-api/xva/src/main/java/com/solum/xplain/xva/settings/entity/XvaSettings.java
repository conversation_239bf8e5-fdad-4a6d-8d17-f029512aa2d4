package com.solum.xplain.xva.settings.entity;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.settings.VersionedSettings;
import java.time.LocalDate;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class XvaSettings extends VersionedSettings {

  private double hullWhiteMeanReversion;
  private int timeEnd;
  private int simulationsForXva;
  private int simulationsForPfe;
  private double pfePercentile;

  public static XvaSettings empty() {
    XvaSettings settings = new XvaSettings();
    settings.setValidFrom(LocalDate.ofEpochDay(0));
    settings.setState(State.ACTIVE);
    return settings;
  }

  @Override
  public boolean valueEquals(Object settings) {
    if (settings instanceof XvaSettings item) {
      return super.valueEquals(settings)
          && this.hullWhiteMeanReversion == item.hullWhiteMeanReversion
          && this.timeEnd == item.timeEnd
          && this.simulationsForXva == item.simulationsForXva
          && this.simulationsForPfe == item.simulationsForPfe
          && this.pfePercentile == item.pfePercentile;
    }
    return false;
  }
}
