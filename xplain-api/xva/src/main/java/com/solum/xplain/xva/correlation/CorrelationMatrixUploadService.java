package com.solum.xplain.xva.correlation;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.ErrorItem.ListOfErrors;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.xva.correlation.csv.CorrelationMatrixCsvLoader;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixSearchForm;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CorrelationMatrixUploadService extends LoggingImportService {

  private final CorrelationMatrixRepository repository;
  private final CorrelationMatrixCsvLoader loader;

  public CorrelationMatrixUploadService(
      AuditEntryService auditEntryService,
      CorrelationMatrixRepository repository,
      CorrelationMatrixCsvLoader loader) {
    super(auditEntryService);
    this.repository = repository;
    this.loader = loader;
  }

  @Override
  protected String getCollection() {
    return "correlationMatrix";
  }

  @Override
  protected String getObjectName() {
    return "Correlation Matrix values";
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCorrelationMatrix(
      String correlationMatrixId, ImportOptions importOptions, byte[] bytes) {
    return getMatrix(correlationMatrixId, importOptions.getStateDate())
        .leftMap(ListOfErrors::from)
        .flatMap(
            m ->
                loader
                    .parse(bytes, importOptions.parsingMode())
                    .map(v -> importCorrelationValues(importOptions, m, v)))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            result -> toReturn(importOptions.getDuplicateAction(), result));
  }

  private ImportResult importCorrelationValues(
      ImportOptions options,
      CorrelationMatrix matrix,
      CsvParserResult<CorrelationMatrixValue> forms) {
    var importLogs = importCorrelationValues(options, matrix, forms.getParsedLines());
    return new ImportResult(importLogs, forms.getWarnings());
  }

  private List<LogItem> importCorrelationValues(
      ImportOptions importOptions, CorrelationMatrix matrix, List<CorrelationMatrixValue> forms) {

    var importItems =
        ImportItems.<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue>builder()
            .existingActiveItems(matrix.getValues())
            .existingItemToKeyFn(v -> v.getId().toConventional())
            .importItems(forms)
            .importItemToKeyFn(v -> v.getId().toConventional())
            .build();
    var action = importOptions.getDuplicateAction();
    return switch (action) {
      case ERROR -> onError(matrix, importItems, importOptions);
      case REPLACE_DELETE -> onReplaceDelete(matrix, importItems, importOptions);
      case REPLACE -> onReplace(matrix, importItems, importOptions);
      case APPEND_DELETE -> onAppendDelete(matrix, importItems, importOptions);
      case APPEND -> onAppend(matrix, importItems, importOptions);
    };
  }

  private List<LogItem> onError(
      CorrelationMatrix matrix,
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      ImportOptions importOptions) {
    var errors = validateItems(importItems, importOptions.getStateDate(), matrix);
    return asLogItems(errors);
  }

  private List<LogItem> onAppend(
      CorrelationMatrix matrix,
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      ImportOptions importOptions) {

    var existingValues =
        importItems.getExistingKeys().stream().map(importItems::existingItem).toList();

    var newValues = importItems.getNewKeys().stream().map(importItems::importItem).toList();

    var valuesToInsert = CollectionUtils.join(newValues, existingValues);
    var updateValues = updateCorrelationMatrix(matrix, importOptions, valuesToInsert);

    return List.of(
        createLogItem(description(matrix.getName(), newValues.size(), 0, 0), updateValues));
  }

  private List<LogItem> onAppendDelete(
      CorrelationMatrix matrix,
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      ImportOptions importOptions) {

    var newValues = importItems.getNewKeys().stream().map(importItems::importItem).toList();

    var existingDuplicateValues =
        importItems.getDuplicateKeys().stream().map(importItems::existingItem).toList();

    var valuesToInsert = CollectionUtils.join(newValues, existingDuplicateValues);
    var updateValues = updateCorrelationMatrix(matrix, importOptions, valuesToInsert);

    return List.of(
        createLogItem(
            description(
                matrix.getName(),
                newValues.size(),
                existingDuplicateValues.size(),
                importItems.getSpareKeys().size()),
            updateValues));
  }

  private List<LogItem> onReplace(
      CorrelationMatrix matrix,
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      ImportOptions importOptions) {

    var importValues = importItems.getImportKeys().stream().map(importItems::importItem).toList();

    var spareValues = importItems.getSpareKeys().stream().map(importItems::existingItem).toList();

    var valuesToInsert = CollectionUtils.join(importValues, spareValues);
    var updateValues = updateCorrelationMatrix(matrix, importOptions, valuesToInsert);

    return List.of(
        createLogItem(
            description(
                matrix.getName(),
                importItems.getNewKeys().size(),
                importItems.getDuplicateKeys().size(),
                0),
            updateValues));
  }

  private List<LogItem> onReplaceDelete(
      CorrelationMatrix matrix,
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      ImportOptions importOptions) {

    var importValues = importItems.getImportKeys().stream().map(importItems::importItem).toList();

    var updateValues = updateCorrelationMatrix(matrix, importOptions, importValues);

    return List.of(
        createLogItem(
            description(
                matrix.getName(),
                importItems.getNewKeys().size(),
                importItems.getDuplicateKeys().size(),
                importItems.getSpareKeys().size()),
            updateValues));
  }

  private String description(String name, int inserted, int updated, int removed) {
    return String.format(
        "Correlation Matrix %s updated. %d new values inserted, "
            + "%d existing values updated,"
            + " %d values removed",
        name, inserted, updated, removed);
  }

  private List<ErrorItem> validateItems(
      ImportItems<CorrelationMatrixValue, CorrelationMatrixId, CorrelationMatrixValue> importItems,
      LocalDate stateDate,
      CorrelationMatrix matrix) {
    return Stream.of(
            newVersionViable(matrix.getValidFrom(), stateDate, matrix.getName()).stream(),
            futureVersionsExists(matrix.getName(), stateDate).stream(),
            errStream(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem),
            errStream(importItems.getSpareKeys(), ImportErrorUtils::missingItem))
        .flatMap(Function.identity())
        .toList();
  }

  private Optional<ErrorItem> newVersionViable(
      LocalDate versionDate, LocalDate stateDate, String name) {
    return Optional.of(versionDate)
        .filter(stateDate::isAfter)
        .map(d -> ImportErrorUtils.newVersionViable(name));
  }

  private List<ErrorItem> futureVersionsExists(String name, LocalDate stateDate) {
    boolean hasFutureVersions = hasFutureVersions(name, stateDate);
    return ImportErrorUtils.orErrors(hasFutureVersions, ImportErrorUtils.futureVersionExists(name));
  }

  private boolean hasFutureVersions(String name, LocalDate stateDate) {
    var form = new CorrelationMatrixSearchForm(name, stateDate);
    return repository.getFutureVersions(form).notEmpty();
  }

  private Stream<ErrorItem> errStream(
      Set<CorrelationMatrixId> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(f -> function.apply(getIdentifier(f)));
  }

  private String getIdentifier(CorrelationMatrixId id) {
    return String.format("Pair of %s and %s", id.getKey1(), id.getKey2());
  }

  private Either<ErrorItem, EntityId> updateCorrelationMatrix(
      CorrelationMatrix matrix,
      ImportOptions importOptions,
      List<CorrelationMatrixValue> valueForms) {
    var versionForm =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions, matrix.getValidFrom(), importOptions::getFutureVersionsAction);
    return repository.updateValues(
        matrix.getEntityId(), matrix.getValidFrom(), versionForm, valueForms);
  }

  private Either<ErrorItem, CorrelationMatrix> getMatrix(String id, LocalDate stateDate) {
    return repository.getActiveCorrelation(id, BitemporalDate.newOf(stateDate));
  }
}
