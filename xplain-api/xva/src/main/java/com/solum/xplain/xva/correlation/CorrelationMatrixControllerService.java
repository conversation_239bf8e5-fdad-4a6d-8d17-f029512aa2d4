package com.solum.xplain.xva.correlation;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.correlation.csv.CorrelationMatrixCsvMapper;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixCreateForm;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixListView;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixRemoveValueForm;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixSearchForm;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixUpdateForm;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixUpdateValueForm;
import com.solum.xplain.xva.correlation.value.CorrelationMatrixView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class CorrelationMatrixControllerService {

  private final CorrelationMatrixRepository repository;

  public CorrelationMatrixControllerService(CorrelationMatrixRepository repository) {
    this.repository = repository;
  }

  public Either<ErrorItem, EntityId> insert(CorrelationMatrixCreateForm form) {
    return repository.insert(form);
  }

  public Either<ErrorItem, EntityId> update(
      String id, LocalDate version, CorrelationMatrixUpdateForm form) {
    return repository.update(id, version, form);
  }

  public Either<ErrorItem, EntityId> updateValue(
      String id, LocalDate version, CorrelationMatrixUpdateValueForm form) {
    return repository.updateValue(id, version, form);
  }

  public Either<ErrorItem, EntityId> removeValue(
      String id, LocalDate version, CorrelationMatrixRemoveValueForm form) {
    return repository.removeValue(id, version, form);
  }

  public Either<ErrorItem, EntityId> archive(
      String id, LocalDate versionDate, ArchiveEntityForm archiveForm) {
    return repository.archive(id, versionDate, archiveForm);
  }

  public Either<ErrorItem, EntityId> remove(String id, LocalDate versionDate) {
    return repository.delete(id, versionDate);
  }

  public List<CorrelationMatrixListView> correlations(
      BitemporalDate stateDate, String baseCcy, boolean withArchived) {
    return repository.correlationMatrixList(
        stateDate, VersionedEntityFilter.of(withArchived), baseCcy);
  }

  public Either<ErrorItem, CorrelationMatrixView> correlation(String id, BitemporalDate stateDate) {
    return repository.correlation(id, stateDate);
  }

  public Either<ErrorItem, FileResponseEntity> getCorrelationMatrixCsvBytes(
      String id, BitemporalDate stateDate) {
    var csvFileName = nameWithTimeStamp("Correlations", stateDate);
    return repository
        .getActiveCorrelation(id, stateDate)
        .map(c -> emptyIfNull(c.getValues()))
        .map(
            c -> {
              var rows = c.stream().map(CorrelationMatrixCsvMapper::toCsvRow).toList();
              var csvFile = new CsvOutputFile(CorrelationMatrixCsvMapper.HEADER, rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public List<CorrelationMatrixListView> getVersions(String id) {
    return repository.getVersionViews(id);
  }

  public DateList getFutureVersionsDates(CorrelationMatrixSearchForm searchForm) {
    return repository.getFutureVersions(searchForm);
  }
}
