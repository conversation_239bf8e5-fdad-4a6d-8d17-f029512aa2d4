package com.solum.xplain.xva.settings.repository;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.settings.GenericVersionedSettingsRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xva.settings.XvaSettingsMapper;
import com.solum.xplain.xva.settings.entity.XvaSettings;
import com.solum.xplain.xva.settings.value.XvaSettingsForm;
import com.solum.xplain.xva.settings.value.XvaSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

@Repository
public class XvaSettingsRepository
    extends GenericVersionedSettingsRepository<XvaSettings, XvaSettingsView> {

  private final XvaSettingsMapper mapper;

  public XvaSettingsRepository(XvaSettingsMapper mapper, MongoOperations mongoOperations) {
    super(mongoOperations, mapper, XvaSettings::empty);
    this.mapper = mapper;
  }

  public XvaSettingsView getXvaSettings(BitemporalDate stateDate) {
    return entityView(stateDate);
  }

  public Either<ErrorItem, EntityId> saveXvaSettings(LocalDate stateDate, XvaSettingsForm form) {
    return getExactSettings(stateDate)
        .flatMap(settings -> update(settings, form.getVersionForm(), s -> mapper.from(form, s)));
  }

  public Either<ErrorItem, EntityId> deleteXvaSettingsVersion(LocalDate stateDate) {
    return getExactSettings(stateDate).flatMap(this::checkFirstVersion).flatMap(this::delete);
  }
}
