package com.solum.xplain.xva.settings;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.xva.locks.XvaLocks.LIBOR_INDICES_LOCK_ID;
import static com.solum.xplain.xva.locks.XvaLocks.OVERNIGHT_INDICES_LOCK_ID;
import static com.solum.xplain.xva.locks.XvaLocks.XVA_SETTINGS_LOCK_ID;
import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS;
import static com.solum.xplain.xva.permissions.XvaAuthorities.AUTHORITY_VIEW_XVA_VALUATION_SETTINGS;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.settings.value.GlobalSettingsSearch;
import com.solum.xplain.xva.settings.value.XvaLiborIndexForm;
import com.solum.xplain.xva.settings.value.XvaLiborIndicesView;
import com.solum.xplain.xva.settings.value.XvaOvernightIndexForm;
import com.solum.xplain.xva.settings.value.XvaOvernightIndicesView;
import com.solum.xplain.xva.settings.value.XvaSettingsForm;
import com.solum.xplain.xva.settings.value.XvaSettingsView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/settings/xva")
public class XvaSettingsController {
  private final XvaSettingsControllerService service;

  public XvaSettingsController(XvaSettingsControllerService service) {
    this.service = service;
  }

  @Operation(summary = "Get advanced xVA settings")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public XvaSettingsView getXvaSettings(@RequestParam LocalDate stateDate) {
    return service.getXvaSettings(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Save advanced xVA settings")
  @CommonErrors
  @PostMapping("/{version}") // NOTE: Should be @PutMapping - don't copy!
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  @RequireLock(name = XVA_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> saveXvaSettings(
      @Valid @RequestBody XvaSettingsForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveXvaSettings(version, form));
  }

  @Operation(summary = "Delete advanced xVA settings")
  @CommonErrors
  @PutMapping("/{version}/delete")
  @RequireLock(name = XVA_SETTINGS_LOCK_ID)
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> deleteXvaSettings(@PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteXvaSettings(version));
  }

  @Operation(summary = "Get advanced xVA settings versions")
  @CommonErrors
  @GetMapping("/versions")
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public List<XvaSettingsView> xvaSettingsVersions() {
    return service.xvaSettingsVersions();
  }

  @Operation(summary = "Get xVA settings future versions")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public DateList getXvaSettingsFutureVersions(@Valid GlobalSettingsSearch search) {
    return service.xvaSettingsFutureVersions(search);
  }

  @Operation(summary = "Get XVA Currency Libor indices")
  @GetMapping("/libor-indices")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public XvaLiborIndicesView getLiborIndices(@RequestParam LocalDate stateDate) {
    return service.getLiborIndices(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Set XVA Currency Libor indices")
  @CommonErrors
  @RequireLock(name = LIBOR_INDICES_LOCK_ID)
  @PostMapping("/libor-indices/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> setLiborIndices(
      @Valid @RequestBody XvaLiborIndexForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveLiborIndices(version, form));
  }

  @Operation(summary = "Delete XVA Currency Libor indices")
  @CommonErrors
  @RequireLock(name = LIBOR_INDICES_LOCK_ID)
  @PutMapping("/libor-indices/{version}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> deleteLiborIndices(@PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteXvaLiborIndicesVersion(version));
  }

  @Operation(summary = "Get XVA Currency Libor indices versions")
  @GetMapping("/libor-indices/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public List<XvaLiborIndicesView> getLiborIndicesVersions() {
    return service.xvaLiborIndicesVersions();
  }

  @Operation(summary = "Get XVA Currency Libor indices future versions")
  @GetMapping("/libor-indices/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public DateList getLiborIndicesFutureVersions(@Valid GlobalSettingsSearch search) {
    return service.xvaLiborIndicesFutureVersions(search);
  }

  // TODO: SXSD-10459-Collapse overnight-indices and libor-indices into one endpoint

  @Operation(summary = "Get XVA Currency Overnight indices")
  @GetMapping("/overnight-indices")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public XvaOvernightIndicesView getOvernightIndices(@RequestParam LocalDate stateDate) {
    return service.getOvernightIndices(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Set XVA Currency Overnight indices")
  @CommonErrors
  @RequireLock(name = OVERNIGHT_INDICES_LOCK_ID)
  @PostMapping("/overnight-indices/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> setOvernightIndices(
      @Valid @RequestBody XvaOvernightIndexForm form, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.saveOvernightIndices(version, form));
  }

  @Operation(summary = "Delete XVA Currency Overnight indices")
  @CommonErrors
  @RequireLock(name = OVERNIGHT_INDICES_LOCK_ID)
  @PutMapping("/overnight-indices/{version}/delete")
  @PreAuthorize(AUTHORITY_MODIFY_XVA_VALUATION_SETTINGS)
  public ResponseEntity<EntityId> deleteOvernightIndices(@PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteOvernightIndicesVersion(version));
  }

  @Operation(summary = "Get XVA Currency Overnight indices versions")
  @GetMapping("/overnight-indices/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public List<XvaOvernightIndicesView> getOvernightIndicesVersions() {
    return service.xvaOvernightIndicesVersions();
  }

  @Operation(summary = "Get XVA Currency Overnight indices future versions")
  @GetMapping("/overnight-indices/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_XVA_VALUATION_SETTINGS)
  public DateList getOvernightIndicesFutureVersions(@Valid GlobalSettingsSearch search) {
    return service.xvaOvernightIndicesFutureVersions(search);
  }
}
