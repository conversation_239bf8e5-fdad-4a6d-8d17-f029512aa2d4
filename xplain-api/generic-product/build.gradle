dependencies {
  implementation project(":xplain-api:core")
  implementation project(":xplain-api:secmaster")
  implementation project(":shared:strata-extension")
  implementation project(":shared:versions")

  implementation "jakarta.inject:jakarta.inject-api:2.0.1"
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

  implementation "com.opengamma.strata:strata-measure:${strataVersion}"
  implementation "com.opengamma.strata:strata-loader:${strataVersion}"

  implementation "commons-beanutils:commons-beanutils:${commonsBeanUtilsVersion}"
  implementation project(path: ':shared:strata-extension')
  testImplementation(testFixtures(project(":xplain-api:core")))
}

