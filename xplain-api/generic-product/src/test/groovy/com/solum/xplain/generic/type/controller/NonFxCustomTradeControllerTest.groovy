package com.solum.xplain.generic.type.controller

import static com.solum.xplain.core.common.EntityId.entityId
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.generic.type.controller.value.NonFxCustomTradeForm
import com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder
import com.solum.xplain.generic.type.helpers.MockMvcConfiguration
import com.solum.xplain.generic.type.product.GenericProductType
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [NonFxCustomTradeController])
class NonFxCustomTradeControllerTest extends Specification {

  private static final String URI = "/portfolio/{id}/custom-trades/non-fx"
  private static final String PORTFOLIO_ID = "portfolio"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  RequestPathVariablesSupport pathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  private PortfolioItemRepository portfolioItemRepository = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should get custom fx view"() {
    setup:
    def trade = GenericPortfolioItemBuilder.singleCreditCcyGenericTrade(GenericProductType.CUSTOM_CREDIT_1)
    1 * service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(trade)

    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
    .param("stateDate", "2020-01-01")
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  def "should create new custom non fx with response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as NonFxCustomTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                               | code | response
    form()                                             | 200  | """{"id":"1"}"""
    form(c -> c.remove("tradeCounterparty"))           | 200  | """{"id":"1"}"""
    form(c -> c.remove("tradeCounterpartyType"))       | 200  | """{"id":"1"}"""
    form(c -> c.remove("description"))                 | 200  | """{"id":"1"}"""
    form(c -> c.remove("clientMetrics"))               | 200  | """{"id":"1"}"""
    form(c -> c.remove("tradeDate"))                   | 200  | """{"id":"1"}"""
    form(c -> c.remove("assetType"))                   | 200  | """{"id":"1"}"""
    form(c -> c.remove("subAssetType"))                | 200  | """{"id":"1"}"""
    form(c -> c.remove("tradeCurrency"))               | 412  | "NotEmpty.nonFxCustomTradeForm.tradeCurrency"
    form(c -> c.remove("notionalValue"))               | 412  | "NotNull.nonFxCustomTradeForm.notionalValue"
    form(c -> c.remove("productType"))                 | 412  | "NotNull.nonFxCustomTradeForm.productType"
    form(c -> c.replace("productType", "CUSTOM_FX_1")) | 412  | "ValidGenericProductType.nonFxCustomTradeForm.productType"
    form(c -> c.remove("externalTradeId"))             | 412  | "NotEmpty.nonFxCustomTradeForm.externalTradeId"
    form(c -> c.remove("startDate"))                   | 412  | "NotNull.nonFxCustomTradeForm.startDate"
    form(c -> c.remove("endDate"))                     | 412  | "NotNull.nonFxCustomTradeForm.endDate"
    form(c -> c.replace("endDate", now()))             | 412  | "ValidPaymentPeriod.nonFxCustomTradeForm.endDate"
    form(c -> c.put("sector", "UNDEFINED"))            | 412  | "Null.nonFxCustomTradeForm.sector"
    form(c -> c.put("productType", "CUSTOM_CREDIT_1")) | 412  | "NotEmpty.nonFxCustomTradeForm.sector"
    form(c -> {
      c.put("productType", "CUSTOM_CREDIT_1")
      c.put("sector", "random")
    })                                                 | 412  | "ValidStringSet.nonFxCustomTradeForm.sector"
  }

  def form(Closure c = { f -> f }) {
    [
      externalTradeId      : "EXTERNAL_TRADE_ID",
      tradeCounterparty    : "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description          : "DESCRIPTION",
      productType          : "CUSTOM_RATES_1",
      startDate            : now(),
      endDate              : now().plusDays(1),
      tradeDate            : now(),
      assetType            : "ASSET",
      subAssetType         : "SUB_ASSET",
      tradeCurrency        : "EUR",
      notionalValue        : 10,
      versionForm          : [
        comment             : "comment",
        validFrom           : now(),
        stateDate           : now(),
        futureVersionsAction: "KEEP"
      ],
      clientMetrics        : [
        presentValue: 10
      ]
    ].tap(c)
  }
}
