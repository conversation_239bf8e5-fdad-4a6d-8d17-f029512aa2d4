package com.solum.xplain.generic.type.secmaster;

import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.generic.type.product.GenericProductGroup;
import com.solum.xplain.generic.type.product.GenericProductType;
import com.solum.xplain.secmaster.trademerge.ProductDetailsMerger;
import com.solum.xplain.secmaster.trademerge.product.FxProductDetailsMerger;
import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class GenericFxProductMerger implements ProductDetailsMerger {
  private final FxProductDetailsMerger fxProductDetailsMerger;

  @Override
  public List<ProductType> productTypes() {
    return Arrays.stream(GenericProductType.values())
        .filter(v -> v.getGroup() == GenericProductGroup.CUSTOM_FX)
        .map(ProductType.class::cast)
        .toList();
  }

  @Override
  public TradeDetails mergeDetails(
      ProductType type, TradeDetails mutableResult, AllocationTradeDetails alloc) {
    return fxProductDetailsMerger.mergeDetails(type, mutableResult, alloc);
  }
}
