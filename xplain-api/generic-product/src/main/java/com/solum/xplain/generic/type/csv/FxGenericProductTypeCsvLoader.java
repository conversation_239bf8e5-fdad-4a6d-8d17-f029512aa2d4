package com.solum.xplain.generic.type.csv;

import static com.opengamma.strata.loader.LoaderUtils.parsePayReceive;
import static com.opengamma.strata.product.common.PayReceive.PAY;
import static com.opengamma.strata.product.common.PayReceive.RECEIVE;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDouble;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validatePositiveValue;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.parseFxCurrency;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.loader.LoaderUtils;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvLoader;
import com.solum.xplain.generic.type.product.GenericProductConstants;
import com.solum.xplain.generic.type.product.details.ResolvableGenericFxTradeDetails;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Component;

@Component
public class FxGenericProductTypeCsvLoader implements ProductCsvLoader {

  @Override
  public List<ProductType> productTypes() {
    return GenericProductConstants.FX_CUSTOM_PRODUCTS;
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    try {
      if (refSecTrade) {
        return Either.right(parseRefSecTrade(row));
      } else {
        return Either.right(parseBespokeTrade(row));
      }

    } catch (RuntimeException ex) {
      return Either.left(
          PARSING_ERROR.entity(
              String.format(
                  "Error at line number %s. Error: %s", row.lineNumber(), ex.getMessage())));
    }
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    var payReceiveMap = payReceiveMap(row);
    return CsvLoaderUtils.parseTradeCurrency(row)
        .orElseGet(
            () -> {
              var leg1Currency = parseCurrency(row, payReceiveMap.get(PAY));
              var leg2Currency = parseCurrency(row, payReceiveMap.get(RECEIVE));
              return CurrencyPair.of(leg1Currency, leg2Currency).toConventional().getBase();
            });
  }

  private ResolvableGenericFxTradeDetails parseBespokeTrade(CsvRow row) {
    var startDate = TradeCsvLoaderUtils.parseStartDate(row);
    var endDate = TradeCsvLoaderUtils.parseEndDate(row, startDate);
    var payReceiveMap = payReceiveMap(row);

    return ResolvableGenericFxTradeDetails.builder()
        .startDate(startDate)
        .endDate(endDate)
        .payCurrency(parseCurrency(row, payReceiveMap.get(PAY)))
        .payCurrencyAmount(PAY.normalize(parseNotional(row, payReceiveMap.get(PAY))))
        .receiveCurrency(parseCurrency(row, payReceiveMap.get(RECEIVE)))
        .receiveCurrencyAmount(parseNotional(row, payReceiveMap.get(RECEIVE)))
        .assetType(GenericTradeDetailsCsvLoader.parseGenericAssetType(row))
        .subAssetType(GenericTradeDetailsCsvLoader.parseGenericSubAssetType(row))
        .additionalInfo(GenericTradeDetailsCsvLoader.parseAdditionalInfo(row))
        .underlying(GenericTradeDetailsCsvLoader.parseUnderlying(row))
        .optionPosition(GenericTradeDetailsCsvLoader.parseOptionPosition(row))
        .build();
  }

  private ResolvableGenericFxTradeDetails parseRefSecTrade(CsvRow row) {
    var startDate = TradeCsvLoaderUtils.parseStartDate(row);
    var endDate = TradeCsvLoaderUtils.parseEndDate(row, startDate);
    var payReceiveMap = payReceiveMap(row);

    return ResolvableGenericFxTradeDetails.builder()
        .startDate(startDate)
        .endDate(endDate)
        .payCurrency(parseCurrency(row, payReceiveMap.get(PAY)))
        .receiveCurrency(parseCurrency(row, payReceiveMap.get(RECEIVE)))
        .fxRate(validatePositiveValue(parseDouble(row, TRADE_REF_SEC_FX_RATE)))
        .assetType(GenericTradeDetailsCsvLoader.parseGenericAssetType(row))
        .subAssetType(GenericTradeDetailsCsvLoader.parseGenericSubAssetType(row))
        .additionalInfo(GenericTradeDetailsCsvLoader.parseAdditionalInfo(row))
        .underlying(GenericTradeDetailsCsvLoader.parseUnderlying(row))
        .optionPosition(GenericTradeDetailsCsvLoader.parseOptionPosition(row))
        .build();
  }

  private Currency parseCurrency(CsvRow row, String leg) {
    return parseFxCurrency(row, addField(leg, LEG_CURRENCY));
  }

  private Double parseNotional(CsvRow row, String leg) {
    var field = addField(leg, LEG_NOTIONAL);
    var notional = LoaderUtils.parseDouble(row.getField(field));
    return validatePositiveValue(notional, field);
  }

  private Map<PayReceive, String> payReceiveMap(CsvRow row) {
    var payReceiveMap =
        Stream.of(LEG_1, LEG_2)
            .collect(
                Collectors.toMap(
                    v -> parsePayReceive(row.getField(addField(v, PAY_RECEIVE))),
                    k -> k,
                    (a, b) -> a));
    if (payReceiveMap.size() == 1) {
      throw new IllegalArgumentException(
          "Detected two legs having the same direction: " + payReceiveMap.keySet());
    }
    return payReceiveMap;
  }
}
