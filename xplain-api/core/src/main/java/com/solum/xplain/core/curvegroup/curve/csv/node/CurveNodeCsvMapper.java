package com.solum.xplain.core.curvegroup.curve.csv.node;

import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.CONVENTION_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.CURVE_NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.FRA_SETTLEMENT;
import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.SERIAL_FUTURE;
import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.TENOR_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodesCsvLoader.TYPE_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeCalculatedView;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CurveNodeCsvMapper extends CsvMapper<CurveNodeCalculatedView> {
  private static final List<CsvColumn<CurveNodeCalculatedView>> COLUMNS =
      List.of(
          CsvColumn.text(
              CurveNodeCalculatedView.Fields.type, TYPE_FIELD, CurveNodeCalculatedView::getType),
          CsvColumn.text(
              CurveNodeCalculatedView.Fields.convention,
              CONVENTION_FIELD,
              CurveNodeCalculatedView::getConvention),
          CsvColumn.text(
              CurveNodeCalculatedView.Fields.period,
              TENOR_FIELD,
              CurveNodeCalculatedView::getPeriod),
          CsvColumn.text(
              CurveNodeCalculatedView.Fields.fraSettlement,
              FRA_SETTLEMENT,
              CurveNodeCalculatedView::getFraSettlement),
          CsvColumn.text(
              CurveNodeCalculatedView.Fields.serialFuture,
              SERIAL_FUTURE,
              CurveNodeCalculatedView::getSerialFuture));

  public CurveNodeCsvMapper() {
    super(COLUMNS, null);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    if (containsField(VersionedNamedEntity.Fields.name)) {
      builder.add(CURVE_NAME_FIELD);
    }
    builder.addAll(super.header());
    return builder.build();
  }

  public CsvRow toCsvRow(String curveName, CurveNodeCalculatedView node) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    if (containsField(VersionedNamedEntity.Fields.name)) {
      builder.add(new CsvField(CURVE_NAME_FIELD, curveName));
    }
    builder.addAll(super.toCsvFields(node));

    return new CsvRow(builder.build());
  }
}
