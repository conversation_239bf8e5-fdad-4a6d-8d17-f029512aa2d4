package com.solum.xplain.core.ipv.group.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ExistingIpvDataGroupNameValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExistingIpvDataGroupName {
  String message() default
      "{com.solum.xplain.api.ipv.group.validation.ExistingIpvDataGroupName.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
