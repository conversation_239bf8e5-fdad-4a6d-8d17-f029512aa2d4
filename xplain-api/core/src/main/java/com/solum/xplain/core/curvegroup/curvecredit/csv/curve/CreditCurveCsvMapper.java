package com.solum.xplain.core.curvegroup.curvecredit.csv.curve;

import static com.solum.xplain.core.classifiers.CreditTranches.formatTrancheLabel;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CCY_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CORP_TICKER_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CREDIT_INDEX_FACTOR_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CREDIT_INDEX_SERIES_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CREDIT_INDEX_START_DATE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CREDIT_INDEX_TRANCHE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CREDIT_INDEX_VERSION_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.CURVE_TYPE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.DOC_CLAUSE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.ENTITY_NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.FIXED_COUPON_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.QUOTE_CONVENTION_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.RECOVERY_RATE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.REFERENCE_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.SECTOR_FIELD;
import static com.solum.xplain.core.curvegroup.curvecredit.csv.curve.CreditCurveCsvLoader.SENIORITY_FIELD;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.curvegroup.curvecredit.CreditCurvesUtils;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView;
import java.util.List;

public class CreditCurveCsvMapper extends CsvMapper<CreditCurveView> {

  private static final List<CsvColumn<CreditCurveView>> COLUMNS =
      List.of(
          CsvColumn.enumField(
              CreditCurveView.Fields.curveType, CURVE_TYPE_FIELD, CreditCurveView::getCurveType),
          CsvColumn.text(
              CreditCurveView.Fields.reference, REFERENCE_FIELD, CreditCurveView::getReference),
          CsvColumn.text(CreditCurveView.Fields.currency, CCY_FIELD, CreditCurveView::getCurrency),
          CsvColumn.text(CreditCurveView.Fields.sector, SECTOR_FIELD, CreditCurveView::getSector),
          CsvColumn.text(
              CreditCurveView.Fields.docClause, DOC_CLAUSE_FIELD, CreditCurveView::getDocClause),
          CsvColumn.text(
              CreditCurveView.Fields.seniority, SENIORITY_FIELD, CreditCurveView::getSeniority),
          CsvColumn.text(
              CreditCurveView.Fields.entityLongName,
              ENTITY_NAME_FIELD,
              CreditCurvesUtils::formatLongName),
          CsvColumn.text(
              CreditCurveView.Fields.corpTicker,
              CORP_TICKER_FIELD,
              i -> i.getCorpTicker() == null ? null : i.getCorpTicker().toUpperCase()),
          CsvColumn.decimal(
              CreditCurveView.Fields.recoveryRate,
              RECOVERY_RATE_FIELD,
              CreditCurveView::getRecoveryRate),
          CsvColumn.decimal(
              CreditCurveView.Fields.fixedCoupon,
              FIXED_COUPON_FIELD,
              CreditCurveView::getFixedCoupon),
          CsvColumn.text(
              CreditCurveView.Fields.quoteConvention,
              QUOTE_CONVENTION_FIELD,
              CreditCurveView::getQuoteConvention),
          CsvColumn.integer(
              CreditCurveView.Fields.creditIndexSeries,
              CREDIT_INDEX_SERIES_FIELD,
              CreditCurveView::getCreditIndexSeries),
          CsvColumn.integer(
              CreditCurveView.Fields.creditIndexVersion,
              CREDIT_INDEX_VERSION_FIELD,
              CreditCurveView::getCreditIndexVersion),
          CsvColumn.date(
              CreditCurveView.Fields.creditIndexStartDate,
              CREDIT_INDEX_START_DATE_FIELD,
              CreditCurveView::getCreditIndexStartDate),
          CsvColumn.bigDecimal(
              CreditCurveView.Fields.creditIndexFactor,
              CREDIT_INDEX_FACTOR_FIELD,
              CreditCurveView::getCreditIndexFactor),
          CsvColumn.text(
              CreditCurveView.Fields.creditIndexTranche,
              CREDIT_INDEX_TRANCHE_FIELD,
              c -> formatTrancheLabel(c.getCreditIndexTranche())));

  public CreditCurveCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
