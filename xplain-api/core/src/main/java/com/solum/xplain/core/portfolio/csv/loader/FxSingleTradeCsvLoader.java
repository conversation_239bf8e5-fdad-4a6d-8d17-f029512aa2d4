package com.solum.xplain.core.portfolio.csv.loader;

import static com.opengamma.strata.loader.LoaderUtils.parsePayReceive;
import static com.opengamma.strata.product.common.PayReceive.PAY;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parsePositiveDouble;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PAYMENT_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableFxForwardDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableFxForwardDetails.ResolvableFxForwardDetailsBuilder;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.product.ProductType;
import io.atlassian.fugue.Either;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Component;

/** Loads FX trades (spot of forward) from CSV files. */
@Component
public class FxSingleTradeCsvLoader
    extends FxTradeCsvLoader<ResolvableFxForwardDetails.ResolvableFxForwardDetailsBuilder> {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.FXFWD);
  }

  @Override
  protected ResolvableFxForwardDetails.ResolvableFxForwardDetailsBuilder createBuilder(
      LocalDate nearLegPaymentDate, @Nonnull LocalDate paymentDate, String paymentDateCnv) {
    return ResolvableFxForwardDetails.builder()
        .businessDayConvention(paymentDateCnv)
        .paymentDate(paymentDate);
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    try {
      LocalDate paymentDate = CsvLoaderUtils.parseDate(row.getField(TRADE_PAYMENT_DATE));
      return Either.right(parseRow(row, paymentDate, refSecTrade));
    } catch (RuntimeException ex) {
      return Either.left(
          Error.PARSING_ERROR.entity(
              "Error at line number " + row.lineNumber() + ". Error: " + ex.getMessage()));
    }
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    return parseFxTradeCcy(row);
  }

  ResolvableFxForwardDetails parseRow(
      CsvRow row, @Nonnull LocalDate paymentDate, boolean refSecTrade) {
    if (refSecTrade) {
      return commonFxFwdDetails(row, paymentDate)
          .fxRate(parsePositiveDouble(row, TRADE_REF_SEC_FX_RATE))
          .build();
    } else {
      var leg1Notional = parsePositiveDouble(row, addField(LEG_1, LEG_NOTIONAL));
      var leg2Notional = parsePositiveDouble(row, addField(LEG_2, LEG_NOTIONAL));
      var leg1Direction = parsePayReceive(row.getField(addField(LEG_1, PAY_RECEIVE)));
      return commonFxFwdDetails(row, paymentDate)
          .payCurrencyAmount(leg1Direction == PAY ? leg1Notional * -1 : leg2Notional * -1)
          .receiveCurrencyAmount(leg1Direction == PAY ? leg2Notional : leg1Notional)
          .build();
    }
  }

  private ResolvableFxForwardDetailsBuilder commonFxFwdDetails(
      CsvRow row, @Nonnull LocalDate paymentDate) {
    return commonFxDetails(row, null, paymentDate);
  }
}
