package com.solum.xplain.core.portfolio.csv;

import com.opengamma.strata.collect.io.CsvRow;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
@AllArgsConstructor
public class RowFilter {
  private final Predicate<CsvRow> filter;
  private final String label;

  public static final RowFilter EMPTY_FILTER = new RowFilter(r -> true, null);
}
