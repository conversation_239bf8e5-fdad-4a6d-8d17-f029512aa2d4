package com.solum.xplain.core.curvegroup.volatility.value.node;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.MatrixValueNode;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.CompareToBuilder;

@Data
@FieldNameConstants
public class VolatilityNodeValueView
    implements MatrixValueNode, Comparable<VolatilityNodeValueView> {

  private String expiry;
  private String tenor;
  private Double value;

  @Override
  public String getRow() {
    return getExpiry();
  }

  @Override
  public String getColumn() {
    return getTenor();
  }

  @Override
  public int compareTo(VolatilityNodeValueView o) {
    return new CompareToBuilder()
        .append(Tenor.parse(this.getTenor()), Tenor.parse(o.getTenor()))
        .append(Tenor.parse(this.getExpiry()), Tenor.parse(o.getExpiry()))
        .toComparison();
  }
}
