package com.solum.xplain.core.common.validation;

import static com.solum.xplain.core.classifiers.Constants.OVERNIGHT_TERM_INDICES;

import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.util.Collection;
import java.util.function.Supplier;

public class TermOvernightIndicesSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return OVERNIGHT_TERM_INDICES.stream().map(OvernightTermIndex::getName).toList();
  }
}
