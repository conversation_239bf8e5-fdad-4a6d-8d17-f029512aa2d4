package com.solum.xplain.core.portfolio.csv;

import com.solum.xplain.core.classifiers.type.SupportedDayCount;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DayCountCsvUtils {

  public static String toExportLabel(@Nullable String dayCount) {
    return Optional.ofNullable(dayCount)
        .flatMap(SupportedDayCount::ofOGLabel)
        .map(SupportedDayCount::label)
        .orElse(null);
  }
}
