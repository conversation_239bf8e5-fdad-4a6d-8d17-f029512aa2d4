package com.solum.xplain.core.company;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.repository.CompanyIpvSettingsRepository;
import com.solum.xplain.core.company.repository.CompanyLegalEntityIpvSettingsRepository;
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.company.repository.CompanyValuationSettingsRepository;
import com.solum.xplain.core.company.value.CompanyIpvSettingsResolver;
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsResolver;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.company.value.CompanyValuationSettingsResolver;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;

@Service
@NullMarked
public class CompanyPortfolioSettingsResolver {

  private final PortfolioRepository portfolioRepository;
  private final CompanyRepository companyRepository;
  private final CompanyValuationSettingsRepository valuationSettingsRepository;
  private final CompanyLegalEntityValuationSettingsRepository
      companyLegalEntityValuationSettingsRepository;
  private final CompanyLegalEntityIpvSettingsRepository companyLegalEntityIpvSettingsRepository;
  private final CompanyIpvSettingsRepository companyIpvSettingsRepository;

  public CompanyPortfolioSettingsResolver(
      PortfolioRepository portfolioRepository,
      CompanyRepository companyRepository,
      CompanyValuationSettingsRepository valuationSettingsRepository,
      CompanyLegalEntityValuationSettingsRepository companyLegalEntityValuationSettingsRepository,
      CompanyLegalEntityIpvSettingsRepository companyLegalEntityIpvSettingsRepository,
      CompanyIpvSettingsRepository companyIpvSettingsRepository) {
    this.portfolioRepository = portfolioRepository;
    this.companyRepository = companyRepository;
    this.valuationSettingsRepository = valuationSettingsRepository;
    this.companyLegalEntityValuationSettingsRepository =
        companyLegalEntityValuationSettingsRepository;
    this.companyLegalEntityIpvSettingsRepository = companyLegalEntityIpvSettingsRepository;
    this.companyIpvSettingsRepository = companyIpvSettingsRepository;
  }

  public Either<ErrorItem, PortfolioSettings<CompanyLegalEntitySettingsView>> portfolioSettings(
      String companyId, String entityId, String portfolioId, BitemporalDate stateDate) {
    return Steps.begin(
            legalEntitySettings(
                valuationSettingsResolver(stateDate, companyId), entityId, portfolioId, stateDate))
        .then(
            s ->
                legalEntitySettings(
                    ipvValuationSettingsResolver(stateDate, companyId),
                    entityId,
                    portfolioId,
                    stateDate))
        .yield(this::portfolioSettings);
  }

  private PortfolioSettings<CompanyLegalEntitySettingsView> portfolioSettings(
      PortfolioSettings<CompanyLegalEntityValuationSettingsView> valuationSettings,
      PortfolioSettings<CompanyLegalEntityIpvSettingsView> ipvSettings) {
    assert Objects.equals(valuationSettings.getView(), ipvSettings.getView());
    var settings =
        CompanyLegalEntitySettingsView.newOf(
            valuationSettings.getSettings(), ipvSettings.getSettings());
    return PortfolioSettings.settings(valuationSettings.getView(), settings);
  }

  public List<PortfolioSettings<CompanyLegalEntitySettingsView>> portfoliosSettings(
      Set<String> portfolioIds, BitemporalDate stateDate) {

    List<PortfolioView> portfolioViewList =
        this.portfolioRepository.activePortfolios(portfolioIds, stateDate);

    Set<String> allRequiredLegalEntities =
        portfolioViewList.stream().map(PortfolioView::getEntityId).collect(toSet());

    Map<String, CompanyLegalEntityValuationSettingsView> legalEntityValuationSettings =
        companyLegalEntityValuationSettingsRepository.getCompanyLegalEntitySettings(
            allRequiredLegalEntities, stateDate);

    Map<String, CompanyLegalEntityIpvSettingsView> legalEntityIpvSettings =
        companyLegalEntityIpvSettingsRepository.getValuationSettingsView(
            allRequiredLegalEntities, stateDate);

    return portfolioViewList.stream()
        .map(
            portfolioView ->
                portfolioSettings(
                    PortfolioSettings.settings(
                        portfolioView,
                        legalEntityValuationSettings.get(portfolioView.getEntityId())),
                    PortfolioSettings.settings(
                        portfolioView, legalEntityIpvSettings.get(portfolioView.getEntityId()))))
        .toList();
  }

  public List<PortfolioSettings<CompanyLegalEntitySettingsView>> portfoliosSettings(
      BitemporalDate stateDate) {
    var ipvSettings =
        settingsStream(companyId -> ipvValuationSettingsResolver(stateDate, companyId), stateDate)
            .collect(toMap(s -> s.getView().getId(), identity()));
    var valuationSettings =
        settingsStream(companyId -> valuationSettingsResolver(stateDate, companyId), stateDate)
            .collect(toMap(s -> s.getView().getId(), identity()));
    return valuationSettings.keySet().stream()
        .map(k -> portfolioSettings(valuationSettings.get(k), ipvSettings.get(k)))
        .toList();
  }

  public List<PortfolioSettings<CompanyLegalEntityIpvSettingsView>>
      companyPortfolioIpvValuationSettings(BitemporalDate stateDate, String companyId) {
    var resolver = ipvValuationSettingsResolver(stateDate, companyId);
    return legalEntitySettings(resolver, stateDate);
  }

  private <T> Either<ErrorItem, PortfolioSettings<T>> legalEntitySettings(
      CompanyLegalEntityValuationSettingsResolver<T> resolver,
      String entityId,
      String portfolioId,
      BitemporalDate stateDate) {
    return portfolioRepository.activeCompanyPortfolios(resolver.getCompanyId(), stateDate).stream()
        .filter(p -> Objects.equals(p.getEntityId(), entityId))
        .filter(p -> Objects.equals(p.getId(), portfolioId))
        .findFirst()
        .map(
            p ->
                PortfolioSettings.settings(
                    p, resolver.legalEntityValuationSettings(p.getEntityId())))
        .map(Either::<ErrorItem, PortfolioSettings<T>>right)
        .orElse(Either.left(OBJECT_NOT_FOUND.entity("Company portfolio settings not found")));
  }

  private <T> List<PortfolioSettings<T>> legalEntitySettings(
      CompanyLegalEntityValuationSettingsResolver<T> resolver, BitemporalDate stateDate) {
    return portfolioRepository.activeCompanyPortfolios(resolver.getCompanyId(), stateDate).stream()
        .map(
            p ->
                PortfolioSettings.settings(
                    p, resolver.legalEntityValuationSettings(p.getEntityId())))
        .toList();
  }

  private CompanyValuationSettingsResolver valuationSettingsResolver(
      BitemporalDate stateDate, String companyId) {
    return valuationSettingsRepository.getCompanyEntitySettingsResolver(companyId, stateDate);
  }

  private CompanyIpvSettingsResolver ipvValuationSettingsResolver(
      BitemporalDate stateDate, String companyId) {
    return companyIpvSettingsRepository.companyLegalEntityIpvSettingsResolver(companyId, stateDate);
  }

  private <T> Stream<PortfolioSettings<T>> settingsStream(
      Function<String, CompanyLegalEntityValuationSettingsResolver<T>> resolverFn,
      BitemporalDate stateDate) {
    return companyRepository.companyList(EntityTeamFilter.emptyFilter()).stream()
        .map(v -> resolverFn.apply(v.getId()))
        .map(r -> legalEntitySettings(r, stateDate))
        .flatMap(Collection::stream);
  }
}
