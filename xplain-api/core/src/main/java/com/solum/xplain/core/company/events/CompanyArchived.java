package com.solum.xplain.core.company.events;

import com.solum.xplain.core.common.EntityEvent;
import com.solum.xplain.core.common.EntityId;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyArchived extends EntityEvent {
  public CompanyArchived(String entityId) {
    super(entityId);
  }

  public static CompanyArchived newOf(EntityId entityId) {
    return new CompanyArchived(entityId.getId());
  }
}
