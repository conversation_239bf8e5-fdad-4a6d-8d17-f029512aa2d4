package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.CurrentVersionAction;
import com.solum.xplain.core.common.value.FutureVersionsAction;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.lang.Nullable;

@ToString(callSuper = true)
@ParameterObject
@EqualsAndHashCode(callSuper = true)
@Getter
public class TradeImportOptions extends ImportOptions {

  @Nullable private final Boolean onlyAllocationTrades;
  @Nullable private final String referenceTradeId;

  public TradeImportOptions(
      @NotNull LocalDate stateDate,
      @NotNull DuplicateAction duplicateAction,
      ParsingMode parsingMode,
      String comment,
      CurrentVersionAction currentVersionAction,
      FutureVersionsAction futureVersionsAction,
      FutureVersionsAction missingEntriesFutureVersionsAction,
      LocalDateTime validationTimestamp,
      Boolean onlyAllocationTrades,
      String referenceTradeId) {
    super(
        stateDate,
        duplicateAction,
        parsingMode,
        comment,
        currentVersionAction,
        futureVersionsAction,
        missingEntriesFutureVersionsAction,
        validationTimestamp);
    this.onlyAllocationTrades = onlyAllocationTrades;
    this.referenceTradeId = referenceTradeId;
  }
}
