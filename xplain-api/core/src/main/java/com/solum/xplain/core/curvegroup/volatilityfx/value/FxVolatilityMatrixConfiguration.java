package com.solum.xplain.core.curvegroup.volatilityfx.value;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityMatrix;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class FxVolatilityMatrixConfiguration extends VolatilityMatrix<FxVolatilityNodeView> {
  private FxVolatilityMatrixConfiguration(
      List<FxVolatilityNodeView> content, LocalDate versionDate) {
    super(content, versionDate);
  }

  public static FxVolatilityMatrixConfiguration configurationFromList(
      List<FxVolatilityNodeView> nodes, LocalDate versionDate) {
    return new FxVolatilityMatrixConfiguration(nodes, versionDate);
  }

  public static FxVolatilityMatrixConfiguration configurationFromList(
      VersionedList<FxVolatilityNodeView> nodes) {
    return new FxVolatilityMatrixConfiguration(nodes.getList(), nodes.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.naturalOrder();
  }
}
