package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class XCcyIborIborSwapConventionMapper extends CsvMapper<XCcyIborIborSwapConvention> {

  private static final List<CsvColumn<XCcyIborIborSwapConvention>> COLUMNS =
      List.of(
          textObject("name", XCcyIborIborSwapConvention::getName),
          textObject("spotDateOffset", XCcyIborIborSwapConvention::getSpotDateOffset),
          textObject("flatLeg.index", x -> x.getFlatLeg().getIndex()),
          textObject("flatLeg.currency", x -> x.getFlatLeg().getCurrency()),
          textObject("flatLeg.dayCount", x -> x.getFlatLeg().getDayCount()),
          textObject("flatLeg.accrualFreq", x -> x.getFlatLeg().getAccrualFrequency()),
          textObject("flatLeg.accrualBDA", x -> x.getFlatLeg().getAccrualBusinessDayAdjustment()),
          textObject("flatLeg.endDateBDA", x -> x.getFlatLeg().getEndDateBusinessDayAdjustment()),
          textObject("flatLeg.stubConv", x -> x.getFlatLeg().getStubConvention()),
          textObject("flatLeg.rollConv", x -> x.getFlatLeg().getRollConvention()),
          textObject("flatLeg.fixDateOffset", x -> x.getFlatLeg().getFixingDateOffset()),
          textObject("flatLeg.fixRelativeTo", x -> x.getFlatLeg().getFixingRelativeTo()),
          textObject("flatLeg.payFreq", x -> x.getFlatLeg().getPaymentFrequency()),
          textObject("flatLeg.payDateOffset", x -> x.getFlatLeg().getPaymentDateOffset()),
          textObject("flatLeg.compounding", x -> x.getFlatLeg().getCompoundingMethod()),
          textObject("flatLeg.notionalExc", x -> x.getFlatLeg().isNotionalExchange()),
          textObject("spreadLeg.index", x -> x.getSpreadLeg().getIndex()),
          textObject("spreadLeg.currency", x -> x.getSpreadLeg().getCurrency()),
          textObject("spreadLeg.dayCount", x -> x.getSpreadLeg().getDayCount()),
          textObject("spreadLeg.accrualFreq", x -> x.getSpreadLeg().getAccrualFrequency()),
          textObject(
              "spreadLeg.accrualBDA", x -> x.getSpreadLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "spreadLeg.endDateBDA", x -> x.getSpreadLeg().getEndDateBusinessDayAdjustment()),
          textObject("spreadLeg.stubConv", x -> x.getSpreadLeg().getStubConvention()),
          textObject("spreadLeg.rollConv", x -> x.getSpreadLeg().getRollConvention()),
          textObject("spreadLeg.fixDateOffset", x -> x.getSpreadLeg().getFixingDateOffset()),
          textObject("spreadLeg.fixRelativeTo", x -> x.getSpreadLeg().getFixingRelativeTo()),
          textObject("spreadLeg.payFreq", x -> x.getSpreadLeg().getPaymentFrequency()),
          textObject("spreadLeg.payDateOffset", x -> x.getSpreadLeg().getPaymentDateOffset()),
          textObject("spreadLeg.compounding", x -> x.getSpreadLeg().getCompoundingMethod()),
          textObject("spreadLeg.notionalExc", x -> x.getSpreadLeg().isNotionalExchange()));

  private XCcyIborIborSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource xccyIborIborSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new XCcyIborIborSwapConventionMapper(), XCcyIborIborSwapConvention.class)
        .export();
  }
}
