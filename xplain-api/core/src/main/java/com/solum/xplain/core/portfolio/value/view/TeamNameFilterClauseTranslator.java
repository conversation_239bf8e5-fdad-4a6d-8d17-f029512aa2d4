package com.solum.xplain.core.portfolio.value.view;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.core.viewconfig.filter.FilterClauseTranslator;
import com.solum.xplain.shared.utils.filter.FilterClause;
import com.solum.xplain.shared.utils.filter.FilterOperation;
import com.solum.xplain.shared.utils.filter.SimpleFilterClause;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

/**
 * Translates team name filter clauses into team ID filter clauses. Also supports a special case for
 * the "Shared" team, which is represented by a boolean field in the database.
 *
 * <p>This translator is used for both {@link
 * com.solum.xplain.core.portfolio.value.PortfolioCountedView} and {@link
 * com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCountedView}. It requires the view object to
 * have:
 *
 * <ul>
 *   <li>A field named "teamIds" which holds String IDs of teams corresponding to the names.
 *   <li>A field named "teamNames" (which is annotated with
 *       {@code @ConfigurableViewQuery(filterClauseTranslator =
 *       TeamNameFilterClauseTranslator.class)}. This field is populated in Java from the teamIds
 *       loaded by a MongoDB query.
 *   <li>A field named "allowAllTeams" which is a boolean indicating if the portfolio is shared
 *       across all teams. If this is set then {@code teamIds} and {@code teamNames} will be empty.
 * </ul>
 */
@Component
@RequiredArgsConstructor
public class TeamNameFilterClauseTranslator implements FilterClauseTranslator {
  private static final String SHARED_STRING = "Shared";
  private final TeamRepository teamRepository;

  @Override
  public boolean replaceFilterClause(SimpleFilterClause clause, Consumer<FilterClause> out) {
    var teams = teamRepository.getTeamsById().values();
    var builder = ImmutableList.<FilterClause>builder();
    Predicate<Object> matcher = clause.toStringPredicate();
    if (matcher.test(SHARED_STRING)) {
      builder.add(
          new SimpleFilterClause(
              PortfolioView.Fields.allowAllTeams, FilterOperation.EQUAL, "true"));
    }
    // Check for any team names which match the clause and turn them into IDs.
    List<String> teamIds =
        teams.stream()
            .filter(team -> matcher.test(team.getName()))
            .map(TeamNameView::getId)
            .toList();
    if (!teamIds.isEmpty()) {
      builder.add(
          new SimpleFilterClause(
              PortfolioView.Fields.teamIds, FilterOperation.EQUAL_STRICT, teamIds));
    }

    ImmutableList<FilterClause> clauses = builder.build();

    return switch (clauses.size()) {
      case 0 -> false; // No matching team names, do not replace the clause, it will fail to match.
      case 1 -> {
        out.accept(clauses.getFirst());
        yield true;
      }
      default -> {
        out.accept(
            (propertyPrefix, type, mappings, conversionService) ->
                new Criteria()
                    .orOperator(
                        clauses.stream()
                            .map(
                                filterClause ->
                                    filterClause.criteria(
                                        propertyPrefix, type, mappings, conversionService))
                            .toList()));
        yield true;
      }
    };
  }
}
