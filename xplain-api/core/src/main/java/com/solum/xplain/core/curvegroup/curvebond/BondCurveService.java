package com.solum.xplain.core.curvegroup.curvebond;

import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveSearch;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@AllArgsConstructor
@Service
public class BondCurveService {

  private final CurveGroupRepository curveGroupRepository;
  private final BondCurveRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  @Transactional
  public Either<ErrorItem, EntityId> createCurve(String groupId, BondCurveForm form) {
    return withinGroup(groupId, g -> repository.createCurve(groupId, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, BondCurveUpdateForm form) {
    return withinGroup(groupId, g -> repository.updateCurve(groupId, curveId, version, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveCurve(
      String groupId, String curveId, LocalDate version, ArchiveEntityForm form) {
    return withinGroup(groupId, g -> repository.archiveCurve(groupId, curveId, version, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteCurve(
      String groupId, String curveId, LocalDate version) {
    return withinGroup(groupId, g -> repository.deleteCurve(groupId, curveId, version));
  }

  public Either<ErrorItem, BondCurveView> getCurve(
      String groupId, String curveId, BitemporalDate stateDate) {
    return withinGroup(groupId, g -> repository.getActiveCurveView(groupId, curveId, stateDate));
  }

  public Either<ErrorItem, List<BondCurveView>> getCurves(
      String groupId, CurveConfigMarketStateForm stateForm, boolean withArchived) {
    var filter = VersionedEntityFilter.of(withArchived);
    return withinGroup(groupId, g -> right(repository.getCurveViews(groupId, stateForm, filter)));
  }

  public Either<ErrorItem, List<BondCurveView>> getCurveVersions(String groupId, String curveId) {
    return withinGroup(groupId, g -> right(repository.getCurveVersionViews(groupId, curveId)));
  }

  public Either<ErrorItem, DateList> getFutureVersions(String groupId, BondCurveSearch search) {
    return withinGroup(groupId, g -> right(repository.getFutureVersions(groupId, search)));
  }

  public Either<ErrorItem, List<BondCurveNodeCalculatedView>> getCurveNodes(
      String groupId,
      String curveId,
      BitemporalDate version,
      CurveConfigMarketStateForm stateForm) {
    var quotes = marketDataQuotesSupport.getFullQuotes(stateForm);
    var priceRequirements = stateForm.priceRequirements();
    return withinGroup(
        groupId,
        g -> right(repository.getCurveNodes(groupId, curveId, version, quotes, priceRequirements)));
  }

  private <T> Either<ErrorItem, T> withinGroup(
      String groupId, Function<CurveGroupView, Either<ErrorItem, T>> applyFunction) {
    return curveGroupRepository.getEither(groupId).flatMap(applyFunction);
  }

  public Either<ErrorItem, List<ChartPoint>> getCurveChartPoints(
      String groupId, String curveId, CurveConfigMarketStateForm stateForm) {
    return withinGroup(
        groupId, g -> right(repository.getCurveChartPoints(groupId, stateForm, curveId)));
  }
}
