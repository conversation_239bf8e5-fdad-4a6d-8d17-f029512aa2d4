package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.classifiers.Constants.SWAPTION_INDICES;

import com.opengamma.strata.basics.index.IborIndex;
import java.util.Collection;
import java.util.function.Supplier;

public class SwaptionTradeIndicesSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return SWAPTION_INDICES.stream().map(IborIndex::getName).toList();
  }
}
