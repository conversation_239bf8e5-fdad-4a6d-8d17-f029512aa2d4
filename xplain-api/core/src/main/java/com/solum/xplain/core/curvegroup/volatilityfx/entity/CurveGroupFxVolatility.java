package com.solum.xplain.core.curvegroup.volatilityfx.entity;

import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupNodesEntry;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.NonNull;

@Data
@Document(collection = CurveGroupFxVolatility.CURVE_GROUP_FX_VOLATILITY_COLLECTION)
@FieldNameConstants
public class CurveGroupFxVolatility extends VersionedEntity implements CurveGroupNodesEntry {

  public static final String CURVE_GROUP_FX_VOLATILITY_COLLECTION = "curveGroupFxVolatility";
  private Set<CurveGroupFxVolatilityNode> nodes = new HashSet<>();

  private String timeInterpolator;
  private String timeExtrapolatorLeft;
  private String timeExtrapolatorRight;
  private String strikeInterpolator;
  private String strikeExtrapolatorLeft;
  private String strikeExtrapolatorRight;

  public static CurveGroupFxVolatility newOf(String groupId) {
    CurveGroupFxVolatility c = new CurveGroupFxVolatility();
    c.setEntityId(groupId);
    c.setRecordDate(LocalDateTime.now());
    c.setState(State.ACTIVE);
    return c;
  }

  public List<CurveGroupFxVolatilityNode> filteredNodes(
      @NonNull ValidNodesFilter filter, @NonNull Set<CurrencyPair> ccysPairs) {
    return ofNullable(nodes).stream()
        .flatMap(Collection::stream)
        .filter(n -> ccysPairs.contains(n.currencyPair()))
        .map(v -> NodeInstrumentWrapper.of(v, v.fxAtmInstrumentDefinition()))
        .collect(Collectors.collectingAndThen(toList(), filter::filterNodes));
  }

  /**
   * Returns list of nodes for the given currency pairs
   *
   * @param currencyPairs currency pairs to filter on.
   */
  public Set<InstrumentDefinition> instrumentsOfCurrencyPairs(
      @NonNull Set<CurrencyPair> currencyPairs) {
    return nodes.stream()
        .filter(n -> currencyPairs.contains(n.currencyPair()))
        .flatMap(n -> n.allInstruments().stream())
        .collect(Collectors.toSet());
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return nodes.stream().flatMap(n -> n.allInstruments().stream()).toList();
  }

  @Override
  public boolean valueEquals(Object object) {
    CurveGroupFxVolatility entity = (CurveGroupFxVolatility) object;
    return Objects.equals(this.timeInterpolator, entity.timeInterpolator)
        && Objects.equals(this.timeExtrapolatorLeft, entity.timeExtrapolatorLeft)
        && Objects.equals(this.timeExtrapolatorRight, entity.timeExtrapolatorRight)
        && Objects.equals(this.strikeInterpolator, entity.strikeInterpolator)
        && Objects.equals(this.strikeExtrapolatorLeft, entity.strikeExtrapolatorLeft)
        && Objects.equals(this.strikeExtrapolatorRight, entity.strikeExtrapolatorRight)
        && super.valueEquals(entity)
        && nullSafeIsEqualCollection(this.nodes, entity.nodes);
  }
}
