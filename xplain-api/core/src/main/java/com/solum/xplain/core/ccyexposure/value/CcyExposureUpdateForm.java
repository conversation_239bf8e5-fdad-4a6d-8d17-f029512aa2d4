package com.solum.xplain.core.ccyexposure.value;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class CcyExposureUpdateForm {

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "currency")
  private String currency;

  private String description;
}
