package com.solum.xplain.core.config.converters.instrument;

import static com.solum.xplain.core.config.converters.JacksonEnumDeserializationUtils.errorMessage;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeResolver;
import jakarta.inject.Provider;
import java.io.IOException;
import java.util.Optional;

public class InstrumentTypeJacksonDeserializer extends StdDeserializer<InstrumentType> {

  private final transient Provider<InstrumentTypeResolver> typeResolverProvider;

  public InstrumentTypeJacksonDeserializer(Provider<InstrumentTypeResolver> typeResolverProvider) {
    super(InstrumentType.class);
    this.typeResolverProvider = typeResolverProvider;
  }

  @Override
  public InstrumentType deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
    var resolver = typeResolverProvider.get();
    var value = p.getText();
    return Optional.ofNullable(value)
        .map(resolver::of)
        .orElseThrow(
            () ->
                ctxt.weirdStringException(
                    value, InstrumentType.class, errorMessage(resolver.values())));
  }
}
