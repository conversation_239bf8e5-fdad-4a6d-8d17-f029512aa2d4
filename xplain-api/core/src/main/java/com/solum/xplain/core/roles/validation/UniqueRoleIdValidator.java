package com.solum.xplain.core.roles.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.roles.RoleRepository;
import com.solum.xplain.core.roles.value.RoleForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UniqueRoleIdValidator implements ConstraintValidator<UniqueRoleId, RoleForm> {

  private final RoleRepository repository;
  private final RequestPathVariablesSupport variablesSupport;

  public boolean isValid(RoleForm form, ConstraintValidatorContext context) {
    if (form != null && form.getExternalId() != null) {
      final String id = variablesSupport.getPathVariable("id");
      if (repository.existsByExternalIdExcluding(form.getExternalId(), id)) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
            .addPropertyNode(RoleForm.Fields.externalId)
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
