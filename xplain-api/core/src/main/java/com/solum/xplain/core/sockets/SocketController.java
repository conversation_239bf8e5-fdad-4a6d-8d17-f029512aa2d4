package com.solum.xplain.core.sockets;

import com.solum.xplain.core.authentication.NoRoleAuthorization;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@RestController
@RequestMapping("/sockets")
public class SocketController {
  private final SocketService socketService;

  public SocketController(SocketService socketService) {
    this.socketService = socketService;
  }

  @GetMapping("/user")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeUser(Authentication auth) {
    return ResponseEntity.of(socketService.subscribe(auth).toOptional());
  }

  @GetMapping("/ipv-data-groups/{groupId}/resolution-status")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeIpvDataGroupResolutionStatus(
      @PathVariable String groupId, Authentication auth) {
    return ResponseEntity.of(socketService.subscribeIpvDataResolutionStatus(auth).toOptional());
  }

  @GetMapping("/valuation")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeValuation(Authentication auth) {
    return ResponseEntity.of(socketService.subscribeValuation(auth).toOptional());
  }

  @GetMapping("/simulation")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeSimulation(Authentication auth) {
    return ResponseEntity.of(socketService.subscribeSimulation(auth).toOptional());
  }

  @GetMapping("/locks")
  @NoRoleAuthorization
  public ResponseEntity<SseEmitter> subscribeLocks(Authentication auth) {
    return ResponseEntity.of(socketService.subscribeLocks(auth).toOptional());
  }

  @ModelAttribute
  public void setResponseHeader(HttpServletResponse response) {
    response.setHeader("X-Accel-Buffering", "no");
  }
}
