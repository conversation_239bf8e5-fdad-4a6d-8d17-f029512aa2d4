package com.solum.xplain.core.curvegroup.curvecredit;

import static java.util.Objects.requireNonNullElse;

import com.solum.xplain.core.classifiers.CdsIndex;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreditCurveMdkNameUtils {
  private static final String NOT_AVAILABLE_MD_NAME_PREFIX = "";

  private static final String FUNDING_NODE_MD_NAME_PREFIX_PATTERN = "%S %S";
  private static final String CDS_NODE_MD_NAME_PREFIX_PATTERN = "%S %S %S %S";
  private static final String CDS_INDEX_NODE_MD_NAME_PREFIX_PATTERN = "%S S%S V%S";
  private static final String CDS_INDEX_TRANCHE_NODE_MD_NAME_PREFIX_PATTERN =
      CDS_INDEX_NODE_MD_NAME_PREFIX_PATTERN + " T%S";

  private static final String MISSING_INDEX_VALUE = "_TBD";
  private static final String MISSING_CDS_VALUE = "TBD";
  private static final String MISSING_INDEX_NAME_VALUE = "INDEXNAME_TBD";

  public static String fundingNodeMdkNamePrefix(CreditCurve curve) {
    return switch (curve.getCurveType()) {
      case CDS ->
          FUNDING_NODE_MD_NAME_PREFIX_PATTERN.formatted(
              requireNonNullElse(curve.getCorpTicker(), MISSING_CDS_VALUE), curve.getCurrency());
      case CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> NOT_AVAILABLE_MD_NAME_PREFIX;
    };
  }

  public static String cdsNodeMdkNamePrefix(CreditCurve curve) {
    return switch (curve.getCurveType()) {
      case CDS ->
          CDS_NODE_MD_NAME_PREFIX_PATTERN.formatted(
              requireNonNullElse(curve.getCorpTicker(), MISSING_CDS_VALUE),
              curve.getCurrency(),
              curve.getSeniority(),
              curve.getDocClause());
      case CREDIT_INDEX -> creditIndexCdsNodeMdkNamePrefix(curve);
      case CREDIT_INDEX_TRANCHE -> creditIndexTrancheCdsNodeMdkNamePrefix(curve);
    };
  }

  private static String creditIndexCdsNodeMdkNamePrefix(CreditCurve curve) {
    return CDS_INDEX_NODE_MD_NAME_PREFIX_PATTERN.formatted(
        requireNonNullElse(
            CdsIndex.labelValueOf(curve.getEntityLongName()), MISSING_INDEX_NAME_VALUE),
        requireNonNullElse(curve.getCreditIndexSeries(), MISSING_INDEX_VALUE),
        requireNonNullElse(curve.getCreditIndexVersion(), MISSING_INDEX_VALUE));
  }

  private static String creditIndexTrancheCdsNodeMdkNamePrefix(CreditCurve curve) {
    return CDS_INDEX_TRANCHE_NODE_MD_NAME_PREFIX_PATTERN.formatted(
        requireNonNullElse(
            CdsIndex.labelValueOf(curve.getEntityLongName()), MISSING_INDEX_NAME_VALUE),
        requireNonNullElse(curve.getCreditIndexSeries(), MISSING_INDEX_VALUE),
        requireNonNullElse(curve.getCreditIndexVersion(), MISSING_INDEX_VALUE),
        requireNonNullElse(curve.getCreditIndexTranche(), MISSING_INDEX_VALUE));
  }
}
