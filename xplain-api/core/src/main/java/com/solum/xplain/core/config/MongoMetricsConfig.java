package com.solum.xplain.core.config;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener;
import org.springframework.boot.autoconfigure.mongo.MongoClientSettingsBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MongoMetricsConfig {

  @Bean
  public MongoClientSettingsBuilderCustomizer mongoClientSettingsBuilderCustomizer(
      MeterRegistry meterRegistry) {
    return builder -> builder.addCommandListener(new MongoMetricsCommandListener(meterRegistry));
  }
}
