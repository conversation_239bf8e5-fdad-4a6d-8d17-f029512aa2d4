package com.solum.xplain.core.accesslog.value;

import com.solum.xplain.core.accesslog.entity.AccessLogEntry;
import com.solum.xplain.core.accesslog.validation.ValidAccessLogFilter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.mongodb.core.query.Criteria;

@Data
@ParameterObject
@ValidAccessLogFilter
public class AccessLogFilter {

  private final String userId;
  private final String name;

  public Criteria filterCriteria() {
    var criteria = new Criteria();
    if (StringUtils.isNotEmpty(userId)) {
      criteria.and(AccessLogEntry.Fields.userId).is(userId);
    }
    if (StringUtils.isNotEmpty(name)) {
      criteria.and(AccessLogEntry.Fields.name).is(name);
    }

    return criteria;
  }
}
