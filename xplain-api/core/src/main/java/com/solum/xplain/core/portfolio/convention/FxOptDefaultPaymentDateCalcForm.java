package com.solum.xplain.core.portfolio.convention;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

@ParameterObject
@Data
public class FxOptDefaultPaymentDateCalcForm {

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private final String domesticCcy;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private final String foreignCcy;

  @NotNull private final LocalDate expiryDate;
}
