package com.solum.xplain.core.curvegroup.curve;

import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.dto.GetCalibratedCurvesRequest;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curve.value.CurveSearch;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveGroupCurveService {
  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupCurveRepository curveRepository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  @Transactional
  public Either<ErrorItem, EntityId> createCurve(String groupId, CurveForm form) {
    return groupEither(groupId)
        .flatMap(g -> curveRepository.createCurve(groupId, form))
        .flatMap(createdCurveId -> clearCalibration(groupId).map(r -> createdCurveId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, CurveUpdateForm form) {
    return groupEither(groupId)
        .flatMap(g -> curveRepository.updateCurve(groupId, curveId, version, form))
        .flatMap(updatedCurveId -> clearCalibration(groupId).map(r -> updatedCurveId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> archiveCurve(
      String groupId, String curveId, LocalDate version, ArchiveEntityForm form) {
    return groupEither(groupId)
        .flatMap(g -> curveRepository.archiveCurve(groupId, curveId, version, form))
        .flatMap(updatedCurveId -> clearCalibration(groupId).map(r -> updatedCurveId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteCurve(
      String groupId, String curveId, LocalDate version) {
    return groupEither(groupId)
        .flatMap(g -> curveRepository.deleteCurve(groupId, curveId, version))
        .flatMap(updatedCurveId -> clearCalibration(groupId).map(r -> updatedCurveId));
  }

  public Either<ErrorItem, CurveView> getCurve(
      String groupId, String curveId, BitemporalDate stateDate) {
    return groupEither(groupId)
        .flatMap(g -> curveRepository.getActiveCurveView(groupId, curveId, stateDate));
  }

  public Either<ErrorItem, List<CurveView>> getCurves(
      String groupId, BitemporalDate stateDate, boolean withArchived) {
    return groupEither(groupId)
        .map(
            g ->
                curveRepository.getCurveViews(
                    groupId, stateDate, VersionedEntityFilter.of(withArchived)));
  }

  public Either<ErrorItem, List<CurveView>> getCurves(
      String groupId,
      BitemporalDate stateDate,
      GetCalibratedCurvesRequest getCalibratedCurvesRequest) {
    return groupEither(groupId)
        .map(
            g ->
                curveRepository.getCurveViews(
                    groupId,
                    stateDate,
                    getCalibratedCurvesRequest,
                    VersionedEntityFilter.of(getCalibratedCurvesRequest.getWithArchived())));
  }

  public Either<ErrorItem, List<CurveView>> getCurveVersions(String groupId, String curveId) {
    return groupEither(groupId).map(g -> curveRepository.getCurveVersionViews(groupId, curveId));
  }

  public Either<ErrorItem, DateList> getFutureVersions(String groupId, CurveSearch search) {
    return groupEither(groupId).map(g -> curveRepository.getFutureVersions(groupId, search));
  }

  public Either<ErrorItem, List<CurveNodeCalculatedView>> getCurveNodes(
      String groupId,
      String curveId,
      BitemporalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      CalibratedCurvesOptions calibrationOptions,
      ReferenceData referenceData) {
    return groupEither(groupId)
        .map(
            g ->
                curveRepository.getCurveNodes(
                    g, curveId, version, marketStateQuotes, calibrationOptions, referenceData));
  }

  public Either<ErrorItem, List<CurveNodeCalculatedView>> getCurveNodes(
      String groupId,
      String curveId,
      BitemporalDate version,
      CurveConfigMarketStateForm stateForm,
      CalibratedCurvesOptions calibrationOptions) {
    var quotes = marketDataQuotesSupport.getFullQuotes(stateForm);
    var marketStateQuotes = new CurveConfigMarketStateQuotes(stateForm, quotes);
    return groupEither(groupId)
        .map(
            g ->
                curveRepository.getCurveNodes(
                    g, curveId, version, marketStateQuotes, calibrationOptions));
  }

  private Either<ErrorItem, EntityId> clearCalibration(String groupId) {
    return curveGroupRepository.clearCalibrationResults(groupId);
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
