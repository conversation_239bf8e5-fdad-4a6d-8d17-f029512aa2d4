package com.solum.xplain.core.accesslog;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.accesslog.entity.AccessLogEntry;
import com.solum.xplain.core.accesslog.value.AccessLogEntryView;
import com.solum.xplain.core.accesslog.value.AccessLogFilter;
import com.solum.xplain.core.accesslog.value.LatestUserAccessView;
import com.solum.xplain.core.common.ListEntries;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class AccessLogRepository {

  private final MongoOperations mongoOperations;

  public void saveLogEntry(AccessLogEntry log) {
    mongoOperations.save(log);
  }

  public ScrollableEntry<AccessLogEntryView> accessLogsScrollable(
      ScrollRequest scrollRequest, AccessLogFilter filter) {
    var aggregationBuilder = ImmutableList.<AggregationOperation>builder();
    aggregationBuilder.add(match(filter.filterCriteria()));
    aggregationBuilder.addAll(
        new ScrollSortOperations(scrollRequest, AccessLogEntryView.Fields.id)
            .withLimitPlusOne()
            .withDefaultSort(defaultSort())
            .build());

    var entries =
        mongoOperations
            .aggregateAndReturn(AccessLogEntryView.class)
            .by(Aggregation.newAggregation(AccessLogEntry.class, aggregationBuilder.build()))
            .all()
            .getMappedResults();
    return ScrollableEntry.limitByPageSize(entries, scrollRequest);
  }

  public ListEntries<LatestUserAccessView> latestUsersLogins() {
    var aggregationBuilder = ImmutableList.<AggregationOperation>builder();
    aggregationBuilder.add(
        sort(defaultSort()),
        group(AccessLogEntry.Fields.userId)
            .first(AccessLogEntry.Fields.name)
            .as(AccessLogEntry.Fields.name)
            .first(AccessLogEntry.Fields.loggedAt)
            .as(AccessLogEntry.Fields.loggedAt),
        project(AccessLogEntry.Fields.name)
            .and(UNDERSCORE_ID)
            .as(LatestUserAccessView.Fields.userId)
            .and(AccessLogEntry.Fields.loggedAt)
            .as(LatestUserAccessView.Fields.lastAccessTimestamp),
        sort(Sort.by(Direction.DESC, LatestUserAccessView.Fields.lastAccessTimestamp)));

    var entries =
        mongoOperations
            .aggregateAndReturn(LatestUserAccessView.class)
            .by(Aggregation.newAggregation(AccessLogEntry.class, aggregationBuilder.build()))
            .all()
            .getMappedResults();
    return ListEntries.newOf(entries);
  }

  private Sort defaultSort() {
    // ObjectId holds timestamp, so we can sort by ID which will be faster than separate field
    return Sort.by(Direction.DESC, AccessLogEntry.Fields.id);
  }
}
