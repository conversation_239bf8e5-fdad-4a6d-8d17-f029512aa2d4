package com.solum.xplain.core.providers.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.providers.DataProvider.INTERNAL_PROVIDER_CODES;
import static io.atlassian.fugue.Either.left;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.providers.enums.DataProviderType;
import io.atlassian.fugue.Either;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class DataProviderCsvLoader extends GenericCsvLoader<DataProvider, String> {
  static final String EXTERNAL_ID_FIELD = "ID";
  static final String NAME_FIELD = "Long Name";
  static final String TYPE_FIELD = "Data Type";

  private static final ClassifierSupplier SUPPLIER =
      new ClassifierSupplier("marketDataProviderType");
  private static final String TYPE_DELIMITER = "\\|";

  @Override
  protected CsvParserResultBuilder<DataProvider, String> createResult(ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        DataProvider::getExternalId, Function.identity(), parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(EXTERNAL_ID_FIELD, NAME_FIELD);
  }

  @Override
  protected Either<ErrorItem, DataProvider> parseLine(CsvRow csvRow) {
    try {
      DataProvider marketDataProvider = new DataProvider();
      var extId = CsvLoaderUtils.parseIdentifier(csvRow, EXTERNAL_ID_FIELD);
      marketDataProvider.setName(csvRow.getValue(NAME_FIELD));
      marketDataProvider.setExternalId(
          validateValue(extId, !INTERNAL_PROVIDER_CODES.contains(extId)));
      marketDataProvider.setTypes(parseTypes(csvRow));

      return Either.right(marketDataProvider);
    } catch (IllegalArgumentException e) {
      return left(rowParsingError(csvRow, e));
    }
  }

  private Set<DataProviderType> parseTypes(CsvRow row) {
    return Arrays.stream(row.getValue(TYPE_FIELD).split(TYPE_DELIMITER))
        .map(v -> CsvLoaderUtils.validateValue(v, SUPPLIER))
        .map(DataProviderType::valueOf)
        .collect(Collectors.toSet());
  }
}
