package com.solum.xplain.core.company.csv;

import static com.solum.xplain.core.company.csv.CommonCompanyCsvFields.COMPANY_DESCRIPTION;
import static com.solum.xplain.core.company.csv.CommonCompanyCsvFields.COMPANY_EXTERNAL_ID;
import static com.solum.xplain.core.company.csv.CommonCompanyCsvFields.COMPANY_NAME;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseCurveConfiguration;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseExternalCompanyId;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseMarketDataGroup;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseSlaDeadline;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseTeams;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseValuationDataGroup;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.company.CompanyCsvData;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedCompanyView;
import com.solum.xplain.core.market.value.MarketDataGroupView;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
@AllArgsConstructor(staticName = "newOf")
public class CompanyCsvLoader extends GenericCsvLoader<CompanyCsvForm, CompanyUniqueKey> {

  static final String ALLOW_ALL_TEAMS = "Allow all teams";
  private final Map<String, MarketDataGroupView> marketDataGroupsMap;
  private final Map<String, IpvDataGroupCondensedCompanyView> ipvDataGroupsMap;
  private final Map<String, String> curveConfigurationsMap;
  private final Map<String, String> teamsMap;
  private final Set<String> userTeamIds;

  @Override
  protected CsvParserResultBuilder<CompanyCsvForm, CompanyUniqueKey> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CompanyUniqueKey::fromForm, CompanyUniqueKey::externalCompanyId, parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(COMPANY_EXTERNAL_ID, TEAMS, ALLOW_ALL_TEAMS);
  }

  @Override
  protected Either<ErrorItem, CompanyCsvForm> parseLine(CsvRow row) {
    return Steps.begin(parseExternalCompanyId(row))
        .then(cId -> parseValuationDataGroup(row, cId, ipvDataGroupsMap))
        .then((cId, vdG) -> parseMarketDataGroup(row, cId, marketDataGroupsMap))
        .then(() -> parseSlaDeadline(row))
        .then(() -> parseCurveConfiguration(row, curveConfigurationsMap))
        .then(() -> parseTeams(row, teamsMap, userTeamIds))
        .yield(
            (cId, vdg, mdg, sla, cconfig, teams) -> {
              var companyName = row.findValue(COMPANY_NAME).orElse(cId);
              var description = row.findValue(COMPANY_DESCRIPTION).orElse(null);

              var csvData =
                  new CompanyCsvData(
                      cId,
                      companyName,
                      description,
                      sla.orElse(null),
                      vdg.orElse(null),
                      mdg.orElse(null),
                      cconfig.orElse(null),
                      teams);

              return CompanyCsvForm.of(csvData);
            });
  }
}
