package com.solum.xplain.core.curvegroup.conventions.bond;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention;
import com.solum.xplain.core.classifiers.BondCurveNodeTypes;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import java.util.Set;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
class ImmutableBondCurveConvention implements CurveConvention {

  private final String name;
  private final Currency currency;
  private final FixedCouponBondYieldConvention yieldConvention;

  @Override
  public String getKey() {
    return getName();
  }

  @Override
  public Set<String> getAllPermissibleNodeTypes() {
    return Set.of(BondCurveNodeTypes.BOND_YIELD_NODE);
  }
}
