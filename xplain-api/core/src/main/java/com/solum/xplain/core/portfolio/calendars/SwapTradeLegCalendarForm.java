package com.solum.xplain.core.portfolio.calendars;

import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.IborIndicesSupplier;
import com.solum.xplain.core.common.validation.OvernightIndicesSupplier;
import com.solum.xplain.core.common.validation.PriceIndicesSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@ValidSwapTradeLegCalendarForm
public class SwapTradeLegCalendarForm {

  @ValidStringSet(
      value = {
        IborIndicesSupplier.class,
        OvernightIndicesSupplier.class,
        PriceIndicesSupplier.class
      })
  private String index;

  @ValidStringSet(CurrenciesSupplier.class)
  private String currency;

  @NotNull private Boolean isOffshore;
}
