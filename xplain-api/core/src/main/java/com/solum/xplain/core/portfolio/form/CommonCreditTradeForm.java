package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_FREQUENCY_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DOC_CLAUSE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.Constants.ACT_360_DAY_COUNT;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.PositionTypeBuySupplier;
import com.solum.xplain.core.common.validation.PositionTypeSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditCurrency;
import com.solum.xplain.core.portfolio.builder.CommonCreditTradeDetails;
import com.solum.xplain.core.portfolio.validation.ValidPaymentPeriod;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.CdsTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.HasPaymentPeriod;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.PositionType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.EnumUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ValidPaymentPeriod
public class CommonCreditTradeForm extends BespokeTradeForm implements HasPaymentPeriod {

  @NotNull private LocalDate startDate;

  @NotNull private LocalDate endDate;

  @NotEmpty
  @ValidStringSet(PositionTypeSupplier.class)
  @ValidStringSet(value = PositionTypeBuySupplier.class, groups = ReferenceTradeGroup.class)
  private String position;

  @NotEmpty @ValidIdentifier private String reference;

  @NotEmpty @ValidCreditCurrency private String currency;

  @NotNull(groups = BespokeTradeGroup.class)
  @Null(groups = ReferenceTradeGroup.class)
  @Positive
  private Double notionalValue;

  @Pattern(regexp = ACT_360_DAY_COUNT, message = "{com.solum.xplain.api.portfolio.form.dayCount}")
  private String dayCount;

  @NotNull private Double fixedRate;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = CREDIT_FREQUENCY_CLASSIFIER)
  private String frequency;

  @NotEmpty(groups = CdsTradeGroup.class)
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = DOC_CLAUSE_CLASSIFIER_NAME)
  private String docClause;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = SECTOR_CLASSIFIER_NAME)
  private String sector;

  @Valid private PaymentDateForm upfrontFee;

  public CommonCreditTradeDetails commonDetails() {
    var commonBuilder =
        CommonCreditTradeDetails.builder()
            .reference(reference)
            .currency(Currency.parse(currency))
            .notional(notionalValue)
            .dayCount(dayCount)
            .endDate(endDate)
            .startDate(startDate)
            .paymentFrequency(Frequency.parse(frequency))
            .fixedRate(fixedRate)
            .sector(EnumUtils.getEnum(CreditSector.class, sector))
            .docClause(EnumUtils.getEnum(CreditDocClause.class, docClause))
            .position(PositionType.valueOf(position));

    Optional.ofNullable(upfrontFee)
        .ifPresent(
            upfront -> {
              commonBuilder.upfrontFee(upfront.getAmount());
              commonBuilder.upfrontDate(upfront.getDate());
              commonBuilder.upfrontConvention(upfront.getConvention());
            });

    return commonBuilder.build();
  }

  @Override
  protected String tradeCurrency() {
    return currency;
  }
}
