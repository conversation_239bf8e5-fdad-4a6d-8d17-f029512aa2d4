package com.solum.xplain.core.settings.csv;

import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class CurveCalibrationSettings<T> {
  private final String curveName;
  private final T settings;

  public static <T> List<CurveCalibrationSettings<T>> of(Map<String, T> settings) {
    return settings.entrySet().stream()
        .map(m -> new CurveCalibrationSettings<>(m.getKey(), m.getValue()))
        .toList();
  }
}
