package com.solum.xplain.core.lock;

import static java.lang.String.format;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class XplainLockResolver {

  private static final String NO_PATH_VARIABLE_ERROR_TEMPLATE =
      "Path variable %s not present in request";

  private final RequestPathVariablesSupport requestPathVariablesSupport;

  public XplainLockResolver(RequestPathVariablesSupport requestPathVariablesSupport) {
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  public List<XplainLock> resolveLocks(@NonNull Method method) {
    var annotations = requireLockAnnotations(method);
    return annotations.stream().map(this::resolveLock).toList();
  }

  private List<RequireLock> requireLockAnnotations(Method method) {
    var annotation = AnnotationUtils.findAnnotation(method, RequireLock.class);
    return annotation == null ? requireLockList(method).toList() : List.of(annotation);
  }

  private Stream<RequireLock> requireLockList(Method method) {
    return Optional.ofNullable(AnnotationUtils.findAnnotation(method, RequireLock.List.class))
        .map(RequireLock.List::value)
        .stream()
        .flatMap(Arrays::stream);
  }

  private XplainLock resolveLock(RequireLock lock) {
    return switch (lock.type()) {
      case PATH_VARIABLE -> XplainLock.newOf(lock.prefix(), resolvePathVariableValue(lock.name()));
      case STATIC -> XplainLock.newOf(lock.prefix(), lock.name());
    };
  }

  private String resolvePathVariableValue(@NonNull String name) {
    return Optional.ofNullable(requestPathVariablesSupport.getPathVariable(name))
        .orElseThrow(
            () -> new IllegalArgumentException(format(NO_PATH_VARIABLE_ERROR_TEMPLATE, name)));
  }
}
