package com.solum.xplain.core.viewconfig.provider;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.viewconfig.ViewConfigurationMapper;
import com.solum.xplain.core.viewconfig.ViewConfigurationRepository;
import com.solum.xplain.core.viewconfig.value.View;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView;
import java.util.List;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
public class RepositoryViewConfigurationProvider implements ViewConfigurationProvider {

  private final ViewConfigurationMapper mapper;
  private final ViewConfigurationRepository repository;

  public RepositoryViewConfigurationProvider(
      ViewConfigurationMapper mapper, ViewConfigurationRepository repository) {
    this.mapper = mapper;
    this.repository = repository;
  }

  @Override
  public <T> List<ViewConfigurationView<T>> provideViewConfigurations(
      Authentication user, Class<T> viewClass) {
    var xplainUser = (XplainPrincipal) user.getPrincipal();
    View<T> scope = new View<>(viewClass);
    return repository.findAllUserVisibleViews(scope, xplainUser.getId()).stream()
        .map(entity -> mapper.toViewConfigurationView(scope, entity, entity.ownedBy(xplainUser)))
        .toList();
  }
}
