package com.solum.xplain.core.market.validation;

import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.market.value.MarketDataKeyForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.stream.Stream;

public class ValidFxRateKeyValidator
    implements ConstraintValidator<ValidFxRateKey, MarketDataKeyForm> {

  public static boolean isValidKey(String key, String instrumentType) {
    if (!CoreInstrumentType.FX_RATE.name().equals(instrumentType)) {
      return true;
    }

    return isValidLength(key)
        && Stream.of(key.split("/")).allMatch(CurrenciesSupplier::validCurrency);
  }

  private static boolean isValidLength(String key) {
    return key != null && key.length() == 7;
  }

  @Override
  public boolean isValid(MarketDataKeyForm form, ConstraintValidatorContext context) {
    if (!isValidKey(form.getKey(), form.getInstrumentType())) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("key")
          .addConstraintViolation();

      return false;
    }
    return true;
  }
}
