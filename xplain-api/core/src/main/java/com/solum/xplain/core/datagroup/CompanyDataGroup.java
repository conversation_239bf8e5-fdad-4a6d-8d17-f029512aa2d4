package com.solum.xplain.core.datagroup;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.entity.CompanyReference;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public interface CompanyDataGroup {
  List<CompanyReference> getCompanies();

  Boolean getAllowAllCompanies();

  default boolean allowedForCompany(String companyId) {
    return getAllowAllCompanies()
        || getCompanies().stream()
            .map(EntityReference::getEntityId)
            .anyMatch(id -> StringUtils.equals(id, companyId));
  }
}
