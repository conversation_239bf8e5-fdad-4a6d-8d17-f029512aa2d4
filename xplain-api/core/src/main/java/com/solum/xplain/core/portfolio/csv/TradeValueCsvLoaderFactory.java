package com.solum.xplain.core.portfolio.csv;

import static com.solum.xplain.core.portfolio.csv.TradeValueRowFilterUtils.portfolioRowsFilter;
import static com.solum.xplain.core.portfolio.csv.TradeValueRowFilterUtils.referenceRowsFilter;
import static com.solum.xplain.core.portfolio.repository.PortfolioRepositoryFilter.activePortfolios;
import static java.util.Collections.singletonMap;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.customfield.CustomFieldNameRepository;
import com.solum.xplain.core.externalsource.IdentifierSourceRepository;
import com.solum.xplain.core.portfolio.PortfolioTeamFilterProvider;
import com.solum.xplain.core.portfolio.ReferenceTradesProvider;
import com.solum.xplain.core.portfolio.csv.loader.CustomFieldsCsvLoader;
import com.solum.xplain.core.portfolio.csv.loader.ExternalTradeIdsCsvLoader;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioItemRefDetailsView;
import com.solum.xplain.core.portfolio.value.PortfolioNamesUniqueKey;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.product.csv.ProductCsvLoaders;
import com.solum.xplain.core.settings.value.ExceptionManagementSettingsProvider;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TradeValueCsvLoaderFactory {
  private final ProductCsvLoaders loaders;
  private final ReferenceTradesProvider referenceTradesProvider;
  private final PortfolioTeamFilterProvider teamFilterProvider;
  private final PortfolioRepository portfolioRepository;
  private final IdentifierSourceRepository identifierSourceRepository;
  private final CustomFieldNameRepository customFieldNameRepository;
  private final ExceptionManagementSettingsProvider exceptionManagementSettingsProvider;

  public TradeCsvLoader standard(
      boolean onlyAllocationTrades, String referenceTradeId, LocalDate stateDate) {
    var referencedTradeIds = references(stateDate);
    var externalTradeIdsCsvLoader = externalTradeIdsCsvLoader();
    var valueCsvLoader =
        tradeValueCsvLoader(
            externalTradeIdsCsvLoader, referencedTradeIds, onlyAllocationTrades, stateDate);

    return new TradeCsvLoader(
        portfolioNames(),
        referenceRowsFilter(onlyAllocationTrades, referenceTradeId),
        valueCsvLoader,
        externalTradeIdsCsvLoader,
        customFieldsCsvLoader());
  }

  public TradeCsvLoader forPortfolio(PortfolioView view, LocalDate stateDate) {
    var referencedTradeIds = references(stateDate);
    var externalTradeIdsCsvLoader = externalTradeIdsCsvLoader();
    var valueCsvLoader =
        tradeValueCsvLoader(externalTradeIdsCsvLoader, referencedTradeIds, false, stateDate);

    return new TradeCsvLoader(
        singletonMap(PortfolioNamesUniqueKey.fromView(view), view),
        portfolioRowsFilter(view),
        valueCsvLoader,
        externalTradeIdsCsvLoader,
        customFieldsCsvLoader());
  }

  private Map<PortfolioNamesUniqueKey, PortfolioCondensedView> portfolioNames() {
    var entityTeamFilter = teamFilterProvider.provideFilter();
    return portfolioRepository
        .portfolioCondensedViewList(activePortfolios(), entityTeamFilter)
        .stream()
        .filter(v -> isNotEmpty(v.getExternalPortfolioId()))
        .collect(
            Collectors.toMap(PortfolioNamesUniqueKey::fromView, Function.identity(), (a, b) -> a));
  }

  private TradeValueCsvLoader tradeValueCsvLoader(
      ExternalTradeIdsCsvLoader externalTradeIdsCsvLoader,
      Map<String, PortfolioItemRefDetailsView> referencedTradeIds,
      boolean onlyAllocationTrades,
      LocalDate stateDate) {

    var customFieldsCsvLoader = customFieldsCsvLoader();
    var onboardingPeriod =
        exceptionManagementSettingsProvider.getOnboardingPeriod(BitemporalDate.newOf(stateDate));

    return new TradeValueCsvLoader(
        loaders,
        externalTradeIdsCsvLoader,
        customFieldsCsvLoader,
        referencedTradeIds,
        onlyAllocationTrades,
        false,
        onboardingPeriod);
  }

  @Nonnull
  private CustomFieldsCsvLoader customFieldsCsvLoader() {
    return new CustomFieldsCsvLoader(customFieldNameRepository.activeFieldExternalIds());
  }

  private ExternalTradeIdsCsvLoader externalTradeIdsCsvLoader() {
    return new ExternalTradeIdsCsvLoader(
        identifierSourceRepository.activeIdentifierSourcesExtIds());
  }

  private Map<String, PortfolioItemRefDetailsView> references(LocalDate stateDate) {
    return referenceTradesProvider.fetchActiveReferenceTradeExternalIds(stateDate);
  }
}
