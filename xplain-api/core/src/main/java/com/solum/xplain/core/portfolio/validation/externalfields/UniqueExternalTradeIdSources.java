package com.solum.xplain.core.portfolio.validation.externalfields;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueExternalTradeIdSourcesValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface UniqueExternalTradeIdSources {
  String message() default
      "{com.solum.xplain.api.portfolio.validation.UniqueExternalTradeIdSources.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
