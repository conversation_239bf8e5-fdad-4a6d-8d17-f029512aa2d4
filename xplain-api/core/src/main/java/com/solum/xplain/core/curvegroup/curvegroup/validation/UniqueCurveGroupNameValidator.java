package com.solum.xplain.core.curvegroup.curvegroup.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UniqueCurveGroupNameValidator
    implements ConstraintValidator<UniqueCurveGroupName, CurveGroupForm> {
  @Autowired private CurveGroupRepository curveGroupRepository;
  @Autowired private RequestPathVariablesSupport requestPathVariablesSupport;

  public boolean isValid(CurveGroupForm form, ConstraintValidatorContext context) {
    if (isNotEmpty(form.getName())) {
      String groupId = requestPathVariablesSupport.getPathVariable("groupId");
      boolean hasDuplicate = curveGroupRepository.hasCurveGroupByName(form.getName(), groupId);

      if (hasDuplicate) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate("NotUnique")
            .addPropertyNode("name")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
