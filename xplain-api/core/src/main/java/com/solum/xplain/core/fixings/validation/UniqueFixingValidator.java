package com.solum.xplain.core.fixings.validation;

import com.solum.xplain.core.fixings.Fixing;
import com.solum.xplain.core.fixings.FixingRepository;
import com.solum.xplain.core.fixings.value.FixingCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.function.Predicate;
import org.springframework.stereotype.Component;

@Component
public class UniqueFixingValidator implements ConstraintValidator<UniqueFixing, FixingCreateForm> {

  private final FixingRepository fixingRepository;

  public UniqueFixingValidator(FixingRepository fixingRepository) {
    this.fixingRepository = fixingRepository;
  }

  @Override
  public boolean isValid(FixingCreateForm value, ConstraintValidatorContext context) {
    var uniqueKey = FixingUniqueKey.from(value);
    return fixingRepository
        .fixingByUniqueKey(uniqueKey)
        .filter(Predicate.not(Fixing::isArchived))
        .isEmpty();
  }
}
