package com.solum.xplain.core.ipv.tradeleveloverride.value;

import com.solum.xplain.core.company.value.ProvidersVoView;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableView;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@Data
@FieldNameConstants
@ConfigurableView
public class TradeLevelOverrideView {

  @ConfigurableViewQuery(sortable = true)
  @NotNull
  private String valuationDataKey;

  @ConfigurableViewQuery(sortable = true)
  @NotNull
  private String externalCompanyId;

  @ConfigurableViewQuery(sortable = true)
  @NotNull
  private String externalEntityId;

  @ConfigurableViewQuery(sortable = true)
  @NotNull
  private String externalPortfolioId;

  @ConfigurableViewQuery(sortable = true)
  @NotNull
  private String externalPortfolioItemId;

  @ConfigurableViewQuery(sortable = true)
  @Nullable
  private String valuationDataGroupName;

  @ConfigurableViewField @Nullable private ProvidersVoView allProvidersData;

  @NotNull private String entityId;

  @NotNull private LocalDate validFrom;
}
