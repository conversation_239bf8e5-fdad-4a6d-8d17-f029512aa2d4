package com.solum.xplain.core.common.csv;

import static com.solum.xplain.core.common.CollectionUtils.toGroupMapConcurrent;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.Sets;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@Getter
public class LargeVersionedImportItems<V, K, E extends EmbeddedVersionEntity<V>>
    implements VersionedImportItems<V, K, E> {

  private final Map<K, EntityForUpdate<V, E>> existingActiveItemsByKeys;
  private final Map<K, EntityForUpdate<V, E>> existingArchivedItemsByKeys;
  private final Map<K, V> importItemsByKeys;

  private final Set<K> newItemsKeys;
  private final Set<K> duplicateActiveItemsKeys;
  private final Set<K> spareItemsKeys;
  private final Set<K> duplicateArchivedItemsKeys;

  private final Map<E, V> newItemsEntities;

  @Builder
  public LargeVersionedImportItems(
      List<EntityForUpdate<V, E>> existingItems,
      Function<EntityForUpdate<V, E>, K> existingItemToKeyFn,
      List<ResolvableEmbeddedVersionValue<V, K>> importItems,
      Function<ResolvableEmbeddedVersionValue<V, K>, K> importItemToKeyFn,
      Function<K, E> newEntityFn) {
    var existingItemsByState = existingItemsByState(existingItems);
    this.existingActiveItemsByKeys =
        CollectionUtils.toMapConcurrent(
            existingItemsByState.get(State.ACTIVE), existingItemToKeyFn);
    this.existingArchivedItemsByKeys =
        CollectionUtils.toMapConcurrent(
            existingItemsByState.get(State.ARCHIVED), existingItemToKeyFn);

    this.importItemsByKeys =
        importItems.parallelStream()
            .collect(toMap(importItemToKeyFn, ResolvableEmbeddedVersionValue::toVersionValue));

    Set<K> activeItemsKeys = existingActiveItemsByKeys.keySet();
    Set<K> archivedItemsKeys = existingArchivedItemsByKeys.keySet();
    Set<K> existingItemsKeys = Sets.union(activeItemsKeys, archivedItemsKeys);
    Set<K> importItemsKeys = importItemsByKeys.keySet();
    this.newItemsKeys = Sets.difference(importItemsKeys, existingItemsKeys);
    this.duplicateActiveItemsKeys = Sets.intersection(importItemsKeys, activeItemsKeys);
    this.duplicateArchivedItemsKeys = Sets.intersection(importItemsKeys, archivedItemsKeys);
    this.spareItemsKeys = Sets.difference(activeItemsKeys, importItemsKeys);
    this.newItemsEntities =
        importItemsByKeys.entrySet().stream()
            .filter(e -> newItemsKeys.contains(e.getKey()))
            .collect(toMap(e -> newEntityFn.apply(e.getKey()), Entry::getValue));
  }

  public Map<E, V> getNewItems() {
    return newItemsEntities;
  }

  public Map<EntityForUpdate<V, E>, V> getActiveDuplicateItems() {
    return importItemsByKeys.entrySet().stream()
        .filter(e -> existingActiveItemsByKeys.containsKey(e.getKey()))
        .collect(toMap(e -> existingActiveItemsByKeys.get(e.getKey()), Entry::getValue));
  }

  public Map<EntityForUpdate<V, E>, V> getArchivedDuplicateItems() {
    return importItemsByKeys.entrySet().stream()
        .filter(e -> existingArchivedItemsByKeys.containsKey(e.getKey()))
        .collect(toMap(e -> existingArchivedItemsByKeys.get(e.getKey()), Entry::getValue));
  }

  public EntityForUpdate<V, E> getExistingActiveItem(K key) {
    return existingActiveItemsByKeys.get(key);
  }

  public List<EntityForUpdate<V, E>> getSpareExistingItems() {
    return collectItems(spareItemsKeys, existingActiveItemsByKeys);
  }

  private Map<State, List<EntityForUpdate<V, E>>> existingItemsByState(
      List<EntityForUpdate<V, E>> existingItems) {
    var itemsByState = toGroupMapConcurrent(existingItems, d -> d.getVersion().getState());
    assert itemsByState.keySet().size() <= 2; // Should be only ACTIVE and/or ARCHIVED
    return itemsByState;
  }

  private <T> List<T> collectItems(Set<K> keys, Map<K, T> itemsMap) {
    return keys.parallelStream().map(itemsMap::get).toList();
  }
}
