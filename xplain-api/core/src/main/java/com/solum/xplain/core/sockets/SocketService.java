package com.solum.xplain.core.sockets;

import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.sockets.constants.CoreSocketReferences;
import com.solum.xplain.core.sockets.events.SocketEvent;
import com.solum.xplain.core.sockets.value.EventResponse;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Component
public class SocketService {
  private static final Logger LOG = getLogger(SocketService.class);

  private final CopyOnWriteArrayList<WrappedEmitter> emitters = new CopyOnWriteArrayList<>();
  private final AuthenticationContext authenticationContext;
  private final Long timeout;

  public SocketService(
      AuthenticationContext authenticationContext, @Value("${app.socket.timeout}") Long timeout) {
    this.authenticationContext = authenticationContext;
    this.timeout = timeout;
  }

  public Either<ErrorItem, SseEmitter> subscribe(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(CoreSocketReferences::forUser)
        .map(WrappedEmitter::defaultEmitter)
        .map(this::registerEmitter);
  }

  public Either<ErrorItem, SseEmitter> subscribeIpvDataResolutionStatus(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(CoreSocketReferences::forIpvResolutionStatus)
        .map(WrappedEmitter::defaultEmitter)
        .map(this::registerEmitter);
  }

  public Either<ErrorItem, SseEmitter> subscribeValuation(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(CoreSocketReferences::forValuation)
        .map(WrappedEmitter::defaultEmitter)
        .map(this::registerEmitter);
  }

  public Either<ErrorItem, SseEmitter> subscribeSimulation(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(CoreSocketReferences::forSimulation)
        .map(WrappedEmitter::defaultEmitter)
        .map(this::registerEmitter);
  }

  public Either<ErrorItem, SseEmitter> subscribeLocks(Authentication auth) {
    return authenticationContext
        .userEither(auth)
        .map(XplainPrincipal::getId)
        .map(CoreSocketReferences::forLocks)
        .map(WrappedEmitter::defaultEmitter)
        .map(this::registerEmitter);
  }

  public SseEmitter registerEmitter(WrappedEmitter wrappedEmitter) {
    this.emitters.add(wrappedEmitter);
    wrappedEmitter.onCompletion(() -> this.emitters.remove(wrappedEmitter));
    wrappedEmitter.onError(
        err -> {
          wrappedEmitter.getEmitter().completeWithError(err);
          this.emitters.remove(wrappedEmitter);
        });
    return wrappedEmitter.getEmitter();
  }

  @Async
  @EventListener
  public void sendEvents(SocketEvent event) {
    List<WrappedEmitter> deadEmitters = new ArrayList<>();
    emitters.stream()
        .filter(event::shouldSend)
        .forEach(
            e -> {
              try {
                e.getEmitter().send(new EventResponse(event.getEventType(), event.getData()));
              } catch (Exception exception) {
                deadEmitters.add(e);
              }
            });
    emitters.removeAll(deadEmitters);
  }

  @Scheduled(fixedRateString = "${app.socket.clean-up-rate}")
  public void cleanDeadEmitters() {
    // Current Tomcat timeout logic doesn't ignore errored out emitters and print unnecessary
    // logs.
    var time = LocalDateTime.now();
    LOG.debug("Socket cleanup started");
    emitters.stream()
        .filter(e -> e.shouldTimeOut(time, timeout))
        .forEach(e -> e.getEmitter().complete());
  }
}
