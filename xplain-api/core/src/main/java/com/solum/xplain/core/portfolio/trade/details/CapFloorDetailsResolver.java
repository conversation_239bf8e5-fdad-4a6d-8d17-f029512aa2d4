package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;

import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CapFloorDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.CAP_FLOOR);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    return tradeDetails
        .tradePositionLeg()
        .map(TradeLegDetails::getIndex)
        .flatMap(ConventionalCurveConfigurations::lookupByIndex)
        .map(CurveConvention::getName)
        .orElse(null);
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
