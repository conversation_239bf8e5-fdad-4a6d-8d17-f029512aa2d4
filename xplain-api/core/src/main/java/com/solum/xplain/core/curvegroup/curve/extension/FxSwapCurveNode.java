package com.solum.xplain.core.curvegroup.curve.extension;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.FxRate;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.data.FxRateId;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataId;
import com.opengamma.strata.data.MarketDataNotFoundException;
import com.opengamma.strata.data.ObservableId;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.param.DatedParameterMetadata;
import com.opengamma.strata.market.param.TenorDateParameterMetadata;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.fx.FxSwapTrade;
import com.opengamma.strata.product.fx.ResolvedFxSwapTrade;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;

@ToString
@Builder
@EqualsAndHashCode
@AllArgsConstructor
public class FxSwapCurveNode implements CurveNode {
  private static final CurveNodeDate DEFAULT_DATE = CurveNodeDate.END;
  private static final CurveNodeDateOrder DEFAULT_ORDER = CurveNodeDateOrder.DEFAULT;

  @NonNull private final MarketTenorFxSwapTemplate template;
  @NonNull private final ObservableId farForwardPointsId;
  @NonNull private final String label;
  @NonNull private final ObservableId tnFarForwardPointsId;
  private final double additionalSpread;

  @NonNull
  @Override
  public String getLabel() {
    return label;
  }

  @Override
  public CurveNodeDateOrder getDateOrder() {
    return DEFAULT_ORDER;
  }

  @Override
  public Set<? extends MarketDataId<?>> requirements() {
    return Set.of(fxRateId(), farForwardPointsId);
  }

  @Override
  public LocalDate date(LocalDate valuationDate, ReferenceData refData) {
    return calculateEnd(valuationDate, refData);
  }

  private LocalDate calculateEnd(LocalDate valuationDate, ReferenceData refData) {
    FxSwapTrade trade = template.createTrade(valuationDate, BuySell.BUY, 1, 1, 0, refData);
    return trade.getProduct().getFarLeg().resolve(refData).getPaymentDate();
  }

  @Override
  public DatedParameterMetadata metadata(LocalDate valuationDate, ReferenceData refData) {
    LocalDate nodeDate = date(valuationDate, refData);
    Tenor tenor = template.tenor();
    return TenorDateParameterMetadata.of(nodeDate, tenor, label);
  }

  @Override
  public FxSwapTrade trade(double quantity, MarketData marketData, ReferenceData refData) {
    FxRate fxRate = marketData.getValue(fxRateId());
    BigDecimal rate = BigDecimal.valueOf(fxRate.fxRate(template.currencyPair()));
    BigDecimal fxPts = BigDecimal.valueOf(marketTenorValue(marketData, farForwardPointsId));
    BuySell buySell = BuySell.ofBuy(quantity > 0);

    var tenor = template.marketTenor();
    var spotDateOffsetDays = spotDateOffsetDays();

    if (tenor == MarketTenor.ON && spotDateOffsetDays == 2) {
      rate = adjustedOnRate(marketData, rate, fxPts, refData);
    }

    if (tenor == MarketTenor.ON && spotDateOffsetDays == 1
        || tenor == MarketTenor.TN && spotDateOffsetDays == 2) {
      rate = rate.subtract(fxPts);
    }
    var shiftedRate = fxPts.doubleValue() + additionalSpread;
    return template.createTrade(
        marketData.getValuationDate(),
        buySell,
        Math.abs(quantity),
        rate.doubleValue(),
        shiftedRate,
        refData);
  }

  // OG spot-discounting market tenor rate adjustments
  private BigDecimal adjustedOnRate(
      MarketData marketData, BigDecimal rate, BigDecimal fxPts, ReferenceData refData) {
    var valDate = marketData.getValuationDate();
    var overnightNodeEndDate = calculateEnd(valDate, refData);
    var spotDate = template.convention().calculateSpotDateFromTradeDate(valDate, refData);

    // if ON end date = spot date, do not observe TN rate
    if (overnightNodeEndDate.isEqual(spotDate)) {
      return rate.subtract(fxPts);
    } else {
      BigDecimal tnFxPts = BigDecimal.valueOf(marketTenorValue(marketData, tnFarForwardPointsId));
      return rate.subtract(tnFxPts).subtract(fxPts);
    }
  }

  private int spotDateOffsetDays() {
    return template.convention().getSpotDateOffset().getDays();
  }

  private double marketTenorValue(MarketData marketData, ObservableId marketDataId) {
    var fxPts = marketData.findValue(marketDataId);
    if (fxPts.isPresent()) {
      return fxPts.get();
    }
    throw new MarketDataNotFoundException(
        Messages.format(
            "Market data not found for '{}' of type '{}'",
            marketDataId,
            marketDataId.getClass().getSimpleName()));
  }

  @Override
  public ResolvedFxSwapTrade resolvedTrade(
      double quantity, MarketData marketData, ReferenceData refData) {
    return trade(quantity, marketData, refData).resolve(refData);
  }

  @Override
  public double initialGuess(MarketData marketData, ValueType valueType) {
    if (ValueType.DISCOUNT_FACTOR.equals(valueType)) {
      return 1d;
    }
    return 0d;
  }

  private FxRateId fxRateId() {
    return FxRateId.of(template.currencyPair());
  }
}
