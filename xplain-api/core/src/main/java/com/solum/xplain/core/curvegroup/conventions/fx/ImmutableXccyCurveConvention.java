package com.solum.xplain.core.curvegroup.conventions.fx;

import static java.lang.String.format;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import java.util.List;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ImmutableXccyCurveConvention implements ConventionalCurveConvention {

  private static final String KEY_PATTERN = "%s | %s";
  private final String name;
  private final Currency baseCurrency;
  private final Currency counterCurrency;
  private final List<Named> nodeConventions;

  static ImmutableXccyCurveConvention xccyCurve(CurrencyPair pair, List<Named> xccyConventions) {
    var conventions = ImmutableList.<Named>builder();
    conventions.add(FxSwapConvention.of(pair));
    conventions.addAll(xccyConventions);
    return new ImmutableXccyCurveConvention(
        pair.getBase() + "/" + pair.getCounter(),
        pair.getBase(),
        pair.getCounter(),
        conventions.build());
  }

  @Override
  public Currency getCurrency() {
    return baseCurrency;
  }

  @Override
  public String getKey() {
    return format(KEY_PATTERN, Stream.of(baseCurrency, counterCurrency).sorted().toArray());
  }

  @Override
  public int getSortOrder() {
    return 3;
  }
}
