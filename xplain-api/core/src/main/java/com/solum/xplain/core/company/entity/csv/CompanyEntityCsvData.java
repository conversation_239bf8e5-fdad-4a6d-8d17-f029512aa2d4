package com.solum.xplain.core.company.entity.csv;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.value.AllowedTeamsForm;

public record CompanyEntityCsvData(
    String companyExternalId,
    String companyId,
    String entityExternalId,
    String entityName,
    String description,
    String slaDeadline,
    EntityReference valuationDataGroup,
    EntityReference marketDataGroup,
    EntityReference curveConfiguration,
    AllowedTeamsForm allowedTeamsForm) {}
