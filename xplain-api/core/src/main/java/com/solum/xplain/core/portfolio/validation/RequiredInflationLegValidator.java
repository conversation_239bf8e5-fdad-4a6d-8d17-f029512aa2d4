package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.value.CalculationType.INFLATION;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredInflationLegValidator
    implements ConstraintValidator<RequiredInflationLeg, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(form.getLeg1(), form.getLeg2()) && !hasInflationLeg(form)) {
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("leg1")
          .addPropertyNode("calculationType")
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private boolean hasInflationLeg(SwapTradeForm form) {
    return form.getLeg1().getCalculationType() == INFLATION
        || form.getLeg2().getCalculationType() == INFLATION;
  }
}
