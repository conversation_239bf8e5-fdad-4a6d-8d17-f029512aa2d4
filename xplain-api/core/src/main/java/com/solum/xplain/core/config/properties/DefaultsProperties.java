package com.solum.xplain.core.config.properties;

import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Data;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Data
@ConfigurationProperties(prefix = "app.default")
@Validated
@NullMarked
public class DefaultsProperties {
  @NotNull private String team;
  @NotNull private String role;
  @NotNull private Set<String> trustedClientIds;

  public boolean isTrustedClient(@Nullable String clientId) {
    for (String trustedClientId : trustedClientIds) {
      if (trustedClientId.equals(clientId)) {
        return true;
      }
    }
    return false;
  }
}
