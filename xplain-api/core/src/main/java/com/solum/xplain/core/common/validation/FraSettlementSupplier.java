package com.solum.xplain.core.common.validation;

import static java.util.stream.Collectors.toSet;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.classifiers.Constants;
import java.util.Collection;
import java.util.function.Supplier;

public class FraSettlementSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Constants.FRA_SETTLEMENT_VALUES.stream().map(Tenor::toString).collect(toSet());
  }
}
