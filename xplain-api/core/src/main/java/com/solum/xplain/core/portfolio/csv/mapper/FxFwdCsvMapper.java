package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PAYMENT_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class FxFwdCsvMapper implements ProductCsvMapper {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.FXFWD);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails tradeDetails) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_PAYMENT_DATE, tradeDetails.getEndDate()));
    builder.add(
        new CsvField(TRADE_BUSINESS_DAY_CONVENTION, tradeDetails.getBusinessDayConvention()));
    builder.addAll(FxTradeCsvMapperUtils.toCsvFields(tradeDetails));
    builder.add(new CsvField(TRADE_REF_SEC_FX_RATE, tradeDetails.getFxRate()));

    return builder.build();
  }
}
