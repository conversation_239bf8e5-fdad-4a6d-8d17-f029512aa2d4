package com.solum.xplain.core.curvegroup.curvebond.csv.node;

import com.solum.xplain.core.common.csv.NodeKey;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm;
import java.time.LocalDate;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BondCurveNodeKey implements NodeKey {

  private final LocalDate maturityDate;

  public static BondCurveNodeKey from(BondCurveNodeForm form) {
    return new BondCurveNodeKey(form.getMaturityDate());
  }

  @Override
  public String getIdentifier() {
    return maturityDate.toString();
  }
}
