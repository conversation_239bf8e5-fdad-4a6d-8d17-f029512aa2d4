package com.solum.xplain.core.classifiers;

import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeProvider;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class CurveConfigurationInstrumentsClassifierProvider implements ClassifiersProvider {

  private final Classifier curveConfigurationInstrumentsClassifier;

  public CurveConfigurationInstrumentsClassifierProvider(
      List<InstrumentTypeProvider> instrumentTypeProviders) {
    curveConfigurationInstrumentsClassifier =
        instrumentTypeProviders.stream()
            .map(InstrumentTypeProvider::instruments)
            .flatMap(Collection::stream)
            .collect(
                Collectors.collectingAndThen(
                    toList(), this::buildConfigurationInstrumentsClassifier));
  }

  @Override
  public List<Classifier> classifiers() {
    return List.of(curveConfigurationInstrumentsClassifier);
  }

  @Override
  public int sortOrder() {
    return 2;
  }

  private Classifier buildConfigurationInstrumentsClassifier(List<InstrumentType> instrumentType) {
    var classifiers =
        instrumentType.stream()
            .filter(InstrumentType::isCurveConfigInstrument)
            .collect(Collectors.groupingBy(InstrumentType::getInstrumentGroup))
            .entrySet()
            .stream()
            .sorted(Comparator.comparing(c -> c.getKey().getSortOrder()))
            .map(
                v ->
                    new Classifier(
                        v.getKey().name(),
                        v.getKey().getLabel(),
                        v.getValue().stream()
                            .map(c -> new Classifier(c.name(), c.getLabel()))
                            .toList()))
            .toList();

    return new Classifier("curveConfigurationInstruments", classifiers);
  }
}
