package com.solum.xplain.core.market.csv;

import static com.solum.xplain.core.providers.enums.DataProviderType.MARKET;

import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.market.MarketDataKeyEntity;
import com.solum.xplain.core.market.MarketDataKeyValue;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.providers.DataProviderRepository;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class MarketDataKeyProviderCsvLoaderFactory {
  private final DataProviderRepository providerRepository;

  public MarketDataKeyProviderCsvLoaderFactory(DataProviderRepository providerRepository) {
    this.providerRepository = providerRepository;
  }

  public MarketDataKeyProviderCsvLoader getLoader(
      List<EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity>> keyList) {
    var providerCodes = providerCodes();
    var keySet = keysSet(keyList);
    return MarketDataKeyProviderCsvLoader.newOf(providerCodes, keySet);
  }

  private Set<String> providerCodes() {
    return providerRepository.dataProvidersList().stream()
        .filter(dp -> dp.getTypes().contains(MARKET))
        .map(DataProvider::getExternalId)
        .collect(Collectors.toUnmodifiableSet());
  }

  private Set<String> keysSet(
      List<EntityForUpdate<MarketDataKeyValue, MarketDataKeyEntity>> keyList) {
    return keyList.stream().map(k -> k.getEntity().getSemanticId()).collect(Collectors.toSet());
  }
}
