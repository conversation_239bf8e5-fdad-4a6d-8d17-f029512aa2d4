package com.solum.xplain.core.market;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionArchiveForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.csv.MarketDataKeyCsvMapper;
import com.solum.xplain.core.market.csv.MarketDataProviderTickerCsvMapper;
import com.solum.xplain.core.market.filter.ProviderFilter;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.market.repository.MarketDataKeyWriteRepository;
import com.solum.xplain.core.market.value.MarketDataKeyForm;
import com.solum.xplain.core.market.value.MarketDataKeySearchForm;
import com.solum.xplain.core.market.value.MarketDataKeyUpdateForm;
import com.solum.xplain.core.market.value.MarketDataKeyView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class MarketDataKeyControllerService {

  private final MarketDataKeyRepository repository;
  private final MarketDataKeyWriteRepository writeRepository;
  private final MarketDataKeyCsvMapper marketDataKeyCsvMapper;
  private final AuthenticationContext authenticationContext;

  @Transactional
  public EntityId insert(MarketDataKeyForm form) {
    return writeRepository.insert(form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String entityId, LocalDate versionDate, MarketDataKeyUpdateForm form) {
    return writeRepository.update(entityId, versionDate, form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> archive(
      String entityId, LocalDate versionDate, ArchiveEntityForm form) {
    return writeRepository.archive(entityId, versionDate, form);
  }

  public Either<ErrorItem, Integer> archiveAll(
      Authentication authentication,
      LocalDate stateDate,
      VersionArchiveForm form,
      TableFilter tableFilter,
      ProviderFilter providerFilter) {
    var ids =
        repository
            .marketDataKeyStream(tableFilter, providerFilter, new BitemporalDate(stateDate))
            .map(DateRangeVersionedEntity::getEntityId)
            .toList();
    return authenticationContext
        .userEither(authentication)
        .map(u -> writeRepository.archiveAll(stateDate, form, ids));
  }

  @Transactional
  public Either<ErrorItem, EntityId> delete(String entityId, LocalDate versionDate) {
    return writeRepository.deleteItem(entityId, versionDate);
  }

  public Either<ErrorItem, MarketDataKeyView> get(String entityId, LocalDate stateDate) {
    return repository.getView(entityId, new BitemporalDate(stateDate));
  }

  public List<MarketDataKeyView> versionsList(String id) {
    return repository.marketDataKeyVersions(id);
  }

  public DateList futureVersions(MarketDataKeySearchForm searchForm) {
    return repository.futureVersions(searchForm);
  }

  public ScrollableEntry<MarketDataKeyView> marketDataKeyScrollable(
      BitemporalDate stateDate,
      ScrollRequest scrollRequest,
      TableFilter filter,
      ProviderFilter providerFilter,
      VersionedEntityFilter versionedEntityFilter) {
    return repository.marketDataKeyScrollable(
        scrollRequest, filter, stateDate, providerFilter, versionedEntityFilter);
  }

  public FileResponseEntity getMarketDataKeyCsvBytes(
      TableFilter filter, Sort sort, LocalDate stateDate) {

    var rows =
        repository.marketDataKeyViews(sort, filter, new BitemporalDate(stateDate)).stream()
            .map(marketDataKeyCsvMapper::toCsvRow)
            .toList();

    var csvFile = new CsvOutputFile(marketDataKeyCsvMapper.header(), rows);
    var csvFileName = nameWithTimeStamp("MDKDefinitions", stateDate);
    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
  }

  public FileResponseEntity getMarketDataKeyTickerCsvBytes(
      TableFilter filter, Sort sort, BitemporalDate stateDate, List<String> selectedColumns) {
    var mapper = new MarketDataProviderTickerCsvMapper(selectedColumns);
    var rows =
        repository.marketDataKeyViews(sort, filter, stateDate).stream()
            .map(mapper::toMarketDataKeyCsvRows)
            .flatMap(Collection::stream)
            .toList();

    var csvFile = new CsvOutputFile(mapper.header(), rows);
    var csvFileName = nameWithTimeStamp("MDKProviders", stateDate);
    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
  }
}
