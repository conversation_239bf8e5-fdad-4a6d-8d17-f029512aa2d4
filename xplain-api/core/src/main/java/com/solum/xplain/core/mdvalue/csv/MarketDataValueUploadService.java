package com.solum.xplain.core.mdvalue.csv;

import static com.solum.xplain.core.common.CollectionUtils.toMapConcurrent;
import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.detailedDescription;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.datavalue.csv.DataValueImportItemsValidatorUtils.validateImportItems;
import static com.solum.xplain.core.error.Error.IMPORT_WARNING;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ImportOverview;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.datavalue.csv.DataValueFileImporter;
import com.solum.xplain.core.datavalue.csv.DataValueFileImporterProvider;
import com.solum.xplain.core.datavalue.csv.DataValueImportItems;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.error.WarningItem;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.market.service.MarketDataKeyResolver;
import com.solum.xplain.core.mdvalue.MarketDataValueMapper;
import com.solum.xplain.core.mdvalue.MarketDataValueRepository;
import com.solum.xplain.core.mdvalue.entity.MarketDataValue;
import com.solum.xplain.core.mdvalue.entity.MarketDataValueUniqueKey;
import com.solum.xplain.core.mdvalue.entity.MarketDataValueVersion;
import com.solum.xplain.core.mdvalue.value.MarketDataValueCreateForm;
import com.solum.xplain.core.mdvalue.value.MarketDataValuesGroup;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Service;

@Service
public class MarketDataValueUploadService {

  private static final String OBJECT_NAME = "Market data values";

  private final MarketDataValueRepository marketDataValueRepository;
  private final MarketDataKeyRepository marketDataKeyRepository;
  private final MarketDataCsvLoaderFactory csvLoaderFactory;
  private final MarketDataValueMapper marketDataValueMapper;
  private final AuditEntryService auditEntryService;
  private final DataValueFileImporterProvider importerProvider;

  public MarketDataValueUploadService(
      AuditEntryService auditEntryService,
      MarketDataValueRepository marketDataValueRepository,
      MarketDataKeyRepository marketDataKeyRepository,
      MarketDataCsvLoaderFactory csvLoaderFactory,
      MarketDataValueMapper marketDataValueMapper,
      DataValueFileImporterProvider importerProvider) {
    this.marketDataValueRepository = marketDataValueRepository;
    this.marketDataKeyRepository = marketDataKeyRepository;
    this.csvLoaderFactory = csvLoaderFactory;
    this.marketDataValueMapper = marketDataValueMapper;
    this.auditEntryService = auditEntryService;
    this.importerProvider = importerProvider;
  }

  public Either<List<ErrorItem>, ValidationResponse> validateFile(
      MarketDataValuesGroup marketDataGroup, byte[] csvBytes, ParsingMode parsingMode) {
    return csvLoaderFactory
        .csvLoader(marketDataGroup.getName())
        .parse(csvBytes, parsingMode)
        .map(
            parserResult ->
                errors(
                    marketDataGroup.getId(),
                    parserResult.getParsedLines(),
                    parserResult.getWarnings()));
  }

  private ValidationResponse errors(
      String groupId, List<MarketDataValueCreateForm> forms, List<ErrorItem> parsingErrors) {
    var errors =
        CollectionUtils.toGroupMapConcurrent(forms, MarketDataValueCreateForm::getDate)
            .entrySet()
            .stream()
            .map(v -> dailyErrors(groupId, v.getKey(), v.getValue()))
            .flatMap(Collection::stream)
            .toList();
    var validationErrors =
        ImmutableList.<ErrorItem>builder().addAll(parsingErrors).addAll(errors).build();
    return ValidationResponse.newOf(validationErrors);
  }

  public Either<List<ErrorItem>, EntityId> upload(
      MarketDataValuesGroup marketDataGroup,
      byte[] csvBytes,
      MarketDataValueImportOptions importOptions,
      BitemporalDate stateDate,
      AssetGroup... assetGroups) {
    return validImportOptions(marketDataGroup.getId(), importOptions)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(options -> csvLoaderFactory.csvLoader(marketDataGroup.getName()))
        .flatMap(p -> p.parse(csvBytes, importOptions.getEffectiveParsingMode()))
        .map(
            r ->
                process(
                    marketDataGroup.getId(),
                    r.getParsedLines(),
                    importOptions,
                    stateDate,
                    r.logItems(),
                    assetGroups));
  }

  private Either<ErrorItem, MarketDataValueImportOptions> validImportOptions(
      String groupId, MarketDataValueImportOptions options) {
    if (marketDataValueRepository.hasChangesAfter(groupId, options.getValidationTimestamp())) {
      return Either.left(stateChanged(OBJECT_NAME));
    }
    return Either.right(options);
  }

  private EntityId process(
      String groupId,
      List<MarketDataValueCreateForm> forms,
      ImportOptions importOptions,
      BitemporalDate stateDate,
      List<LogItem> parsingWarnings,
      AssetGroup... assetGroups) {
    final var keyResolver = keyResolver(stateDate, assetGroups);
    var importOverviews =
        CollectionUtils.toGroupMapConcurrent(forms, MarketDataValueCreateForm::getDate)
            .entrySet()
            .stream()
            .map(e -> upload(groupId, e.getKey(), e.getValue(), importOptions, keyResolver))
            .reduce(initialOverview(parsingWarnings), ImportOverview::sum);

    logImport(importOverviews);
    return EntityId.entityId(groupId);
  }

  private ImportOverview initialOverview(List<LogItem> warningItems) {
    return ImportOverview.builder().identifier(OBJECT_NAME).warnings(warningItems).build();
  }

  private MarketDataKeyResolver keyResolver(BitemporalDate stateDate, AssetGroup... assetGroups) {
    return marketDataKeyRepository.keyResolver(stateDate, assetGroups);
  }

  private ImportOverview upload(
      String groupId,
      LocalDate date,
      List<MarketDataValueCreateForm> dateForms,
      ImportOptions importOptions,
      MarketDataKeyResolver keyResolver) {
    var importItems = buildItems(groupId, date, dateForms, importOptions.getComment());
    var result = importer().importItems(importOptions.getDuplicateAction(), importItems);

    var unresolvedMarketValues = unresolvedEntriesLogs(keyResolver, importItems.newItems());
    return ImportOverview.builder()
        .identifier(OBJECT_NAME)
        .insertedCount(result.getNewEntriesCount())
        .updatedCount(result.getReplacedEntriesCount())
        .archivedCount(result.getArchivedEntriesCount())
        .warnings(unresolvedMarketValues)
        .build();
  }

  private DataValueFileImporter<MarketDataValueVersion, MarketDataValueUniqueKey, MarketDataValue>
      importer() {
    return importerProvider.importer(marketDataValueRepository);
  }

  private List<ErrorItem> dailyErrors(
      String groupId, LocalDate date, List<MarketDataValueCreateForm> forms) {
    var data = buildItems(groupId, date, forms, null);
    return validateImportItems(data, date);
  }

  private List<MarketDataValue> existingEntities(String groupId, LocalDate date) {
    return marketDataValueRepository.dailyEntities(groupId, BitemporalDate.newOf(date));
  }

  private void logImport(ImportOverview importOverview) {
    var description = detailedDescription(importOverview);
    auditEntryService.newEntryWithLogs(
        AuditEntry.of(getCollection(), description), importOverview.getWarnings());
  }

  private DataValueImportItems<MarketDataValueVersion, MarketDataValueUniqueKey, MarketDataValue>
      buildItems(
          String groupId,
          LocalDate date,
          List<MarketDataValueCreateForm> forms,
          String importComment) {
    var entitiesByKey = existingItemsByKey(groupId, date);
    var importItemsByKey = importItemsByKey(groupId, forms, importComment);
    return DataValueImportItems
        .<MarketDataValueVersion, MarketDataValueUniqueKey, MarketDataValue>builder()
        .importItemsByKeys(importItemsByKey)
        .existingItemsByKey(entitiesByKey)
        .importComment(importComment)
        .build();
  }

  private Map<MarketDataValueUniqueKey, MarketDataValue> existingItemsByKey(
      String groupId, LocalDate date) {
    return toMapConcurrent(existingEntities(groupId, date), MarketDataValueUniqueKey::from);
  }

  private Map<MarketDataValueUniqueKey, MarketDataValue> importItemsByKey(
      String groupId, List<MarketDataValueCreateForm> forms, String importComment) {
    return forms.stream()
        .map(f -> f.withComment(importComment))
        .collect(
            Collectors.toMap(
                MarketDataValueUniqueKey::from, v -> marketDataValueMapper.newEntity(groupId, v)));
  }

  private List<LogItem> unresolvedEntriesLogs(
      MarketDataKeyResolver resolver, List<MarketDataValue> values) {
    if (IterableUtils.isEmpty(values)) {
      return List.of();
    }
    return resolver.filterUnresolvableValues(values).stream()
        .map(
            v ->
                String.format(
                    "Mapping for %s provider and %s ticker was not found",
                    v.getProvider(), v.getTicker()))
        .map(e -> WarningItem.of(IMPORT_WARNING, e))
        .map(LogItem.class::cast)
        .toList();
  }

  protected String getCollection() {
    return MarketDataValue.class.getSimpleName();
  }
}
