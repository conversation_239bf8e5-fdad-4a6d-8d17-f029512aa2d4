package com.solum.xplain.core.market.value;

import com.solum.xplain.core.common.validation.ValidDataProviderCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketDataProviderTickerForm {

  @NotEmpty
  @ValidDataProviderCode
  @Schema(description = "Provider, should be valid data provider ID")
  private String code;

  @NotEmpty
  @Schema(description = "Ticker for value mapping")
  private String ticker;

  @Schema(
      description = "Supported Bid/Mid/Ask price type, default BID_ONLY if not provided",
      nullable = true)
  private MdkProviderBidAskType bidAskType;

  @NotNull
  @Positive
  @Schema(description = "Value factor for scaling value")
  private BigDecimal factor;
}
