package com.solum.xplain.core.ipv.group;

import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.company.mapper.CompanyReferenceMapper;
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup;
import com.solum.xplain.core.ipv.group.form.IpvDataGroupForm;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCountedView;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(uses = {ObjectIdMapper.class, CompanyReferenceMapper.class})
public interface IpvDataGroupMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  @Mapping(target = "allowAllCompanies", source = "allowedCompaniesForm.allowAll")
  @Mapping(target = "companies", source = "allowedCompaniesForm.companyIds")
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "name", ignore = true)
  IpvDataGroup toEntity(IpvDataGroupForm updateForm, @MappingTarget IpvDataGroup existing);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", constant = "false")
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  @Mapping(target = "allowAllCompanies", source = "allowedCompaniesForm.allowAll")
  @Mapping(target = "companies", source = "allowedCompaniesForm.companyIds")
  IpvDataGroup toNewEntity(IpvDataGroupForm form);

  IpvDataGroupCountedView toCountedView(
      IpvDataGroupView view, Integer valuesCount, Integer navValuesCount);

  IpvDataGroup copy(IpvDataGroup group);

  @Mapping(target = "archived", constant = "true")
  IpvDataGroup copyArchived(IpvDataGroup value);
}
