package com.solum.xplain.core.ccyexposure;

import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureUpdateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.form.FormMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = CollectionUtils.class)
public interface CcyExposureMapper
    extends FormMapper<CcyExposureCreateForm, CcyExposureUpdateForm, CcyExposure> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  CcyExposure toEntity(CcyExposureCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "archived", ignore = true)
  CcyExposure toEntity(CcyExposureUpdateForm form, @MappingTarget CcyExposure ccyExposure);

  CcyExposureView toView(CcyExposure ccyExposure);

  CcyExposureCreateForm toForm(CcyExposure ccyExposure);
}
