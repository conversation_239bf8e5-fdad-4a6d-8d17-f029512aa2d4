package com.solum.xplain.core.company.repository;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.value.FutureVersionsAction.DELETE;
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.company.csv.IpvSettingsImportUpdateData;
import com.solum.xplain.core.company.entity.CompanyIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings;
import com.solum.xplain.core.company.entity.IpvValuationProviders;
import com.solum.xplain.core.company.entity.IpvValuationSettings;
import com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvForm;
import com.solum.xplain.core.company.events.CompanyArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated;
import com.solum.xplain.core.company.events.CompanyLegalEntityImported;
import com.solum.xplain.core.company.form.CompanyLegalEntityIpvSettingsForm;
import com.solum.xplain.core.company.mapper.LegalEntityIpvSettingsMapper;
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.group.events.IpvDataGroupUpdated;
import com.solum.xplain.core.product.ProductTypeResolver;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
public class CompanyLegalEntityIpvSettingsRepository
    extends GenericUniqueVersionedEntityRepository<CompanyLegalEntityIpvSettings> {
  private final MongoOperations mongoOperations;
  private final CompanyIpvSettingsRepository companyIpvSettingsRepository;
  private final LegalEntityIpvSettingsMapper mapper;
  private final ProductTypeResolver productTypeResolver;
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final CompanyEntityValuationSettingsResolver companyEntityIpvSettingsResolver;

  public CompanyLegalEntityIpvSettingsRepository(
      MongoOperations mongoOperations,
      @Lazy CompanyIpvSettingsRepository companyIpvSettingsRepository,
      LegalEntityIpvSettingsMapper mapper,
      ProductTypeResolver productTypeResolver,
      IpvDataGroupRepository ipvDataGroupRepository,
      CompanyEntityValuationSettingsResolver companyEntityIpvSettingsResolver) {
    super(mongoOperations, mapper);
    this.mongoOperations = mongoOperations;
    this.companyIpvSettingsRepository = companyIpvSettingsRepository;
    this.mapper = mapper;
    this.productTypeResolver = productTypeResolver;
    this.ipvDataGroupRepository = ipvDataGroupRepository;
    this.companyEntityIpvSettingsResolver = companyEntityIpvSettingsResolver;
  }

  @Override
  protected Criteria uniqueEntityCriteria(CompanyLegalEntityIpvSettings entity) {
    return hasEntityId(entity.getEntityId());
  }

  public Either<ErrorItem, EntityId> updateCompanyEntityIpvSettings(
      String entityId, LocalDate version, CompanyLegalEntityIpvSettingsForm form) {
    return entityExact(entityId, version)
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> mapper.fromForm(form, copiedEntity)));
  }

  public EntityId updateCompanyEntityIpvSettings(
      String companyId,
      String entityId,
      CompanyLegalEntityCsvForm entityCsvForm,
      ImportOptions importOptions) {

    var stateDate = importOptions.bitemporalDate();
    var defaultSettings = companyIpvSettingsRepository.companySettingsEntity(companyId, stateDate);

    var entitySettings =
        entity(entityId, stateDate, active())
            .getOr(() -> CompanyLegalEntityIpvSettings.newOf(companyId, entityId));
    var entitySettingsWithDefaults =
        withCompanyDefaults(defaultSettings, mapper.copy(entitySettings));

    var vf =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions, entitySettings.getValidFrom(), importOptions::getFutureVersionsAction);

    var vdg = entityCsvForm.getValuationDataGroup();
    var sla = entityCsvForm.getSlaDeadline();

    var importData = new IpvSettingsImportUpdateData(vdg, sla, vf);

    return update(
        entitySettings,
        vf,
        v ->
            companyEntityIpvSettingsResolver.updateIpvSettings(
                v, entitySettingsWithDefaults, importData));
  }

  public List<CompanyLegalEntityIpvSettingsView> entityValuationSettingsVersions(String entityId) {
    return entityVersions(entityId).stream().map(mapper::toView).toList();
  }

  public CompanyLegalEntityIpvSettingsView companyEntityIpvSettingsView(
      String companyId, String entityId, BitemporalDate stateDate) {
    var entity = companyEntityIpvSettings(companyId, entityId, stateDate);
    return mapper.toView(entity);
  }

  public CompanyLegalEntityIpvSettings companyEntityIpvSettings(
      String companyId, String entityId, BitemporalDate stateDate) {
    var defaultSettings = companyIpvSettingsRepository.companySettingsEntity(companyId, stateDate);
    var entitySettings = entityIpvSettings(defaultSettings, entityId, stateDate);
    return withCompanyDefaults(defaultSettings, entitySettings);
  }

  private CompanyLegalEntityIpvSettings withCompanyDefaults(
      CompanyIpvSettings defaultSettings, CompanyLegalEntityIpvSettings entity) {
    entity.applyDefaults(defaultSettings);
    return entity;
  }

  private CompanyLegalEntityIpvSettings entityIpvSettings(
      CompanyIpvSettings defaultSettings, String entityId, BitemporalDate stateDate) {
    return entity(entityId, stateDate, active())
        .getOr(() -> defaultSettings.toDefaultLegalEntitySettings(entityId));
  }

  /**
   * Return all the currently active settings for the state date across all company legal entities.
   * Note that this will include records where the {@code settingsType} is {@link
   * com.solum.xplain.core.company.CompanySettingsType#DEFAULT DEFAULT} and therefore the company
   * settings should be used instead.
   *
   * @param stateDate bitemporal identifying record and actual dates.
   * @return all company legal entity settings applicable at the specified state date
   */
  public List<CompanyLegalEntityIpvSettings> findAllLatestVersions(BitemporalDate stateDate) {
    return entities(stateDate, active(), new Criteria(), Sort.unsorted());
  }

  public List<CompanyLegalEntityIpvSettings> getValuationSettings(
      String companyId, BitemporalDate stateDate) {
    return entities(
        stateDate, active(), where(CompanyLegalEntityIpvSettings.Fields.companyId).is(companyId));
  }

  public Map<String, CompanyLegalEntityIpvSettingsView> getValuationSettingsView(
      Collection<String> entityIds, BitemporalDate stateDate) {
    var companyListEntities =
        entities(stateDate, active(), where(VersionedEntity.Fields.entityId).in(entityIds));
    return companyListEntities.stream()
        .map(mapper::toView)
        .collect(toMap(CompanyLegalEntityIpvSettingsView::getEntityId, Function.identity()));
  }

  public EntityReference ipvDataGroup(String ipvDataGroupId) {
    return EntityReference.fetchedBy(
        ipvDataGroupId, ipvDataGroupRepository::ipvDataGroupCondensedView);
  }

  @EventListener
  public void onIpvDataGroupUpdate(IpvDataGroupUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(IpvValuationSettings.Fields.products)
                .elemMatch(
                    where(
                            propertyName(
                                IpvValuationProviders.Fields.ipvDataGroup,
                                EntityReference.Fields.entityId))
                        .is(event.getEntityId()))),
        new Update()
            .set(
                (propertyName(
                    IpvValuationSettings.Fields.products,
                    "$[elIpvDataGroup]",
                    IpvValuationProviders.Fields.ipvDataGroup,
                    EntityReference.Fields.name)),
                event.getForm().getName())
            .filterArray(
                where(
                        propertyName(
                            "elIpvDataGroup",
                            IpvValuationProviders.Fields.ipvDataGroup,
                            EntityReference.Fields.entityId))
                    .is(event.getEntityId())),
        CompanyLegalEntityIpvSettings.class);
  }

  @EventListener
  public void onCompanyArchived(CompanyArchived companyArchived) {
    entities(
            ROOT_DATE,
            where(CompanyLegalEntityValuationSettings.Fields.companyId)
                .is(companyArchived.getEntityId()))
        .forEach(this::forceArchive);
  }

  private void forceArchive(CompanyLegalEntityIpvSettings e) {
    archive(
        e,
        new ArchiveEntityForm(
            NewVersionFormV2.builder()
                .comment("Entity archived")
                .validFrom(ROOT_DATE)
                .futureVersionsAction(DELETE)
                .build()));
  }

  @EventListener
  public void onCompanyEntityCreated(CompanyLegalEntityCreated event) {
    if (event instanceof CompanyLegalEntityImported importedEvent) {
      onCompanyEntityCreatedImport(importedEvent);
    } else {
      insert(
          CompanyLegalEntityIpvSettings.newOf(event.getCompanyId(), event.getEntityId()),
          NewVersionFormV2.newDefault());
    }
  }

  private void onCompanyEntityCreatedImport(CompanyLegalEntityImported event) {
    var importVdg = event.getValuationDataGroup();
    var importSla = event.getSlaDeadline();
    var importCompanyId = event.getCompanyId();
    var importEntityId = event.getEntityId();
    var ipvSettings = CompanyLegalEntityIpvSettings.newOf(importCompanyId, importEntityId);

    var existingIpvSettings =
        companyEntityIpvSettings(importCompanyId, importEntityId, event.getStateDate());

    if (importVdg != null || importSla != null) {
      ipvSettings.applyDefaults(existingIpvSettings);
      ipvSettings.setSettingsType(CompanySettingsType.BESPOKE);
    }

    if (importVdg != null) {
      ipvSettings.setAllProductsForVdg(productTypeResolver.values(), importVdg);
    }

    if (importSla != null) {
      ipvSettings.setSlaDeadline(SlaDeadline.valueOf(importSla));
    }

    insert(ipvSettings, NewVersionFormV2.newDefault());
  }

  @EventListener
  public void onCompanyEntityArchived(CompanyLegalEntityArchived event) {
    entityExact(event.getEntityId(), ROOT_DATE).forEach(this::forceArchive);
  }

  public DateList getFutureVersions(String entityId, LocalDate stateDate) {
    var searchCriteria = hasEntityId(entityId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }
}
