package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateHeader;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.EXTERNAL_TRADE_ID_PREFIX;
import static org.apache.commons.lang3.StringUtils.removeStart;

import com.opengamma.strata.collect.io.CsvIterator;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode
@ToString
public final class ExternalTradeIdsCsvLoader {
  private final List<String> activeIdentifierSources;

  public ExternalTradeIdsCsvLoader(List<String> activeIdentifierSources) {
    this.activeIdentifierSources = activeIdentifierSources;
  }

  public void validateHeaders(CsvIterator iterator) {
    var headers = iterator.headers();
    parseHeaders(headers).forEach(column -> validateHeader(column.source, activeIdentifierSources));
  }

  public List<ExternalIdentifier> parseIdentifiers(CsvRow row) {
    return parseHeaders(row.headers()).stream()
        .flatMap(
            field ->
                CsvLoaderUtils.parseOptionalIdentifier(row, field.header)
                    .map(value -> new ExternalIdentifier(value, field.source))
                    .stream())
        .toList();
  }

  private List<ExternalIdColumn> parseHeaders(List<String> headers) {
    return headers.stream()
        .filter(this::isExternalSourceColumn)
        .map(header -> new ExternalIdColumn(header, parseSource(header)))
        .toList();
  }

  private boolean isExternalSourceColumn(String header) {
    return header.startsWith(EXTERNAL_TRADE_ID_PREFIX);
  }

  private String parseSource(String header) {
    return removeStart(header, EXTERNAL_TRADE_ID_PREFIX);
  }

  private record ExternalIdColumn(String header, String source) {}
}
