package com.solum.xplain.core.curvegroup.volatilityfx;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.volatilityfx.csv.skew.FxVolatilitySkewCsvImportService;
import com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility.FxVolatilityNodeCsvImportService;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveGroupFxVolatilityImportService {

  private final CurveGroupRepository curveGroupRepository;
  private final FxVolatilityNodeCsvImportService nodeCsvImportService;
  private final FxVolatilitySkewCsvImportService skewCsvImportService;

  public CurveGroupFxVolatilityImportService(
      CurveGroupRepository curveGroupRepository,
      FxVolatilityNodeCsvImportService nodeCsvImportService,
      FxVolatilitySkewCsvImportService skewCsvImportService) {
    this.curveGroupRepository = curveGroupRepository;
    this.nodeCsvImportService = nodeCsvImportService;
    this.skewCsvImportService = skewCsvImportService;
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> uploadMatrixNodes(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return curveGroupRepository
        .getGroup(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> nodeCsvImportService.importVolatilities(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> uploadSkews(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return curveGroupRepository
        .getGroup(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> skewCsvImportService.importVolatilitySkews(groupId, importOptions, bytes));
  }
}
