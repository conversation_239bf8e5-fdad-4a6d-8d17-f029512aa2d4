package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.CompanyPortfolio;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
public record ImportPortfolio(String id, PortfolioUniqueKey key) implements CompanyPortfolio {

  @Override
  public String getCompanyId() {
    return key.companyId();
  }

  @Override
  public String getEntityId() {
    return key.entityId();
  }

  @Override
  public String getExternalPortfolioId() {
    return key.externalPortfolioId();
  }
}
