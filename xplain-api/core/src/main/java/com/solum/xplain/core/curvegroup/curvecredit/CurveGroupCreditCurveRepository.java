package com.solum.xplain.core.curvegroup.curvecredit;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curvegroup.utils.CalibrationValueUtils.chartPoints;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.Streams;
import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveNodeExportView;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveIsdaNode;
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCreditNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveFundingNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveSearch;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveForm;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
public class CurveGroupCreditCurveRepository
    extends GenericUniqueVersionedEntityRepository<CreditCurve> {
  private final MongoOperations mongoOperations;
  private final CreditCurveMapper mapper;
  private final ConversionService conversionService;
  private final CurveGroupEntryCountSupport countSupport;
  private final ReferenceData referenceData;

  public CurveGroupCreditCurveRepository(
      MongoOperations mongoOperations,
      CreditCurveMapper mapper,
      ConversionService conversionService,
      CurveGroupEntryCountSupport countSupport,
      ReferenceData referenceData) {
    super(mongoOperations, mapper);
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.conversionService = conversionService;
    this.countSupport = countSupport;
    this.referenceData = referenceData;
  }

  public static Criteria uniqueEntityCriteria(String curveGroupId, String curveName) {
    return groupIdCriteria(curveGroupId).and(VersionedNamedEntity.Fields.name).is(curveName);
  }

  private static Criteria groupIdCriteria(String curveGroupId) {
    return where(CreditCurve.Fields.curveGroupId).is(curveGroupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(CreditCurve entity) {
    return uniqueEntityCriteria(entity.getCurveGroupId(), entity.getName());
  }

  @Override
  protected CreditCurve beforeStoring(CreditCurve entity) {
    return entity.orderNodes(referenceData);
  }

  @Override
  protected Sort defaultEntitiesSort() {
    return Sort.by(
        CreditCurve.Fields.reference, CreditCurve.Fields.seniority, CreditCurve.Fields.currency);
  }

  public Either<ErrorItem, EntityId> createCurve(String groupId, CdsCurveForm form) {
    return insert(mapper.toCdsCurve(form, groupId, CreditCurve.newOf()), form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> createCurve(String groupId, CreditIndexCurveForm form) {
    return insert(
        mapper.toCreditIndexCurve(form, groupId, CreditCurve.newOf()), form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> createCurve(String groupId, CreditIndexTrancheCurveForm form) {
    return insert(
        mapper.toCreditIndexTrancheCurve(form, groupId, CreditCurve.newOf()),
        form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, CdsCurveUpdateForm form) {
    return updateCurve(groupId, curveId, version, c -> updateCdsCurve(c, form, true));
  }

  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, CreditIndexCurveUpdateForm form) {
    return updateCurve(groupId, curveId, version, c -> updateIndexCurve(c, form, true));
  }

  private Either<ErrorItem, EntityId> updateCurve(
      String groupId,
      String curveId,
      LocalDate version,
      Function<CreditCurve, Either<ErrorItem, EntityId>> updateFn) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(updateFn);
  }

  public Either<ErrorItem, EntityId> updateIndexCurve(
      CreditCurve curve, CreditIndexCurveUpdateForm form, boolean includeNodes) {
    return Either.right(
        update(
            curve, form.getVersionForm(), c -> mapper.toCreditIndexCurve(form, c, includeNodes)));
  }

  public Either<ErrorItem, EntityId> updateCdsCurve(
      CreditCurve curve, CdsCurveUpdateForm form, boolean includeNodes) {
    return Either.right(
        update(curve, form.getVersionForm(), c -> mapper.toCdsCurve(form, c, includeNodes)));
  }

  public Either<ErrorItem, EntityId> archiveCurve(
      String groupId, String curveId, LocalDate version, ArchiveEntityForm form) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(entity -> archive(entity, form));
  }

  public Either<ErrorItem, EntityId> deleteCurve(
      String groupId, String curveId, LocalDate version) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(this::delete);
  }

  public List<CreditCurveView> getCurveViews(
      String groupId,
      BitemporalDate stateDate,
      VersionedEntityFilter filter,
      TableFilter tableFilter,
      Sort sort) {
    var criteria = groupIdCriteria(groupId).andOperator(tableFilterCriteria(tableFilter));
    return entities(stateDate, filter, criteria, sort).stream().map(this::toView).toList();
  }

  private Criteria tableFilterCriteria(TableFilter tableFilter) {
    return tableFilter.criteria(CreditCurveView.class, conversionService);
  }

  public Either<ErrorItem, CreditCurveView> getActiveCurveView(
      String groupId, String curveId, LocalDate stateDate) {
    return getActiveCurve(groupId, curveId, stateDate).map(this::toView);
  }

  public List<CreditCurveView> getCurveVersionViews(String groupId, String curveId) {
    return curveInGroup(groupId, curveId)
        .map(this::entityVersions)
        .map(vv -> vv.stream().map(this::toView).toList())
        .getOrElse(List.of());
  }

  public List<CreditCurve> getActiveCurves(String groupId, BitemporalDate stateDate) {
    return entities(stateDate, active(), groupIdCriteria(groupId));
  }

  public Either<ErrorItem, CreditCurve> getActiveCurve(
      String groupId, String curveId, LocalDate stateDate) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entity(entityId, new BitemporalDate(stateDate), active()));
  }

  public DateList getFutureVersions(String groupId, CreditCurveSearch search) {
    var searchCriteria = uniqueEntityCriteria(groupId, search.getCurveName());
    return futureVersionsByCriteria(searchCriteria, search.getStateDate());
  }

  public List<CreditCurveNodeExportView> allCurveNodes(
      String curveGroupId, String curveId, LocalDate version) {
    var eitherCurve = getActiveCurve(curveGroupId, curveId, version);
    return eitherCurve.map(this::curveNodes).getOrElse(List.of());
  }

  private List<CreditCurveNodeExportView> curveNodes(CreditCurve curve) {
    var curveName = curve.getName();
    return Streams.concat(
            curve.getCdsNodes().stream(),
            curve.getFundingNodes().stream(),
            curve.getIndexNodes().stream())
        .map(n -> CreditCurveNodeExportView.newOf(curveName, n))
        .toList();
  }

  public List<CreditCurveCreditNodeCalculatedView> curvesCdsNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      Map<String, CalculationMarketValueFullView> spreads,
      LocalDate valuationDate,
      CurveConfigMarketStateForm stateForm,
      CalculationDiscountingType calibrationCurrency,
      ReferenceData refData) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                toViewNodes(
                    curveGroup,
                    c,
                    CreditCurve::getCdsNodes,
                    spreads,
                    stateForm,
                    calibrationCurrency,
                    valuationDate,
                    refData))
        .getOrElse(List.of());
  }

  public List<CreditCurveCreditNodeCalculatedView> curveCdsNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      LocalDate valuationDate,
      CalculationDiscountingType calibrationCurrency,
      ReferenceData refData) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                toViewNodes(
                    curveGroup,
                    c,
                    CreditCurve::getCdsNodes,
                    marketStateQuotes.quotes(),
                    marketStateQuotes.stateForm(),
                    calibrationCurrency,
                    valuationDate,
                    refData))
        .getOrElse(List.of());
  }

  public List<CreditCurveCreditNodeCalculatedView> curveCdsNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      LocalDate valuationDate,
      CalculationDiscountingType calibrationCurrency) {

    return curveCdsNodes(
        curveGroup,
        curveId,
        version,
        marketStateQuotes,
        valuationDate,
        calibrationCurrency,
        this.referenceData);
  }

  public List<CreditCurveCreditNodeCalculatedView> getIndexNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      Map<String, CalculationMarketValueFullView> spreads,
      LocalDate valuationDate,
      CurveConfigMarketStateForm stateForm) {
    return getIndexNodes(curveGroup, curveId, version, spreads, valuationDate, stateForm, null);
  }

  public List<CreditCurveCreditNodeCalculatedView> getIndexNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      Map<String, CalculationMarketValueFullView> spreads,
      LocalDate valuationDate,
      CurveConfigMarketStateForm stateForm,
      CalculationDiscountingType calibrationCurrency) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                toViewNodes(
                    curveGroup,
                    c,
                    CreditCurve::getIndexNodes,
                    spreads,
                    stateForm,
                    calibrationCurrency,
                    valuationDate,
                    this.referenceData))
        .getOrElse(List.of());
  }

  public List<CreditCurveCreditNodeCalculatedView> getIndexNodesWithRefDataOverride(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      LocalDate valuationDate,
      CalculationDiscountingType calibrationCurrency,
      ReferenceData refDataOverride) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                toViewNodes(
                    curveGroup,
                    c,
                    CreditCurve::getIndexNodes,
                    marketStateQuotes.quotes(),
                    marketStateQuotes.stateForm(),
                    calibrationCurrency,
                    valuationDate,
                    refDataOverride))
        .getOrElse(List.of());
  }

  private <T extends CreditCurveIsdaNode> List<CreditCurveCreditNodeCalculatedView> toViewNodes(
      CurveGroupView curveGroup,
      CreditCurve curve,
      Function<CreditCurve, List<T>> nodesFn,
      Map<String, CalculationMarketValueFullView> spreads,
      CurveConfigMarketStateForm stateForm,
      CalculationDiscountingType calibrationCurrency,
      LocalDate valuationDate,
      ReferenceData refData) {
    var rates =
        getCurveChartPoints(curveGroup, curve.getEntityId(), stateForm, calibrationCurrency);
    var priceType = stateForm.priceRequirements().getCurvesPriceType();

    return nodesFn.apply(curve).stream()
        .map(
            n ->
                CreditCurveCreditNodeCalculatedView.of(
                    curve, n, valuationDate, spreads, rates, priceType, refData))
        .toList();
  }

  public List<CreditCurveFundingNodeCalculatedView> getFundingNodes(
      CurveGroupView curveGroup,
      String curveId,
      LocalDate version,
      Map<String, CalculationMarketValueFullView> spreads,
      InstrumentPriceRequirements priceRequirements) {
    var eitherCurve = getActiveCurve(curveGroup.getId(), curveId, version);
    var priceType = priceRequirements.getCurvesPriceType();
    return eitherCurve
        .map(
            c ->
                c.getFundingNodes().stream()
                    .map(
                        n ->
                            CreditCurveFundingNodeCalculatedView.of(
                                c.getReference(), c.getCurrency(), n, spreads, priceType))
                    .toList())
        .getOrElse(List.of());
  }

  public Map<LocalDate, ChartPoint> getCurveChartPoints(
      CurveGroupView curveGroup,
      String curveId,
      CurveConfigMarketStateForm stateForm,
      CalculationDiscountingType calibrationCurrency) {
    return getActiveCurve(curveGroup.getId(), curveId, stateForm.getStateDate())
        .map(c -> chartPoints(curveGroup, stateForm, calibrationCurrency, c.getChartPoints()))
        .getOrElse(Map.of());
  }

  public void updateCalibrationResults(
      String groupId, LocalDate stateDate, Function<String, List<ChartPoint>> chartPointsF) {
    var curves = getActiveCurves(groupId, new BitemporalDate(stateDate));
    curves.forEach(
        curve -> {
          curve.setChartPoints(chartPointsF.apply(curve.getName()));
          mongoOperations.save(curve);
        });
  }

  public void clearCalibrationResults(String groupId) {
    var query = query(groupIdCriteria(groupId));
    var update = new Update().unset(CreditCurve.Fields.chartPoints);
    mongoOperations.updateMulti(query, update, CreditCurve.class);
  }

  private CreditCurveView toView(CreditCurve curve) {
    return mapper.toView(curve);
  }

  private Either<ErrorItem, String> curveInGroup(String groupId, String curveId) {
    var criteria = groupIdCriteria(groupId).and(VersionedEntity.Fields.entityId).is(curveId);
    var curveInGroup = mongoOperations.exists(query(criteria), CreditCurve.class);
    return Eithers.cond(curveInGroup, OBJECT_NOT_FOUND.entity("Credit curve not found"), curveId);
  }

  public List<CurveGroupEntryCount> activeEntriesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeEntriesCount(filter, CreditCurve.class);
  }
}
