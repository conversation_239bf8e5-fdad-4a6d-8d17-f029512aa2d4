package com.solum.xplain.core.company.value;

import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class SlaDeadlinePortfolioView extends PortfolioCondensedView implements Serializable {
  private SlaDeadline slaDeadline;

  public static SlaDeadlinePortfolioView newOf(
      String portfolioId,
      String externalPortfolioId,
      String companyId,
      String externalCompanyId,
      String entityId,
      String externalEntityId,
      SlaDeadline slaDeadline) {
    var view = new SlaDeadlinePortfolioView();
    view.setId(portfolioId);
    view.setExternalPortfolioId(externalPortfolioId);
    view.setCompanyId(companyId);
    view.setExternalCompanyId(externalCompanyId);
    view.setEntityId(entityId);
    view.setExternalEntityId(externalEntityId);
    view.setSlaDeadline(slaDeadline);
    return view;
  }
}
