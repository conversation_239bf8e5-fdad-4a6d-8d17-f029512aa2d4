package com.solum.xplain.core.common.validation;

import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.VALIDATION_INSTANCE;

import com.solum.xplain.core.classifiers.Classifier;
import java.util.Collection;
import java.util.List;
import java.util.function.Supplier;
import org.springframework.util.Assert;

public class ClassifierSupplier implements Supplier<Collection<String>> {

  private final String classifier;
  private List<String> values;

  public ClassifierSupplier(String classifier) {
    Assert.notNull(classifier, "Classifier must not be null!");
    this.classifier = classifier;
  }

  @Override
  public Collection<String> get() {
    if (this.values == null) {
      initializeValues();
    }
    return this.values;
  }

  private synchronized void initializeValues() {
    if (this.values == null) {
      this.values =
          VALIDATION_INSTANCE.classifiers().stream()
              .filter(c -> classifier.equals(c.getId()))
              .map(Classifier::getValues)
              .flatMap(List::stream)
              .map(Classifier::getId)
              .toList();
    }
  }
}
