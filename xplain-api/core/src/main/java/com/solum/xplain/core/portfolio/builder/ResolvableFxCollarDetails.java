package com.solum.xplain.core.portfolio.builder;

import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import com.solum.xplain.extensions.enums.CallPutType;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ResolvableFxCollarDetails extends AbstractResolvableFxDetails {
  @Nullable private final Double otherOptionCounterNotional;
  @Nullable private final Double otherOptionStrike;

  @Override
  protected void additionalOptionDetails(OptionTradeDetails optionDetails) {
    validateStrikes();
    optionDetails.setOtherOptionCounterNotional(otherOptionCounterNotional);
    optionDetails.setOtherOptionStrike(manualOrCalculatedOtherStrike());
  }

  private void validateStrikes() {
    double strike = manualOrCalculatedStrike();
    double otherStrike = manualOrCalculatedOtherStrike();

    if (callPutType == CallPutType.CALL || callPutType == null) {
      if (strike < otherStrike) {
        throw new IllegalArgumentException(
            "Call Strike must be greater than or equal to Put Strike. "
                + "Call Strike: "
                + strike
                + ", Put Strike: "
                + otherStrike);
      }
    } else {
      if (otherStrike < strike) {
        throw new IllegalArgumentException(
            "Call Strike must be greater than or equal to Put Strike. "
                + "Call Strike: "
                + otherStrike
                + ", Put Strike: "
                + strike);
      }
    }
  }

  protected double manualOrCalculatedOtherStrike() {
    if (payCurrencyAmount != null
        && receiveCurrencyAmount != null
        && otherOptionCounterNotional != null) {
      return callPutType == CallPutType.PUT
          ? Math.abs(otherOptionCounterNotional / payCurrencyAmount)
          : Math.abs(otherOptionCounterNotional / receiveCurrencyAmount);
    }
    return otherOptionStrike != null ? otherOptionStrike : 0d;
  }
}
