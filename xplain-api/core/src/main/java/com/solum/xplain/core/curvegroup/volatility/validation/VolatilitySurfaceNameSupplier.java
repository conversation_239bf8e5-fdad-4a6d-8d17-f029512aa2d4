package com.solum.xplain.core.curvegroup.volatility.validation;

import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.volatilitySurfaceNames;

import com.solum.xplain.core.classifiers.Classifier;
import java.util.Collection;
import java.util.function.Supplier;

public class VolatilitySurfaceNameSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return volatilitySurfaceNames().getValues().stream().map(Classifier::getId).toList();
  }
}
