package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@EqualsAndHashCode
public class PortfolioCondensedView implements Serializable {
  @ConfigurableViewIgnore private String id;

  @ConfigurableViewQuery(sortable = true)
  private String externalPortfolioId;

  @ConfigurableViewIgnore private String companyId;

  @ConfigurableViewQuery(sortable = true)
  private String externalCompanyId;

  @ConfigurableViewIgnore private String entityId;

  @ConfigurableViewQuery(sortable = true)
  private String externalEntityId;

  public PortfolioCondensedView() {}

  public static PortfolioCondensedView newOf(
      String portfolioId,
      String externalPortfolioId,
      String companyId,
      String externalCompanyId,
      String entityId,
      String externalEntityId) {
    var view = new PortfolioCondensedView();
    view.setId(portfolioId);
    view.setExternalPortfolioId(externalPortfolioId);
    view.setCompanyId(companyId);
    view.setExternalCompanyId(externalCompanyId);
    view.setEntityId(entityId);
    view.setExternalEntityId(externalEntityId);
    return view;
  }
}
