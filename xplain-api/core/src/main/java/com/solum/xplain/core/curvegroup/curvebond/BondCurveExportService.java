package com.solum.xplain.core.curvegroup.curvebond;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.curvegroup.curve.csv.CurvePointsCsvBuilder.ofSingleValueType;
import static io.atlassian.fugue.Either.right;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvMapper;
import com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvMapper;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class BondCurveExportService {

  private static final String BOND_CURVES_FILE_PREFIX = "BondCurveList";
  private static final String CURVE_NODES = "CurveNodes";
  private static final String BOND_CURVE_NODES = "BondCurveNodes";
  private static final String BOND_CURVE_CHART = "BondCurveBondYield";
  private static final String BOND_YIELD = "Yield";

  private final CurveGroupRepository curveGroupRepository;
  private final BondCurveRepository curveRepository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;

  public Either<ErrorItem, FileResponseEntity> getCurvesCsvBytes(
      String groupId, BitemporalDate stateDate) {
    var csvFileName = nameWithTimeStamp(BOND_CURVES_FILE_PREFIX, stateDate.getActualDate());
    return withinGroup(
        groupId,
        g -> {
          var views = curveRepository.getCurveViewsForExport(groupId, stateDate);
          var mapper = new BondCurveCsvMapper();
          var rows = views.stream().map(mapper::toCsvRow).toList();
          var csvFile = new CsvOutputFile(mapper.header(), rows);
          return right(FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName));
        });
  }

  public Either<ErrorItem, FileResponseEntity> getNodesCsvBytes(
      String groupId,
      String curveId,
      BitemporalDate stateDate,
      CurveConfigMarketStateForm stateForm) {
    return withinGroup(
        groupId,
        g ->
            curveRepository
                .getActiveCurveView(groupId, curveId, stateDate)
                .map(
                    c -> {
                      var csvFile = getCurveNodesCsv(groupId, stateForm, List.of(c));
                      var csvFileName =
                          nameWithTimeStamp(c.getName(), CURVE_NODES, stateForm.getStateDate());
                      return FileResponseEntity.csvFile(csvFile, csvFileName);
                    }));
  }

  public Either<ErrorItem, FileResponseEntity> getNodesCsvBytes(
      String groupId, BitemporalDate version, CurveConfigMarketStateForm stateForm) {
    return withinGroup(
        groupId,
        g -> {
          var curves = curveRepository.getCurveViewsForExport(groupId, version);
          var prefix = getPrefix(BOND_CURVE_NODES, curves, BondCurveView::getName, CURVE_NODES);
          var csvFileName = nameWithTimeStamp(prefix, stateForm.getStateDate());
          var csvFile = getCurveNodesCsv(groupId, stateForm, curves);
          return right(FileResponseEntity.csvFile(csvFile, csvFileName));
        });
  }

  public Either<ErrorItem, FileResponseEntity> getMDKDefinitionsCsvBytes(
      String groupId,
      String curveConfigurationId,
      List<String> curveIds,
      BitemporalDate stateDate) {
    return withinGroup(
        groupId,
        g -> {
          var curves =
              curveRepository.getActiveCurves(groupId, stateDate).stream()
                  .filter(c -> curveIds.contains(c.getEntityId()))
                  .toList();
          return definitionExportService.instrumentMdkDefinitions(
              curves,
              getPrefix(BOND_CURVE_NODES, curves, BondCurve::getName, CURVE_NODES),
              BondCurve::allInstruments,
              stateDate.getActualDate(),
              curveConfigurationId);
        });
  }

  public Either<ErrorItem, FileResponseEntity> getCurveChartPointsCsv(
      String groupId, String curveId, CurveConfigMarketStateForm stateForm) {
    return curveRepository
        .getActiveCurveView(groupId, curveId, stateForm.bitemporalDate())
        .map(
            c -> {
              var points = curveRepository.getCurveChartPoints(groupId, stateForm, curveId);
              var csvFileName = nameWithTimeStamp(BOND_CURVE_CHART, stateForm.bitemporalDate());
              var csvFile = ofSingleValueType(BOND_YIELD).withPoints(points, c.getName()).toCsv();
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getAllCurvesChartPointsCsv(
      String groupId, CurveConfigMarketStateForm stateForm) {
    return withinGroup(
        groupId,
        cg -> {
          var csvFileName = nameWithTimeStamp(cg.getName(), BOND_YIELD, stateForm.getStateDate());
          var builder = ofSingleValueType(BOND_YIELD);
          for (var c :
              curveRepository.getCurveViewsForExport(groupId, stateForm.bitemporalDate())) {
            var points = curveRepository.getCurveChartPoints(groupId, stateForm, c.getEntityId());
            builder.withPoints(points, c.getName());
          }
          return right(FileResponseEntity.csvFile(builder.toCsv().writeToByteArray(), csvFileName));
        });
  }

  private ByteArrayResource getCurveNodesCsv(
      String groupId, CurveConfigMarketStateForm stateForm, List<BondCurveView> views) {
    var quotes = marketDataQuotesSupport.getFullQuotes(stateForm);
    var priceRequirements = stateForm.priceRequirements();
    var nodeCsvMapper = new BondCurveNodeCsvMapper();
    return views.stream()
        .map(
            c ->
                curveRepository
                    .getCurveNodes(
                        groupId,
                        c.getEntityId(),
                        BitemporalDate.newOf(c.getValidFrom()),
                        quotes,
                        priceRequirements)
                    .stream()
                    .map(node -> nodeCsvMapper.toCsvRow(c.getName(), node))
                    .toList())
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> new CsvOutputFile(nodeCsvMapper.header(), l)))
        .writeToByteArray();
  }

  private <T> Either<ErrorItem, T> withinGroup(
      String groupId, Function<CurveGroupView, Either<ErrorItem, T>> applyFunction) {
    return curveGroupRepository.getEither(groupId).flatMap(applyFunction);
  }
}
