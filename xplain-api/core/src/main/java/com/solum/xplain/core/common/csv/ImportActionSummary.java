package com.solum.xplain.core.common.csv;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Maybe;
import io.atlassian.fugue.Option;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ImportActionSummary {

  private final Maybe<EntityId> entityId;

  private final List<ErrorItem> errors;
  private final List<ErrorItem> warnings;
  private final Integer changesCount;

  public static ImportActionSummary empty() {
    return new ImportActionSummary(Option.none(), List.of(), List.of(), 0);
  }

  public static ImportActionSummary newOf(EntityId entityId, List<ErrorItem> errors) {
    return new ImportActionSummary(Option.option(entityId), errors, List.of(), 0);
  }

  public static ImportActionSummary newOf(EntityId entityId, Integer changesCount) {
    return new ImportActionSummary(Option.option(entityId), List.of(), List.of(), changesCount);
  }

  public static ImportActionSummary ofParsingWarnings(List<ErrorItem> warnings) {
    return new ImportActionSummary(Option.none(), List.of(), warnings, 0);
  }
}
