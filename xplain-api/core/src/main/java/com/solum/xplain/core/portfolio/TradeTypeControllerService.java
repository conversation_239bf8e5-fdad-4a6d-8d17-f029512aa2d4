package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class TradeTypeControllerService {

  private final PortfolioItemWriteRepository portfolioItemWriteRepository;
  private final PortfolioItemRepository portfolioItemRepository;
  private final AuthenticationContext authenticationContext;
  private final PortfolioRepository portfolioRepository;

  @Transactional
  public Either<ErrorItem, EntityId> insert(String id, ParsableToTradeValue form) {
    return getUserPortfolio(id)
        .flatMap(p -> portfolioItemWriteRepository.insert(p.getView().getId(), form));
  }

  public Either<ErrorItem, PortfolioItem> tradeView(
      String id, String tradeEntityId, BitemporalDate bitemporalState) {
    return getUserPortfolio(id)
        .flatMap(
            p -> portfolioItemRepository.portfolioItemLatest(id, tradeEntityId, bitemporalState));
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String portfolioId, LocalDate version, ParsableToTradeValue edit, String tradeEntityId) {
    return getUserPortfolio(portfolioId)
        .flatMap(
            p -> portfolioItemWriteRepository.update(portfolioId, tradeEntityId, version, edit));
  }

  private Either<ErrorItem, UserTeamEntity<PortfolioView>> getUserPortfolio(String id) {
    var user = authenticationContext.currentUser();
    return portfolioRepository.getUserPortfolioView(user, id);
  }
}
