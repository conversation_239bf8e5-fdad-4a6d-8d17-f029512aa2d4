package com.solum.xplain.core.ipv.tradeleveloverride;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VALUATION_DATA;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_DATA;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.IPV_DATA_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideSearchForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideUpdateForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideView;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/deal-exceptions")
@AllArgsConstructor
public class TradeLevelOverrideController {

  private final TradeLevelOverrideControllerService tradeLevelOverrideService;
  private final TradeLevelOverrideUploadService uploadService;

  @Operation(summary = "New deal exception")
  @PostMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public EntityId newTradeLevelOverride(@Valid @RequestBody TradeLevelOverrideForm form) {
    return tradeLevelOverrideService.insert(form);
  }

  @Operation(summary = "Validate upload deal exceptions csv file")
  @PostMapping(value = "/upload/validate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public ResponseEntity<ValidationResponse> validateUploadTradeLevelOverrides(
      @RequestPart MultipartFile file,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadService.validate(file.getBytes(), parsingMode, stateDate));
  }

  @Operation(summary = "Upload deal exceptions csv file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public ResponseEntity<List<EntityId>> uploadTradeLevelOverrides(
      @RequestPart MultipartFile file, @Valid ImportOptions importOptions) throws IOException {
    return eitherErrorItemsResponse(
        uploadService.uploadTradeLevelOverrides(file.getBytes(), importOptions));
  }

  @Operation(summary = "Export deal exceptions in csv format")
  @CommonErrors
  @GetMapping("/csv")
  @SortedFiltered
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ByteArrayResource> getTradeLevelOverridesCsv(
      TableFilter tableFilter,
      @SortDefault(
              sort = {
                TradeLevelOverride.Fields.externalCompanyId,
                TradeLevelOverride.Fields.externalEntityId,
                TradeLevelOverride.Fields.externalPortfolioId,
                TradeLevelOverride.Fields.externalPortfolioItemId,
              })
          Sort sort,
      @RequestParam LocalDate stateDate) {
    return tradeLevelOverrideService
        .getTradeLevelOverrideCsv(sort, tableFilter, stateDate)
        .toResponse();
  }

  @Operation(summary = "Get active deal exceptions")
  @GetMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public List<TradeLevelOverride> activeTradeLevelOverrides(@RequestParam LocalDate stateDate) {
    return tradeLevelOverrideService.activeTradeLevelOverrides(BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Get deal exceptions scrollable entry")
  @ScrolledFiltered
  @CommonErrors
  @Sorted
  @GetMapping("/all")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA) // TODO: DLO permissions
  public ScrollableEntry<TradeLevelOverrideView> getAll(
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      @RequestParam(required = false) boolean withArchived,
      @RequestParam LocalDate stateDate) {
    return tradeLevelOverrideService.getAll(
        scrollRequest, tableFilter, withArchived, BitemporalDate.newOf(stateDate));
  }

  @Operation(summary = "Update deal exception")
  @PutMapping("/{entityId}/{versionDate}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public ResponseEntity<EntityId> updateTradeLevelOverride(
      @PathVariable String entityId,
      @PathVariable LocalDate versionDate,
      @Valid @RequestBody TradeLevelOverrideUpdateForm form) {
    return eitherErrorItemResponse(tradeLevelOverrideService.update(entityId, versionDate, form));
  }

  @Operation(summary = "Archive deal exception")
  @PutMapping("/{entityId}/{versionDate}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public ResponseEntity<EntityId> archiveTradeLevelOverride(
      @PathVariable String entityId,
      @PathVariable LocalDate versionDate,
      @Valid @RequestBody ArchiveEntityForm archiveForm) {
    return eitherErrorItemResponse(
        tradeLevelOverrideService.archive(entityId, versionDate, archiveForm));
  }

  @Operation(summary = "Delete deal exception")
  @PutMapping("/{entityId}/{versionDate}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA) // TODO: DLO permissions
  @RequireLock(name = IPV_DATA_LOCK_ID) // TODO: DLO locks
  public ResponseEntity<EntityId> deleteTradeLevelOverride(
      @PathVariable String entityId, @PathVariable LocalDate versionDate) {
    return eitherErrorItemResponse(tradeLevelOverrideService.delete(entityId, versionDate));
  }

  @Operation(summary = "Get deal exception future versions")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA) // TODO: DLO permissions
  public DateList getFutureVersions(@Valid TradeLevelOverrideSearchForm search) {
    return tradeLevelOverrideService.futureVersions(search);
  }
}
