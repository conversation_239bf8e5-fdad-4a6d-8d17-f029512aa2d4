package com.solum.xplain.core.curvegroup.volatility.csv.node;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.GroupedItemsImportService;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.volatility.CurveGroupVolatilityRepository;
import com.solum.xplain.core.curvegroup.volatility.VolatilityMapper;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VolatilityNodeCsvImportService
    extends GroupedItemsImportService<VolatilitySurface, VolatilityNodeForm, ExpiryTenorKey> {

  private final VolatilityNodeCsvLoader csvLoader;
  private final VolatilityMapper mapper;
  private final CurveGroupVolatilityRepository repository;

  public VolatilityNodeCsvImportService(
      AuditEntryService auditEntryService,
      VolatilityNodeCsvLoader csvLoader,
      VolatilityMapper mapper,
      CurveGroupVolatilityRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.mapper = mapper;
    this.repository = repository;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForSurface(
      String groupId,
      String surfaceId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return fetchSurface(groupId, surfaceId, versionDate)
        .map(
            c ->
                maybeImport(
                    importOptions.parsingMode(),
                    csvLoader.parseForCurve(bytes, c, importOptions.getDuplicateAction()),
                    curvesNodes -> importNodes(List.of(c), importOptions, curvesNodes)))
        .fold(
            e -> toErrorReturn(importOptions.getDuplicateAction(), List.of(e)),
            result -> toReturn(importOptions.getDuplicateAction(), result));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForAll(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    var curves = fetchSurfaces(groupId, importOptions.getStateDate());
    var logItems =
        maybeImport(
            importOptions.parsingMode(),
            csvLoader.parse(bytes, curves, importOptions.getDuplicateAction()),
            curvesNodes -> importNodes(curves, importOptions, curvesNodes));
    return toReturn(importOptions.getDuplicateAction(), logItems);
  }

  protected List<LogItem> importNodes(
      List<VolatilitySurface> surfaces,
      ImportOptions importOptions,
      List<NamedList<VolatilityNodeForm>> curveNodes) {
    return curveNodes.stream()
        .map(
            nn -> {
              var surface = findSurface(surfaces, nn.getName());
              var importItems =
                  ImportItems.<VolatilityNodeForm, ExpiryTenorKey, VolatilityNodeForm>builder()
                      .existingActiveItems(nodeForms(surface))
                      .existingItemToKeyFn(ExpiryTenorKey::from)
                      .importItems(nn.getItems())
                      .importItemToKeyFn(ExpiryTenorKey::from)
                      .build();
              return importForEntity(surface, importItems, importOptions);
            })
        .flatMap(Collection::stream)
        .toList();
  }

  private List<VolatilityNodeForm> nodeForms(VolatilitySurface surface) {
    return surface.getNodes().stream().map(mapper::toNodeForm).toList();
  }

  @Override
  public String getCollection() {
    return VolatilitySurface.VOLATILITY_SURFACE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "IR Volatility surfaces' ATM  Swaption Volatility nodes";
  }

  @Override
  protected String entityIdentifier(VolatilitySurface curve) {
    return curve.getName();
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      VolatilitySurface surface, List<VolatilityNodeForm> nodeForms, NewVersionFormV2 versionForm) {
    VolatilitySurfaceUpdateForm form = mapper.toUpdateForm(surface);
    form.setVersionForm(versionForm);
    form.setNodes(nodeForms);
    return update(surface, form);
  }

  @Override
  protected boolean hasFutureVersions(VolatilitySurface surface, LocalDate stateDate) {
    var search = new VolatilitySurfaceSearch(surface.getName(), stateDate);
    return repository.getFutureVersions(surface.getCurveGroupId(), search).notEmpty();
  }

  private Either<ErrorItem, EntityId> update(VolatilitySurface e, VolatilitySurfaceUpdateForm f) {
    return repository.updateSurface(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  private List<VolatilitySurface> fetchSurfaces(String groupId, LocalDate stateDate) {
    return repository.getActiveSurfaces(groupId, BitemporalDate.newOf(stateDate));
  }

  private Either<ErrorItem, VolatilitySurface> fetchSurface(
      String groupId, String curveId, LocalDate stateDate) {
    return repository.getActiveSurface(groupId, curveId, stateDate);
  }

  private VolatilitySurface findSurface(
      List<VolatilitySurface> surfaces, String existingCurveName) {
    return IterableUtils.find(surfaces, s -> Objects.equals(s.getName(), existingCurveName));
  }
}
