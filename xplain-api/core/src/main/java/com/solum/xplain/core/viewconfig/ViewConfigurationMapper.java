package com.solum.xplain.core.viewconfig;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.viewconfig.entity.ColumnDefinition;
import com.solum.xplain.core.viewconfig.entity.ColumnDefinitionGroup;
import com.solum.xplain.core.viewconfig.entity.ViewConfiguration;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupView;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.View;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationCreateForm;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationUpdateForm;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.util.Assert;

@Mapper(uses = {ObjectIdMapper.class, AuditUserMapper.class})
public abstract class ViewConfigurationMapper {
  @Resource PaletteService paletteService;

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  public abstract ViewConfiguration fromForm(ViewConfigurationCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "shared", constant = "false")
  @Mapping(target = "name", expression = "java(\"Copy of \" + original.name())")
  public abstract ViewConfiguration clone(ViewConfigurationView<?> original);

  @Mapping(target = "fieldName", source = "fieldDefinition.name")
  abstract ColumnDefinition clone(ColumnDefinitionView original);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "scope", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  public abstract ViewConfiguration fromForm(
      ViewConfigurationUpdateForm updateForm, @MappingTarget ViewConfiguration configuration);

  public <T> ViewConfigurationView<T> toViewConfigurationView(
      @Context View<T> scope, ViewConfiguration viewConfiguration, boolean isOwner) {
    Assert.isTrue(
        viewConfiguration.getScope().equals(scope), "Scope must match view configuration scope");
    //noinspection unchecked
    return toViewConfigurationViewRaw(viewConfiguration, scope, isOwner);
  }

  @Mapping(target = "isOwner", expression = "java(isOwner)")
  @SuppressWarnings("rawtypes")
  abstract ViewConfigurationView toViewConfigurationViewRaw(
      ViewConfiguration viewConfiguration, @Context View<?> scope, boolean isOwner);

  abstract List<ColumnDefinitionGroupView> toColumnDefinitionGroupViewList(
      List<ColumnDefinitionGroup> groups, @Context View<?> scope);

  @Mapping(
      target = "columnDefinitions",
      nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  abstract ColumnDefinitionGroupView toColumnDefinitionGroupView(
      ColumnDefinitionGroup group, @Context View<?> scope);

  /**
   * @return Column definition with matching field from palette, if there is a matching field
   *     definition for this column definition, otherwise empty.
   */
  @Nullable
  ColumnDefinitionView toDefinitionView(ColumnDefinition columnDefinition, @Context View<?> scope) {
    Optional<FieldDefinitionView> fieldDefinitionView =
        paletteService
            .findPaletteView(scope.viewClass())
            .flatMap(
                palette ->
                    palette.fieldDefinitions().stream()
                        .filter(def -> def.name().equals(columnDefinition.getFieldName()))
                        .findAny());
    return fieldDefinitionView
        .map(fieldDefinition -> toDefinitionView(columnDefinition, fieldDefinition))
        .orElse(null);
  }

  @AfterMapping
  protected void removeNullColumnDefinitions(
      @MappingTarget List<ColumnDefinitionView> columnDefinitions) {
    // Close eyes... we're mutating the ArrayList
    columnDefinitions.removeIf(Objects::isNull);
  }

  protected abstract ColumnDefinitionView toDefinitionView(
      ColumnDefinition definition, FieldDefinitionView fieldDefinition);
}
