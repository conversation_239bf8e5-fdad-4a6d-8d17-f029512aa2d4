package com.solum.xplain.core.ccyexposure.value;

import com.solum.xplain.core.common.validation.NotZero;
import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CashflowForm implements HasVersionForm {

  @NotNull private String ccyExposureId;

  /** This becomes the VersionedNamedEntity.name as well as being mapped to the date */
  @NotNull private LocalDate date;

  @NotNull @NotZero private Double amount;

  @Valid @NotNull private NewVersionFormV2 versionForm;

  /** Constructor for use when parsing CSV */
  public CashflowForm(String ccyExposureId, LocalDate date, Double amount) {
    this.ccyExposureId = ccyExposureId;
    this.date = date;
    this.amount = amount;
  }

  public String getName() {
    return date.toString();
  }

  public static CashflowForm newOfWithDefaultVersion(
      String ccyExposureId, LocalDate date, Double amount) {
    var form = new CashflowForm(ccyExposureId, date, amount);
    form.setVersionForm(NewVersionFormV2.newDefault("Default settings"));
    return form;
  }
}
