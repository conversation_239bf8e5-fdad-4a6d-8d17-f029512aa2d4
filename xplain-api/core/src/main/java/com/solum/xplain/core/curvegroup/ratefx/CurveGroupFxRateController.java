package com.solum.xplain.core.curvegroup.ratefx;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_FX_RATE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_FX_RATE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.lock.RequireLock;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/curve-group/{groupId}/fx-rate")
@AllArgsConstructor
public class CurveGroupFxRateController {

  private final CurveGroupFxRatesService service;
  private final CurveGroupFxRatesImportService importService;
  private final CurveGroupFxRatesExportService exportService;

  @Operation(summary = "Creates new or updates existing FX Rates")
  @PostMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_FX_RATE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> createFxRates(
      @PathVariable("groupId") String groupId, @Valid @RequestBody CurveGroupFxRatesForm form) {
    return eitherErrorItemResponse(service.create(groupId, form));
  }

  @Operation(summary = "Gets FX Rates")
  @GetMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FX_RATE)
  public ResponseEntity<CurveGroupFxRatesView> getFxRates(
      @PathVariable("groupId") String groupId, @RequestParam("stateDate") LocalDate stateDate) {
    return eitherErrorItemResponse(service.get(groupId, newOf(stateDate)));
  }

  @Operation(summary = "Updates FX rates")
  @PutMapping("/{version}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_FX_RATE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateFxRates(
      @PathVariable("groupId") String groupId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody CurveGroupFxRatesForm form) {
    return eitherErrorItemResponse(service.update(groupId, version, form));
  }

  @Operation(summary = "Deletes (sets status to DELETED) FX Rates")
  @PutMapping("/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_FX_RATE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> deleteFxRates(
      @PathVariable("groupId") String groupId, @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(service.delete(groupId, version));
  }

  @Operation(summary = "Get FX Rates versions")
  @GetMapping("/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FX_RATE)
  public ResponseEntity<List<CurveGroupFxRatesView>> getVersions(
      @PathVariable("groupId") String groupId) {
    return eitherErrorItemResponse(service.getVersions(groupId));
  }

  @Operation(summary = "Gets FX Rates future versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FX_RATE)
  public ResponseEntity<DateList> getFxRatesFutureVersionsDates(
      @PathVariable("groupId") String groupId, @RequestParam("stateDate") LocalDate stateDate) {
    return eitherErrorItemResponse(service.getFutureVersionsDates(groupId, stateDate));
  }

  @Operation(summary = "Uploads FX Rates CSV file")
  @PostMapping(value = "/nodes/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_FX_RATE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> uploadFxRatesCsv(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodes(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Gets FX Rates values")
  @GetMapping("/nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FX_RATE)
  public ResponseEntity<VersionedList<CurveGroupFxRatesNodeValueView>> getFxRatesValues(
      @PathVariable("groupId") String groupId, CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(service.getRatesValues(groupId, stateForm));
  }

  @Operation(summary = "Gets Fx Rates CSV file")
  @GetMapping("/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_FX_RATE)
  public ResponseEntity<ByteArrayResource> getFxRatesCsv(
      @PathVariable("groupId") String groupId,
      CurveConfigMarketStateForm stateForm,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getFxRatesCsvBytes(groupId, stateForm, selectedColumns));
  }

  @Operation(summary = "Gets FX Rates MDK definitions CSV")
  @GetMapping("/nodes/mdk-definitions/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getMdkDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam(required = false) String configurationId) {
    return eitherErrorItemFileResponse(
        exportService.getFxRatesMdkDefinitionCsvBytes(groupId, stateDate, configurationId));
  }
}
