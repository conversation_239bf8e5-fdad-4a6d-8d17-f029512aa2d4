package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import com.solum.xplain.core.utils.FrequencyUtils;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;

public class ValidCapFloorAccrualFrequencyValidator
    implements ConstraintValidator<ValidCapFloorAccrualFrequency, CapFloorTradeForm> {

  public static final String INVALID_ACCRUAL_FREQUENCY =
      "Accrual Frequency must come from IBOR index attributes";

  public static boolean validateAccrualFrequency(String accrualFrequency, String iborIndexTenor) {
    return accrualFrequency.equals(iborIndexTenor);
  }

  @Override
  public boolean isValid(CapFloorTradeForm form, ConstraintValidatorContext context) {
    if (ObjectUtils.anyNull(form, form.getCalculationIborIndex(), form.getAccrualFrequency())) {
      return true;
    }
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("accrualFrequency")
        .addConstraintViolation();
    return Steps.begin(parseFrequency(form.getAccrualFrequency()))
        .then(() -> parseIndexTenor(form.getCalculationIborIndex()))
        .yield(ValidCapFloorAccrualFrequencyValidator::validateAccrualFrequency)
        .orElse(true);
  }

  private Optional<String> parseFrequency(String frequencyStr) {
    return Checked.now(() -> FrequencyUtils.toStringNoPrefix(Frequency.parse(frequencyStr)))
        .toOptional();
  }

  private Optional<String> parseIndexTenor(String iborIndex) {
    return Checked.now(() -> IborIndex.of(iborIndex).getTenor().toString()).toOptional();
  }
}
