package com.solum.xplain.core.settings.value;

import com.solum.xplain.core.common.versions.settings.VersionedSettingsView;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InflationSeasonalitySettingsView extends VersionedSettingsView {
  private List<CurveSeasonalityView> curveSeasonalities;
}
