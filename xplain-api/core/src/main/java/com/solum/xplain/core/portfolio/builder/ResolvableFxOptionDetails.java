package com.solum.xplain.core.portfolio.builder;

import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ResolvableFxOptionDetails extends AbstractResolvableFxDetails {
  @Override
  protected void additionalOptionDetails(
      OptionTradeDetails optionDetails) {} // super method does set all required fields
}
