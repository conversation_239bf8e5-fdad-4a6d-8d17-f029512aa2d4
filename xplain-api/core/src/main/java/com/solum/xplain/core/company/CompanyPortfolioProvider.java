package com.solum.xplain.core.company;

import java.util.List;
import java.util.Map;
import org.jspecify.annotations.NullMarked;

@NullMarked
public interface CompanyPortfolioProvider {

  /**
   * Counts the number of portfolios for each company ID provided.
   *
   * @param companyIds List of company IDs to count portfolios for.
   * @return A map where the key is the company ID and the value is the count of portfolios.
   */
  Map<String, Integer> countAll(List<String> companyIds);
}
