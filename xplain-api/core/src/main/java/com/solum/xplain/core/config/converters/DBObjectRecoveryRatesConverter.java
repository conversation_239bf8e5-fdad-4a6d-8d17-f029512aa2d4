package com.solum.xplain.core.config.converters;

import static com.solum.xplain.core.config.converters.CustomJodaBeanSerializer.JODA_SERIALIZER;

import com.opengamma.strata.pricer.credit.RecoveryRates;
import org.bson.Document;
import org.springframework.core.convert.converter.Converter;

public class DBObjectRecoveryRatesConverter implements Converter<Document, RecoveryRates> {
  @Override
  public RecoveryRates convert(Document source) {
    return (RecoveryRates) JODA_SERIALIZER.jsonReader().read(source.toJson());
  }
}
