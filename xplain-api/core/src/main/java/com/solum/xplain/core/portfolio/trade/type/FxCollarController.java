package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.FxCollarTradeForm;
import com.solum.xplain.core.portfolio.value.FxCollarTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/fx-collar")
@AllArgsConstructor
public class FxCollarController
    implements BespokeTradeTypedController<FxCollarTradeForm, FxCollarTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, FxCollarTradeView> toViewFunction(PortfolioItem e) {
    return FxCollarTradeView.of(e);
  }
}
