package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor
@FieldNameConstants
@Builder(access = AccessLevel.PRIVATE)
public class TradeValue {

  private String description;
  private ProductType productType;
  private TradeDetails tradeDetails;
  private AllocationTradeDetails allocationTradeDetails;
  private ClientMetrics clientMetrics;
  private OnboardingDetails onboardingDetails;
  private List<ExternalIdentifier> externalIdentifiers;
  private List<CustomTradeField> customFields;

  public static TradeValue calibrationTradeValue(ProductType type, TradeDetails details) {
    return TradeValue.builder().productType(type).tradeDetails(details).build();
  }

  public static TradeValue tradeValue(
      ProductType type,
      TradeDetails details,
      String description,
      ClientMetrics clientMetrics,
      OnboardingDetails onboardingDetails,
      List<ExternalIdentifier> externalIdentifiers,
      List<CustomTradeField> customFields) {
    return TradeValue.builder()
        .description(description)
        .productType(type)
        .tradeDetails(details)
        .clientMetrics(clientMetrics)
        .onboardingDetails(onboardingDetails)
        .externalIdentifiers(externalIdentifiers)
        .customFields(customFields)
        .build();
  }

  public static TradeValue allocationTradeValue(
      AllocationTradeDetails details,
      String description,
      OnboardingDetails onboardingDetails,
      List<ExternalIdentifier> externalIdentifiers,
      List<CustomTradeField> customFields) {
    return TradeValue.builder()
        .description(description)
        .allocationTradeDetails(details)
        .onboardingDetails(onboardingDetails)
        .externalIdentifiers(externalIdentifiers)
        .customFields(customFields)
        .build();
  }
}
