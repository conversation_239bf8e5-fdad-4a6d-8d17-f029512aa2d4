package com.solum.xplain.core.audit.value;

import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class AuditLogView {

  private String comment;
  private VersionDiffs diff;
  private AuditUser createdBy;
  private LocalDateTime createdAt;
}
