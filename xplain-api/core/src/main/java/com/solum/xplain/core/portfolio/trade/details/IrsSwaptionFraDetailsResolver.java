package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.JOIN_ON;
import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.resolveLegIndices;
import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;

import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class IrsSwaptionFraDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.IRS, CoreProductType.SWAPTION, CoreProductType.FRA);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    return resolveLegIndices(tradeDetails)
        .map(TradeLegDetails::getIndex)
        .map(ConventionalCurveConfigurations::lookupByIndex)
        .flatMap(Optional::stream)
        .map(CurveConvention::getName)
        .collect(Collectors.joining(JOIN_ON));
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
