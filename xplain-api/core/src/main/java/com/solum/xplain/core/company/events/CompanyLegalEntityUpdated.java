package com.solum.xplain.core.company.events;

import com.solum.xplain.core.common.EntityEvent;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyLegalEntityUpdated extends EntityEvent {
  private final String companyId;

  public CompanyLegalEntityUpdated(String companyId, String entityId) {
    super(entityId);
    this.companyId = companyId;
  }

  public static CompanyLegalEntityUpdated newOf(String companyId, String entityId) {
    return new CompanyLegalEntityUpdated(companyId, entityId);
  }

  public String getCompanyId() {
    return companyId;
  }
}
