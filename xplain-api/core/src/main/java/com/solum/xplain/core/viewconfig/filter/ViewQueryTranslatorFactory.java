package com.solum.xplain.core.viewconfig.filter;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Factory to obtain view query translators which can translate queries for a data type which use
 * field names from a front end perspective, to the actual data source paths on the View using
 * information from a {@link com.solum.xplain.core.viewconfig.value.PaletteView PaletteView}. If
 * there is no Palette available for the given class, a no-op translator is returned.
 */
@Service
@RequiredArgsConstructor
public class ViewQueryTranslatorFactory {
  private final PaletteService paletteService;
  private final Cache<Class<?>, ViewQueryTranslator> translatorCache =
      Caffeine.newBuilder().build();

  public ViewQueryTranslator getTranslator(Class<?> viewClass) {
    return translatorCache.get(viewClass, this::createTranslator);
  }

  private <T> ViewQueryTranslator createTranslator(Class<? extends T> viewClass) {
    return paletteService
        .findPaletteView(viewClass)
        .<ViewQueryTranslator>map(PaletteViewQueryTranslator::new)
        .orElse(ViewQueryTranslator.NOOP_TRANSLATOR);
  }
}
