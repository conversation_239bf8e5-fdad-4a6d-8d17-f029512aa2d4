package com.solum.xplain.core.lock;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.datagrid.ClusterLock;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

@Slf4j
@Profile("!mockMvc")
@Component
@AllArgsConstructor
public class DefaultLockingInterceptor implements LockingInterceptor {

  private final XplainLockResolver lockResolver;
  private final LockingSupport lockingSupport;
  private final ObjectMapper objectMapper;

  private final ThreadLocal<List<ClusterLock>> closedLocks =
      ThreadLocal.withInitial(ArrayList::new);

  @Override
  public boolean preHandle(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull Object handler) {
    return method(handler).map(method -> processLocks(response, method)).orElse(true);
  }

  private Optional<Method> method(Object handler) {
    return Optional.of(handler)
        .filter(HandlerMethod.class::isInstance)
        .map(HandlerMethod.class::cast)
        .map(HandlerMethod::getMethod);
  }

  private boolean processLocks(HttpServletResponse response, Method method) {
    var xplainLocks = lockResolver.resolveLocks(method);
    return xplainLocks.stream()
        .map(l -> withLock(l, response))
        .reduce(Boolean.TRUE, Boolean::logicalAnd);
  }

  private boolean withLock(XplainLock lock, HttpServletResponse response) {
    return lockingSupport.tryLock(lock).fold(err -> writeError(err, response), this::storeLock);
  }

  private boolean storeLock(ClusterLock lock) {
    closedLocks.get().add(lock);
    return true;
  }

  private boolean writeError(ErrorItem error, HttpServletResponse response) {
    try {
      response.setStatus(403);
      response.getWriter().write(objectMapper.writeValueAsString(error));
      response.getWriter().flush();
      return false;
    } catch (Exception e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void afterCompletion(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull Object handler,
      @Nullable Exception ex) {
    releaseClosedLocks();
  }

  private void releaseClosedLocks() {
    closedLocks.get().forEach(lockingSupport::releaseLock);
    closedLocks.remove();
  }
}
