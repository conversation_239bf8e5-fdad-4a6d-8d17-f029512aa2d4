package com.solum.xplain.core.company.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueCompanyEntityExternalIdValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface UniqueCompanyEntityExternalId {
  String message() default
      "{com.solum.xplain.api.company.validation.UniqueCompanyEntityExternalId.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
