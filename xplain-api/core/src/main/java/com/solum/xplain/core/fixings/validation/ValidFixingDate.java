package com.solum.xplain.core.fixings.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidFixingDateValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidFixingDate {

  String MESSAGE_KEY = "com.solum.xplain.api.fixings.validation.ValidFixingDate.message";

  String message() default "{" + MESSAGE_KEY + "}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
