package com.solum.xplain.core.market.value;

import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.ALL_ASSET_CLASS_GROUPS;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.ALL_INSTRUMENTS;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.market.validation.UniqueMarketDataKeyProvider;
import com.solum.xplain.core.market.validation.ValidClassifier;
import com.solum.xplain.core.market.validation.ValidMarketDataKeyInstrument;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
@ValidMarketDataKeyInstrument
public class MarketDataKeyUpdateForm {

  @NotEmpty
  @Schema(description = "Display name")
  private String name;

  @NotNull
  @ValidClassifier(classifierId = ALL_ASSET_CLASS_GROUPS)
  @Schema(description = "Asset group, permissible values from classifier " + ALL_ASSET_CLASS_GROUPS)
  private String assetGroup;

  @NotNull
  @ValidClassifier(classifierId = ALL_INSTRUMENTS)
  @Schema(description = "Instrument type, permissible values from classifier " + ALL_INSTRUMENTS)
  private String instrumentType;

  @Valid
  @UniqueMarketDataKeyProvider
  @Schema(description = "Provider mappings")
  private List<MarketDataProviderTickerForm> providerTickers;

  @Valid
  @NotNull
  @Schema(description = "Version form")
  private NewVersionFormV2 versionForm;
}
