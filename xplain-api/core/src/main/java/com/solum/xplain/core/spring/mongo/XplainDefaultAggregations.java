package com.solum.xplain.core.spring.mongo;

import com.solum.xplain.shared.spring.mongo.AggregationParameters;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
@AggregationParameters(XplainAggregationParameterResolver.class)
public @interface XplainDefaultAggregations {}
