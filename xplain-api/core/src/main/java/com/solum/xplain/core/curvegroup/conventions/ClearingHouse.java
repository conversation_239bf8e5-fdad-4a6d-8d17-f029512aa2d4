package com.solum.xplain.core.curvegroup.conventions;

import com.solum.xplain.core.portfolio.value.CounterpartyType;
import java.util.Optional;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;

@Getter
@RequiredArgsConstructor
public enum ClearingHouse {
  NONE(""),
  LCH("-LCH");

  private final String suffix;

  public static ClearingHouse resolveFromCounterparty(@Nullable CounterpartyType type) {
    return Optional.ofNullable(type)
        .filter(CounterpartyType.CLEARED::equals)
        .map(c -> ClearingHouse.LCH)
        .orElse(ClearingHouse.NONE);
  }
}
