package com.solum.xplain.core.mdvalue.value;

import com.solum.xplain.core.market.value.MarketDataProviderTickerView;
import com.solum.xplain.core.mdvalue.entity.HasProviderTicker;
import java.math.BigDecimal;
import java.math.MathContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class MarketDataValueFlatView implements HasProviderTicker {
  private String id;

  private String ticker;
  private LocalDate date;
  private String provider;
  private ValueBidAskType bidAsk;

  private BigDecimal value;

  private String comment;
  private LocalDateTime modifiedAt;
  private String modifiedBy;

  public static MarketDataValueFlatView fromProvider(
      MarketDataProviderTickerView ticker, BigDecimal value, ValueBidAskType bidAsk) {
    var view = new MarketDataValueFlatView();
    view.setValue(value);
    view.setProvider(ticker.getCode());
    view.setTicker(ticker.getTicker());
    view.setBidAsk(bidAsk);
    return view;
  }

  public MarketDataValueFlatView applyFactor(BigDecimal factor) {
    var view = new MarketDataValueFlatView();
    view.setId(id);
    view.setProvider(provider);
    view.setTicker(ticker);
    view.setValue(value.divide(factor, MathContext.DECIMAL64));
    view.setDate(date);
    view.setBidAsk(bidAsk);
    return view;
  }
}
