package com.solum.xplain.core.company.mapper;

import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.form.CompanyCreateForm;
import com.solum.xplain.core.company.form.CompanyLegalEntityCreateForm;
import com.solum.xplain.core.company.form.CompanyLegalEntityUpdateForm;
import com.solum.xplain.core.company.form.CompanyUpdateForm;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.bson.types.ObjectId;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

@Mapper(uses = ObjectIdMapper.class)
public interface CompanyMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "numberOfPortfolios", ignore = true)
  @Mapping(target = "numberOfEntities", ignore = true)
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  Company fromForm(CompanyCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "numberOfPortfolios", ignore = true)
  @Mapping(target = "numberOfEntities", ignore = true)
  @Mapping(target = "externalCompanyId", ignore = true)
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  Company fromForm(CompanyUpdateForm form, @MappingTarget Company company);

  @Mapping(target = "archived", constant = "true")
  Company copyArchived(Company company);

  Company copy(Company entity);

  @Mapping(target = "creatorName", source = "entity.createdBy.name")
  @Mapping(target = "creatorId", source = "entity.createdBy.userId")
  @Mapping(target = "modifiedBy", source = "entity.modifiedBy.name")
  CompanyLegalEntityView toEntityView(CompanyLegalEntity entity, List<String> teamNames);

  @Mapping(target = "creatorName", source = "entity.createdBy.name")
  @Mapping(target = "creatorId", source = "entity.createdBy.userId")
  @Mapping(target = "modifiedBy", source = "entity.modifiedBy.name")
  @Mapping(target = "teamNames", source = "entity", qualifiedByName = "teamNamesLookup")
  CompanyLegalEntityView toEntityView(
      CompanyLegalEntity entity, @Context Map<String, String> teamNamesLookup);

  @Named("teamNamesLookup")
  default List<String> teamNamesLookup(
      CompanyLegalEntity entity, @Context Map<String, String> teamNamesLookup) {
    return emptyIfNull(entity.getTeamIds()).stream()
        .map(ObjectId::toHexString)
        .map(teamNamesLookup::get)
        .filter(Objects::nonNull)
        .toList();
  }

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "companyId", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "vdkPrefix", ignore = true)
  @Mapping(target = "externalCompanyId", ignore = true)
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  CompanyLegalEntity fromForm(
      CompanyLegalEntityCreateForm form, @MappingTarget CompanyLegalEntity entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "companyId", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "externalId", ignore = true)
  @Mapping(target = "vdkPrefix", ignore = true)
  @Mapping(target = "externalCompanyId", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "allowAllTeams", source = "allowedTeamsForm.allowAll")
  @Mapping(target = "teamIds", source = "allowedTeamsForm.teamIds")
  CompanyLegalEntity fromForm(
      CompanyLegalEntityUpdateForm form, @MappingTarget CompanyLegalEntity entity);

  CompanyLegalEntity copy(CompanyLegalEntity entity);

  @Mapping(target = "archived", constant = "true")
  CompanyLegalEntity copyArchived(CompanyLegalEntity entity);
}
