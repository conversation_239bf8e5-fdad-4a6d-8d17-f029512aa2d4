package com.solum.xplain.core.ipv.group.validation;

import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class UniqueIpvDataGroupNameValidator
    implements ConstraintValidator<UniqueIpvDataGroupName, String> {

  private final IpvDataGroupRepository repository;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  private boolean includeSelfId;

  public UniqueIpvDataGroupNameValidator(
      IpvDataGroupRepository repository, RequestPathVariablesSupport requestPathVariablesSupport) {
    this.repository = repository;
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  @Override
  public void initialize(UniqueIpvDataGroupName constraintAnnotation) {
    this.includeSelfId = constraintAnnotation.includeSelfId();
  }

  @Override
  public boolean isValid(String name, ConstraintValidatorContext context) {
    String groupId = requestPathVariablesSupport.getPathVariable("groupId");
    boolean isCreate = (groupId == null);

    if (!isCreate && isEmpty(name)) {
      return true;
    }

    if (isNotEmpty(name)) {
      String id = includeSelfId ? requestPathVariablesSupport.getPathVariable("groupId") : null;
      return !repository.existsByName(name, id);
    }
    return false;
  }
}
