package com.solum.xplain.core.providers.value;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.providers.validation.UniqueDataProvider;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@UniqueDataProvider
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DataProviderCreateForm extends DataProviderUpdateForm {

  @NotEmpty @ValidIdentifier private String externalId;
}
