package com.solum.xplain.core.portfolio.calendars;

import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/portfolio/calendar")
public class TradeCalendarController {

  @Operation(summary = "Gets CDS trade calendar")
  @GetMapping("/cds-trade")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public HolidayCalendarId getCdsTradeCalendar(@Valid CdsTradeCalendarForm search) {
    return TradeCalendarUtils.getCdsTradeCalendar(search.getCurrency());
  }

  @Operation(summary = "Gets FX trade calendar")
  @GetMapping("/fx-trade")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public HolidayCalendarId getFxTradeCalendar(@Valid FxTradeCalendarForm form) {
    return TradeCalendarUtils.getFxTradeCalendar(form.getDomesticCcy(), form.getForeignCcy());
  }

  @Operation(summary = "Gets Cap Floor trade calendar")
  @GetMapping("/cap-floor-trade")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public HolidayCalendarId getCapFloorTradeCalendar(@Valid CapFloorTradeCalendarForm form) {
    return TradeCalendarUtils.getCapFloorTradeCalendar(form.getIborIndex(), form.getIsOffshore());
  }

  @Operation(summary = "Gets Swap trade calendar")
  @GetMapping("/swap-trade")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public HolidayCalendarId getSwapTradeCalendar(@Valid SwapTradeCalendarForm form) {
    return TradeCalendarUtils.getSwapTradeCalendar(form.getLeg1form(), form.getLeg2form());
  }

  @Operation(summary = "Gets calendar by currency")
  @GetMapping("/ccy-calendar")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public HolidayCalendarId getSingleCurrencyCalendar(@Valid SingleCurrencyCalendarForm form) {
    return TradeCalendarUtils.getCalendarByCurrency(form.getCurrency());
  }
}
