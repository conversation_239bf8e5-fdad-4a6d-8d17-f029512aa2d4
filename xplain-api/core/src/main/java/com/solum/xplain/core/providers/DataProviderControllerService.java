package com.solum.xplain.core.providers;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.providers.DataProvider.INTERNAL_PROVIDER_CODES;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.providers.csv.DataProviderCsvMapper;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.providers.value.DataProviderCreateForm;
import com.solum.xplain.core.providers.value.DataProviderUpdateForm;
import com.solum.xplain.core.providers.value.DataProviderView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class DataProviderControllerService {

  private final DataProviderRepository repository;

  public Either<ErrorItem, EntityId> insert(DataProviderCreateForm form) {
    return repository.insert(form);
  }

  public Either<ErrorItem, EntityId> update(String id, DataProviderUpdateForm form) {
    return repository.update(id, form);
  }

  public Either<ErrorItem, EntityId> archive(String id) {
    return repository.archive(id);
  }

  @Transactional
  public List<EntityId> archiveAll(TableFilter tableFilter) {
    return repository.archiveAll(tableFilter);
  }

  public List<DataProviderView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    return repository.dataProvidersViewList(tableFilter, sort, archived);
  }

  public List<DataProviderView> listByType(DataProviderType type) {
    return repository.dataProvidersViewListForType(type);
  }

  public FileResponseEntity getMarketDataProviders(
      List<String> selectedColumns, TableFilter tableFilter, Sort sort, LocalDate stateDate) {
    DataProviderCsvMapper mapper = new DataProviderCsvMapper(selectedColumns);
    var providers =
        repository.dataProvidersViewList(tableFilter, sort, false).stream()
            .filter(v -> !INTERNAL_PROVIDER_CODES.contains(v.getExternalId()))
            .map(mapper::toCsvRow)
            .toList();

    var csvFile = new CsvOutputFile(mapper.header(), providers);
    var csvFileName = nameWithTimeStamp("DataProviders", stateDate);
    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
  }
}
