package com.solum.xplain.core.curvegroup.volatility.entity;

import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.volatility.classifier.SkewType;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;

@Data
@FieldNameConstants
public class VolatilitySurfaceSkew {

  private String surfaceSkewId;
  private BigDecimal skewValue;

  public static VolatilitySurfaceSkew of(BigDecimal skewValue) {
    VolatilitySurfaceSkew volatilitySurfaceStrike = new VolatilitySurfaceSkew();
    volatilitySurfaceStrike.surfaceSkewId = new ObjectId().toString();
    volatilitySurfaceStrike.skewValue = skewValue;
    return volatilitySurfaceStrike;
  }

  public List<InstrumentDefinition> allInstruments(
      String name,
      String index,
      String ccy,
      SkewType skewType,
      String underlying,
      List<VolatilitySurfaceNode> nodes) {
    return ofNullable(nodes).stream()
        .flatMap(Collection::stream)
        .map(v -> v.skewInstrument(name, index, ccy, skewValue, skewType, underlying))
        .toList();
  }

  public List<VolatilitySurfaceNode> filteredNodes(
      String name,
      ValidNodesFilter filter,
      String index,
      String ccy,
      SkewType skewType,
      String underlying,
      List<VolatilitySurfaceNode> nodes) {
    return ofNullable(nodes).stream()
        .flatMap(Collection::stream)
        .map(
            v ->
                NodeInstrumentWrapper.of(
                    v, v.skewInstrument(name, index, ccy, skewValue, skewType, underlying)))
        .collect(Collectors.collectingAndThen(toList(), filter::filterNodes));
  }

  @EqualsAndHashCode.Include(replaces = "skewValue")
  private BigDecimal normalisedSkewValue() {
    return skewValue == null ? null : skewValue.stripTrailingZeros();
  }
}
