package com.solum.xplain.core.common.validation;

import static com.google.common.collect.ImmutableSet.toImmutableSet;

import com.google.common.collect.ImmutableSet;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;

public class ValidCollectionSetValidator
    implements ConstraintValidator<ValidCollectionStringSet, List<String>> {
  private Set<String> valueList;

  @Override
  public void initialize(ValidCollectionStringSet constraint) {
    valueList =
        Arrays.stream(constraint.value())
            .map(s -> resolveValues(s, constraint.supplierArgument()))
            .flatMap(Collection::stream)
            .collect(toImmutableSet());
  }

  private Set<String> resolveValues(
      Class<? extends Supplier<Collection<String>>> supplier, String argument) {
    try {
      if (StringUtils.isEmpty(argument)) {
        return ImmutableSet.copyOf(supplier.getConstructor().newInstance().get());
      } else {
        return ImmutableSet.copyOf(
            supplier.getConstructor(String.class).newInstance(argument).get());
      }
    } catch (NoSuchMethodException
        | InvocationTargetException
        | InstantiationException
        | IllegalAccessException e) {
      throw new IllegalStateException(e.getMessage(), e);
    }
  }

  @Override
  public boolean isValid(List<String> values, ConstraintValidatorContext context) {
    if (values == null) {
      return true;
    } else {
      return valueList.containsAll(values);
    }
  }
}
