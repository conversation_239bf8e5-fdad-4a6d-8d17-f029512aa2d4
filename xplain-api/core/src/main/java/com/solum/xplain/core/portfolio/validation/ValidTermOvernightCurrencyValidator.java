package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidTermOvernightCurrencyValidator
    implements ConstraintValidator<ValidTermOvernightIndexCurrency, SwapLegForm> {

  private static final String VALID_CURRENCY = "USD";
  private static final String ERROR_MESSAGE = "Term Overnight legs must be in USD currency";

  @Override
  public boolean isValid(SwapLegForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    boolean isTermOvernight = CalculationType.TERM_OVERNIGHT.equals(form.getCalculationType());
    boolean isValidCurrency = VALID_CURRENCY.equals(form.getNotionalCurrency());

    if (isTermOvernight && !isValidCurrency) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(ERROR_MESSAGE)
          .addPropertyNode("notionalCurrency")
          .addConstraintViolation();
      return false;
    }
    return true;
  }
}
