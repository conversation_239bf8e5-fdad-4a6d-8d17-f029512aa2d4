package com.solum.xplain.core.classifiers;

import static java.util.Comparator.comparing;

import com.google.common.collect.ImmutableCollection;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.opengamma.strata.collect.named.Named;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ClassifierUtils {

  static <T extends Named> Stream<Classifier> valuesStream(
      ExtendedEnum<T> extendedEnum, Function<T, Classifier> mapper) {
    ImmutableCollection<T> values = extendedEnum.lookupAllNormalized().values();
    return valuesStream(mapper, values);
  }

  static <T extends Named> Stream<Classifier> valuesStream(
      Function<T, Classifier> mapper, Collection<T> values) {
    return values.stream().map(mapper).sorted(comparing(Classifier::getId));
  }

  public static <T extends Named> Stream<Classifier> valuesStream(Collection<T> values) {
    return valuesStream(v -> new Classifier(v.getName()), values);
  }

  public static <T extends Named> Stream<Classifier> valuesStream(ExtendedEnum<T> extendedEnum) {
    return valuesStream(extendedEnum, v -> new Classifier(v.getName()));
  }

  static <T extends Named> List<Classifier> values(
      ExtendedEnum<T> extendedEnum, Function<T, Classifier> mapper) {
    return valuesStream(extendedEnum, mapper).toList();
  }

  public static <T extends Enum<T>> Classifier enumClassifier(
      String name, Class<T> type, Function<T, String> idFn) {
    return enumClassifier(name, type, idFn, v -> null);
  }

  public static <T extends Enum<T>> Classifier enumClassifier(
      String name, Class<T> type, Function<T, String> idFn, Function<T, String> nameFn) {
    return new Classifier(
        name,
        null,
        Stream.of(type.getEnumConstants())
            .map(v -> new Classifier(idFn.apply(v), nameFn.apply(v)))
            .toList(),
        type);
  }

  public static Classifier sortById(Classifier classifier) {
    return sortBy(classifier, Classifier::getId);
  }

  public static Classifier sortByName(Classifier classifier) {
    return sortBy(classifier, Classifier::getName);
  }

  private static Classifier sortBy(Classifier classifier, Function<Classifier, String> sortField) {
    return new Classifier(
        classifier.getId(),
        classifier.getName(),
        classifier.getValues().stream().sorted(comparing(sortField)).toList(),
        classifier.getSourceType());
  }
}
