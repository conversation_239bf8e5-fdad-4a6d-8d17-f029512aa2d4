package com.solum.xplain.core.curvegroup.ratefx.validation;

import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class ValidFxRateCurrenciesValidator
    implements ConstraintValidator<ValidFxRateCurrencies, CurveGroupFxRatesNodeForm> {
  public static List<Currency> validForeignCurrencies(Currency domesticCcy) {
    return EXPLICIT_CURRENCIES.subList(
        EXPLICIT_CURRENCIES.indexOf(domesticCcy) + 1, EXPLICIT_CURRENCIES.size());
  }

  @Override
  public boolean isValid(CurveGroupFxRatesNodeForm form, ConstraintValidatorContext context) {

    if (!StringUtils.isEmpty(form.getDomesticCurrency())
        && !StringUtils.isEmpty(form.getForeignCurrency())) {

      if (form.getDomesticCurrency().length() == 3 && form.getForeignCurrency().length() == 3) {
        return validForeignCurrencies(Currency.of(form.getDomesticCurrency()))
            .contains(Currency.of(form.getForeignCurrency()));
      }
      return false;
    }
    return true;
  }
}
