package com.solum.xplain.core.datavalue.csv;

import lombok.Data;

@Data(staticConstructor = "newOf")
public class DataUpdateSummary {

  private final int newEntriesCount;
  private final int replacedEntriesCount;
  private final int archivedEntriesCount;

  public DataUpdateSummary sum(DataUpdateSummary summary) {
    var totalNewEntries = newEntriesCount + summary.newEntriesCount;
    var totalReplacedEntries = replacedEntriesCount + summary.replacedEntriesCount;
    var totalArchivedEntries = archivedEntriesCount + summary.archivedEntriesCount;
    return DataUpdateSummary.newOf(totalNewEntries, totalReplacedEntries, totalArchivedEntries);
  }
}
