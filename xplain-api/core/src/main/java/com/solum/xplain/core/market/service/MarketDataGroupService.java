package com.solum.xplain.core.market.service;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.value.UserEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.ErrorItem.ListOfErrors;
import com.solum.xplain.core.market.filter.MarketDataGroupFilter;
import com.solum.xplain.core.market.mapping.MarketDataGroupMapper;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import com.solum.xplain.core.market.value.MarketDataGroupCountedView;
import com.solum.xplain.core.market.value.MarketDataGroupForm;
import com.solum.xplain.core.market.value.MarketDataGroupView;
import com.solum.xplain.core.mdvalue.MarketDataValueRepository;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class MarketDataGroupService {
  private final AuthenticationContext authenticationContext;
  private final MarketDataGroupRepository marketDataGroupRepository;
  private final MarketDataValueRepository marketDataValueRepository;
  private final CurveGroupRepository curveGroupRepository;
  private final MarketDataGroupMapper groupMapper;

  public MarketDataGroupService(
      AuthenticationContext authenticationContext,
      MarketDataGroupRepository marketDataGroupRepository,
      MarketDataValueRepository marketDataValueRepository,
      CurveGroupRepository curveGroupRepository,
      MarketDataGroupMapper groupMapper) {
    this.authenticationContext = authenticationContext;
    this.marketDataGroupRepository = marketDataGroupRepository;
    this.marketDataValueRepository = marketDataValueRepository;
    this.curveGroupRepository = curveGroupRepository;
    this.groupMapper = groupMapper;
  }

  public ScrollableEntry<MarketDataGroupCountedView> getAll(
      ScrollRequest scrollRequest,
      Authentication auth,
      MarketDataGroupFilter filter,
      TableFilter tableFilter,
      BitemporalDate stateDate) {
    return authenticationContext
        .userEither(auth)
        .map(
            user ->
                marketDataGroupRepository
                    .groupScrollable(user, scrollRequest, filter, tableFilter)
                    .map(v -> toCountedView(v, stateDate)))
        .getOrElse(ScrollableEntry.empty());
  }

  public List<EntityNameView> list(Authentication auth, List<String> companyIds) {
    return authenticationContext
        .userEither(auth)
        .map(user -> marketDataGroupRepository.groupNameList(user, companyIds))
        .getOrElse(List.of());
  }

  public Either<ErrorItem, EntityId> create(Authentication auth, MarketDataGroupForm newForm) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u ->
                BooleanUtils.isTrue(newForm.getAllowedTeamsForm().getAllowAll())
                    ? Either.right(u)
                    : u.allowedTeams(newForm.getAllowedTeamsForm().getTeamIds()))
        .flatMap(u -> marketDataGroupRepository.insert(newForm));
  }

  public Either<ErrorItem, MarketDataGroupCountedView> get(
      Authentication auth, String marketDataGroupId, BitemporalDate stateDate) {
    return authenticationContext
        .userEither(auth)
        .flatMap(user -> get(user, marketDataGroupId))
        .map(v -> toCountedView(v, stateDate));
  }

  private MarketDataGroupCountedView toCountedView(
      MarketDataGroupView view, BitemporalDate stateDate) {
    return groupMapper.toCountedView(
        view, marketDataValueRepository.getValuesCount(view.getId(), stateDate));
  }

  public Either<ErrorItem, MarketDataGroupView> get(
      XplainPrincipal user, String marketDataGroupId) {
    return marketDataGroupRepository.userDataGroup(user, marketDataGroupId);
  }

  public Either<ErrorItem, EntityId> archiveMarketDataGroup(
      Authentication auth, String marketDataGroupId) {
    return executeEdit(
        auth, marketDataGroupId, () -> marketDataGroupRepository.archive(marketDataGroupId));
  }

  public Either<ErrorItem, EntityId> updateMarketDataGroup(
      Authentication auth, String marketDataGroupId, MarketDataGroupForm updateForm) {
    return executeEdit(
        auth,
        marketDataGroupId,
        () ->
            authenticationContext
                .userEither(auth)
                .flatMap(u -> marketDataGroupRepository.update(updateForm, marketDataGroupId)));
  }

  private Either<ErrorItem, List<CurveGroup>> getConnectedCurveGroups(
      Authentication auth, String marketDataGroupId) {
    return authenticationContext
        .userEither(auth)
        .map(user -> curveGroupRepository.marketDataGroupCalibratedGroups(marketDataGroupId));
  }

  public Either<ErrorItem, UserEntity<MarketDataGroupView>> userEntity(
      Authentication auth, String groupId) {
    return authenticationContext
        .userEither(auth)
        .flatMap(u -> get(u, groupId).map(v -> UserEntity.marketDataGroup(u, v)));
  }

  public void clearConnectedCalibrations(Authentication auth, String marketDataGroupId) {
    getConnectedCurveGroups(auth, marketDataGroupId)
        .forEach(
            list ->
                list.forEach(group -> curveGroupRepository.clearCalibrationResults(group.getId())));
  }

  private <T> Either<ErrorItem, T> executeEdit(
      Authentication auth,
      String marketDataGroupId,
      Supplier<Either<ErrorItem, T>> functionToExecute) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u ->
                this.get(u, marketDataGroupId)
                    .flatMap(it -> functionToExecute.get())
                    .map(
                        it -> {
                          clearConnectedCalibrations(auth, marketDataGroupId);
                          return it;
                        }));
  }

  public Either<ErrorItem, EntityId> markGroupUpdated(UserEntity<MarketDataGroupView> u) {
    return marketDataGroupRepository.groupUpdated(u.getUser(), u.getView().getId());
  }

  public <T> Either<List<ErrorItem>, T> executeEditWithListOfErrors(
      Authentication auth,
      String marketDataGroupId,
      Function<UserEntity<MarketDataGroupView>, Either<List<ErrorItem>, T>> functionToExecute) {
    return executeEditWith(auth, marketDataGroupId, functionToExecute, ListOfErrors::from);
  }

  private <A, T> Either<A, T> executeEditWith(
      Authentication auth,
      String marketDataGroupId,
      Function<UserEntity<MarketDataGroupView>, Either<A, T>> functionToExecute,
      Function<ErrorItem, A> errorMapper) {
    return authenticationContext
        .userEither(auth)
        .leftMap(errorMapper)
        .flatMap(
            u ->
                this.get(u, marketDataGroupId)
                    .leftMap(errorMapper)
                    .flatMap(it -> functionToExecute.apply(UserEntity.marketDataGroup(u, it)))
                    .map(
                        it -> {
                          clearConnectedCalibrations(auth, marketDataGroupId);
                          return it;
                        }));
  }
}
