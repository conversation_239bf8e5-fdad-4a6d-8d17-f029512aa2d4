package com.solum.xplain.core.classifiers.csv;

import com.opengamma.strata.product.fra.type.ImmutableFraConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class ForwardRateAgreementConventionCsvMapper extends CsvMapper<ImmutableFraConvention> {
  private static final List<CsvColumn<ImmutableFraConvention>> COLUMNS =
      List.of(
          CsvColumn.textObject("name", ImmutableFraConvention::getName),
          CsvColumn.textObject("index", ImmutableFraConvention::getIndex),
          CsvColumn.textObject("currency", ImmutableFraConvention::getCurrency),
          CsvColumn.textObject("dayCount", ImmutableFraConvention::getDayCount),
          CsvColumn.textObject("spotDateOffset", ImmutableFraConvention::getSpotDateOffset),
          CsvColumn.textObject(
              "businessDayAdjustment", ImmutableFraConvention::getBusinessDayAdjustment),
          CsvColumn.textObject("fixingDateOffset", ImmutableFraConvention::getFixingDateOffset),
          CsvColumn.textObject("paymentDateOffset", ImmutableFraConvention::getPaymentDateOffset),
          CsvColumn.textObject("discountingMethod", ImmutableFraConvention::getDiscounting));

  protected ForwardRateAgreementConventionCsvMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource fraConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new ForwardRateAgreementConventionCsvMapper(), ImmutableFraConvention.class)
        .export();
  }
}
