package com.solum.xplain.core.portfolio.trade;

import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCdsCurveName;
import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCreditIndexCurveName;
import static com.solum.xplain.core.curvegroup.curvecredit.CreditCurveNameUtils.resolveCreditIndexTrancheCurveName;
import static com.solum.xplain.core.portfolio.CoreProductType.CDS;
import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX;
import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX_TRANCHE;

import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSeniority;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/** Resolves the curve name for a credit trade. */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CreditTradeDetailsCurveNameResolver {

  public static String curveName(
      String currencyCode,
      ProductType productType,
      String reference,
      CreditSeniority seniority,
      CreditDocClause docClause,
      String creditIndexTranche) {
    if (productType == CDS) {
      return resolveCdsCurveName(reference, currencyCode, seniority.name(), docClause.name());
    } else if (productType == CREDIT_INDEX) {
      return resolveCreditIndexCurveName(reference, currencyCode);
    } else if (productType == CREDIT_INDEX_TRANCHE) {
      return resolveCreditIndexTrancheCurveName(reference, creditIndexTranche, currencyCode);
    } else {
      throw new IllegalArgumentException(
          String.format("Unhandled credit trade type %s", productType));
    }
  }
}
