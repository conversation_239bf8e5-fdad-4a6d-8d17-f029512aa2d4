package com.solum.xplain.core.settings.mappers;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.settings.entity.IpvTaskDefaultTeams;
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams;
import com.solum.xplain.core.settings.form.TaskDefaultTeamsForm;
import com.solum.xplain.core.settings.value.TaskDefaultTeamsView;
import com.solum.xplain.core.teams.TeamNameMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(uses = {ObjectIdMapper.class, TeamNameMapper.class, AuditUserMapper.class})
public interface TaskDefaultTeamsMapper {

  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  MdTaskDefaultTeams from(TaskDefaultTeamsForm form, @MappingTarget MdTaskDefaultTeams settings);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  MdTaskDefaultTeams copy(MdTaskDefaultTeams settings);

  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  IpvTaskDefaultTeams from(TaskDefaultTeamsForm form, @MappingTarget IpvTaskDefaultTeams settings);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  IpvTaskDefaultTeams copy(IpvTaskDefaultTeams settings);

  TaskDefaultTeamsView toView(MdTaskDefaultTeams taskDefaultTeams);

  TaskDefaultTeamsView toView(IpvTaskDefaultTeams taskDefaultTeams);
}
