package com.solum.xplain.core.curvegroup.curvecredit.xva;

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentGroup.ALL_CREDIT;
import static com.solum.xplain.core.instrument.InstrumentPriceGroup.CURVE_PRICE;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.InstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentPriceGroup;
import com.solum.xplain.core.instrument.InstrumentType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum CoreXvaInstrumentType implements InstrumentType {

  /*  Duplicates XvaInstrumentType.FUNDING.
      Should not be exposed to UI.
      Only used until Funding Nodes are moved to XVA module
  */
  FUNDING("Funding Spread", CoreXvaAssetClass.FUNDING, ALL_CREDIT, 6, CURVE_PRICE);

  private final String label;
  private final AssetClass assetClass;
  private final InstrumentGroup instrumentGroup;
  private final int sortOrder;

  private final InstrumentPriceGroup priceGroup;

  @Override
  public String getLabel() {
    return label;
  }

  @Override
  public AssetClass getAssetClass() {
    return assetClass;
  }

  @Override
  public InstrumentGroup getInstrumentGroup() {
    return instrumentGroup;
  }

  @Override
  public int getSortOrder() {
    return sortOrder;
  }

  @Override
  public InstrumentPriceGroup getPriceGroup() {
    return priceGroup;
  }
}
