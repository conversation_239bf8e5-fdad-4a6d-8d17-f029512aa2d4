package com.solum.xplain.core.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import org.bson.types.ObjectId;

public class ValidObjectIdsValidator implements ConstraintValidator<ValidObjectId, List<String>> {

  @Override
  public boolean isValid(List<String> objectIds, ConstraintValidatorContext context) {
    return Stream.ofNullable(objectIds)
        .flatMap(Collection::stream)
        .allMatch(id -> Objects.nonNull(id) && ObjectId.isValid(id));
  }
}
