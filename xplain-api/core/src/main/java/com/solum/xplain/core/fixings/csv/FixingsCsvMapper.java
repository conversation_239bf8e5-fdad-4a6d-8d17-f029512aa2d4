package com.solum.xplain.core.fixings.csv;

import static com.solum.xplain.core.fixings.csv.FixingsCsvLoader.DATE_FIELD;
import static com.solum.xplain.core.fixings.csv.FixingsCsvLoader.PUBLICATION_DATE_FIELD;
import static com.solum.xplain.core.fixings.csv.FixingsCsvLoader.REFERENCE_FIELD;
import static com.solum.xplain.core.fixings.csv.FixingsCsvLoader.VALUE_FIELD;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.fixings.value.FixingView;
import java.util.List;

public class FixingsCsvMapper extends CsvMapper<FixingView> {

  private static final List<CsvColumn<FixingView>> COLUMNS =
      List.of(
          CsvColumn.date(FixingView.Fields.date, DATE_FIELD, FixingView::getDate),
          CsvColumn.date(
              FixingView.Fields.publicationDate,
              PUBLICATION_DATE_FIELD,
              FixingView::getPublicationDate),
          CsvColumn.text(FixingView.Fields.reference, REFERENCE_FIELD, FixingView::getReference),
          CsvColumn.bigDecimal(FixingView.Fields.value, VALUE_FIELD, FixingView::getValue));

  public FixingsCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
