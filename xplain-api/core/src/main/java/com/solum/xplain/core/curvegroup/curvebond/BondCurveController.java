package com.solum.xplain.core.curvegroup.curvebond;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_BOND_CURVE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_BOND_CURVE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.Filtered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveSearch;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.lock.RequireLock;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@AllArgsConstructor
@RestController
@RequestMapping("/curve-group/{groupId}/bond-curves")
public class BondCurveController {

  private final BondCurveService service;
  private final BondCurveExportService exportService;
  private final BondCurveImportService importService;

  @Operation(summary = "Creates BOND curve")
  @CommonErrors
  @PostMapping()
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> createCurve(
      @PathVariable("groupId") String groupId, @Valid @RequestBody BondCurveForm form) {
    return eitherErrorItemResponse(service.createCurve(groupId, form));
  }

  @Operation(summary = "Updates BOND curve")
  @PutMapping("/{curveId}/{version}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody BondCurveUpdateForm form) {
    return eitherErrorItemResponse(service.updateCurve(groupId, curveId, version, form));
  }

  @Operation(summary = "Archives (sets status to ARCHIVED) BOND curve")
  @PutMapping("/{curveId}/{version}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> archiveCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody ArchiveEntityForm form) {
    return eitherErrorItemResponse(service.archiveCurve(groupId, curveId, version, form));
  }

  @Operation(summary = "Deletes (sets status to DELETED) BOND curve")
  @PutMapping("/{curveId}/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> deleteCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(service.deleteCurve(groupId, curveId, version));
  }

  @Operation(summary = "Gets BOND curves")
  @GetMapping
  @CommonErrors
  @Sorted
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<List<BondCurveView>> getCurves(
      @PathVariable("groupId") String groupId,
      @RequestParam(required = false) boolean withArchived,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(service.getCurves(groupId, stateForm, withArchived));
  }

  @Operation(summary = "Gets BOND Curve")
  @GetMapping("/{curveId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<BondCurveView> getCurve(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getCurve(groupId, curveId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Gets BOND curve versions")
  @GetMapping("/{curveId}/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<List<BondCurveView>> getCurveVersions(
      @PathVariable("groupId") String groupId, @PathVariable("curveId") String curveId) {
    return eitherErrorItemResponse(service.getCurveVersions(groupId, curveId));
  }

  @Operation(summary = "Gets BOND curve future versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<DateList> getCurveFutureVersions(
      @PathVariable("groupId") String groupId, @Valid BondCurveSearch form) {
    return eitherErrorItemResponse(service.getFutureVersions(groupId, form));
  }

  @Operation(summary = "Gets BOND curve nodes with calibration information")
  @GetMapping("/{curveId}/{version}/nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<List<BondCurveNodeCalculatedView>> getCurveNodes(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(
        service.getCurveNodes(groupId, curveId, BitemporalDate.newOf(version), stateForm));
  }

  @Operation(summary = "Gets BOND curves CSV")
  @GetMapping("/curves-csv")
  @Filtered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<ByteArrayResource> getCurvesCsv(
      @PathVariable("groupId") String groupId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.getCurvesCsvBytes(groupId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Gets BOND curve nodes CSV")
  @GetMapping("/{curveId}/{version}/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm) {
    var versionDate = BitemporalDate.newOf(version);
    return eitherErrorItemFileResponse(
        exportService.getNodesCsvBytes(groupId, curveId, versionDate, stateForm));
  }

  @Operation(summary = "Gets all BOND curves nodes CSV")
  @GetMapping("/{version}/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<ByteArrayResource> getCurvesNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemFileResponse(
        exportService.getNodesCsvBytes(groupId, BitemporalDate.newOf(version), stateForm));
  }

  @Operation(summary = "Gets BOND curves MDK definitions CSV")
  @GetMapping("/mdk-definitions/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getMarketDataKeyDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate stateDate,
      @RequestParam List<String> curveIds,
      @RequestParam(required = false) String configurationId) {
    var bitemporal = BitemporalDate.newOf(stateDate);
    return eitherErrorItemFileResponse(
        exportService.getMDKDefinitionsCsvBytes(groupId, configurationId, curveIds, bitemporal));
  }

  @Operation(summary = "Uploads BOND curves CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurves(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadCurves(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads all BOND curves nodes CSV file")
  @PostMapping(value = "/upload-nodes", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurvesNodes(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodes(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads BOND curve nodes CSV file")
  @PostMapping(
      value = "/{curveId}/{version}/nodes/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_BOND_CURVE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadCurveNodes(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodesForCurve(
            groupId, curveId, version, importOptions, file.getBytes()));
  }

  @Operation(summary = "Gets BOND curve chart points")
  @GetMapping("/{curveId}/chart-points")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<List<ChartPoint>> getCurveChartPoints(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(service.getCurveChartPoints(groupId, curveId, stateForm));
  }

  @Operation(summary = "Gets BOND curve points csv")
  @GetMapping("/{curveId}/chart-points/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveChartPointsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemFileResponse(
        exportService.getCurveChartPointsCsv(groupId, curveId, stateForm));
  }

  @Operation(summary = "Gets all BOND curves points csv")
  @GetMapping("/all/chart-points/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_BOND_CURVE)
  public ResponseEntity<ByteArrayResource> getAllCurvesChartPointsCsv(
      @PathVariable("groupId") String groupId,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemFileResponse(
        exportService.getAllCurvesChartPointsCsv(groupId, stateForm));
  }
}
