package com.solum.xplain.core.market.value;

import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.market.MarketDataKeyValue;
import lombok.Data;

@Data
public class MarketDataKeyCsvForm
    implements ResolvableEmbeddedVersionValue<MarketDataKeyValue, String> {
  private String key;
  private String name;
  private String assetGroup;
  private String instrumentType;

  @Override
  public String getEntityId() {
    return key;
  }

  @Override
  public MarketDataKeyValue toVersionValue() {
    var marketDataKey = new MarketDataKeyValue();
    marketDataKey.setName(this.name.toUpperCase());
    marketDataKey.setAssetGroup(this.assetGroup);
    marketDataKey.setInstrumentType(this.instrumentType);
    return marketDataKey;
  }
}
