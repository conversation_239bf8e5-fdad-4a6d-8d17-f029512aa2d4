package com.solum.xplain.core.company.value;

import lombok.Data;

@Data
public class CompanyLegalEntitySettingsView {

  private CompanyLegalEntityValuationSettingsView valuationSettings;
  private CompanyLegalEntityIpvSettingsView ipvSettings;

  public static CompanyLegalEntitySettingsView newOf(
      CompanyLegalEntityValuationSettingsView valuationSettings,
      CompanyLegalEntityIpvSettingsView ipvSettings) {
    CompanyLegalEntitySettingsView v = new CompanyLegalEntitySettingsView();
    v.setValuationSettings(valuationSettings);
    v.setIpvSettings(ipvSettings);
    return v;
  }
}
