package com.solum.xplain.core.company.entity;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyLegalEntityReference extends EntityReference {
  private String externalEntityId;

  public static CompanyLegalEntityReference of(CompanyLegalEntityView view) {
    CompanyLegalEntityReference entity = new CompanyLegalEntityReference();
    entity.setEntityId(view.getId());
    entity.setName(view.getName());
    entity.setExternalEntityId(view.getExternalId());
    return entity;
  }
}
