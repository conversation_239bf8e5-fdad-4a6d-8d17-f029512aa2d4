package com.solum.xplain.core.common.validation.identifier;

import static com.solum.xplain.core.error.Error.VALIDATION_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.solum.xplain.core.common.validation.ValidationMessagesBundle;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IdentifierValidationUtils {

  public static final String INVALID_IDENTIFIER_MESSAGE_KEY = "ValidIdentifier.message";
  public static final String INVALID_EXTERNAL_IDENTIFIER_MESSAGE_KEY =
      "ValidExternalIdentifier.message";
  public static final String INVALID_REF_IDENTIFIER_MESSAGE_KEY =
      "ValidReferenceIdentifier.message";

  private static final Pattern VALID_CHARACTERS_PATTERN = Pattern.compile("[!-`]*");

  public static final Pattern VALID_EXTERNAL_IDENTIFIER_PATTERN =
      Pattern.compile(
          "\\p{Graph}([ \\p{Graph}]*\\p{Graph}|\\p{Graph})?", Pattern.UNICODE_CHARACTER_CLASS);
  public static final String VALID_REF_REGEX = "^REF_.*$";
  private static final Pattern VALID_REF_PATTERN = Pattern.compile(VALID_REF_REGEX);

  public static Supplier<String> invalidIdentifierMessage() {
    return () -> ValidationMessagesBundle.get(INVALID_IDENTIFIER_MESSAGE_KEY);
  }

  public static Supplier<String> invalidExternalIdentifierMessage() {
    return () -> ValidationMessagesBundle.get(INVALID_EXTERNAL_IDENTIFIER_MESSAGE_KEY);
  }

  public static boolean isValid(@NonNull String identifier) {
    return VALID_CHARACTERS_PATTERN.matcher(identifier).matches();
  }

  public static boolean isValidExternalIdentifier(String identifier) {
    return VALID_EXTERNAL_IDENTIFIER_PATTERN.matcher(identifier).matches();
  }

  public static Either<ErrorItem, String> isValidReference(@NonNull String identifier) {
    if (VALID_CHARACTERS_PATTERN.matcher(identifier).matches()
        && VALID_REF_PATTERN.matcher(identifier).matches()) {
      return right(identifier);
    }

    return left(
        VALIDATION_ERROR.entity(ValidationMessagesBundle.get(INVALID_REF_IDENTIFIER_MESSAGE_KEY)));
  }
}
