package com.solum.xplain.core.roles;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.roles.value.RoleForm;
import com.solum.xplain.core.roles.value.RoleListView;
import com.solum.xplain.core.roles.value.RoleView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class RoleControllerService {
  private final RoleRepository roleRepository;

  public Either<ErrorItem, EntityId> insert(RoleForm form) {
    return roleRepository.insert(form);
  }

  public ScrollableEntry<RoleListView> roles(
      ScrollRequest scrollRequest, TableFilter tableFilter, RoleFilter roleFilter) {
    return roleRepository.list(scrollRequest, tableFilter, roleFilter);
  }

  public Either<ErrorItem, RoleView> role(String id) {
    return roleRepository.getView(id);
  }

  public Either<ErrorItem, EntityId> update(String id, RoleForm form) {
    return roleRepository.update(id, form);
  }

  public Either<ErrorItem, EntityId> archiveRole(String id) {
    return roleRepository.setRoleArchived(id, true);
  }

  public Either<ErrorItem, EntityId> restoreRole(String id) {
    return roleRepository.setRoleArchived(id, false);
  }

  public Either<ErrorItem, EntityId> remove(String id) {
    return roleRepository.remove(id);
  }
}
