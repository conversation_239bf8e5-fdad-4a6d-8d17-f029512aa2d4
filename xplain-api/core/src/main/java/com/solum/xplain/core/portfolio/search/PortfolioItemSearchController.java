package com.solum.xplain.core.portfolio.search;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.search.SearchRequest;
import com.solum.xplain.core.search.SearchResponse;
import com.solum.xplain.core.search.SearchTradeView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/search/trades")
public class PortfolioItemSearchController {
  private final PortfolioItemSearchService service;

  @GetMapping
  @Operation(summary = "Search trades")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_TRADE)
  public ResponseEntity<SearchResponse<SearchTradeView>> searchTrades(
      @Valid SearchRequest request) {
    return ResponseEntity.ok(service.search(request));
  }
}
