package com.solum.xplain.core.curvegroup.conventions.fx;

import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.GBP_LIBOR_3M_EUR_EURIBOR_3M;
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.GBP_LIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.core.curvegroup.conventions.fx.ImmutableXccyCurveConvention.xccyCurve;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.AED_EIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.AUD_BBSW_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.AUD_BBSW_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CAD_CDOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CAD_CDOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CHF_LIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CHF_LIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CZK_PRIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.CZK_PRIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.DKK_CIBOR_3M_USD_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.DKK_CIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.HKD_HIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.HUF_BUBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.HUF_BUBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.ILS_TLBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.JPY_LIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.JPY_LIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.KRW_CD_13W_USD_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.MXN_TIIE_4W_USD_LIBOR_1M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.MYR_KLIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.NOK_NIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.NOK_NIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.NZD_BKBM_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.NZD_BKBM_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.PLN_WIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.PLN_WIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.SAR_SAIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.SEK_STIBOR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.SEK_STIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.SGD_SOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.SGD_SOR_6M_USD_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.THB_THBFIX_6M_USD_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.TRY_TRLIBOR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.TWD_TAIBOR_3M_USD_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.ZAR_JIBAR_3M_EUR_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXCcyIborIborSwapConventions.ZAR_JIBAR_3M_USD_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.CAD_CORRA_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.CHF_SARON_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.CLP_TNA_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.CLP_TNA_USD_SOFR_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.COP_OIBR_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.EUR_ESTR_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.GBP_SONIA_EUR_ESTR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.GBP_SONIA_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.INR_OMIBOR_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.INR_OMIBOR_USD_SOFR_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.JPY_TONAR_EUR_ESTR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.JPY_TONAR_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.MXN_F_TIIE_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.MXN_F_TIIE_USD_SOFR_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.SGD_SORA_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.THB_THOR_USD_SOFR;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.THB_THOR_USD_SOFR_OFFSHORE;
import static com.solum.xplain.extensions.product.ExtendedXccyOvernightOvernightSwapConventions.TRY_TLREF_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.CNY_FIXED_6M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.COP_FIXED_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.INR_FIXED_6M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.KRW_FIXED_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.MYR_FIXED_6M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.THB_FIXED_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyfixedois.StandardXCcyFixedOvernightSwapConventions.TWD_FIXED_6M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.AED_EIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.AUD_BBSW_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.CNY_REPO_1W_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.CZK_PRIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.CZK_PRIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.DKK_CIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.DKK_CIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.EUR_EURIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.HKD_HIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.HUF_BUBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.HUF_BUBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.ILS_TLBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.KRW_CD_13W_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.MXN_TIIE_4W_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.MYR_KLIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.NOK_NIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.NOK_NIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.NZD_BKBM_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.PLN_WIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.PLN_WIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.SAR_SAIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.SAR_SAIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.SEK_STIBOR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.SEK_STIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.TWD_TAIBOR_3M_USD_SOFR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.ZAR_JIBAR_3M_EUR_ESTR;
import static com.solum.xplain.extensions.xccyiborois.StandardXCcyIborOvernightSwapConventions.ZAR_JIBAR_3M_USD_SOFR;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConvention;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.extensions.constants.PermissibleCurrencies;
import com.solum.xplain.extensions.xccyfixedois.XCcyFixedOvernightSwapConvention;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapConvention;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class XccyCurveConventions {

  private static final List<XCcyIborIborSwapConvention> XCCY_IBORIBOR_CONVENTIONS =
      List.of(
          CAD_CDOR_3M_EUR_EURIBOR_3M,
          CAD_CDOR_3M_USD_LIBOR_3M,
          AED_EIBOR_3M_USD_LIBOR_3M,
          AUD_BBSW_3M_EUR_EURIBOR_3M,
          AUD_BBSW_3M_USD_LIBOR_3M,
          EUR_EURIBOR_3M_USD_LIBOR_3M,
          CHF_LIBOR_3M_EUR_EURIBOR_3M,
          CHF_LIBOR_3M_USD_LIBOR_3M,
          CZK_PRIBOR_3M_EUR_EURIBOR_3M,
          CZK_PRIBOR_3M_USD_LIBOR_3M,
          DKK_CIBOR_3M_USD_EURIBOR_3M,
          DKK_CIBOR_3M_USD_LIBOR_3M,
          GBP_LIBOR_3M_EUR_EURIBOR_3M,
          GBP_LIBOR_3M_USD_LIBOR_3M,
          HUF_BUBOR_3M_EUR_EURIBOR_3M,
          HUF_BUBOR_3M_USD_LIBOR_3M,
          ILS_TLBOR_3M_USD_LIBOR_3M,
          JPY_LIBOR_3M_EUR_EURIBOR_3M,
          JPY_LIBOR_3M_USD_LIBOR_3M,
          KRW_CD_13W_USD_LIBOR_6M,
          MXN_TIIE_4W_USD_LIBOR_1M,
          MYR_KLIBOR_3M_USD_LIBOR_3M,
          NOK_NIBOR_3M_EUR_EURIBOR_3M,
          NOK_NIBOR_3M_USD_LIBOR_3M,
          NZD_BKBM_3M_EUR_EURIBOR_3M,
          NZD_BKBM_3M_USD_LIBOR_3M,
          PLN_WIBOR_3M_EUR_EURIBOR_3M,
          PLN_WIBOR_3M_USD_LIBOR_3M,
          SAR_SAIBOR_3M_USD_LIBOR_3M,
          SEK_STIBOR_3M_EUR_EURIBOR_3M,
          SEK_STIBOR_3M_USD_LIBOR_3M,
          SGD_SOR_6M_USD_LIBOR_6M,
          SGD_SOR_3M_USD_LIBOR_3M,
          THB_THBFIX_6M_USD_LIBOR_6M,
          TRY_TRLIBOR_3M_USD_LIBOR_3M,
          TWD_TAIBOR_3M_USD_LIBOR_6M,
          ZAR_JIBAR_3M_EUR_EURIBOR_3M,
          ZAR_JIBAR_3M_USD_LIBOR_3M,
          HKD_HIBOR_3M_USD_LIBOR_3M);

  private static final List<XCcyOvernightOvernightSwapConvention> XCCY_OISOIS_CONVENTIONS =
      List.of(
          CHF_SARON_USD_SOFR,
          CLP_TNA_USD_SOFR,
          COP_OIBR_USD_SOFR,
          EUR_ESTR_USD_SOFR,
          GBP_SONIA_USD_SOFR,
          JPY_TONAR_USD_SOFR,
          CAD_CORRA_USD_SOFR,
          TRY_TLREF_USD_SOFR,
          THB_THOR_USD_SOFR,
          SGD_SORA_USD_SOFR,
          GBP_SONIA_EUR_ESTR,
          JPY_TONAR_EUR_ESTR,
          INR_OMIBOR_USD_SOFR,
          MXN_F_TIIE_USD_SOFR,
          THB_THOR_USD_SOFR_OFFSHORE,
          INR_OMIBOR_USD_SOFR_OFFSHORE,
          MXN_F_TIIE_USD_SOFR_OFFSHORE,
          CLP_TNA_USD_SOFR_OFFSHORE);

  private static final List<XCcyIborOvernightSwapConvention> XCCY_IBOROIS_CONVENTIONS =
      List.of(
          AUD_BBSW_3M_USD_SOFR,
          CZK_PRIBOR_3M_USD_SOFR,
          NZD_BKBM_3M_USD_SOFR,
          DKK_CIBOR_3M_USD_SOFR,
          HUF_BUBOR_3M_USD_SOFR,
          MXN_TIIE_4W_USD_SOFR,
          NOK_NIBOR_3M_USD_SOFR,
          PLN_WIBOR_3M_USD_SOFR,
          SEK_STIBOR_3M_USD_SOFR,
          EUR_EURIBOR_3M_USD_SOFR,
          AED_EIBOR_3M_USD_SOFR,
          HKD_HIBOR_3M_USD_SOFR,
          ILS_TLBOR_3M_USD_SOFR,
          KRW_CD_13W_USD_SOFR,
          MYR_KLIBOR_3M_USD_SOFR,
          SAR_SAIBOR_3M_USD_SOFR,
          CNY_REPO_1W_USD_SOFR,
          TWD_TAIBOR_3M_USD_SOFR,
          CZK_PRIBOR_3M_EUR_ESTR,
          DKK_CIBOR_3M_EUR_ESTR,
          HUF_BUBOR_3M_EUR_ESTR,
          NOK_NIBOR_3M_EUR_ESTR,
          PLN_WIBOR_3M_EUR_ESTR,
          SAR_SAIBOR_3M_EUR_ESTR,
          SEK_STIBOR_3M_EUR_ESTR,
          ZAR_JIBAR_3M_EUR_ESTR,
          ZAR_JIBAR_3M_USD_SOFR);

  private static final List<XCcyFixedOvernightSwapConvention> XCCY_FIXEDOIS_CONVENTIONS =
      List.of(
          INR_FIXED_6M_USD_SOFR,
          THB_FIXED_3M_USD_SOFR,
          TWD_FIXED_6M_USD_SOFR,
          CNY_FIXED_6M_USD_SOFR,
          COP_FIXED_3M_USD_SOFR,
          KRW_FIXED_3M_USD_SOFR,
          MYR_FIXED_6M_USD_SOFR);

  public static final List<ConventionalCurveConvention> XCCY_CURVES =
      PermissibleCurrencies.FX_SWAP_PAIRS.stream()
          .map(pair -> xccyCurve(pair, resolveXccyConventions(pair)))
          .collect(Collectors.toUnmodifiableList());

  private static List<Named> resolveXccyConventions(CurrencyPair pair) {
    var builder = ImmutableList.<Named>builder();
    XCCY_IBORIBOR_CONVENTIONS.stream()
        .filter(c -> c.getCurrencyPair().toConventional().equals(pair.toConventional()))
        .forEach(builder::add);
    XCCY_OISOIS_CONVENTIONS.stream()
        .filter(c -> c.getCurrencyPair().toConventional().equals(pair.toConventional()))
        .forEach(builder::add);
    XCCY_IBOROIS_CONVENTIONS.stream()
        .filter(c -> c.getCurrencyPair().toConventional().equals(pair.toConventional()))
        .forEach(builder::add);
    XCCY_FIXEDOIS_CONVENTIONS.stream()
        .filter(c -> c.getCurrencyPair().toConventional().equals(pair.toConventional()))
        .forEach(builder::add);
    return builder.build();
  }
}
