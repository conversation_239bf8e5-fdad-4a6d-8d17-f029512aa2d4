package com.solum.xplain.core.viewconfig.validation;

import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.viewconfig.ViewConfigurationRepository;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UniqueViewConfigurationValidator
    implements ConstraintValidator<UniqueViewConfiguration, ViewConfigurationCreateForm> {

  private final ViewConfigurationRepository repository;
  private final AuthenticationContext authenticationContext;

  @Override
  public boolean isValid(ViewConfigurationCreateForm value, ConstraintValidatorContext context) {
    if (anyNull(value.getName(), value.getScope())) {
      return true;
    }

    var currentUser = authenticationContext.currentUser();
    return !repository.existsByCreatedByUserIdAndScopeAndNameIgnoreCase(
        currentUser.getId(), value.getScope(), value.getName());
  }
}
