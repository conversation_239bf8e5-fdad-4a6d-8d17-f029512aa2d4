package com.solum.xplain.core.curvegroup.curvecredit.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CREDIT_QUOTE_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SECTOR_CLASSIFIER_NAME;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.FixedRateSupplier;
import com.solum.xplain.core.common.validation.ValidNumberSet;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curvecredit.validation.CreditCurveCommonFormGroupProvider;
import com.solum.xplain.core.curvegroup.curvecredit.validation.CreditCurveFixedCouponGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@GroupSequenceProvider(CreditCurveCommonFormGroupProvider.class)
public abstract class CreditCurveCommonForm implements HasVersionForm {
  @NotNull
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = SECTOR_CLASSIFIER_NAME)
  private String sector;

  @Min(0)
  @Max(1)
  @NotNull
  private BigDecimal recoveryRate;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CREDIT_QUOTE_CONVENTION_CLASSIFIER)
  private String quoteConvention;

  @NotNull(groups = CreditCurveFixedCouponGroup.class)
  @ValidNumberSet(FixedRateSupplier.class)
  private Double fixedCoupon;

  @Valid @NotNull private NewVersionFormV2 versionForm;
}
