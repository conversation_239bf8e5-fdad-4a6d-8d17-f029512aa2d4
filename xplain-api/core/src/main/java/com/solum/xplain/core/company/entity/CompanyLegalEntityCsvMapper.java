package com.solum.xplain.core.company.entity;

import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.ALLOW_ALL_TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.COMPANY_EXTERNAL_ID;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.CURVE_CONFIGURATION;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.MARKET_DATA_GROUP;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.SLA_DEADLINE;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.VALUATION_DATA_GROUP;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_DESCRIPTION;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_EXTERNAL_ID;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_NAME;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.company.CommonCompanyEntityImportView;
import java.util.List;

public class CompanyLegalEntityCsvMapper extends CsvMapper<CompanyLegalEntityImportView> {

  private static final List<CsvColumn<CompanyLegalEntityImportView>> COLUMNS =
      List.of(
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.companyId,
              COMPANY_EXTERNAL_ID,
              CompanyLegalEntityImportView::getCompanyId),
          CsvColumn.text(
              CompanyLegalEntityImportView.Fields.entityId,
              ENTITY_EXTERNAL_ID,
              CompanyLegalEntityImportView::getEntityId),
          CsvColumn.text(
              CompanyLegalEntityImportView.Fields.entityName,
              ENTITY_NAME,
              CompanyLegalEntityImportView::getEntityName),
          CsvColumn.text(
              CompanyLegalEntityImportView.Fields.description,
              ENTITY_DESCRIPTION,
              CompanyLegalEntityImportView::getDescription),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.slaDeadline,
              SLA_DEADLINE,
              CompanyLegalEntityImportView::getSlaDeadline),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.valuationDataGroup,
              VALUATION_DATA_GROUP,
              CompanyLegalEntityImportView::getValuationDataGroup),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.marketDataGroup,
              MARKET_DATA_GROUP,
              CompanyLegalEntityImportView::getMarketDataGroup),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.curveConfiguration,
              CURVE_CONFIGURATION,
              CompanyLegalEntityImportView::getCurveConfiguration),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.teams,
              TEAMS,
              v -> v.getTeams() != null ? String.join("|", v.getTeams()) : null),
          CsvColumn.text(
              CommonCompanyEntityImportView.Fields.allowAllTeams,
              ALLOW_ALL_TEAMS,
              CompanyLegalEntityImportView::getAllowAllTeams));

  public CompanyLegalEntityCsvMapper() {
    super(COLUMNS, null);
  }
}
