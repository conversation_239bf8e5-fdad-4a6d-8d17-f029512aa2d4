package com.solum.xplain.core.config;

import com.solum.xplain.core.authentication.ActivitySessionInterceptor;
import com.solum.xplain.core.common.GroupRequestArgumentResolver;
import com.solum.xplain.core.lock.LockingInterceptor;
import com.solum.xplain.core.refdata.ReferenceDataInterceptor;
import com.solum.xplain.shared.utils.filter.FilterArgumentResolver;
import com.solum.xplain.shared.utils.filter.ScrollRequestArgumentResolver;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.lang.NonNull;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

  private final LockingInterceptor lockingInterceptor;
  private final ReferenceDataInterceptor referenceDataInterceptor;
  private final Optional<ActivitySessionInterceptor> activitySessionInterceptor;

  @Override
  public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
    argumentResolvers.add(new FilterArgumentResolver());
    argumentResolvers.add(new ScrollRequestArgumentResolver());
    argumentResolvers.add(new GroupRequestArgumentResolver());
  }

  @Override
  public void addFormatters(@NonNull FormatterRegistry registry) {
    DateTimeFormatterRegistrar reigistrar = new DateTimeFormatterRegistrar();
    reigistrar.setUseIsoFormat(true);
    reigistrar.registerFormatters(registry);
  }

  @Bean
  public RequestLoggingFilter createLoggingFilter() {
    RequestLoggingFilter loggingFilter = new RequestLoggingFilter();
    loggingFilter.setIncludeClientInfo(true);
    loggingFilter.setIncludePayload(true);
    loggingFilter.setIncludeQueryString(true);
    loggingFilter.setMaxPayloadLength(2000);
    return loggingFilter;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(lockingInterceptor);
    registry.addInterceptor(referenceDataInterceptor);
    activitySessionInterceptor.ifPresent(registry::addInterceptor);
  }
}
