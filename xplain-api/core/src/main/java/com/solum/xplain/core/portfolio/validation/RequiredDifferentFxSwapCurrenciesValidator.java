package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentFxSwapCurrenciesValidator
    implements ConstraintValidator<RequiredDifferentFxSwapCurrencies, FxSwapTradeForm> {

  public boolean isValid(FxSwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    var baseCurrency = form.getBaseCurrency();
    var counterCurrency = form.getCounterCurrency();

    if (baseCurrency != null && baseCurrency.equals(counterCurrency)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("RequiredDifferentFxSwapCurrencies")
          .addPropertyNode("counterCurrency")
          .addConstraintViolation();

      return false;
    }

    return true;
  }
}
