package com.solum.xplain.core.search;

import com.solum.xplain.core.portfolio.value.ExternalIdentifierView;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class SearchTradeView {
  private final String tradeId;
  private final String portfolioId;

  private final String externalTradeId;
  private final String productType;
  private final LocalDate tradeDate;

  private final String externalCompanyId;
  private final String externalEntityId;
  private final String externalPortfolioId;
  private List<ExternalIdentifierView> externalIdentifiers;
}
