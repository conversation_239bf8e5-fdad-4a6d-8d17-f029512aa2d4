package com.solum.xplain.core.common.validation;

import com.solum.xplain.core.externalsource.IdentifierSourceRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class ValidIdentifierSourceValidator
    implements ConstraintValidator<ValidIdentifierSource, String> {

  private final IdentifierSourceRepository identifierSourceRepository;

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (!StringUtils.isEmpty(value)) {
      return identifierSourceRepository.existsByExternalId(value);
    }
    return true;
  }
}
