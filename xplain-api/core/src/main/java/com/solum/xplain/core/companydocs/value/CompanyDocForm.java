package com.solum.xplain.core.companydocs.value;

import com.solum.xplain.core.companydocs.validation.ValidDocumentSize;
import com.solum.xplain.core.companydocs.validation.ValidDocumentType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class CompanyDocForm {

  @NotNull @ValidDocumentSize @ValidDocumentType private final MultipartFile file;
  @NotEmpty private final String description;
}
