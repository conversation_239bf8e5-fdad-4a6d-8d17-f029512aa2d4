package com.solum.xplain.core.datavalue.csv;

import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.datavalue.DataValueUpdateResolver;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.DataValuesHolderWriteRepository;
import com.solum.xplain.core.datavalue.VersionedValue;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class DataValueFileImporter<V extends VersionedValue, K, E extends DataValuesHolder<V>> {

  private final DataValuesHolderWriteRepository<V, E> writeRepository;

  public DataUpdateSummary importItems(
      DuplicateAction action, DataValueImportItems<V, K, E> importItems) {
    var updatesResolver = buildUpdateResolver(action, importItems);
    return writeRepository.updateForImport(updatesResolver);
  }

  private DataValueUpdateResolver<V, E> buildUpdateResolver(
      DuplicateAction action, DataValueImportItems<V, K, E> importItems) {
    return switch (action) {
      case REPLACE_DELETE -> updateResolver(importItems, true, true);
      case REPLACE -> updateResolver(importItems, true, false);
      case APPEND_DELETE -> updateResolver(importItems, false, true);
      case APPEND -> updateResolver(importItems, false, false);
      default -> throw new IllegalStateException("Unexpected value: " + action);
    };
  }

  private DataValueUpdateResolver<V, E> updateResolver(
      DataValueImportItems<V, K, E> items, boolean replace, boolean deleteSpares) {
    var builder =
        DataValueUpdateResolver.<V, E>builder()
            .comment(items.getImportComment())
            .entitiesToAppend(items.newItems());
    if (replace) {
      builder.entitiesToReplace(items.replaceItems());
    }
    if (deleteSpares) {
      builder.entitiesToArchive(items.spareItems());
    }
    return builder.build();
  }
}
