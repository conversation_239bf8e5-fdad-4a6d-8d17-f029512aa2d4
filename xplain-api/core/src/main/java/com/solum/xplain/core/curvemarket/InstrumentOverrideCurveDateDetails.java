package com.solum.xplain.core.curvemarket;

import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Instrument-specific curve date value object. The primary use case for the class is constructing
 * {@link com.solum.xplain.core.mdvalue.value.ResolvedMarketData} sets for various instrument types
 * on different dates prior to trade valuation, which can be a requirement for simulations, such as
 * PnL.
 */
@Getter
@AllArgsConstructor(staticName = "of")
public class InstrumentOverrideCurveDateDetails {

  // the curve date used for reporting purposes, applicable to all instrument types
  private final LocalDate reportingCurveDate;
  // for instruments with type not in overrideCurveDate.instruments
  private final LocalDate nonOverrideCurveDate;
  private final InstrumentCurveDate overrideCurveDate;
}
