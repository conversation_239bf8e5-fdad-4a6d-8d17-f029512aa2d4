package com.solum.xplain.core.curvegroup.curvecredit;

import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.product.credit.type.CdsConvention;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveFundingNode;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveNode;
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveFundingNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveForm;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = {CreditCurveNameUtils.class, CollectionUtils.class})
public interface CreditCurveMapper extends VersionedEntityMapper<CreditCurve> {

  CreditCurveMapper INSTANCE = Mappers.getMapper(CreditCurveMapper.class);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", source = "groupId")
  @Mapping(target = "chartPoints", ignore = true)
  @Mapping(target = "creditIndexFactor", ignore = true)
  @Mapping(target = "creditIndexSeries", ignore = true)
  @Mapping(target = "creditIndexVersion", ignore = true)
  @Mapping(target = "creditIndexStartDate", ignore = true)
  @Mapping(target = "creditIndexTranche", ignore = true)
  @Mapping(target = "indexNodes", ignore = true)
  @Mapping(target = "orderNodes", ignore = true)
  @Mapping(target = "curveType", constant = "CDS")
  @Mapping(target = "name", expression = "java(form.creditCurveName())")
  CreditCurve toCdsCurve(CdsCurveForm form, String groupId, @MappingTarget CreditCurve curve);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", source = "groupId")
  @Mapping(target = "chartPoints", ignore = true)
  @Mapping(target = "cdsNodes", ignore = true)
  @Mapping(target = "fundingNodes", ignore = true)
  @Mapping(target = "corpTicker", ignore = true)
  @Mapping(target = "seniority", ignore = true)
  @Mapping(target = "orderNodes", ignore = true)
  @Mapping(target = "creditIndexTranche", ignore = true)
  @Mapping(target = "curveType", constant = "CREDIT_INDEX")
  @Mapping(target = "name", expression = "java(form.creditCurveName())")
  CreditCurve toCreditIndexCurve(
      CreditIndexCurveForm form, String groupId, @MappingTarget CreditCurve curve);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", source = "groupId")
  @Mapping(target = "chartPoints", ignore = true)
  @Mapping(target = "cdsNodes", ignore = true)
  @Mapping(target = "fundingNodes", ignore = true)
  @Mapping(target = "corpTicker", ignore = true)
  @Mapping(target = "seniority", ignore = true)
  @Mapping(target = "orderNodes", ignore = true)
  @Mapping(target = "curveType", constant = "CREDIT_INDEX_TRANCHE")
  @Mapping(target = "name", expression = "java(form.creditCurveName())")
  CreditCurve toCreditIndexTrancheCurve(
      CreditIndexTrancheCurveForm form, String groupId, @MappingTarget CreditCurve curve);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "curveType", ignore = true)
  @Mapping(target = "reference", ignore = true)
  @Mapping(target = "currency", ignore = true)
  @Mapping(target = "seniority", ignore = true)
  @Mapping(target = "docClause", ignore = true)
  @Mapping(target = "chartPoints", ignore = true)
  @Mapping(target = "creditIndexFactor", ignore = true)
  @Mapping(target = "creditIndexSeries", ignore = true)
  @Mapping(target = "creditIndexVersion", ignore = true)
  @Mapping(target = "creditIndexStartDate", ignore = true)
  @Mapping(target = "creditIndexTranche", ignore = true)
  @Mapping(target = "indexNodes", ignore = true)
  @Mapping(
      target = "cdsNodes",
      conditionExpression = "java(includeNodes && form.getCdsNodes() != null)",
      defaultExpression = "java(includeNodes ? null : curve.getCdsNodes())")
  @Mapping(
      target = "fundingNodes",
      conditionExpression = "java(includeNodes && form.getFundingNodes() != null)",
      defaultExpression = "java(includeNodes ? null : curve.getFundingNodes())")
  @Mapping(target = "orderNodes", ignore = true)
  CreditCurve toCdsCurve(
      CdsCurveUpdateForm form, @MappingTarget CreditCurve curve, boolean includeNodes);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "curveType", ignore = true)
  @Mapping(target = "reference", ignore = true)
  @Mapping(target = "currency", ignore = true)
  @Mapping(target = "seniority", ignore = true)
  @Mapping(target = "chartPoints", ignore = true)
  @Mapping(target = "corpTicker", ignore = true)
  @Mapping(target = "cdsNodes", ignore = true)
  @Mapping(target = "fundingNodes", ignore = true)
  @Mapping(target = "orderNodes", ignore = true)
  @Mapping(target = "creditIndexTranche", ignore = true)
  @Mapping(
      target = "indexNodes",
      conditionExpression = "java(includeNodes && form.getIndexNodes() != null)",
      defaultExpression = "java(includeNodes ? null : curve.getIndexNodes())")
  CreditCurve toCreditIndexCurve(
      CreditIndexCurveUpdateForm form, @MappingTarget CreditCurve curve, boolean includeNodes);

  @Mapping(target = "versionForm", ignore = true)
  CdsCurveUpdateForm toCdsUpdateForm(CreditCurve curve);

  @Mapping(target = "versionForm", ignore = true)
  CreditIndexCurveUpdateForm toIndexUpdateForm(CreditCurve curve);

  CreditCurveNodeForm toNodeForm(CreditCurveNode node);

  @Mapping(target = "ticker", ignore = true)
  @Mapping(target = "creditSpreadId", ignore = true)
  @Mapping(target = "creditSpread", ignore = true)
  @Mapping(target = "creditSpreadAsk", ignore = true)
  @Mapping(target = "creditSpreadMid", ignore = true)
  @Mapping(target = "creditSpreadBid", ignore = true)
  @Mapping(target = "calibrationPriceType", ignore = true)
  @Mapping(target = "dataSource", ignore = true)
  @Mapping(target = "key", expression = "java(curveNode.getKey(reference, currency))")
  CreditCurveFundingNodeCalculatedView toCalculatedNodeView(
      CreditCurveFundingNode curveNode, String reference, String currency);

  @Mapping(target = "numberOfCdsNodes", expression = "java(numberOfCdsNodes(curve))")
  @Mapping(
      target = "numberOfFundingNodes",
      expression = "java(CollectionUtils.size(curve.getFundingNodes()))")
  @Mapping(target = "calendar", expression = "java(calendarName(curve.cdsConvention()))")
  @Mapping(
      target = "calibrated",
      expression = "java(CollectionUtils.isNotEmpty(curve.getChartPoints()))")
  CreditCurveView toView(CreditCurve curve);

  default int numberOfCdsNodes(CreditCurve curve) {
    return CollectionUtils.size(curve.getCdsNodes()) + CollectionUtils.size(curve.getIndexNodes());
  }

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "orderNodes", ignore = true)
  CreditCurve copy(CreditCurve entity);

  default String calendarName(CdsConvention cdsConvention) {
    return Optional.ofNullable(cdsConvention)
        .map(CdsConvention::getSettlementDateOffset)
        .map(DaysAdjustment::getCalendar)
        .map(HolidayCalendarId::getName)
        .orElse(null);
  }
}
