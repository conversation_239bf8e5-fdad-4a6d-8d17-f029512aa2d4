package com.solum.xplain.core.curvegroup.instrument;

import com.solum.xplain.core.instrument.AssetClass;

public enum CoreAssetClass implements AssetClass {
  IR_RATE("IR Rates", CoreAssetGroup.RATES),
  INFLATION_RATE("Inflation Rates", CoreAssetGroup.RATES),
  SWAPTION_VOLS("Swaption Vols", CoreAssetGroup.RATES),
  SWAPTION_SKEW("Swaption Skew", CoreAssetGroup.RATES),
  CAPFLOOR_VOLS("Cap/floor Vols", CoreAssetGroup.RATES),

  // Credit
  CDS("CDS", CoreAssetGroup.CREDIT),
  CREDIT_INDEX("Credit Index", CoreAssetGroup.CREDIT),
  CREDIT_INDEX_TRANCHE("Credit Index Tranche", CoreAssetGroup.CREDIT),

  // FX
  FX_RATES("Spot FX", CoreAssetGroup.FX),
  FX_SWAP("FX Swap", CoreAssetGroup.FX),
  FX_VOLS("FX Vols", CoreAssetGroup.FX),
  FX_VOL_SKEW("FX Vol Skew", CoreAssetGroup.FX);

  private final String name;
  private final CoreAssetGroup group;

  CoreAssetClass(String name, CoreAssetGroup group) {
    this.name = name;
    this.group = group;
  }

  @Override
  public String getLabel() {
    return name;
  }

  public CoreAssetGroup getGroup() {
    return group;
  }
}
