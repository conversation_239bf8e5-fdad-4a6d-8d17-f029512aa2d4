package com.solum.xplain.core.company.mapper;

import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.rules.RulesService;
import com.solum.xplain.core.rules.jeasy.SafeRules;
import java.util.Optional;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.RuleBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;

public class IpvValuationRulesMapper {
  public static final String TRADE_FACT = "trade";
  public static final int TRADE_RULE_PRIORITY = Ordered.HIGHEST_PRECEDENCE;
  public static final int ENTITY_RULE_PRIORITY = TRADE_RULE_PRIORITY + 1;
  public static final int COMPANY_RULE_PRIORITY = Ordered.LOWEST_PRECEDENCE;
  protected final RuleBuilder ruleBuilder = new RuleBuilder();
  @Autowired protected IpvValuationProvidersMapper ipvValuationProvidersMapper;
  @Autowired protected RulesService<Rule, SafeRules> rulesService;
  @Autowired private CompanyRepository companyRepository;
  @Autowired private CompanyLegalEntityRepository companyLegalEntityRepository;

  protected boolean tradeCompanyMatches(Optional<String> externalCompanyId, Facts facts) {
    TradeFact tradeFact = facts.get(TRADE_FACT);
    return externalCompanyId.map(id -> id.equals(tradeFact.getExternalCompanyId())).orElse(false);
  }

  protected boolean tradeCompanyAndEntityMatches(
      Optional<String> externalCompanyId, Optional<String> externalEntityId, Facts facts) {
    TradeFact tradeFact = facts.get(TRADE_FACT);

    return externalCompanyId.map(id -> id.equals(tradeFact.getExternalCompanyId())).orElse(false)
        && externalEntityId.map(id -> id.equals(tradeFact.getExternalEntityId())).orElse(false);
  }

  Optional<String> toExternalCompanyId(String companyEntityId) {
    return companyRepository
        .companyEntity(companyEntityId)
        .map(Company::getExternalCompanyId)
        .toOptional();
  }

  Optional<String> toExternalEntityId(String companyEntityId, String companyLegalEntityId) {
    return companyLegalEntityRepository
        .entity(companyEntityId, companyLegalEntityId)
        .map(CompanyLegalEntity::getExternalId)
        .toOptional();
  }
}
