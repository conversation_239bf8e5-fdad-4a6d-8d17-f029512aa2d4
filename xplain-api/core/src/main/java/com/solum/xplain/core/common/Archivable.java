package com.solum.xplain.core.common;

/**
 * Interface for entities that can be archived. The entity must have a field named "archived" of
 * type boolean.
 *
 * @param <T> the entity type
 */
public interface Archivable<T> {

  class Fields {
    public static final String archived = "archived";
  }

  default T archived() {
    this.setArchived(true);
    return (T) this;
  }

  default void setArchived(boolean archived) {
    throw new UnsupportedOperationException(
        "Entity does not have an archived field - or is not accessible");
  }
}
