package com.solum.xplain.core.ipv.group.form;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.common.value.AllowedCompaniesForm;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.core.ipv.group.validation.ImmutableIpvDataGroupName;
import com.solum.xplain.core.ipv.group.validation.UniqueIpvDataGroupName;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class IpvDataGroupForm {
  @UniqueIpvDataGroupName @ImmutableIpvDataGroupName private final String name;

  @NotNull private final PricingSlot pricingSlot;

  @Valid @NotNull private final AllowedCompaniesForm allowedCompaniesForm;

  @NotNull @Valid private final AllowedTeamsForm allowedTeamsForm;
}
