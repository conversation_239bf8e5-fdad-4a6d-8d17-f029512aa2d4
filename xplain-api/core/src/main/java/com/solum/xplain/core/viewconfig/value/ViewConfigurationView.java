package com.solum.xplain.core.viewconfig.value;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;

/** a named list of Column Definition Groups for a View, with a freezeFirstGroup flag */
@Schema(
    description =
        "User-defined (or built-in) configuration for displaying the data in a particular view")
public record ViewConfigurationView<T>(
    @Schema(
            description =
                "Unique identifier for this view configuration. Only present for user created configurations")
        String id,
    @Schema(
            description = "Configurable view this configuration applies to",
            requiredMode = REQUIRED)
        View<T> scope,
    @Schema(description = "Name of this view configuration", requiredMode = REQUIRED) String name,
    @Schema(
            description = "True if the first column definition group should be frozen (not scroll)",
            requiredMode = REQUIRED)
        boolean freezeFirstGroup,
    @Schema(description = "Groups of column definitions", requiredMode = REQUIRED)
        List<ColumnDefinitionGroupView> columnDefinitionGroups,
    @Schema(description = "Current user is owner of this configuration") boolean isOwner,
    @Schema(description = "Owner of view") String createdBy,
    @Schema(description = "Last modification date") LocalDateTime lastModifiedAt) {}
