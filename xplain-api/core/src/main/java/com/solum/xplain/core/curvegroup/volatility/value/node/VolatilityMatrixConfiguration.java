package com.solum.xplain.core.curvegroup.volatility.value.node;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityMatrix;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class VolatilityMatrixConfiguration extends VolatilityMatrix<VolatilityNodeView> {

  private VolatilityMatrixConfiguration(List<VolatilityNodeView> content, LocalDate versionDate) {
    super(content, versionDate);
  }

  public static VolatilityMatrixConfiguration configurationFromList(
      VersionedList<VolatilityNodeView> ver) {
    return new VolatilityMatrixConfiguration(ver.getList(), ver.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(Tenor::parse);
  }
}
