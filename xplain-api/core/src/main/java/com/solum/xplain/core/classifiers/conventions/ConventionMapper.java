package com.solum.xplain.core.classifiers.conventions;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConvention;
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.IborIborSwapConvention;
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConvention;
import com.solum.xplain.core.utils.FrequencyUtils;
import com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConvention;
import java.time.Period;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ConventionMapper {

  ConventionMapper INSTANCE = Mappers.getMapper(ConventionMapper.class);

  @Mapping(target = "calculationType", constant = "FIXED")
  @Mapping(target = "rateCutOffDays", ignore = true)
  Leg toLeg(FixedRateSwapLegConvention leg);

  @Mapping(target = "calculationType", constant = "IBOR")
  @Mapping(target = "rateCutOffDays", ignore = true)
  Leg toLeg(IborRateSwapLegConvention leg);

  @Mapping(target = "calculationType", constant = "INFLATION")
  @Mapping(target = "rateCutOffDays", ignore = true)
  Leg toLeg(InflationRateSwapLegConvention leg);

  @Mapping(target = "calculationType", constant = "OVERNIGHT")
  Leg toLeg(OvernightRateSwapLegConvention leg);

  @Mapping(target = "name", constant = "FixedIborSwap")
  @Mapping(target = "payLeg", source = "fixedLeg")
  @Mapping(target = "receiveLeg", source = "floatingLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(FixedIborSwapConvention convention);

  @Mapping(target = "name", constant = "FixedInflationSwap")
  @Mapping(target = "payLeg", source = "fixedLeg")
  @Mapping(target = "receiveLeg", source = "floatingLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(FixedInflationSwapConvention convention);

  @Mapping(target = "name", constant = "FixedOvernightSwap")
  @Mapping(target = "payLeg", source = "fixedLeg")
  @Mapping(target = "receiveLeg", source = "floatingLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(FixedOvernightSwapConvention convention);

  @Mapping(target = "name", constant = "IborIborSwap")
  @Mapping(target = "payLeg", source = "flatLeg")
  @Mapping(target = "receiveLeg", source = "spreadLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(IborIborSwapConvention convention);

  @Mapping(target = "name", constant = "OvernightIborSwap")
  @Mapping(target = "payLeg", source = "overnightLeg")
  @Mapping(target = "receiveLeg", source = "iborLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(OvernightIborSwapConvention convention);

  @Mapping(target = "name", constant = "XCcyIborIborSwap")
  @Mapping(target = "payLeg", source = "spreadLeg")
  @Mapping(target = "receiveLeg", source = "flatLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(XCcyIborIborSwapConvention convention);

  @Mapping(target = "name", constant = "OvernightTermOvernightSwap")
  @Mapping(target = "payLeg", source = "termOvernightLeg")
  @Mapping(target = "receiveLeg", source = "overnightLeg")
  @Mapping(target = "conventionName", source = "name")
  SwapConvention to(OvernightTermOvernightSwapConvention convention);

  default String map(Currency currency) {
    return String.valueOf(currency);
  }

  default String map(Frequency frequency) {
    return FrequencyUtils.toStringNoPrefix(frequency);
  }

  default String map(Named named) {
    return named.getName();
  }

  default String map(BusinessDayAdjustment businessDayAdjustment) {
    return map(businessDayAdjustment.getConvention());
  }

  default Integer map(DaysAdjustment daysAdjustment) {
    return daysAdjustment.getDays();
  }

  default String map(Period period) {
    return FrequencyUtils.toStringNoPrefix(period);
  }
}
