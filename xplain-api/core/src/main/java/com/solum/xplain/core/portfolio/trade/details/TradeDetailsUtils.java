package com.solum.xplain.core.portfolio.trade.details;

import static java.util.Optional.ofNullable;

import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeDetailsUtils {
  public static final String JOIN_ON = " vs ";

  public static Stream<TradeLegDetails> resolveLegIndices(TradeDetails trade) {
    return trade
        .legsStream()
        .sorted(
            Comparator.comparingDouble(
                l -> Optional.ofNullable(l.getInitialValue()).orElse(Double.MAX_VALUE)))
        .filter(
            c -> c.getType() == CalculationType.OVERNIGHT || c.getType() == CalculationType.IBOR);
  }

  public static double tradeCcyOrPayLegPriorityNotional(TradeDetails tradeDetails) {
    if (tradeDetails.currencyPair() != null) {
      return tradeDetails
          .legsStream()
          .filter(v -> Objects.equals(v.currency(), tradeDetails.tradeCurrency()))
          .findFirst()
          .map(TradeLegDetails::getNotional)
          .orElse(0D);
    } else {
      return ofNullable(tradeDetails.getPayLeg())
          .or(() -> ofNullable(tradeDetails.getReceiveLeg()))
          .map(TradeLegDetails::getNotional)
          .orElse(0D);
    }
  }
}
