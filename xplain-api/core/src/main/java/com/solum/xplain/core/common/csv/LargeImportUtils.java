package com.solum.xplain.core.common.csv;

import static java.util.Objects.isNull;

import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.error.ErrorItem;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.function.Supplier;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LargeImportUtils {

  public static <V, K, E extends EmbeddedVersionEntity<V>> List<ErrorItem> validateImportItems(
      VersionedImportItems<V, K, E> importItems, LocalDate stateDate) {
    final List<ErrorItem> errors = new ArrayList<>();
    validateExistingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateMissingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateViableNewVersions(importItems, stateDate).ifPresent(errors::add);
    validateDuplicateItems(importItems.getDuplicateActiveItemsKeys()).ifPresent(errors::add);
    validateMissingItems(importItems.getSpareItemsKeys()).ifPresent(errors::add);
    return errors;
  }

  public static <K> Optional<ErrorItem> validateDuplicateItems(Collection<K> duplicateKeys) {
    return validateKeys(duplicateKeys, ImportErrorUtils::duplicateItem);
  }

  public static <K> Optional<ErrorItem> validateMissingItems(Collection<K> spareKeys) {
    return validateKeys(spareKeys, ImportErrorUtils::missingItem);
  }

  private static <K> Optional<ErrorItem> validateKeys(
      Collection<K> keys, Supplier<ErrorItem> errorFn) {
    return keys.isEmpty() ? Optional.empty() : Optional.of(errorFn.get());
  }

  public static <K> Optional<ErrorItem> validateExistingItemFutureVersions(
      Collection<K> keys, Predicate<K> hasFutureVersionFn) {
    return validateFutureVersions(keys, hasFutureVersionFn, ImportErrorUtils::futureVersionExists);
  }

  public static <V, K, E extends EmbeddedVersionEntity<V>>
      Optional<ErrorItem> validateExistingItemFutureVersions(
          VersionedImportItems<V, K, E> importItems, LocalDate stateDate) {
    return validateFutureVersions(
        importItems.getActiveDuplicateItems().keySet(),
        k -> k.getEntity().hasFutureVersion(stateDate),
        ImportErrorUtils::futureVersionExists);
  }

  public static <V, K, E extends EmbeddedVersionEntity<V>>
      Optional<ErrorItem> validateMissingItemFutureVersions(
          VersionedImportItems<V, K, E> importItems, LocalDate stateDate) {
    return validateFutureVersions(
        importItems.getSpareExistingItems(),
        k -> k.getEntity().hasFutureVersion(stateDate),
        ImportErrorUtils::missingEntryFutureVersionExists);
  }

  public static <K> Optional<ErrorItem> validateMissingItemFutureVersions(
      Collection<K> keys, Predicate<K> hasFutureVersionFn) {
    return validateFutureVersions(
        keys, hasFutureVersionFn, ImportErrorUtils::missingEntryFutureVersionExists);
  }

  private static <K> Optional<ErrorItem> validateFutureVersions(
      Collection<K> keys, Predicate<K> futureVersionFn, Supplier<ErrorItem> errorItemFn) {
    return keys.stream().filter(futureVersionFn).findAny().map(k -> errorItemFn.get());
  }

  public static <V, E extends EmbeddedVersionEntity<V>>
      Optional<ErrorItem> validateExistingItemsViableNewVersions(
          Collection<EntityForUpdate<V, E>> existingItems, LocalDate stateDate) {
    return existingItems.stream()
        .filter(e -> isNewVersionViable(e, stateDate))
        .findAny()
        .map(s -> ImportErrorUtils.newVersionViable());
  }

  public static <V, K, E extends EmbeddedVersionEntity<V>>
      Optional<ErrorItem> validateViableNewVersions(
          VersionedImportItems<V, K, E> importItems, LocalDate stateDate) {
    return validateNewVersionViable(importItems.getNewItemsKeys(), importItems, stateDate)
        .or(
            () ->
                validateNewVersionViable(
                    importItems.getDuplicateActiveItemsKeys(), importItems, stateDate))
        .or(
            () ->
                validateNewVersionViable(
                    importItems.getDuplicateArchivedItemsKeys(), importItems, stateDate));
  }

  private static <V, K, E extends EmbeddedVersionEntity<V>>
      Optional<ErrorItem> validateNewVersionViable(
          Set<K> keys, VersionedImportItems<V, K, E> importItems, LocalDate stateDate) {
    return keys.stream()
        .filter(k -> isNewVersionViable(importItems.getExistingActiveItem(k), stateDate))
        .findAny()
        .map(s -> ImportErrorUtils.newVersionViable());
  }

  private static <V, E extends EmbeddedVersionEntity<V>> boolean isNewVersionViable(
      EntityForUpdate<V, E> existingItem, LocalDate stateDate) {
    return isNull(existingItem) || stateDate.isAfter(existingItem.getVersion().getValidFrom());
  }
}
