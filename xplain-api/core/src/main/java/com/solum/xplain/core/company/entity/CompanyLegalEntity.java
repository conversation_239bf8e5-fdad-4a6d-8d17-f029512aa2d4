package com.solum.xplain.core.company.entity;

import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.common.team.WithTeams;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = CompanyLegalEntity.COMPANY_LEGAL_ENTITY_COLLECTION)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class CompanyLegalEntity extends AuditableDiffable<CompanyLegalEntity> implements WithTeams {

  public static final String COMPANY_LEGAL_ENTITY_COLLECTION = "companyLegalEntity";

  private String companyId;
  private String name;
  private String description;
  private String externalId;

  private String vdkPrefix;
  private String externalCompanyId;

  private Boolean allowAllTeams;
  private List<ObjectId> teamIds;

  @CreatedBy private AuditUser createdBy;
  @CreatedDate private LocalDateTime createdAt;
  private boolean archived;

  public static CompanyLegalEntity newOf(String companyId) {
    CompanyLegalEntity c = new CompanyLegalEntity();
    c.setCompanyId(companyId);
    return c;
  }

  @Override
  public VersionDiffs diff(CompanyLegalEntity obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append("archived", this.archived, obj.archived)
            .append("description", this.description, obj.description)
            .append("name", this.name, obj.name)
            .append("teamIds", this.teamIds, obj.teamIds)
            .append("allowAllTeams", this.allowAllTeams, obj.allowAllTeams)
            .build());
  }
}
