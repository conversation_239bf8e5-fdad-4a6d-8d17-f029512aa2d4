package com.solum.xplain.core.company.value;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

import com.solum.xplain.core.company.CommonCompanyEntityImportView;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.FieldNameConstants;

@Getter
@FieldNameConstants
@EqualsAndHashCode
public class CompanyImportView extends CommonCompanyEntityImportView {

  public CompanyImportView(
      String companyId,
      String slaDeadline,
      String valuationDataGroup,
      String marketDataGroup,
      String curveConfiguration,
      List<String> teams,
      String allowAllTeams,
      String companyName,
      String description) {
    super(
        companyId,
        slaDeadline,
        valuationDataGroup,
        marketDataGroup,
        curveConfiguration,
        teams,
        allowAllTeams);
    this.companyName = companyName;
    this.description = description;
  }

  @Schema(description = "Company Name", requiredMode = NOT_REQUIRED)
  private final String companyName;

  @Schema(description = "Company Description", requiredMode = NOT_REQUIRED)
  private final String description;
}
