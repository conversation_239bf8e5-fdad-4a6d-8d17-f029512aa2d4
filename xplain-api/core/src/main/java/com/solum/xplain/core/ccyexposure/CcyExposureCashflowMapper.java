package com.solum.xplain.core.ccyexposure;

import com.solum.xplain.core.ccyexposure.entity.Cashflow;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.ccyexposure.value.CombinedCcyExposureView;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.common.versions.VersionedFormMapper;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = CollectionUtils.class)
public interface CcyExposureCashflowMapper
    extends VersionedEntityMapper<Cashflow>, VersionedFormMapper<CashflowForm, Cashflow> {

  @Mapping(target = "id", source = "ccyExposure.id")
  @Mapping(target = "modifiedAt", source = "ccyExposure.modifiedAt")
  @Mapping(target = "modifiedBy", source = "ccyExposure.modifiedBy")
  CombinedCcyExposureView toCombinedView(CcyExposure ccyExposure, List<Cashflow> cashflows);

  CashflowView toView(Cashflow cashflow);
}
