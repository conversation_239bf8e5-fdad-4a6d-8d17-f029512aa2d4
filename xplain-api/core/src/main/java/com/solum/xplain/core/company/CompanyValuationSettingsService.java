package com.solum.xplain.core.company;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.form.ValuationSettingsForm;
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository;
import com.solum.xplain.core.company.repository.CompanyValuationSettingsRepository;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.company.value.CompanyLegalEntityView;
import com.solum.xplain.core.company.value.CompanyValuationSettingsView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CompanyValuationSettingsService {
  private final CompanyValuationSettingsRepository repository;
  private final CompanyLegalEntityValuationSettingsRepository legalEntitySettingsrepository;
  private final CompanyLegalEntityService legalEntityControllerService;
  private final CompanyControllerService companyControllerService;
  private final ValuationSettingsNamesResolver resolver;

  public CompanyValuationSettingsService(
      CompanyValuationSettingsRepository repository,
      CompanyLegalEntityValuationSettingsRepository legalEntitySettingsrepository,
      CompanyLegalEntityService legalEntityControllerService,
      CompanyControllerService companyControllerService,
      ValuationSettingsNamesResolver resolver) {
    this.repository = repository;
    this.legalEntitySettingsrepository = legalEntitySettingsrepository;
    this.legalEntityControllerService = legalEntityControllerService;
    this.companyControllerService = companyControllerService;
    this.resolver = resolver;
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateDefaultSettingsVersion(
      String companyId,
      LocalDate versionDate,
      Authentication authentication,
      ValuationSettingsForm form) {
    return companyControllerService
        .getUserCompany(authentication, companyId)
        .flatMap(
            u ->
                repository.updateValuationSettings(
                    companyId,
                    versionDate,
                    form,
                    resolver.getNames(
                        versionDate,
                        form.getMarketDataGroupId(),
                        form.getCurveConfigurationId(),
                        form.getNonFxCurveConfigurationId())));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateCompanyEntitySettingsVersion(
      String companyId,
      String entityId,
      LocalDate versionDate,
      Authentication authentication,
      ValuationSettingsForm form) {
    return legalEntityControllerService
        .userLegalEntity(authentication, companyId, entityId)
        .flatMap(
            u ->
                legalEntitySettingsrepository.updateCompanyEntityValuationSettings(
                    entityId,
                    versionDate,
                    form,
                    resolver.getNames(
                        versionDate,
                        form.getMarketDataGroupId(),
                        form.getCurveConfigurationId(),
                        form.getNonFxCurveConfigurationId())));
  }

  public Either<ErrorItem, List<CompanyLegalEntityValuationSettingsView>>
      getCompanyEntitySettingsVersions(Authentication auth, String companyId, String entityId) {
    return legalEntityControllerService
        .userLegalEntity(auth, companyId, entityId)
        .map(u -> legalEntitySettingsrepository.getCompanyValuationSettingsVersions(entityId));
  }

  public Either<ErrorItem, List<CompanyValuationSettingsView>> getCompanySettingsVersions(
      Authentication auth, String companyId) {
    return companyControllerService
        .getUserCompany(auth, companyId)
        .map(u -> repository.getCompanyValuationSettingsVersions(companyId));
  }

  public Either<ErrorItem, List<CompanyLegalEntityValuationSettingsView>> getAllEntitySettings(
      Authentication auth, String companyId, BitemporalDate stateDate) {
    return legalEntityControllerService
        .list(auth, companyId)
        .map(
            ids ->
                repository
                    .getCompanyEntitySettingsResolver(companyId, stateDate)
                    .settings(ids.stream().map(CompanyLegalEntityView::getId).toList()));
  }

  public Either<ErrorItem, CompanyLegalEntityValuationSettingsView> getEntitySettings(
      Authentication auth, String companyId, String entityId, BitemporalDate stateDate) {
    return legalEntityControllerService
        .userLegalEntity(auth, companyId, entityId)
        .map(
            l ->
                legalEntitySettingsrepository.getCompanyEntitySettingsView(
                    companyId, entityId, stateDate));
  }

  public Either<ErrorItem, CompanyValuationSettingsView> getCompanyValuationSettings(
      Authentication auth, String companyId, BitemporalDate stateDate) {
    return companyControllerService
        .getUserCompany(auth, companyId)
        .map(u -> repository.getDefaultValuationSettings(companyId, stateDate));
  }

  public Either<ErrorItem, DateList> getValuationSettingsFutureVersions(
      Authentication authentication, String companyId, LocalDate stateDate) {
    return companyControllerService
        .getUserCompany(authentication, companyId)
        .map(u -> repository.getFutureVersions(companyId, stateDate));
  }

  public Either<ErrorItem, DateList> getLegalEntitiesSettingsFutureVersions(
      Authentication auth, String companyId, String entityId, LocalDate stateDate) {
    return legalEntityControllerService
        .userLegalEntity(auth, companyId, entityId)
        .map(u -> legalEntitySettingsrepository.getFutureVersions(entityId, stateDate));
  }

  public Either<ErrorItem, EntityId> deleteValuationSettings(
      Authentication auth, String companyId, LocalDate version) {
    return companyControllerService
        .getUserCompany(auth, companyId)
        .flatMap(g -> repository.deleteValuationSettings(companyId, version));
  }

  public Either<ErrorItem, EntityId> deleteEntityValuationSettings(
      Authentication auth, String companyId, String entityId, LocalDate version) {
    return legalEntityControllerService
        .userLegalEntity(auth, companyId, entityId)
        .flatMap(u -> legalEntitySettingsrepository.deleteValuationSettings(entityId, version));
  }
}
