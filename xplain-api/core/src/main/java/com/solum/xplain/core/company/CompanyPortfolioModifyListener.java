package com.solum.xplain.core.company;

import static com.solum.xplain.core.common.CollectionUtils.chunked;

import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.portfolio.event.CompanyPortfoliosModified;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@NullMarked
@Component
public class CompanyPortfolioModifyListener {

  private final CompanyRepository companyRepository;
  private final List<CompanyPortfolioProvider> companyPortfolioProviders;

  public CompanyPortfolioModifyListener(
      CompanyRepository companyRepository,
      List<CompanyPortfolioProvider> companyPortfolioProviders) {
    this.companyRepository = companyRepository;
    this.companyPortfolioProviders = companyPortfolioProviders;
  }

  @EventListener
  public void onCompanyPortfolioModify(CompanyPortfoliosModified event) {
    chunked(event.companyIds().stream()).forEach(this::updatePortfolioCounts);
  }

  private void updatePortfolioCounts(List<String> companyIds) {
    Map<String, Integer> counts =
        companyPortfolioProviders.stream()
            .flatMap(p -> p.countAll(companyIds).entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));

    if (!counts.isEmpty()) {
      companyRepository.updatePortfolioCounts(counts);
    }
  }
}
