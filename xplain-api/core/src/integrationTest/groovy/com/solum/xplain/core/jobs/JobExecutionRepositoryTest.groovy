package com.solum.xplain.core.jobs

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.helper.IntegrationSpecification
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class JobExecutionRepositoryTest extends IntegrationSpecification {

  @Autowired
  JobExecutionRepository testObject

  @Autowired
  MongoOperations mongoOperations

  def "should mark job as started"() {
    setup:
    def jobName = "SUPER_TASK"
    def jobParams = Map.of("super", "param")

    when:
    def result = testObject.start(jobName, jobParams)

    then:
    verifyAll(result) {
      startTime != null
      lastUpdated != null
      endTime == null
      status == JobExecutionStatus.STARTED
      name == jobName
      jobParameters == jobParams
    }
  }

  def "should mark existing job as completed"() {
    setup:
    def job = new JobExecutionBuilder()
      .name("job 1")
      .status(JobExecutionStatus.STARTED)
      .startTime(LocalDateTime.now())
      .build()
    mongoOperations.save(job)

    when:
    testObject.markAsCompleted(job.id)

    then:
    def result = mongoOperations.findById(job.id, JobExecution)
    verifyAll(result) {
      status == JobExecutionStatus.COMPLETED
      name == job.name
      jobParameters == job.jobParameters
      startTime != null
      endTime != null
      lastUpdated != null
    }
  }

  def "should mark unknown job as completed"() {
    setup:
    def job = new JobExecutionBuilder()
      .name("job 1")
      .status(JobExecutionStatus.UNKNOWN)
      .startTime(LocalDateTime.now())
      .build()
    mongoOperations.save(job)

    when:
    testObject.markAsCompleted(job.id)

    then:
    def result = mongoOperations.findById(job.id, JobExecution)
    verifyAll(result) {
      status == JobExecutionStatus.COMPLETED
      name == job.name
      jobParameters == job.jobParameters
      startTime != null
      endTime != null
      lastUpdated != null
    }
  }

  @Unroll
  def "should find all in progress jobs with #jobParams"() {
    setup:
    def name = "job 1"
    def status = JobExecutionStatus.STARTED
    def jobs = [
      new JobExecutionBuilder().name(name).status(status).jobParameters(jobParams).build(),
      new JobExecutionBuilder().name(name).status(status).jobParameters(jobParams).build(),
    ]
    mongoOperations.insert(jobs, JobExecution)
    mongoOperations.insert(new JobExecutionBuilder().name(name).status(JobExecutionStatus.COMPLETED).jobParameters(jobParams).build())

    when:
    def results = testObject.inProgress(name, jobParams)

    then:
    results.size() == 2
    results.id == jobs.id

    where:
    jobParams << [null, Map.of("PARAM_1", "PARAM_1_VALUE")]
  }


  def "should mark still running jobs as UNKNOWN"() {
    setup:
    def jobName = "Job 1"
    def stateDate = BitemporalDate.newOf(LocalDate.parse("2022-11-25"), LocalDateTime.parse("2022-11-25T10:15:00"))
    def frozenJob = new JobExecutionBuilder()
      .name(jobName)
      .status(JobExecutionStatus.STARTED)
      .startTime(LocalDateTime.parse("2022-11-25T10:10:00"))
      .build()
    def startedJob = new JobExecutionBuilder()
      .name(jobName)
      .status(JobExecutionStatus.STARTED)
      .startTime(LocalDateTime.parse("2022-11-25T10:15:00"))
      .build()
    def completedJOb = new JobExecutionBuilder()
      .name(jobName)
      .status(JobExecutionStatus.COMPLETED)
      .startTime(LocalDateTime.parse("2022-11-25T10:12:00"))
      .endTime(LocalDateTime.parse("2022-11-25t10:15:00"))
      .build()
    mongoOperations.insert([frozenJob, startedJob, completedJOb], JobExecution.class)

    when:
    testObject.markAllStartedAsUnknown(jobName, stateDate)

    then:
    def frozenJobResult = mongoOperations.findById(frozenJob.id, JobExecution)
    verifyAll(frozenJobResult) {
      status == JobExecutionStatus.UNKNOWN
      name == jobName
      endTime == null
      startTime != null
      lastUpdated != null
    }

    and:
    def startedJobResult = mongoOperations.findById(startedJob.id, JobExecution)
    verifyAll(startedJobResult) {
      status == JobExecutionStatus.STARTED
      name == jobName
      endTime == null
      startTime != null
      lastUpdated != null
    }
  }
}
