package com.solum.xplain.core.market.service

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.google.common.io.ByteStreams
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.CurrentVersionAction
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataKey
import com.solum.xplain.core.market.MarketDataKeyEntity
import com.solum.xplain.core.market.MarketDataKeyValue
import com.solum.xplain.core.market.MarketDataProviderTicker
import com.solum.xplain.core.users.AuditUser
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class MarketDataKeyUploadServiceIntegrationTest extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  @Resource
  MarketDataKeyUploadService uploadService

  def cleanup() {
    mongoTemplate.dropCollection(MarketDataKeyEntity)
    mongoTemplate.dropCollection(MarketDataKey)
  }

  @Unroll
  def "importing identical MDK should not trash providers with #duplicateAction and #currentVersionAction"() {
    def csvFile = ByteStreams.toByteArray(getClass().getResourceAsStream("/market/csv/SingleMdk.csv"))

    given: "I have an existing MDK set up"
    def marketDataKeyEntity = new MarketDataKeyEntity(
      semanticId: "2MX8M_GBP-LIBOR-6M",
      versions: [
        new EmbeddedVersion<MarketDataKeyValue>(
        validFrom: LocalDate.of(1970, Month.JANUARY, 1),
        recordFrom: LocalDateTime.of(2023, Month.NOVEMBER, 24, 10, 4, 54, 723),
        state: "ACTIVE",
        comment: "Import",
        createdBy: new AuditUser(
        userId: "auth0|63073fedb5df60f77bec5f63",
        username: "Solum",
        name: "Solum"
        ),
        value: new MarketDataKeyValue(
        name: "GBP FRA 2MX8M",
        assetGroup: "RATES",
        instrumentType: "FRA",
        providerTickers: [
          new MarketDataProviderTicker(
          code: "ICAP",
          ticker: "GBPFRA6_8M",
          bidAskType: "MID_ONLY",
          factor: 100
          ),
          new MarketDataProviderTicker(
          code: "TULLETT",
          ticker: "ASFRAGBP02M08M",
          bidAskType: "MID_ONLY",
          factor: 100
          )
        ]
        )
        )
      ]
      )
    mongoTemplate.insert(marketDataKeyEntity)

    def mdk = new MarketDataKey(
      entityId: marketDataKeyEntity.id,
      comment: "Import",
      key: "2MX8M_GBP-LIBOR-6M",
      name: "GBP FRA 2MX8M",
      assetGroup: "RATES",
      instrumentType: "FRA",
      providerTickers: [
        new MarketDataProviderTicker(
        code: "ICAP",
        ticker: "GBPFRA6_8M",
        bidAskType: "MID_ONLY",
        factor: 100
        ),
        new MarketDataProviderTicker(
        code: "TULLETT",
        ticker: "ASFRAGBP02M08M",
        bidAskType: "MID_ONLY",
        factor: 100
        )
      ],
      recordFrom: LocalDateTime.of(2023, Month.NOVEMBER, 24, 10, 4, 54, 723),
      validFrom: LocalDate.of(1970, Month.JANUARY, 1),
      state: "ACTIVE",
      modifiedAt: LocalDateTime.of(2023, Month.NOVEMBER, 24, 10, 4, 54, 723),
      modifiedBy: new AuditUser(
      userId: "auth0|63073fedb5df60f77bec5f63",
      username: "Solum",
      name: "Solum"
      ),
      version: LocalDate.of(1970, Month.JANUARY, 1),
      validities: [
        new DateRangeVersionValidity(
        validTo: LocalDate.of(9999, Month.DECEMBER, 31),
        recordFrom: LocalDateTime.of(2023, Month.NOVEMBER, 24, 10, 4, 54, 723),
        recordTo: LocalDateTime.of(9999, Month.DECEMBER, 31, 0, 0, 0, 0)
        )
      ]
      )
    mongoTemplate.insert(mdk)

    when: "importing identical MDK"
    def response = uploadService.uploadMarketDataKeys(csvFile, new ImportOptions(LocalDate.now(), duplicateAction, STRICT, "Import", currentVersionAction, futureVersionsAction,
      FutureVersionsAction.KEEP, mdk.recordFrom))

    then: "record should be imported successfully"
    response.isRight()

    and: "no new version should be created and providers should be unchanged"
    with(mongoTemplate.find(query(where(MarketDataKey.Fields.key).is(mdk.key)), MarketDataKey)) { it ->
      size() == 1
      it.get(0).entityId == mdk.entityId
      it.get(0).version == mdk.version
      it.get(0).validities.size() == 1
      it.get(0).providerTickers.size() == 2
    }

    where:
    duplicateAction                | currentVersionAction            | futureVersionsAction
    DuplicateAction.REPLACE        | CurrentVersionAction.UPDATE     | FutureVersionsAction.KEEP
    DuplicateAction.REPLACE        | CurrentVersionAction.CREATE_NEW | FutureVersionsAction.KEEP
    DuplicateAction.REPLACE        | CurrentVersionAction.CREATE_NEW | FutureVersionsAction.DELETE
    DuplicateAction.APPEND         | CurrentVersionAction.CREATE_NEW | FutureVersionsAction.KEEP
    DuplicateAction.APPEND_DELETE  | CurrentVersionAction.CREATE_NEW | FutureVersionsAction.KEEP
    DuplicateAction.REPLACE_DELETE | CurrentVersionAction.CREATE_NEW | FutureVersionsAction.KEEP
  }
}
