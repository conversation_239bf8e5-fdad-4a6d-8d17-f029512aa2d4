package com.solum.xplain.core.common.versions


import lombok.experimental.FieldNameConstants
import org.springframework.data.mongodb.core.mapping.Document

@Document
@FieldNameConstants
class TestUniqueVersionedEntity extends VersionedEntity {

  private String name

  String getName() {
    return name
  }

  void setName(String name) {
    this.name = name
  }

  @Override
  boolean valueEquals(Object object) {
    TestUniqueVersionedEntity other = (TestUniqueVersionedEntity) object
    return super.valueEquals(other) && Objects.equals(name, other.name)
  }
}
