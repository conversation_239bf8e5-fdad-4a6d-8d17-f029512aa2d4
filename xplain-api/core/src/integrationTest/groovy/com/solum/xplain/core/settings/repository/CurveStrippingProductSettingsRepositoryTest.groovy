package com.solum.xplain.core.settings.repository

import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.settings.entity.CurveStrippingProductSettings
import com.solum.xplain.core.settings.form.CurveStrippingProductForm
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CurveStrippingProductSettingsRepositoryTest extends IntegrationSpecification {

  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.now(), LocalDateTime.now().plusHours(1))

  @Resource
  MongoOperations operations

  @Resource
  CurveStrippingProductSettingsRepository repository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), CurveStrippingProductSettings.class)
  }

  def "should get product settings"() {
    setup:
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0), state: State.ACTIVE))
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0), state: State.ACTIVE))
    when:
    def result = repository.getCurveStrippingProductSettings(STATE_DATE)
    then:
    result != null
    result.validFrom == ofEpochDay(0)
    result.modifiedAt != null
    result.modifiedBy.name == creator.name
  }

  def "should get product settings view"() {
    setup:
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0), state: State.ACTIVE))

    when:
    def result = repository.entityView(STATE_DATE)
    then:
    result != null
    result.validFrom == ofEpochDay(0)
    result.modifiedAt != null
    result.modifiedBy == creator.name
  }

  def "should get product settings versions"() {
    setup:
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0)))
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0)))
    when:
    def result = repository.entityVersions()
    then:
    result.size() == 2
    result[0].validFrom == ofEpochDay(0)
    result[1].validFrom == ofEpochDay(0)
  }

  def "should save product settings"() {
    setup:
    operations.insert(new CurveStrippingProductSettings(validFrom: ofEpochDay(0)))
    def form = new CurveStrippingProductForm.CurveStrippingProductFormBuilder()
      .cdsCurvePriorities(["TENOR_3M"])
      .fxCurvePriorities(["TENOR_3M"])
      .fxCcyFallback("USD")
      .fxCcyOverride("USD")
      .fxCcyPriority([])
      .inflationCurvePriorities(["OIS"])
      .xccyCcyFallback("EUR")
      .versionForm(new NewVersionFormV2("comment", ofEpochDay(1), LocalDate.now(), FutureVersionsAction.KEEP))
      .build()

    when:
    def result = repository.saveProductSettings(ofEpochDay(0), form)

    then:
    result.isRight()
    def loaded = operations.findAll(CurveStrippingProductSettings)
    loaded.size() == 2
    loaded[1].validFrom == ofEpochDay(1)
    loaded[1].comment == "comment"
    loaded[1].cdsCurvePriorities == ["TENOR_3M"]
    loaded[1].fxCurvePriorities == ["TENOR_3M"]
    loaded[1].fxCcyFallback == "USD"
    loaded[1].fxCcyOverride == "USD"
    loaded[1].fxCcyPriority == []
    loaded[1].inflationCurvePriorities == ["OIS"]
    loaded[1].xccyCcyFallback == "EUR"
  }

  def "should delete product settings version"() {
    setup:
    def settings = new CurveStrippingProductSettings(validFrom: ofEpochDay(1))
    operations.insert(settings)
    when:
    def result = repository.deleteProductSettingsVersion(ofEpochDay(1))
    then:
    result.isRight()
    def loaded = operations.findAll(CurveStrippingProductSettings)
    loaded.size() == 2
    loaded[1].state == State.DELETED
  }

  def "should fail delete product settings ROOT version"() {
    setup:
    def settings = new CurveStrippingProductSettings(validFrom: ofEpochDay(0))
    operations.insert(settings)
    when:
    def result = repository.deleteProductSettingsVersion(ofEpochDay(0))
    then:
    result.isLeft()
    result.left().get().reason == Error.OPERATION_NOT_ALLOWED
  }

  def "should get discount settings future versions dates list"() {
    setup:
    def v1 = new CurveStrippingProductSettings(validFrom: ofEpochDay(0))
    def v2 = new CurveStrippingProductSettings(validFrom: ofEpochDay(1))
    def v3 = new CurveStrippingProductSettings(validFrom: ofEpochDay(2))
    def v4 = new CurveStrippingProductSettings(validFrom: ofEpochDay(3))
    def v5 = new CurveStrippingProductSettings(validFrom: ofEpochDay(4), state: State.DELETED)
    operations.insertAll([v1, v2, v3, v4, v5])

    when:
    def result = repository.futureVersions(ofEpochDay(1))

    then:
    result.dates == [ofEpochDay(2), ofEpochDay(3)]
  }
}
