package com.solum.xplain.core.curvegroup.curvegroup

import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupViewBuilder.curveGroupView
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_VOL
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_VOL_SKEW
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.SWAPTION_ATM
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.SWAPTION_SKEW
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofBondCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFx
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFxVol
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofVol
import static com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder.surfaceWithNodes
import static com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNodeBuilder.sampleFxOptVolNode
import static com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey.configMarketKey
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.time.LocalDate.parse
import static org.springframework.data.mongodb.core.query.Criteria.where

import com.solum.xplain.core.audit.entity.AuditLog
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.AllowedCompaniesForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveBuilder
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationMarketData
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupFilter
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesBuilder
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNodeBuilder
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNodeBuilder
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.MarketDataGroupBuilder
import com.solum.xplain.core.market.events.MarketDataGroupUpdated
import com.solum.xplain.core.market.value.MarketDataGroupForm
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.core.users.events.UserUpdated
import com.solum.xplain.core.users.value.EditUserForm
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Direction
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupRepositoryTest extends IntegrationSpecification {

  @Resource
  CurveGroupRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), MarketDataGroup)
    operations.remove(new Query(), Curve)
    operations.remove(new Query(), CreditCurve)
    operations.remove(new Query(), VolatilitySurface)
    operations.remove(new Query(), CurveGroupFxRates)
    operations.remove(new Query(), CurveGroupFxVolatility)
    operations.remove(new Query(), CurveGroup)
    operations.remove(new Query(), BondCurve)
  }

  def "should load curve group list as list"() {
    setup:
    def marketData = new MarketDataGroupBuilder().build()
    operations.insert(marketData)
    def group = new CurveGroupBuilder()
      .calibrationMarketDataGroup(new CalibrationMarketData(marketData, RAW_PRIMARY, "configId"))
      .build()
    def group2 = new CurveGroupBuilder()
      .archived(true)
      .build()
    operations.insertAll([group, group2])

    when:
    def loaded = repository.curveGroupList()

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.name == group.name
    view.updatedAt != null
    view.creatorName == "Full Name"
    view.creatorId == "userId"
    view.calibrationMarketDataGroupName == marketData.name
    view.calibrationMarketDataGroupId == marketData.id
  }

  def "should load curve group list"() {
    setup:
    def marketData = new MarketDataGroupBuilder().build()
    operations.insert(marketData)
    def group = new CurveGroupBuilder()
      .calibrationMarketDataGroup(new CalibrationMarketData(marketData, RAW_PRIMARY, "configId"))
      .calibrationCurrency(CalculationDiscountingType.LOCAL_CURRENCY)
      .calibrationStrippingType(CalculationStrippingType.LIBOR)
      .calibrationPriceRequirements(bidRequirements())
      .auditLogs([new AuditLog()])
      .build()
    operations.insert(group)
    def filter = new CurveGroupFilter()
    def tableFilter = emptyTableFilter()
    when:
    def loaded = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1, Sort.by(Direction.ASC, "name")),
      tableFilter,
      filter)
    then:
    loaded.lastRow == 1
    def view = loaded.content[0]
    view.name == group.name
    view.updatedAt != null
    view.creatorName == "Full Name"
    view.creatorId == "userId"
    view.numberOfCurves == 0
    view.numberOfCreditCurves == 0
    view.numberOfVolatilitySurfaces == 0
    view.numberOfFxVolatilities == 0
    view.numberOfFxRateNodes == 0
    view.calibrationMarketDataGroupName == marketData.name
    view.calibrationMarketDataGroupId == marketData.id
    view.calibrationCurrency == CalculationDiscountingType.LOCAL_CURRENCY
    view.calibrationStrippingType == CalculationStrippingType.LIBOR
    view.calibrationPriceRequirements == bidRequirements()
    view.auditLogs.size() == 1
  }

  def "should load archived curve group"() {
    setup:
    def group = new CurveGroupBuilder().archived(true).build()
    operations.insert(group)

    when:
    def loaded = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      emptyTableFilter(),
      new CurveGroupFilter(archived: true))

    then:
    loaded.lastRow == 1
    loaded.content[0].archived

    when:
    loaded = repository.curveGroupCountedList(
      LocalDate.now(),
      ScrollRequest.of(0, 1),
      emptyTableFilter(),
      new CurveGroupFilter(archived: false))

    then:
    loaded.lastRow == null
  }


  def "should insert curve group"() {
    setup:
    def form = new CurveGroupForm()
    form.name = "Name"

    when:
    def result = repository.insert(form).right().get()

    then:
    result.id != null
    def loaded = operations.findOne(new Query(where("id").is(result.id)), CurveGroup.class)
    loaded.name == "Name"
    loaded.modifiedBy.name == "Full Name"
  }


  def "should update curve group"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def edit = new CurveGroupForm()
    edit.name = "New name"
    when:
    def result = repository.update(curveGroupView(group), edit)

    then:
    result.isRight()
    result.right().get().id == group.id
    def loaded = operations.findOne(new Query(where("id").is(result.getOrNull().id)), CurveGroup.class)
    loaded.name == "New name"
    loaded.auditLogs.size() == 1
  }

  def "should update curve group with no correlation matrix"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def edit = new CurveGroupForm()
    edit.name = "New name"

    when:
    def result = repository.update(curveGroupView(group), edit)

    then:
    result.isRight()
    result.right().get().id == group.id
  }

  def "should return false while checking if curve group exists with different name"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    when:
    def result = repository.hasCurveGroupByName("anotherName", null)

    then:
    !result
  }

  def "should return false while checking if archived curve group exists with same name"() {
    setup:
    def group = new CurveGroupBuilder().archived(true).build()
    operations.insert(group)

    when:
    def result = repository.hasCurveGroupByName("Name", null)

    then:
    !result
  }

  def "should verify that if curve group already exists with same name"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    when:
    def withoutExcludingSelfExists = repository.hasCurveGroupByName(group.name, null)
    def excludingSelftExists = repository.hasCurveGroupByName(group.name, group.id)

    then:
    withoutExcludingSelfExists
    !excludingSelftExists
  }

  def "updateCurveGroupCalibration should update calibration"() {
    setup:
    def marketData = new MarketDataGroupBuilder().build()
    operations.insert(marketData)
    def group = curveGroup()
    operations.insert(group)

    def stateKey = configMarketKey(
      marketData.id,
      "curveConfigurationId",
      RAW_PRIMARY,
      BitemporalDate.newOf(LocalDate.ofEpochDay(0)),
      LocalDate.ofEpochDay(0),
      bidRequirements()
      )
    def valuationDate = LocalDate.ofEpochDay(0)

    when:
    def loaded = repository.updateCurveGroupCalibration(group.getId(), CalculationDiscountingType.LOCAL_CURRENCY, CalculationStrippingType.LIBOR, valuationDate, stateKey)

    then:
    loaded.isRight()
    def result = operations.findById(group.id, CurveGroup)
    result.calibrationStatus == CalibrationStatus.CALIBRATED
    result.calibrationDate == LocalDate.ofEpochDay(0)
    result.calibrationMarketDataGroup.marketDataGroupId == marketData.id
    result.calibrationMarketDataGroup.marketDataGroupName == marketData.name
    result.calibrationMarketDataGroup.curveConfigurationId == "curveConfigurationId"
    result.calibrationMarketDataGroup.sourceType == RAW_PRIMARY
    LocalDateTime.now().isAfter(result.calibratedAt)
    result.calibrationCurrency == CalculationDiscountingType.LOCAL_CURRENCY
    result.calibrationStrippingType == CalculationStrippingType.LIBOR
    result.calibrationPriceRequirements == bidRequirements()
  }

  def "clearCalibrationResults should clear calibration"() {
    setup:
    def valuationDate = parse("2017-01-01")
    def group = new CurveGroupBuilder()
      .calibrationDate(valuationDate)
      .calibrationStatus(CalibrationStatus.CALIBRATED)
      .calibrationCurrency(CalculationDiscountingType.DISCOUNT_EUR)
      .calibrationStrippingType(CalculationStrippingType.LIBOR)
      .calibrationPriceRequirements(bidRequirements())
      .build()
    operations.insert(group)

    when:
    def loaded = repository.clearCalibrationResults(group.id)

    then:
    loaded.isRight()
    def result = operations.findById(group.id, CurveGroup)
    result.calibrationStatus == CalibrationStatus.NOT_CALIBRATED
    result.calibrationCurrency == null
    result.calibrationStrippingType == null
    result.calibrationPriceRequirements == null
    result.calibrationDate == null
    result.calibratedAt == null
  }

  def "should load full curve group"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    when:
    def loaded = repository.getFull(group.id)

    then:
    loaded.isRight()
  }

  def "should not load full curve group if archived"() {
    setup:
    def group = new CurveGroupBuilder()
      .archived(true)
      .build()
    operations.insert(group)

    when:
    def loaded = repository.getFull(group.id)

    then:
    loaded.left
    loaded.left().get().reason == Error.OBJECT_NOT_FOUND
  }

  def "should archive curve group"() {
    setup:
    def group = new CurveGroupBuilder().build()
    operations.insert(group)

    when:
    def result = repository.archive(group.id)

    then:
    result.isRight()

    when:
    def groups = operations.findAll(CurveGroup)

    then:
    !groups.empty
    groups[0].archived
  }

  def "should update market data group name"() {
    setup:
    def marketData = new MarketDataGroupBuilder().build()
    operations.insert(marketData)
    def group = new CurveGroupBuilder()
      .calibrationMarketDataGroup(new CalibrationMarketData(marketData, RAW_PRIMARY, "configId"))
      .build()
    operations.insert(group)

    def edit = new MarketDataGroupForm("New name", null, new AllowedCompaniesForm(true, []), new AllowedTeamsForm(true, []))

    when:
    repository.onMarketDataGroupUpdated(new MarketDataGroupUpdated(marketData.id, edit))

    then:
    def loaded = operations.findOne(new Query(where("id").is(group.id)), CurveGroup.class)
    loaded.calibrationMarketDataGroup.marketDataGroupName == "New name"
  }

  def "should update creator name on user update event"() {
    setup:
    def group = curveGroup()

    operations.insert(group)

    def form = new EditUserForm(
      name: "Updated name",
      username: "updated_username"
      )

    when:
    repository.onEvent(new UserUpdated(user.id, form))

    then:
    def result = operations.findById(group.id, CurveGroup)
    result.createdBy.name == "Updated name"
    result.createdBy.username == "updated_username"
    result.modifiedBy.name == "Updated name"
    result.modifiedBy.username == "updated_username"
  }

  def "should return correct instruments"() {
    setup:
    def rd = { e -> e.setRecordDate(STATE_DATE.recordDate.minusSeconds(1)) }

    operations.insert(eur3m().tap(rd))
    operations.insert(new CurveGroupFxRatesBuilder()
      .entityId("curveGroupId")
      .nodes([new CurveGroupFxRatesNodeBuilder().build()] as Set)
      .build()
      .tap(rd))
    operations.insert(surfaceWithNodes().tap(rd))
    operations.insert(new CurveGroupFxVolatilityBuilder()
      .entityId("curveGroupId")
      .nodes([sampleFxOptVolNode()] as Set)
      .build()
      .tap(rd))
    operations.insert(new BondCurveBuilder().build().tap(rd))

    when:
    def result = repository.allInstruments("curveGroupId", STATE_DATE)

    then:
    result.size() == 27
    result[0] == ofIrCurve("EUR", "EUR 3M", FIXED_IBOR_SWAP,
      "1D", "1D", "1D_EUR-FIXED-1Y-EURIBOR-3M", "EUR 12M 30U/360 VS 3M SWAP 1D")
    result[1] == ofIrCurve("EUR", "EUR 3M", FIXED_IBOR_SWAP,
      "1Y", "1Y", "1Y_EUR-FIXED-1Y-EURIBOR-3M", "EUR 12M 30U/360 VS 3M SWAP 1Y")
    result[2] == ofVol("EUR 3M Vols", "EUR", SWAPTION_ATM, "1YV1Y_ATM_EUR-EURIBOR-3M", "EUR 3M VOLS SWO ATM " +
      "1YV1Y", "1Y", "1Yx1Y", "EUR 3M")
    result[6] == ofVol("EUR 3M Vols", "EUR", SWAPTION_SKEW, "1YV1Y_+1_EUR-EURIBOR-3M", "EUR 3M VOLS SWO +1BP " +
      "1YV1Y", "1Y", "1Yx1Y", "EUR 3M")
    result[20] == ofFx(CoreAssetClass.FX_RATES, "EUR/USD", FX_RATE, "EUR/USD", "EUR/USD SPOT")
    result[21] == ofFxVol(FX_VOL, "EUR/USD", "1Y", "1Y_EUR/USDV", "EUR/USD ATM FX VOLS 1Y")
    result[22] == ofFxVol(FX_VOL_SKEW, "EUR/USD", "1Y", "1Y_EUR/USD1B", "EUR/USD 1D BF FX VOLS 1Y")
    result[23] == ofFxVol(FX_VOL_SKEW, "EUR/USD", "1Y", "1Y_EUR/USD1R", "EUR/USD 1D RR FX VOLS 1Y")
    result[24] == ofFxVol(FX_VOL_SKEW, "EUR/USD", "1Y", "1Y_EUR/USD2B", "EUR/USD 2D BF FX VOLS 1Y")
    result[25] == ofFxVol(FX_VOL_SKEW, "EUR/USD", "1Y", "1Y_EUR/USD2R", "EUR/USD 2D RR FX VOLS 1Y")
    result[26] == ofBondCurve("UKGT", "GBP", "31DEC2023_CUSIP", "31DEC2023 CUSIP")
  }

  def "should return correct group with entries counts"() {
    setup:
    operations.insert(eur3m())
    operations.insert(new CurveGroupFxRatesBuilder()
      .entityId("curveGroupId")
      .nodes([new CurveGroupFxRatesNodeBuilder().build()] as Set)
      .build())
    operations.insert(surfaceWithNodes())
    operations.insert(new CurveGroupFxVolatilityBuilder()
      .entityId("curveGroupId")
      .nodes([
        sampleFxOptVolNode(),
        new CurveGroupFxVolatilityNodeBuilder().foreignCurrency("GBP").build()
      ] as Set)
      .build())
    operations.insert(new CreditCurveBuilder()
      .curveGroupId("curveGroupId")
      .build())
    operations.insert(new BondCurveBuilder().build())
    def group = curveGroup()
    group.id = "curveGroupId"
    operations.insert(group)
    when:
    def result = repository.getCountedEither(LocalDate.now(), "curveGroupId")
    then:
    result.isRight()
    def view = result.getOrNull()
    view.numberOfCurves == 1
    view.numberOfFxRateNodes == 1
    view.numberOfFxVolatilities == 2
    view.numberOfVolatilitySurfaces == 1
    view.numberOfCreditCurves == 1
    view.numberOfBondCurves == 1
  }

  def "getCurveGroups returns map of id to Either with CurveGroupView for inserted groups"() {
    given:
    def group1 = new CurveGroupBuilder().name("Group1").build()
    def group2 = new CurveGroupBuilder().name("Group2").build()
    def group3 = new CurveGroupBuilder().name("Group3").build()
    operations.insertAll([group1, group2, group3])

    when:
    def result = repository.getCurveGroups([group1.id, group2.id, "notfound"])

    then:
    result.size() == 3
    result[group1.id].isRight()
    result[group2.id].isRight()
    result["notfound"].isLeft()
    result[group1.id].right().get().name == "Group1"
    result[group2.id].right().get().name == "Group2"
    result["notfound"].left().get().reason == Error.OBJECT_NOT_FOUND
  }
}
