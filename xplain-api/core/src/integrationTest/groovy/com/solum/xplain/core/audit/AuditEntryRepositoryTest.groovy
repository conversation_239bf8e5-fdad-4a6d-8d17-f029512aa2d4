package com.solum.xplain.core.audit

import static com.solum.xplain.core.common.GroupRequest.emptyGroupRequest
import static com.solum.xplain.core.error.Error.CALCULATION_ERROR
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter

import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.audit.entity.AuditEntryItem
import com.solum.xplain.core.audit.value.AuditEntryItemView
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class AuditEntryRepositoryTest extends IntegrationSpecification {

  @Resource
  AuditEntryRepository repository
  @Resource
  MongoOperations operations

  static XplainPrincipal creator = user("userId")

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), AuditEntry)
    operations.remove(new Query(), AuditEntryItem)
  }

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def "should insert audit entry"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    when:
    def result = repository.insert(entry)

    then:
    result != null
    result.eventCount == 0
  }

  def "should get audit entry scrollable for collection"() {
    setup:
    AuditEntry entry1 = AuditEntry.of("collection", "description1")
    entry1.eventCount = 1
    operations.insert(entry1)
    AuditEntry entry2 = AuditEntry.of("collection", "description2")
    entry2.eventCount = 2
    operations.insert(entry2)
    when:
    def result = repository.auditEntryScroll(ScrollRequest.of(0, 10), LocalDateTime.now().minusDays(1))
    then:
    result.content != null
    result.content.size() == 2
    def res = result.content.get(0)
    res.date != null
    res.id == entry2.id
    res.description == entry2.description
    res.eventCount == 2
  }

  def "should get audit empty scrollable when different user"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    entry.description = "description"
    operations.insert(entry)
    when:
    def result = repository.auditEntryScroll(
      ScrollRequest.of(0, 10),
      LocalDateTime.now().minusDays(1))
    then:
    result.content != null
    result.content.size() == 1
  }

  def "should get audit entry by reference and collection name"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description", "id")
    operations.insert(entry)
    when:
    def result = repository.auditEntryByReference("collection", "id")
    then:
    result.isRight()
  }

  def "should get audit entry logs"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    entry.description = "description"
    operations.insert(entry)
    operations.insert(AuditEntryItem.fromErrorItem(entry.id, OPERATION_NOT_ALLOWED.entity()))
    when:
    def result = repository.entryLog(
      entry.id,
      emptyTableFilter(),
      ScrollRequest.of(0, 10, Sort.by(AuditEntryItemView.Fields.reason)),
      emptyGroupRequest())
    then:
    result.isRight()
    result.rightOr(null).content.size() == 1
    result.rightOr(null).content.get(0).reason == OPERATION_NOT_ALLOWED
  }

  def "should get audit entry stream"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    operations.insert(entry)
    operations.insert(AuditEntryItem.fromErrorItem(entry.id, OPERATION_NOT_ALLOWED.entity("Desc1")))
    operations.insert(AuditEntryItem.fromErrorItem(entry.id, OBJECT_NOT_FOUND.entity("Desc2")))
    AuditEntry entry2 = AuditEntry.of("collection", "description")
    operations.insert(entry2)

    when:
    def result = repository.auditLogStream(creator)
      .toList()
    then:
    result.size() == 2
    result.get(0).description == "description"
    result.get(0).entryItemList.size() == 2
    result.get(0).entryItemList.get(0).description == "Desc1"
    result.get(0).entryItemList.get(0).reason == OPERATION_NOT_ALLOWED
    result.get(0).entryItemList.get(1).description == "Desc2"
    result.get(0).entryItemList.get(1).reason == OBJECT_NOT_FOUND
    result.get(1).description == "description"
  }


  def "should get empty audit entry logs, when no logs present"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")

    entry.description = "description"
    operations.insert(entry)
    when:
    def result = repository.entryLog(
      entry.id,
      emptyTableFilter(),
      ScrollRequest.of(0, 10),
      emptyGroupRequest())
    then:
    result.isRight()
    result.rightOr(null).content.size() == 0
  }


  def "should add entry logs"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    entry.description = "description"
    operations.insert(entry)

    when:
    repository.addLogs(entry, [CALCULATION_ERROR.entity("err")])

    then:
    def loaded = operations.findAll(AuditEntryItem.class)
    loaded.size() == 1
    loaded[0].reason == CALCULATION_ERROR
    loaded[0].description == "err"

    def loadedAuditEntry = operations.findAll(AuditEntry.class)[0]
    loadedAuditEntry.eventCount == 1
  }

  def "should add entry logs without audit user"() {
    setup:
    AuditEntry entry = AuditEntry.of("collection", "description")
    entry.id = ObjectId.get().toHexString()
    operations.insert(entry)

    when:
    repository.addLogs(entry, [CALCULATION_ERROR.entity("err")])

    then:
    def loaded = operations.findAll(AuditEntryItem.class)
    loaded.size() == 1
    loaded[0].reason == CALCULATION_ERROR
    loaded[0].description == "err"

    def loadedAuditEntry = operations.findAll(AuditEntry.class)[0]
    loadedAuditEntry.eventCount == 1
  }
}
