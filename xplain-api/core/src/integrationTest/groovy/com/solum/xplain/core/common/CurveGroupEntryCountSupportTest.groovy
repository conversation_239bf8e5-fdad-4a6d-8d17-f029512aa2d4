package com.solum.xplain.core.common

import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter.listOfGroups
import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter.singleGroup
import static java.time.LocalDate.now
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesBuilder
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupEntryCountSupportTest extends IntegrationSpecification {
  static String CURVE_GROUP_ID = ObjectId.get().toHexString()

  @Resource
  CurveGroupEntryCountSupport countSupport
  @Resource
  MongoOperations operations

  def cleanup() {
    operations.remove(new Query(), CurveGroupFxVolatility)
    operations.remove(new Query(), Curve)
  }

  def "should calculate correct currency pairs count of fx volatilities"() {
    setup:
    def fxVol = new CurveGroupFxVolatilityBuilder()
      .entityId(CURVE_GROUP_ID)
      .validFrom(LocalDate.parse("2020-01-01"))
      .state(State.ACTIVE)
      .nodes([
        new CurveGroupFxVolatilityNode(
        expiry: "1Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 10,
        delta2: 20,
        ),
        new CurveGroupFxVolatilityNode(
        expiry: "2M",
        domesticCurrency: "EUR",
        foreignCurrency: "GBP"
        ),
        new CurveGroupFxVolatilityNode(
        expiry: "3M",
        domesticCurrency: "EUR",
        foreignCurrency: "AUD"
        ),
        new CurveGroupFxVolatilityNode(
        expiry: "2M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD"
        ),
      ]
      as Set)
      .build()
    operations.insert(fxVol)
    def counter = countSupport.activeFxVolsEntriesCount(singleGroup(now(), CURVE_GROUP_ID))
    expect:
    counter.size() == 1
    counter[0].count == 3
  }

  def "should calculate correct fx rates count"() {
    setup:
    def fxVol = new CurveGroupFxRatesBuilder()
      .entityId(CURVE_GROUP_ID)
      .validFrom(LocalDate.parse("2020-01-01"))
      .state(State.ACTIVE)
      .nodes([
        new CurveGroupFxRatesNode(
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        ),
        new CurveGroupFxRatesNode(
        domesticCurrency: "EUR",
        foreignCurrency: "GBP"
        ),
        new CurveGroupFxRatesNode(
        domesticCurrency: "EUR",
        foreignCurrency: "AUD"
        )
      ]
      as Set)
      .build()
    operations.insert(fxVol)
    def counter = countSupport.activeFxRateEntriesCount(singleGroup(now(), CURVE_GROUP_ID))
    expect:
    counter.size() == 1
    counter[0].count == 3
  }

  @Unroll
  def "should return empty currency pair counter when nodes not active"() {
    setup:
    def fxVol = new CurveGroupFxVolatilityBuilder()
      .entityId(CURVE_GROUP_ID)
      .validFrom(LocalDate.parse("2020-01-01"))
      .state(state)
      .nodes([
        new CurveGroupFxVolatilityNode(
        expiry: "1Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 10,
        delta2: 20,
        ),
        new CurveGroupFxVolatilityNode(
        expiry: "2M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ]
      as Set)
      .build()
    operations.insert(fxVol)
    def counter = countSupport.activeFxVolsEntriesCount(singleGroup(date, CURVE_GROUP_ID))
    expect:
    counter.size() == 0

    where:
    state          | date
    State.ARCHIVED | now()
    State.DELETED  | now()
    State.ACTIVE   | ofEpochDay(0)
  }

  def "should return currency pair counter for list of curve groups"() {
    setup:
    def fxVol1 = new CurveGroupFxVolatilityBuilder()
      .entityId("cgId1")
      .validFrom(LocalDate.parse("2020-01-01"))
      .state(State.ACTIVE)
      .nodes([
        new CurveGroupFxVolatilityNode(
        expiry: "1Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 10,
        delta2: 20,
        )
      ]
      as Set)
      .build()
    def fxVol2 = new CurveGroupFxVolatilityBuilder()
      .entityId("cgId2")
      .validFrom(LocalDate.parse("2020-01-01"))
      .state(State.ACTIVE)
      .nodes([
        new CurveGroupFxVolatilityNode(
        expiry: "1Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 10,
        delta2: 20,
        ),
        new CurveGroupFxVolatilityNode(
        expiry: "2M",
        domesticCurrency: "EUR",
        foreignCurrency: "GBP")
      ] as Set)
      .build()
    operations.insertAll([fxVol1, fxVol2])
    def counter = countSupport.activeFxVolsEntriesCount(listOfGroups(now(), ["cgId1", "cgId2"]))
    expect:
    counter.size() == 2
    assert counter.stream()
    .filter({ c -> c.curveGroupId == "cgId1" })
    .allMatch({ c -> c.count == 1 })

    assert counter.stream()
    .filter({ c -> c.curveGroupId == "cgId2" })
    .allMatch({ c -> c.count == 2 })
  }

  def "should calculate curve group entries"() {
    setup:
    def curve1 = new CurveBuilder()
      .curveGroupId(CURVE_GROUP_ID)
      .build()
    def curve1_2 = new CurveBuilder()
      .entityId(curve1.entityId)
      .curveGroupId(CURVE_GROUP_ID)
      .state(State.ARCHIVED)
      .validFrom(now().plusDays(1))
      .build()
    def curve2 = new CurveBuilder()
      .curveGroupId(CURVE_GROUP_ID)
      .state(State.ARCHIVED)
      .build()
    operations.insertAll([curve1, curve1_2, curve2])
    def counter = countSupport.activeEntriesCount(singleGroup(date, CURVE_GROUP_ID), Curve)
    expect:
    counter == expected
    where:
    date              | expected
    now()             | [new CurveGroupEntryCount(curveGroupId: CURVE_GROUP_ID, count: 1)]
    now().plusDays(2) | []
  }
}
