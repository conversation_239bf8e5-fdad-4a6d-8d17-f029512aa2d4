package com.solum.xplain.core.test;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

@EnableWebSecurity
@EnableMethodSecurity(proxyTargetClass = true)
@TestConfiguration
public class TestSecurityConfig {}
