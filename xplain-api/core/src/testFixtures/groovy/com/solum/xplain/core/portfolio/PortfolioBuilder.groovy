package com.solum.xplain.core.portfolio

import com.solum.xplain.core.company.CompanyBuilder
import com.solum.xplain.core.company.CompanyLegalEntityBuilder
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.company.value.CompanyLegalEntityView
import com.solum.xplain.core.users.AuditUser
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDateTime

@Builder(builderStrategy = ExternalStrategy, forClass = Portfolio)
class PortfolioBuilder {
  public static final String EXT_PORTFOLIO_ID = "extPortfolioId"
  public static final String NAME = "Portfolio name"

  PortfolioBuilder() {
    name(NAME)
    externalPortfolioId(EXT_PORTFOLIO_ID)
    company(CompanyReference.of(CompanyBuilder.company()))
    entity(CompanyLegalEntityReference.of(new CompanyLegalEntityView(id: "entityId", externalId: CompanyLegalEntityBuilder.EXTERNAL_ENTITY_ID, name: "name")))
    allowAllTeams(true)
    createdBy(new AuditUser("id", "username", "Full Name"))
    createdAt(LocalDateTime.now())
    lastModifiedBy(new AuditUser("id", "username", "Full Name"))
    lastModifiedAt(LocalDateTime.now())
  }

  static Portfolio portfolio(AuditUser auditUser) {
    return new PortfolioBuilder()
      .createdBy(auditUser)
      .build()
  }

  static Portfolio portfolio() {
    return new PortfolioBuilder().build()
  }
}
