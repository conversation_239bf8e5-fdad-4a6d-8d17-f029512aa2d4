package com.solum.xplain.core.curvegroup.curvecredit.entity

import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CreditCurveIndexNode, includeSuperProperties = true)
class CreditCurveIndexNodeBuilder {
  CreditCurveIndexNodeBuilder() {
    tenor("1Y")
  }

  static creditCurveIndexNode() {
    new CreditCurveIndexNodeBuilder().build()
  }

  static creditCurveIndexNode(String period) {
    new CreditCurveIndexNodeBuilder()
      .tenor(period)
      .type(CreditCurveNodeType.CREDIT_INDEX)
      .build()
  }

  static creditCurveIndexTrancheNode(String period) {
    new CreditCurveIndexNodeBuilder()
      .tenor(period)
      .type(CreditCurveNodeType.CREDIT_INDEX_TRANCHE)
      .build()
  }
}
