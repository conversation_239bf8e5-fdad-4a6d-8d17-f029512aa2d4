package com.solum.xplain.core.providers

import com.solum.xplain.core.providers.enums.DataProviderType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = DataProvider)
class DataProviderBuilder {
  DataProviderBuilder() {
    name("Test provider")
    types(Set.of(DataProviderType.MARKET, DataProviderType.VALUATION))
  }
}
