package com.solum.xplain.core.portfolio

import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.parse

import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.VersionedDataAggregations
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import com.solum.xplain.core.portfolio.trade.CustomTradeField
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.trade.OnboardingDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.portfolio.trade.TradeValue
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.extensions.enums.PositionType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDate
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy,
forClass = PortfolioItem,
includeSuperProperties = true)
class PortfolioItemBuilder {

  PortfolioItemBuilder(String tradeId = "externalTradeId") {
    entityId(ObjectId.get().toHexString())
    externalCompanyId("externalCompanyId")
    externalEntityId("externalEntityId")
    externalPortfolioId("extPortfolioId")
    externalTradeId(tradeId)
    valuationDataKey("externalCompanyId_externalEntityId_extPortfolioId_${tradeId}")
    state(State.ACTIVE)
    version(LocalDate.ofEpochDay(0))
    validFrom(LocalDate.ofEpochDay(0))
    recordFrom(LocalDate.ofEpochDay(0).atStartOfDay())
    clientMetrics(new ClientMetrics(presentValue: 10.0))
    validities([
      new DateRangeVersionValidity(VersionedDataAggregations.MAX_DATE, LocalDate.ofEpochDay(0).atStartOfDay(), VersionedDataAggregations.MAX_DATE_TIME)
    ])
    onboardingDetails(new OnboardingDetails(vendorOnboardingDate: parse("2024-02-01"), dealCost: 1, accountingCost: 2, xplainCostCheck: true))
    modifiedBy(AuditUser.of(user("userId")))
    modifiedAt(LocalDate.ofEpochDay(0).atStartOfDay())
  }

  def static irsPortfolioItem(String portfolioId = "000000000000000000000001",
    String tradeId = "tradeId",
    LocalDate tradeDate = parse("2018-04-12")) {
    return trade(CoreProductType.IRS, TradeDetailsBuilder.irsDetails(tradeDate), portfolioId, tradeId)
  }

  def static xccyPortfolioItem(String portfolioId = "000000000000000000000001",
    String tradeId = "tradeId",
    LocalDate tradeDate = parse("2018-04-12")) {
    return trade(CoreProductType.XCCY, TradeDetailsBuilder.xccySwap(tradeDate), portfolioId, tradeId)
  }

  def static cdsPortfolioItem(String portfolioId = "000000000000000000000001",
    String tradeId = "tradeId",
    LocalDate tradeDate = parse("2018-04-12")) {
    return trade(CoreProductType.CDS, TradeDetailsBuilder.cdsTradeDetails(tradeDate), portfolioId, tradeId)
  }

  def static trade(ProductType productType,
    TradeDetails details,
    String portfolioId = "000000000000000000000001",
    String tradeId = "externalTradeId") {
    return new PortfolioItemBuilder(tradeId)
      .portfolioId(new ObjectId(portfolioId))
      .productType(productType)
      .tradeDetails(details)
      .externalIdentifiers([new ExternalIdentifier("externalSource", "externalSourceId")])
      .customFields([new CustomTradeField("F1", "value1")])
      .build()
  }

  def static allocationTrade(String portfolioId = "000000000000000000000001",
    String tradeId = "externalTradeId") {
    def details = new AllocationTradeDetails(referenceTradeId: "refId", allocationNotional: 1000, positionType: PositionType.BUY, counterParty: "cp", counterPartyType: CounterpartyType.BILATERAL, clientMetrics: new ClientMetrics(presentValue: 123), description: "comments")
    allocationTrade(details, portfolioId, tradeId)
  }

  def static allocationTrade(AllocationTradeDetails details,
    String portfolioId = "000000000000000000000001",
    String tradeId = "externalTradeId") {
    return new PortfolioItemBuilder(tradeId)
      .portfolioId(new ObjectId(portfolioId))
      .productType(CoreProductType.IRS)
      .tradeDetails(TradeDetailsBuilder.overnightSwap(LocalDate.parse("2017-12-20"), "INFO_CP", CounterpartyType.CLEARED))
      .allocationTradeDetails(details)
      .version(LocalDate.ofEpochDay(1))
      .build()
  }

  static PortfolioItemEntity toData(PortfolioItem item, LocalDate validFrom = item.getValidFrom()) {
    def value = new TradeValue(
      description: item.getDescription(),
      productType: item.getProductType(),
      tradeDetails: item.getTradeDetails(),
      clientMetrics: item.getClientMetrics(),
      allocationTradeDetails: item.getAllocationTradeDetails(),
      onboardingDetails: item.getOnboardingDetails())
    def data = new PortfolioItemEntity(PortfolioItemUniqueKey.newOf(item))
    data.setVersions([
      new EmbeddedVersion<TradeValue>(validFrom, item.getRecordFrom(), item.getState(), item.getComment(), null, value)
    ])
    data
  }
}
