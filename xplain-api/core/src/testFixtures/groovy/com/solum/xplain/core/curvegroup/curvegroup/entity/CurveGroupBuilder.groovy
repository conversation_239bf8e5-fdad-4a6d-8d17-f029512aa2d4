package com.solum.xplain.core.curvegroup.curvegroup.entity

import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDateTime
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = CurveGroup)
class CurveGroupBuilder {
  CurveGroupBuilder() {
    name("Name")
    createdBy(AuditUser.of(UserBuilder.user("userId")))
    modifiedBy(AuditUser.of(UserBuilder.user("userId")))
    createdAt(LocalDateTime.now())
    updatedAt(LocalDateTime.now())
    calibrationStatus(CalibrationStatus.NOT_CALIBRATED)
  }

  static CurveGroup curveGroup() {
    return new CurveGroupBuilder().build()
  }

  static curveGroupWithId() {
    return new CurveGroupBuilder().id(ObjectId.get().toString()).build()
  }

  static curveGroupWith(Closure<CurveGroupBuilder> closure) {
    return new CurveGroupBuilder()
      .id("id")
      .with(closure)
      .build()
  }
}
