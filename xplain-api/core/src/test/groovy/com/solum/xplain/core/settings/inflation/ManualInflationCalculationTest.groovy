package com.solum.xplain.core.settings.inflation

import static java.time.LocalDate.now

import com.opengamma.strata.data.MarketData
import com.solum.xplain.core.settings.CurveSeasonalityBuilder
import com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType
import spock.lang.Specification

class ManualInflationCalculationTest extends Specification {

  def "should calculate manual seasonalities"() {
    setup:
    def seasonality = new CurveSeasonalityBuilder().build()
    seasonality.setPriceIndex("GB-RPI")
    def result = ManualInflationCalculation.newOf(seasonality).calculate(MarketData.empty(now()))

    expect:
    result.isRight()
    def definition = result.getOrNull()
    definition.settingsType == InflationSeasonalitySettingsType.MANUAL
    definition.observationPeriod == null
    definition.jan == 0.00208819500000
    definition.feb == 0.00176171200000
    definition.mar == 0.00213916000000
    definition.apr == 0.00214769600000
    definition.may == 0.00161012300000
    definition.jun == 0.00025928500000
    definition.jul == -0.00174823100000
    definition.aug == -0.00105970100000
    definition.sep == -0.00003592400000
    definition.oct == -0.00080377000000
    definition.nov == -0.00315363900000
    definition.dec == -0.00320490500000
  }
}
