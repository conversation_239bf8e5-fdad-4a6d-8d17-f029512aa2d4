package com.solum.xplain.core.company

import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.form.CompanyLegalEntityIpvSettingsForm
import com.solum.xplain.core.company.form.IpvValuationProvidersForm
import com.solum.xplain.core.company.form.IpvValuationSettingsForm
import com.solum.xplain.core.company.value.CompanyIpvSettingsView
import com.solum.xplain.core.company.value.IpvValuationSettingsView
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.CoreProductTypeProvider
import com.solum.xplain.core.product.MockProductProvider
import com.solum.xplain.core.product.MockProductProvider.MockProductType
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.product.ProductTypeResolver
import com.solum.xplain.core.providers.DataProviderRepository
import com.solum.xplain.core.providers.enums.DataProviderType
import com.solum.xplain.core.test.MockMvcConfiguration
import java.time.LocalDate
import java.util.function.Function
import java.util.stream.Collectors
import java.util.stream.Stream
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [CompanyIpvSettingsController])
class CompanyIpvSettingsControllerTest extends Specification {

  static def STATE_DATE = LocalDate.parse("1999-01-01")

  @SpringBean
  CompanyIpvSettingsService service = Mock()

  @SpringBean
  IpvDataGroupRepository ipvDataGroupRepository = Mock()

  @SpringBean
  DataProviderRepository dataProviderRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport support = new RequestPathVariablesSupport()

  @SpringBean
  ProductTypeResolver productTypeProvider = new ProductTypeResolver([new CoreProductTypeProvider(), new MockProductProvider()])

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper mapper

  def "should get default valuation settings"() {
    setup:

    service.companyIpvValuationSettings(
    _ as Authentication,
    "id",
    { it.getActualDate() == STATE_DATE } as BitemporalDate
    ) >> right(new CompanyIpvSettingsView())

    def results = mockMvc.perform(get('/companies/id/ipv-settings/1999-01-01')
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  @Unroll
  def "should update default valuation settings with #settingsForm and response #code  #responseBody"() {
    setup:

    ipvDataGroupRepository.ipvDataGroupCondensedView("ipvDataGroup") >> Optional.of(new IpvDataGroupCondensedView
    (companyIds: ["id"]))
    ipvDataGroupRepository.ipvDataGroupCondensedView("ipvDataGroupId1") >> Optional.empty()
    service.updateDefaultIpvSettingsVersion(
    "id",
    STATE_DATE,
    _ as Authentication,
    _ as IpvValuationSettingsForm) >> right(EntityId.entityId("id"))
    dataProviderRepository.existsByType(_ as String, DataProviderType.VALUATION) >> true

    def results = mockMvc.perform(put('/companies/id/ipv-settings/1999-01-01')
    .with(csrf())
    .content(mapper.writeValueAsString(settingsForm))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }
    where:
    settingsForm                                                                                                              | code | responseBody
    ipvValuationSettingsForm()                                                                                                | 200  | "id"
    ipvValuationSettingsForm().tap({ c -> c.put("products", null) })                                | 412  | "NotEmpty.ipvValuationSettingsForm.products"
    ipvValuationSettingsForm().tap({ c -> c.put("products", products("ipvDataGroupId1", "bbg")) })  | 412  | "Valuation Data groups are not valid"
    ipvValuationSettingsForm().tap({ c -> c.put("products", products("ipvDataGroup", "XPLAIN")) })  | 412  | "Xplain provider is not allowed for product type MOCK_PRODUCT_TYPE"
    ipvValuationSettingsForm().tap({ c -> c.put("products", products("ipvDataGroup", "NAV")) })     | 412  | "ValidDataProviderCode"
    ipvValuationSettingsForm().tap({ c -> c.put("slaDeadline", null) })                             | 412  | "NotNull.ipvValuationSettingsForm.slaDeadline"
  }

  def "should update default valuation settings where company id's in ipv data group is null"() {
    setup:
    def settingsForm = ipvValuationSettingsForm()

    ipvDataGroupRepository.ipvDataGroupCondensedView("ipvDataGroup") >> Optional.of(new IpvDataGroupCondensedView
    (companyIds: null, allowAllCompanies: true))
    ipvDataGroupRepository.ipvDataGroupCondensedView("ipvDataGroupId1") >> Optional.empty()
    service.updateDefaultIpvSettingsVersion(
    "id",
    STATE_DATE,
    _ as Authentication,
    _ as IpvValuationSettingsForm) >> right(EntityId.entityId("id"))
    dataProviderRepository.existsByType(_ as String, DataProviderType.VALUATION) >> true

    when:
    def results = mockMvc.perform(put('/companies/id/ipv-settings/1999-01-01')
    .with(csrf())
    .content(mapper.writeValueAsString(settingsForm))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results.response.status == 200
    results.response.getContentAsString().indexOf("id") >= 0
  }

  @WithMockUser
  def "should delete ipv settings"() {
    setup:
    1 * service.deleteIpvSettings(
    _,
    "companyId",
    STATE_DATE,
    ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
    .perform(put("/companies/companyId/ipv-settings/1999-01-01/delete")
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results.response.status == 200
  }

  def "should get default valuation settings versions"() {
    setup:

    service.getCompanySettingsVersions(_ as Authentication, "id") >> right([])

    def results = mockMvc.perform(get('/companies/id/ipv-settings/versions')
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get company portfolios valuation settings scrollable"() {
    setup:
    service.getEntitySettingsList(
    _ as Authentication,
    "id",
    { it.getActualDate() == STATE_DATE } as BitemporalDate
    ) >> right([])

    def results = mockMvc.perform(get('/companies/id/ipv-settings/entities')
    .param("stateDate", "1999-01-01")
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get entities valuation settings"() {
    setup:
    service.getCompanyEntitySettings(
    _ as Authentication,
    "id",
    "id",
    { it.getActualDate() == STATE_DATE } as BitemporalDate
    ) >> right(new IpvValuationSettingsView())

    def results = mockMvc.perform(get('/companies/id/ipv-settings/entities/id/1999-01-01')
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get entities valuation settings versions list"() {
    setup:

    service.getEntityValuationSettingsVersions(_ as Authentication, "id", "id") >> right([])

    def results = mockMvc.perform(get('/companies/id/ipv-settings/entities/id/versions')
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  @Unroll
  def "should update entity valuation settings"() {
    setup:
    ipvDataGroupRepository.ipvDataGroupCondensedView("ipvDataGroup") >> ipvExists
    service.updateEntityValuationSettings(
    "id",
    "id",
    STATE_DATE,
    _ as Authentication,
    _ as CompanyLegalEntityIpvSettingsForm) >> right(EntityId.entityId("id"))
    dataProviderRepository.existsByType(_ as String, DataProviderType.VALUATION) >> true

    def results = mockMvc.perform(put('/companies/id/ipv-settings/entities/id/1999-01-01')
    .with(csrf())
    .content(mapper.writeValueAsString(settingsForm))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    ipvExists                                                       | settingsForm                                                                                                             | code | responseBody
    Optional.of(new IpvDataGroupCondensedView(companyIds: ["id"]))  | portfolioIpvSettingsForm()                                                                                               | 200  | "id"
    Optional.of(new IpvDataGroupCondensedView(companyIds: ["id"]))  | portfolioIpvSettingsForm().tap({ c -> c.put("products", products(null, "BBG")) })              | 412  | "NotEmpty.companyLegalEntityIpvSettingsForm.products.ipvDataGroupId"
    Optional.empty()                                                | portfolioIpvSettingsForm()                                                                                               | 412  | "Valuation Data groups are not valid"
    Optional.of(new IpvDataGroupCondensedView(companyIds: ["id"]))  | portfolioIpvSettingsForm().tap({ c -> c.put("products", products("ipvDataGroup", "XPLAIN")) }) | 412  | "Xplain provider is not allowed for product type MOCK_PRODUCT_TYPE"
    Optional.of(new IpvDataGroupCondensedView(companyIds: ["id"]))  | portfolioIpvSettingsForm().tap({ c -> c.put("products", products("ipvDataGroup", "NAV")) })    | 412  | "ValidDataProviderCode"
    Optional.of(new IpvDataGroupCondensedView(companyIds: ["id"]))  | portfolioIpvSettingsForm().tap({ c -> c.put("slaDeadline", null) })                            | 412  | "NotNull.companyLegalEntityIpvSettingsForm.slaDeadline"
  }

  @Unroll
  def "should update entities valuation settings to default"() {
    setup:
    service.updateEntityValuationSettings(
    "id",
    "id",
    STATE_DATE,
    _ as Authentication,
    _ as CompanyLegalEntityIpvSettingsForm) >> right(EntityId.entityId("id"))

    def results = mockMvc.perform(put('/companies/id/ipv-settings/entities/id/1999-01-01')
    .with(csrf())
    .content(mapper.writeValueAsString(settingsForm))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    settingsForm                                                                                                        | code | responseBody
    ["settingsType": "DEFAULT", versionForm: NewVersionFormV2.newDefault()]                                             | 200  | "id"
    ["settingsType": "BESPOKE", versionForm: NewVersionFormV2.newDefault()]                                             | 412  | ""
    portfolioDefaultIpvSettingsForm()                                                                                   | 200  | "id"
    portfolioDefaultIpvSettingsForm().tap({ c -> c.put("slaDeadline", SlaDeadline.OTHER) })   | 412  | "Null.companyLegalEntityIpvSettingsForm.slaDeadline"
    portfolioDefaultIpvSettingsForm().tap({ c -> c.put("products", products(null, "BBG")) })  | 412  | "Null.companyLegalEntityIpvSettingsForm.products"
  }

  @WithMockUser
  def "should delete entity ipv settings"() {
    setup:
    1 * service.deleteEntityIpvSettings(
    _,
    "companyId",
    "id",
    STATE_DATE,
    ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
    .perform(put("/companies/companyId/ipv-settings/entities/id/1999-01-01/delete")
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    results.response.status == 200
  }

  def "should get company ipv settings versions dates"() {
    setup:
    1 * service.getIpvSettingsFutureVersions(_, "id", STATE_DATE) >> right(List.of())

    when:
    def results = mockMvc
    .perform(get("/companies/id/ipv-settings/future-versions/search")
    .param("stateDate", STATE_DATE.toString()))
    .andReturn()

    then:
    results.response.status == 200
  }

  def "should get legal entity settings versions dates"() {
    setup:
    1 * service.getEntitiesIpvSettingsFutureVersions(_, "id", "entityId", STATE_DATE) >> right(List.of())

    when:
    def results = mockMvc
    .perform(get("/companies/id/ipv-settings/entities/entityId/future-versions/search")
    .param("stateDate", STATE_DATE.toString()))
    .andReturn()

    then:
    results.response.status == 200
  }

  def ipvValuationSettingsForm(String provider = "BBG") {
    [
      slaDeadline   : SlaDeadline.OTHER,
      products      : products("ipvDataGroup", provider),
      versionForm   : NewVersionFormV2.newDefault()
    ]
  }

  def portfolioIpvSettingsForm(String provider = "BBG") {
    [
      settingsType  : "BESPOKE",
      slaDeadline   : SlaDeadline.OTHER,
      products      : products("ipvDataGroup", provider),
      versionForm   : NewVersionFormV2.newDefault()
    ]
  }

  def portfolioDefaultIpvSettingsForm() {
    [
      settingsType  : "DEFAULT",
      slaDeadline   : null,
      products      : null,
      versionForm   : NewVersionFormV2.newDefault()
    ]
  }

  static Map<ProductType, IpvValuationProvidersForm> products(String ipvDataGroupId, String provider1 = null, String provider2 = null, String provider3 = null, String provider4 = null) {
    def bbg = new IpvValuationProvidersForm(ipvDataGroupId, provider1, provider2, provider3, provider4)

    return Stream.concat(Arrays.stream(CoreProductType.values()), Stream.of(MockProductType.MOCK_PRODUCT_TYPE))
    .collect(Collectors.toMap(Function.identity(), i -> bbg))
  }
}
