package com.solum.xplain.core.viewconfig.provider

import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.core.viewconfig.ViewConfigurationMapper
import com.solum.xplain.core.viewconfig.ViewConfigurationRepository
import com.solum.xplain.core.viewconfig.entity.ViewConfiguration
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupView
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionView
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView
import com.solum.xplain.core.viewconfig.value.FieldType
import com.solum.xplain.core.viewconfig.value.View
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class RepositoryViewConfigurationProviderTest extends Specification {
  static class DummyView {}
  static View<DummyView> DUMMY_VIEW = new View<>(DummyView)
  static FieldDefinitionView FIELD1 = FieldDefinitionView.ofNamedProperty("name1", FieldType.STRING, "name1", false)
  static FieldDefinitionView FIELD2 = FieldDefinitionView.ofNamedProperty("name2", FieldType.NUMBER, "name2", false)
  static DEFAULT_DUMMY_CONFIG = new ViewConfigurationView<DummyView>(
  null,
  DUMMY_VIEW,
  ViewConfigurationProvider.DEFAULT_VIEW_NAME,
  false,
  [
    new ColumnDefinitionGroupView("name1", [new ColumnDefinitionView(FIELD1, true, true, null, null)]),
    new ColumnDefinitionGroupView("name2", [new ColumnDefinitionView(FIELD2, true, true, null, 2)])
  ],
  true,
  null,
  null)
  static USER = UserBuilder.user("userId")
  static AUTHENTICATION = new TestingAuthenticationToken(USER, null)

  ViewConfigurationMapper mapper = Mock(ViewConfigurationMapper)
  ViewConfigurationRepository repository = Mock(ViewConfigurationRepository)

  RepositoryViewConfigurationProvider repositoryProvider

  def setup() {
    repositoryProvider = new RepositoryViewConfigurationProvider(mapper, repository)
  }


  def "should provide view configurations with user=#userId isOwner=#isOwner"() {
    given:
    def viewConfiguration = new ViewConfiguration(createdBy: new AuditUser(userId: userId))

    when:
    def result = repositoryProvider.provideViewConfigurations(AUTHENTICATION, DUMMY_VIEW.viewClass())

    then:
    1 * repository.findAllUserVisibleViews(DUMMY_VIEW, USER.id) >> [viewConfiguration]
    1 * mapper.toViewConfigurationView(DUMMY_VIEW, viewConfiguration, isOwner) >> DEFAULT_DUMMY_CONFIG

    and:
    result == [DEFAULT_DUMMY_CONFIG]

    where:
    userId | isOwner
    USER.id | true
    "other" | false
  }
}
