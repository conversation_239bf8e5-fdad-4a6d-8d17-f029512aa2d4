package com.solum.xplain.core.curvegroup.volatilityfx


import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.value.VersionedList
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityMatrixConfiguration
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityMatrixValues
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeView
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import spock.lang.Specification

class CurveGroupFxVolatilityServiceTest extends Specification {
  def static GROUP_ID = "groupId"
  def static STATE_DATE = now()
  def static VALUATION_DATE = now().plusYears(1)
  def static VERSION_DATE = NewVersionFormV2.ROOT_DATE

  def curveGroupRepository = Mock(CurveGroupRepository)
  def repository = Mock(CurveGroupFxVolatilityRepository)
  def marketDataQuotesSupport = Mock(MarketDataQuotesSupport)

  def service = new CurveGroupFxVolatilityService(curveGroupRepository, repository, marketDataQuotesSupport)


  def setup() {
    def curveGroup = new CurveGroupView(id: GROUP_ID)
    curveGroupRepository.getEither(GROUP_ID) >> right(curveGroup)
  }

  def "should create FX volatility"() {
    setup:
    def form = new CurveGroupFxVolatilityForm(
      nodes: [
        new CurveGroupFxVolatilityNodeForm(
        expiry: "1M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ]
      )

    1 * repository.createVolatility(GROUP_ID, form) >> right(new EntityId("1"))

    when:
    def result = service.create(GROUP_ID, form)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should create FX volatility with default values"() {
    setup:
    def defaultForm = new CurveGroupFxVolatilityForm(
      versionForm: NewVersionFormV2.newDefault(),
      timeInterpolator: "TimeSquare",
      timeExtrapolatorLeft: "Flat",
      timeExtrapolatorRight: "Flat",
      strikeInterpolator: "Linear",
      strikeExtrapolatorLeft: "Flat",
      strikeExtrapolatorRight: "Flat"
      )

    when:
    def result = service.createDefault(GROUP_ID)

    then:
    1 * repository.createVolatility(GROUP_ID, defaultForm) >> right(new EntityId("1"))

    and:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should update FX volatility"() {
    setup:
    def form = new CurveGroupFxVolatilityForm(
      nodes: [
        new CurveGroupFxVolatilityNodeForm(
        expiry: "1M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ]
      )

    1 * repository.updateVolatility(GROUP_ID, VERSION_DATE, form) >> right(new EntityId("1"))

    when:
    def result = service.update(GROUP_ID, VERSION_DATE, form)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should delete FX volatility"() {
    setup:
    1 * repository.deleteVolatility(GROUP_ID, VERSION_DATE) >> right(new EntityId("1"))

    when:
    def result = service.delete(GROUP_ID, VERSION_DATE)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should get FX volatility versions"() {
    setup:
    1 * repository.getVolatilityVersionViews(GROUP_ID) >> List.of(new VolatilitySurfaceView())

    when:
    def result = service.getVersions(GROUP_ID)

    then:
    result.isRight()
  }

  def "should get FX volatility future versions"() {
    setup:
    def dateList = new DateList([])
    1 * repository.getFutureVersions(GROUP_ID, STATE_DATE) >> dateList

    when:
    def result = service.getFutureVersionsDates(GROUP_ID, STATE_DATE)

    then:
    result.isRight()
    def futureVersions = result.right().get() as DateList
    futureVersions == dateList
  }

  def "should get FX volatility matrix configuration"() {
    setup:
    1 * repository.getVolatilityNodesViews(GROUP_ID, STATE_DATE) >>
      new VersionedList<>(
      VERSION_DATE,
      [
        new FxVolatilityNodeView(
        expiry: "1M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ]
      )

    when:
    def result = service.getMatrixConfiguration(GROUP_ID, STATE_DATE)

    then:
    result.isRight()
    def configuration = result.right().get() as FxVolatilityMatrixConfiguration
    configuration.versionDate == VERSION_DATE
    configuration.rows == ["1M"]
    configuration.columns == ["EUR/USD"]
  }

  def "should get FX volatility matrix values"() {
    setup:
    def stateForm = Mock(CurveConfigMarketStateForm)
    1 * stateForm.getStateDate() >> STATE_DATE

    1 * marketDataQuotesSupport.getFullQuotes(stateForm) >> [:]

    1 * repository.getVolatilityNodesValuesViews(GROUP_ID, STATE_DATE, VALUATION_DATE, _) >>
      new VersionedList<>(
      VERSION_DATE,
      [
        new FxVolatilityNodeValueView(
        expiry: "1M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        value: 10d
        )
      ]
      )

    when:
    def result = service.getMatrixValues(GROUP_ID, stateForm, VALUATION_DATE)

    then:
    result.isRight()
    def configuration = result.right().get() as FxVolatilityMatrixValues
    configuration.versionDate == VERSION_DATE
    configuration.rows == ["1M"]
    configuration.columns == ["EUR/USD"]
    configuration.values.size() == 1
  }
}
