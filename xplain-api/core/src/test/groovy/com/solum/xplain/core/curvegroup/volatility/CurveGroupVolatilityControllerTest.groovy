package com.solum.xplain.core.curvegroup.volatility

import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.curvemarket.CurveMarketSample.getMARKET_STATE_FORM
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.validation.UniqueEntitySupport
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.value.VersionedList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixConfiguration
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixValues
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixConfiguration
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixValues
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.test.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [CurveGroupVolatilityController])
@MockMvcConfiguration
class CurveGroupVolatilityControllerTest extends Specification {

  def static GROUP_ID = "groupId"
  def static SURFACE_ID = "surfaceId"
  def static VERSION_DATE = parse("2000-01-01")
  def static SURFACE_SKEW_ID = "surfaceSkewId"
  def static STATE_DATE = parse("2018-10-10")
  def static CURVE_DATE = parse("2018-10-11")
  def static CSV_BYTES = "csv".getBytes()
  def static CSV_FILE = new MockMultipartFile("file", "filename.txt", "text/plain", CSV_BYTES)
  def static EXPORTED_CSV = FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")

  @SpringBean
  private CurveGroupVolatilityService service = Mock()
  @SpringBean
  private CurveGroupVolatilityImportService importService = Mock()
  @SpringBean
  private CurveGroupVolatilityExportService exportService = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  private UniqueEntitySupport uniqueEntitySupport = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper

  @Unroll
  @WithMockUser
  def "should validate when creating with response #code #responseBody and form (#form)"() {
    setup:
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, VolatilitySurface.class) >> false

    service.createSurface(GROUP_ID, _ as VolatilitySurfaceForm) >>
      right(EntityId.entityId("1"))

    def results = mockMvc
      .perform(post("/curve-group/groupId/volatility")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                              | code | responseBody
    surfaceForm()                                                                     | 200  | "id"
    surfaceForm().tap { b -> b.put("name", "Name") }                                  | 412  | "ValidStringSet.volatilitySurfaceForm.name"
    surfaceForm().tap { b -> b.put("versionForm", null) }                             | 412  | "NotNull.volatilitySurfaceForm.versionForm"
    surfaceForm().tap { b -> b.put("xInterpolator", null) }                           | 412  | "NotEmpty.volatilitySurfaceForm.xInterpolator"
    surfaceForm().tap { b -> b.put("xInterpolator", "X") }                            | 412  | "ValidStringSet.volatilitySurfaceForm.xInterpolator"
    surfaceForm().tap { b -> b.put("xInterpolator", "LogLinearDiscountFactor") }      | 412  | "ValidStringSet.volatilitySurfaceForm.xInterpolator"
    surfaceForm().tap { b -> b.put("xExtrapolatorLeft", null) }                       | 412  | "NotEmpty.volatilitySurfaceForm.xExtrapolatorLeft"
    surfaceForm().tap { b -> b.put("xExtrapolatorLeft", "LogLinearDiscountFactor") }  | 412  | "ValidStringSet.volatilitySurfaceForm.xExtrapolatorLeft"
    surfaceForm().tap { b -> b.put("xExtrapolatorLeft", "X") }                        | 412  | "ValidStringSet.volatilitySurfaceForm.xExtrapolatorLeft"
    surfaceForm().tap { b -> b.put("xExtrapolatorRight", null) }                      | 412  | "NotEmpty.volatilitySurfaceForm.xExtrapolatorRight"
    surfaceForm().tap { b -> b.put("xExtrapolatorRight", "X") }                       | 412  | "ValidStringSet.volatilitySurfaceForm.xExtrapolatorRight"
    surfaceForm().tap { b -> b.put("yInterpolator", null) }                           | 412  | "NotEmpty.volatilitySurfaceForm.yInterpolator"
    surfaceForm().tap { b -> b.put("yInterpolator", "LogLinearDiscountFactor") }      | 412  | "ValidStringSet.volatilitySurfaceForm.yInterpolator"
    surfaceForm().tap { b -> b.put("yInterpolator", "X") }                            | 412  | "ValidStringSet.volatilitySurfaceForm.yInterpolator"
    surfaceForm().tap { b -> b.put("yExtrapolatorLeft", null) }                       | 412  | "NotEmpty.volatilitySurfaceForm.yExtrapolatorLeft"
    surfaceForm().tap { b -> b.put("yExtrapolatorLeft", "X") }                        | 412  | "ValidStringSet.volatilitySurfaceForm.yExtrapolatorLeft"
    surfaceForm().tap { b -> b.put("yExtrapolatorRight", null) }                      | 412  | "NotEmpty.volatilitySurfaceForm.yExtrapolatorRight"
    surfaceForm().tap { b -> b.put("yExtrapolatorRight", "X") }                       | 412  | "ValidStringSet.volatilitySurfaceForm.yExtrapolatorRight"
    surfaceForm().tap { b -> b.put("yExtrapolatorRight", "LogLinearDiscountFactor") } | 412  | "ValidStringSet.volatilitySurfaceForm.yExtrapolatorRight"
    surfaceForm().tap { b -> b.put("skewType", null) }                                | 412  | "NotEmpty.volatilitySurfaceForm.skewType"
    surfaceForm().tap { b -> b.put("capletValuationModel", null) }                    | 412  | "NotEmpty.volatilitySurfaceForm.capletValuationModel"
    surfaceForm().tap { b -> b.put("sabr", null) }                                    | 412  | "NotNull.volatilitySurfaceForm.sabr"
    surfaceForm().tap { b -> b.put("sabrBeta", 1d) }                                  | 412  | "Null.volatilitySurfaceForm.sabrBeta"
    surfaceForm().tap { b -> b.put("sabrShift", 1d) }                                 | 412  | "Null.volatilitySurfaceForm.sabrShift"
    surfaceForm().tap { b -> b.putAll([sabr: true, sabrBeta: 0.5, sabrShift: 0.03]) } | 200  | "id"
    surfaceForm().tap { b -> b.putAll([sabr: true, sabrShift: 0.03]) }                | 412  | "NotNull.volatilitySurfaceForm.sabrBeta"
    surfaceForm().tap { b -> b.putAll([sabr: true, sabrBeta: 0.5]) }                  | 412  | "NotNull.volatilitySurfaceForm.sabrShift"
  }

  @WithMockUser
  def "should validate when creating and fail for non-unique name"() {
    setup:
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, VolatilitySurface.class) >> true

    service.createSurface(GROUP_ID, _ as VolatilitySurfaceForm) >>
      right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(post("/curve-group/groupId/volatility")
      .with(csrf())
      .content(objectMapper.writeValueAsString(surfaceForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()


    then:
    with(results.getResponse()) {
      getStatus() == 412
      getContentAsString().indexOf("IR Volatility Surface name must be unique") >= 0
    }
  }

  @Unroll
  @WithMockUser
  def "should validate when updated with response #code #responseBody and form (#form)"() {
    setup:
    service.updateSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      _ as VolatilitySurfaceUpdateForm
      ) >> right(EntityId.entityId("1"))

    def results = mockMvc
      .perform(put("/curve-group/groupId/volatility/surfaceId/2000-01-01")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                    | code | responseBody
    surfaceUpdateForm()                                                                     | 200  | "id"
    surfaceUpdateForm().tap { b -> b.put("versionForm", null) }                             | 412  | "NotNull.volatilitySurfaceUpdateForm.versionForm"
    surfaceUpdateForm().tap { b -> b.put("xInterpolator", null) }                           | 412  | "NotEmpty.volatilitySurfaceUpdateForm.xInterpolator"
    surfaceUpdateForm().tap { b -> b.put("xInterpolator", "X") }                            | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.xInterpolator"
    surfaceUpdateForm().tap { b -> b.put("xExtrapolatorLeft", null) }                       | 412  | "NotEmpty.volatilitySurfaceUpdateForm.xExtrapolatorLeft"
    surfaceUpdateForm().tap { b -> b.put("xExtrapolatorLeft", "X") }                        | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.xExtrapolatorLeft"
    surfaceUpdateForm().tap { b -> b.put("xExtrapolatorRight", null) }                      | 412  | "NotEmpty.volatilitySurfaceUpdateForm.xExtrapolatorRight"
    surfaceUpdateForm().tap { b -> b.put("xExtrapolatorRight", "X") }                       | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.xExtrapolatorRight"
    surfaceUpdateForm().tap { b -> b.put("yInterpolator", null) }                           | 412  | "NotEmpty.volatilitySurfaceUpdateForm.yInterpolator"
    surfaceUpdateForm().tap { b -> b.put("yInterpolator", "X") }                            | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.yInterpolator"
    surfaceUpdateForm().tap { b -> b.put("yExtrapolatorLeft", null) }                       | 412  | "NotEmpty.volatilitySurfaceUpdateForm.yExtrapolatorLeft"
    surfaceUpdateForm().tap { b -> b.put("yExtrapolatorLeft", "X") }                        | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.yExtrapolatorLeft"
    surfaceUpdateForm().tap { b -> b.put("yExtrapolatorRight", null) }                      | 412  | "NotEmpty.volatilitySurfaceUpdateForm.yExtrapolatorRight"
    surfaceUpdateForm().tap { b -> b.put("yExtrapolatorRight", "X") }                       | 412  | "ValidStringSet.volatilitySurfaceUpdateForm.yExtrapolatorRight"
    surfaceUpdateForm().tap { b -> b.put("skewType", null) }                                | 412  | "NotEmpty.volatilitySurfaceUpdateForm.skewType"
    surfaceUpdateForm().tap { b -> b.put("capletValuationModel", null) }                    | 412  | "NotEmpty.volatilitySurfaceUpdateForm.capletValuationModel"
    surfaceUpdateForm().tap { b -> b.put("sabr", null) }                                    | 412  | "NotNull.volatilitySurfaceUpdateForm.sabr"
    surfaceUpdateForm().tap { b -> b.put("sabrBeta", 1d) }                                  | 412  | "Null.volatilitySurfaceUpdateForm.sabrBeta"
    surfaceUpdateForm().tap { b -> b.put("sabrShift", 1d) }                                 | 412  | "Null.volatilitySurfaceUpdateForm.sabrShift"
    surfaceUpdateForm().tap { b -> b.putAll([sabr: true, sabrBeta: 0.5, sabrShift: 0.03]) } | 200  | "id"
    surfaceUpdateForm().tap { b -> b.putAll([sabr: true, sabrShift: 0.03]) }                | 412  | "NotNull.volatilitySurfaceUpdateForm.sabrBeta"
    surfaceUpdateForm().tap { b -> b.putAll([sabr: true, sabrBeta: 0.5]) }                  | 412  | "NotNull.volatilitySurfaceUpdateForm.sabrShift"
    surfaceUpdateForm().tap { b -> b.putAll([skewNodes: []]) }                              | 412  | "Null.volatilitySurfaceUpdateForm.skewNodes"
    surfaceUpdateForm().tap { b -> b.putAll([skewNodes: [], skewType: "MONEYNESS"]) }       | 200  | "id"
  }

  @WithMockUser
  def "should archive surface"() {
    setup:
    1 * service.archiveSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      _ as ArchiveEntityForm
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/curve-group/groupId/volatility/surfaceId/2000-01-01/archive")
      .content(objectMapper.writeValueAsString(new ArchiveEntityForm(NewVersionFormV2.newDefault())))
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf())).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should delete surface"() {
    setup:
    1 * service.deleteSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/curve-group/groupId/volatility/surfaceId/2000-01-01/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surfaces"() {
    setup:
    def sort = Sort.by(VolatilitySurfaceView.Fields.name)
    1 * service.getSurfaces(
      GROUP_ID, {
        verifyAll(it, BitemporalDate) {
          actualDate == STATE_DATE
        }
      },
      true,
      sort
      ) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility")
      .param("stateDate", STATE_DATE.toString())
      .param("withArchived", "true"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface"() {
    setup:
    1 * service.getSurface(
      GROUP_ID,
      SURFACE_ID,
      STATE_DATE
      ) >> right(new VolatilitySurfaceView())

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId")
      .param("stateDate", STATE_DATE.toString()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should surface versions"() {
    setup:
    1 * service.getSurfaceVersions(
      GROUP_ID,
      SURFACE_ID
      ) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/versions"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should surface future versions dates"() {
    setup:
    1 * service.getSurfaceFutureVersions(
      GROUP_ID,
      new VolatilitySurfaceSearch("SEARCH-NAME", STATE_DATE)
      ) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/future-versions/search")
      .param("name", "SEARCH-NAME")
      .param("stateDate", STATE_DATE.toString()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface ATM matrix"() {
    setup:
    1 * service.getSurfaceAtmMatrixConfiguration(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      ) >> right(VolatilityMatrixConfiguration.configurationFromList(VersionedList.empty()))

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/nodes"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface ATM matrix values"() {
    setup:
    1 * service.getSurfaceAtmMatrixValues(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      MARKET_STATE_FORM
      ) >> right(VolatilityMatrixValues.configurationFromList(
      [new VolatilityNodeValueView(tenor: "1Y", expiry: "1Y", value: 1)],
      LocalDate.ofEpochDay(0))
      )

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/nodes/values")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name())
      ).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface Cap/Floor (caplet) matrix"() {
    setup:
    1 * service.getSurfaceCapletMatrixConfiguration(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      ) >> right(CapletVolatilityMatrixConfiguration.configurationFromList(VersionedList.empty()))

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/caplet-nodes"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface Cap/Floor (caplet) matrix values"() {
    setup:
    1 * service.getSurfaceCapletMatrixValues(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      CurveMarketSample.MARKET_STATE_FORM
      ) >> right(CapletVolatilityMatrixValues.configurationFromList(
      [new CapletVolatilityNodeValueView(strike: BigDecimal.ONE, tenor: "1Y", value: 1)],
      LocalDate.ofEpochDay(0))
      )

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/caplet-nodes/values")
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name())
      .param("curvesPriceType", BID_PRICE.name())
      .param("dscCurvesPriceType", BID_PRICE.name())
      .param("fxRatesPriceType", BID_PRICE.name())
      .param("volsPriceType", BID_PRICE.name())
      .param("volsSkewsPriceType", BID_PRICE.name())
      ).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface skews"() {
    setup:
    1 * service.getSurfaceSkews(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      ) >> right([new VolatilitySurfaceSkewView()])

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/surface-skews"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface skew (Absolute strike/Moneyness) matrix"() {
    setup:
    1 * service.getSurfaceSkewMatrixConfiguration(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      SURFACE_SKEW_ID
      ) >> right(VolatilityMatrixConfiguration.configurationFromList(VersionedList.empty()))

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/surface-skews/surfaceSkewId/nodes"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get surface skew (Absolute strike/Moneyness) matrix values"() {
    setup:
    1 * service.getSurfaceSkewMatrixValues(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      SURFACE_SKEW_ID,
      CurveMarketSample.MARKET_STATE_FORM
      ) >> right(VolatilityMatrixValues.configurationFromList(
      [new VolatilityNodeValueView(tenor: "1Y", expiry: "1Y", value: 1)],
      LocalDate.ofEpochDay(0))
      )

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/surface-skews/surfaceSkewId/nodes/values")
      .param("marketDataGroupId", CurveMarketSample.MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", CurveMarketSample.MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", CurveMarketSample.MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", CurveMarketSample.MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", CurveMarketSample.MARKET_STATE_FORM.marketDataSource.name())
      .param("curvesPriceType", BID_PRICE.name())
      .param("dscCurvesPriceType", BID_PRICE.name())
      .param("fxRatesPriceType", BID_PRICE.name())
      .param("volsPriceType", BID_PRICE.name())
      .param("volsSkewsPriceType", BID_PRICE.name())
      ).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload surfaces CSV file"() {
    setup:
    1 * importService.uploadSurfaces(
      GROUP_ID, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      _) >> right([EntityId.entityId("entityId")])

    when:
    def file = new MockMultipartFile(
      "file",
      "filename.txt",
      "text/plain",
      CSV_BYTES
      )

    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/upload")
      .file(file)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("entityId") >= 0
    }
  }

  @WithMockUser
  def "should upload nodes for all surfaces"() {
    setup:
    1 * importService.uploadNodes(
      GROUP_ID, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_BYTES
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/nodes/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload surface nodes"() {
    setup:
    1 * importService.uploadNodesForSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_FILE.getBytes()
      ) >> right(EntityId.entityId("1"))


    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/surfaceId/2000-01-01/nodes/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload caplet nodes for all surfaces"() {
    1 * importService.uploadCapletNodes(
      GROUP_ID, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_BYTES
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/caplet-nodes/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload surface caplet nodes"() {
    setup:
    1 * importService.uploadCapletNodesForSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_FILE.getBytes()
      ) >> right(EntityId.entityId("1"))


    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/surfaceId/2000-01-01/caplet-nodes/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload skews for all surfaces"() {
    1 * importService.uploadSkews(
      GROUP_ID, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_BYTES
      ) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/surface-skews/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should upload surface skews"() {
    setup:
    1 * importService.uploadSkewsForSurface(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE, {
        it.stateDate == STATE_DATE && it.duplicateAction == ERROR
      },
      CSV_FILE.getBytes()
      ) >> right(EntityId.entityId("1"))


    when:
    def results = mockMvc
      .perform(multipart("/curve-group/groupId/volatility/surfaceId/2000-01-01/surface-skews/upload")
      .file(CSV_FILE)
      .param("stateDate", STATE_DATE.toString())
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  def "should get all surfaces CSV"() {
    setup:
    1 * exportService.getAllSurfacesCsvBytes(
      GROUP_ID,
      STATE_DATE,
      _,
      _
      ) >> response

    def results = mockMvc.perform(get("/curve-group/groupId/volatility/csv")
      .param("stateDate", STATE_DATE.toString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get all surfaces ATM matrices volatility nodes values CSV"() {
    setup:
    1 * exportService.getAllSurfacesNodesCsvBytes(
      GROUP_ID, {
        verifyAll(it, BitemporalDate) {
          it.actualDate == MARKET_STATE_FORM.getStateDate()
        }
      },
      MARKET_STATE_FORM,
      _,
      _
      ) >> response

    def results
    results = mockMvc
      .perform(get("/curve-group/groupId/volatility/nodes/csv")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get surface ATM matrix volatility nodes values CSV"() {
    setup:
    1 * exportService.getSurfaceNodesCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      MARKET_STATE_FORM,
      _
      ) >> response

    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/nodes/csv")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get all surfaces Cap/Floor matrices volatility nodes values CSV"() {
    setup:
    1 * exportService.getAllSurfacesCapletNodesCsvBytes(
      GROUP_ID,
      MARKET_STATE_FORM.getStateDate(),
      MARKET_STATE_FORM,
      _,
      _
      ) >> response

    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/caplet-nodes/csv")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get surface Cap/Floor matrix nodes values CSV"() {
    setup:
    1 * exportService.getSurfaceCapletNodesCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      MARKET_STATE_FORM,
      _
      ) >> response

    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/caplet-nodes/csv")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get all surfaces skews CSV"() {
    setup:
    1 * exportService.getAllSurfacesSkewsCsvBytes(
      GROUP_ID,
      STATE_DATE,
      _
      ) >> response

    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surface-skews/csv")
      .param("stateDate", STATE_DATE.toString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def "should get surface skews CSV"() {
    setup:
    1 * exportService.getSurfaceSkewsCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      STATE_DATE,
      _
      ) >> response

    def results = mockMvc
      .perform(get("/curve-group/groupId/volatility/surfaceId/2000-01-01/surface-skews/csv")
      .param("stateDate", STATE_DATE.toString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)).andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    response                     | code | responseBody
    right(EXPORTED_CSV)          | 200  | "test"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  def surfaceForm() {
    surfaceUpdateForm().tap { it ->
      it.put("name", "AED 3M Vols")
    }
  }

  def surfaceUpdateForm() {
    [
      xInterpolator       : "Linear",
      xExtrapolatorLeft   : "Flat",
      xExtrapolatorRight  : "Flat",
      yInterpolator       : "Linear",
      yExtrapolatorLeft   : "Flat",
      yExtrapolatorRight  : "Flat",
      skewType            : "ATM_ONLY",
      capletValuationModel: "NORMAL",
      sabr                : false,
      versionForm         : NewVersionFormV2.newDefault()
    ]
  }
}
