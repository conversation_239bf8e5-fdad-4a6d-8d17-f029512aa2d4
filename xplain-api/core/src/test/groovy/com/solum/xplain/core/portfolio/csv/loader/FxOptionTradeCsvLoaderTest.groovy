package com.solum.xplain.core.portfolio.csv.loader

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_IDENTIFIER
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_DATE_ADJ_CONVENTION
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_TIME
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_ZONE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_FXOPTION_CALL_PUT
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PAYMENT_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_AMOUNT
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE_ADJ_CONVENTION
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.io.CsvFile
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.builder.ResolvableFxOptionDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.extensions.enums.CallPutType
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.time.LocalTime
import java.util.stream.Collectors
import spock.lang.Specification
import spock.lang.Unroll

class FxOptionTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {

  def FX_OPT_HEADERS = [
    TRADE_POSITION,
    TRADE_FXOPTION_CALL_PUT,
    TRADE_EXPIRY_DATE,
    TRADE_EXPIRY_DATE_ADJ_CONVENTION,
    TRADE_EXPIRY_TIME,
    TRADE_EXPIRY_ZONE,
    TRADE_PREMIUM_AMOUNT,
    TRADE_PREMIUM_CURRENCY,
    TRADE_PREMIUM_DATE,
    TRADE_PREMIUM_DATE_ADJ_CONVENTION,
    LEG_1 + "." + PAY_RECEIVE,
    LEG_1 + "." + LEG_CURRENCY,
    LEG_1 + "." + LEG_IDENTIFIER,
    LEG_1 + "." + LEG_NOTIONAL,
    LEG_2 + "." + PAY_RECEIVE,
    LEG_2 + "." + LEG_CURRENCY,
    LEG_2 + "." + LEG_IDENTIFIER,
    LEG_2 + "." + LEG_NOTIONAL,
    TRADE_BUSINESS_DAY_CONVENTION,
    TRADE_PAYMENT_DATE,
    TRADE_REF_SEC_FX_RATE,
    TRADE_CURRENCY
  ]

  def LOADER = new FxOptionTradeCsvLoader(new FxSingleTradeCsvLoader(), ReferenceData.standard())

  @Unroll
  def "should parse fx opt expected #resultPaymentDate when #ccy1 #ccy2 #csvPaymentDate #csvExpiryDate"() {
    setup:
    def csvRow = [
      "BUY",
      callPut,
      csvExpiryDate,
      "",
      "",
      "",
      "",
      "",
      "2023-01-15",
      "",
      "Pay",
      ccy1,
      "",
      "10",
      "Rec",
      ccy2,
      "",
      "20",
      "",
      csvPaymentDate,
      "",
      ""
    ]

    def firstRow = CsvFile.of(FX_OPT_HEADERS, [csvRow]).row(0)

    expect:
    def res = LOADER.parse(firstRow, false)
    res.isRight()
    def fxDetails = (ResolvableFxOptionDetails) res.getOrNull()
    fxDetails.paymentDate == LocalDate.parse(resultPaymentDate)

    where:
    ccy1  | ccy2  | callPut | csvPaymentDate | csvExpiryDate | resultPaymentDate
    "EUR" | "USD" | "PUT"   | "2023-01-15"   | "2023-01-01"  | "2023-01-15"
    "EUR" | "USD" | "PUT"   | ""             | "2023-01-01"  | "2023-01-03"
    "EUR" | "USD" | "PUT"   | ""             | "2023-01-03"  | "2023-01-05"
    "EUR" | "USD" | "PUT"   | ""             | "2023-01-05"  | "2023-01-09"
    "EUR" | "USD" | "PUT"   | ""             | "2023-01-02"  | "2023-01-04"
    "EUR" | "USD" | "PUT"   | ""             | "2023-04-05"  | "2023-04-11"
    "USD" | "EUR" | "CALL"  | ""             | "2023-01-01"  | "2023-01-03"
    "USD" | "CAD" | "PUT"   | ""             | "2023-01-01"  | "2023-01-03"
    "CNY" | "PLN" | "PUT"   | ""             | "2023-02-01"  | "2023-02-03"
    "CNY" | "RUB" | "PUT"   | ""             | "2023-02-01"  | "2023-02-02"
  }

  @Unroll
  def "should parse FXOPT trade currency where #callPut #payLeg #recLeg #explicitCcy then #expectedCcy"() {
    setup:
    def csvRows = [
      [
        "BUY",
        callPut,
        "2023-01-01",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "Pay",
        payLeg,
        "",
        "10",
        "Rec",
        recLeg,
        "",
        "20",
        "",
        "",
        "",
        explicitCcy
      ],
    ]
    def csv = CsvFile.of(FX_OPT_HEADERS, csvRows)

    expect:
    LOADER.parseTradeCcy(csv.row(0)) == expectedCcy

    where:
    callPut | payLeg | recLeg | explicitCcy | expectedCcy
    "CALL"  | "EUR"  | "USD"  | ""          | Currency.USD
    "CALL"  | "USD"  | "EUR"  | ""          | Currency.EUR
    "PUT"   | "EUR"  | "USD"  | ""          | Currency.EUR
    "PUT"   | "EUR"  | "USD"  | "USD"       | Currency.USD
  }

  def "should correctly parse FXOPTION trade"() {
    setup:
    def rows = loadResource("FxOptionTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 6
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result.businessDayAdjustmentType == null
    result.endDate == LocalDate.parse("2014-10-10")
    result.businessDayConvention == "Following"
    result.receiveLeg.extLegIdentifier == "TRADEFX_LEG1"
    result.receiveLeg.currency == "AED"
    result.receiveLeg.notional == 100
    result.payLeg.extLegIdentifier == "TRADEFX_LEG2"
    result.payLeg.notional == -200
    result.payLeg.currency == "CNY"
    result.optionTradeDetails.expiryDate == LocalDate.parse("2014-02-28")
    result.optionTradeDetails.expiryTime == LocalTime.parse("10:00")
    result.optionTradeDetails.expiryZone == "UTC"
    result.optionTradeDetails.premiumDate == LocalDate.parse("2020-10-10")
    result.optionTradeDetails.premiumDateConvention == "ModifiedFollowing"
    result.optionTradeDetails.premiumCurrency == "EUR"
    result.optionTradeDetails.premiumValue == 10000

    def result2 = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result2.optionTradeDetails.premiumDate == LocalDate.parse("2020-11-10")
    result2.optionTradeDetails.premiumDateConvention == "Following"
    result2.optionTradeDetails.premiumCurrency == "AED"
    result2.receiveLeg.extLegIdentifier == null
    result2.payLeg.notional == -100
    result2.payLeg.currency == "AED"
    result2.receiveLeg.extLegIdentifier == null
    result2.receiveLeg.currency == "CNY"
    result2.receiveLeg.notional == 200

    def result3 = parsedRows[2].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result3.optionTradeDetails.callPutType == CallPutType.CALL
    result3.payLeg.currency == "CNY"
    result3.payLeg.notional == -200
    result3.receiveLeg.notional == 100
    result3.receiveLeg.currency == "AED"

    def result4 = parsedRows[3].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result4.optionTradeDetails.callPutType == CallPutType.PUT
    result4.receiveLeg.notional == 200
    result4.receiveLeg.currency == "CNY"
    result4.payLeg.currency == "AED"
    result4.payLeg.notional == -100

    def result5 = parsedRows[4].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result5.optionTradeDetails.callPutType == CallPutType.CALL
    result5.payLeg.notional == -200
    result5.payLeg.currency == "AED"
    result5.receiveLeg.currency == "CNY"
    result5.receiveLeg.notional == 100

    def result6 = parsedRows[5].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result6.optionTradeDetails.callPutType == CallPutType.PUT
    result6.payLeg.currency == "CNY"
    result6.payLeg.notional == -100
    result6.receiveLeg.notional == 200
    result6.receiveLeg.currency == "AED"
  }

  def "should return FXOPTION parse errors"() {
    setup:
    def rows = loadResource("FxOptionTradesInvalid.csv")

    when:
    def parsedRow = LOADER.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Invalid order: Expected 'expiryDate' <= 'paymentDate', but found: '2019-02-28' > '2014-10-10'"
    1   | "Error at line number 3. Error: No enum constant com.solum.xplain.extensions.enums.PositionType.INVALID"
    2   | "Error at line number 4. Error: Unsupported value: SDS. Supported values: [AED, ARS, AUD"
    3   | "Error at line number 5. Error: Detected two legs having the same ID: TRADEFX_LEG1, TRADEFX_LEG1."
    4   | "Error at line number 6. Error: No value was found for 'FXO Call / Put"
    5   | "Error at line number 7. Error: Unable to parse pay/receive from '', must be one of 'Pay', 'Receive', 'Rec', 'P', or 'R' (case insensitive)"
    6   | "Error at line number 8. Error: Unable to parse pay/receive from '', must be one of 'Pay', 'Receive', 'Rec', 'P', or 'R' (case insensitive)"
    7   | "Error at line number 9. Error: Premium Date or Trade Date is required"
  }
}
