package com.solum.xplain.core.curvegroup.curvecredit

import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveFormBuilder.creditCurveForm
import static com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveFormBuilder.creditIndexCurveForm
import static com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveFormBuilder.creditIndexTrancheCurveForm
import static com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveFormBuilder.creditIndexUpdateForm
import static com.solum.xplain.core.curvemarket.CurveMarketSample.getMARKET_STATE_FORM
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE
import static java.util.Collections.emptyList
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.validation.UniqueEntitySupport
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveSearch
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.xva.CoreXvaInstrumentType
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.instrument.InstrumentTypeResolver
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.utils.filter.TableFilter
import groovy.json.JsonGenerator
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [CurveGroupCreditCurveController])
@MockMvcConfiguration
class CurveGroupCreditCurveControllerTest extends Specification {
  @SpringBean
  private CurveGroupCreditCurveService service = Mock()
  @SpringBean
  private CurveGroupCreditCurveExportService exportService = Mock()
  @SpringBean
  private CurveGroupCreditCurveImportService importService = Mock()

  @SpringBean
  private CurveGroupCreditCurveRepository repository = Mock()
  @SpringBean
  private UniqueEntitySupport uniqueEntitySupport = Mock()
  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  private InstrumentTypeResolver instrumentResolver = Mock()

  @Autowired
  private MockMvc mockMvc

  def generator = new JsonGenerator.Options()
  .addConverter(LocalDate) { it.format(ISO_LOCAL_DATE) }
  .excludeFieldsByName("instrument")
  .build()

  @WithMockUser
  def "should not validate non-unique name"() {
    setup:
    service.createCdsCurve(_ as String, _ as CdsCurveForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> true
    instrumentResolver.assetGroupInstruments(CoreAssetGroup.CREDIT) >> []
    when:
    def results = mockMvc.perform(post("/curve-group/curveGroupId/credit-curves/cds")
      .with(csrf())
      .content(generator.toJson(creditCurveForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 412
      getContentAsString().indexOf("Credit curve ID must be unique") >= 0
    }
  }

  @Unroll
  @WithMockUser
  def "should perform validation when creating cds credit curve with response #code #responseBody and form (#form)"() {
    setup:
    service.createCdsCurve(_ as String, _ as CdsCurveForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false
    instrumentResolver.assetGroupInstruments(CoreAssetGroup.CREDIT) >> [CoreXvaInstrumentType.FUNDING]

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/cds")
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                         | code | responseBody
    creditCurveForm()                                                                                            | 200  | "id"
    creditCurveForm { b -> b.recoveryRate(1) }                                                                   | 200  | "id"
    creditCurveForm { b -> b.recoveryRate(0) }                                                                   | 200  | "id"
    creditCurveForm { b -> b.fixedCoupon(0.0025) }                                                               | 200  | "id"
    creditCurveForm { b -> b.currency("EUR") }                                                                   | 200  | "id"
    creditCurveForm { b -> b.quoteConvention("QUOTED_SPREAD") }                                                  | 200  | "id"
    creditCurveForm { b ->
      b.quoteConvention("PAR_SPREAD")
      b.fixedCoupon(null)
    }                                                                                                            | 200  | "id"
    creditCurveForm { b ->
      b.quoteConvention("QUOTED_SPREAD")
      b.fixedCoupon(null)
    }                                                                                                            | 412  | "NotNull.cdsCurveForm.fixedCoupon"
    creditCurveForm { b -> b.versionForm(null) }                                                                 | 412  | "NotNull.cdsCurveForm.versionForm"
    creditCurveForm { b -> b.reference("") }                                                                     | 412  | "NotEmpty.cdsCurveForm.reference"
    creditCurveForm { b -> b.reference("abc") }                                                                  | 412  | "ValidIdentifier.cdsCurveForm.reference"
    creditCurveForm { b -> b.recoveryRate(null) }                                                                | 412  | "NotNull.cdsCurveForm.recoveryRate"
    creditCurveForm { b -> b.recoveryRate(2) }                                                                   | 412  | "Max.cdsCurveForm.recoveryRate"
    creditCurveForm { b -> b.recoveryRate(-1) }                                                                  | 412  | "Min.cdsCurveForm.recoveryRate"
    creditCurveForm { b -> b.recoveryRate(1.5) }                                                                 | 412  | "Max.cdsCurveForm.recoveryRate"
    creditCurveForm { b -> b.recoveryRate(1.1) }                                                                 | 412  | "Max.cdsCurveForm.recoveryRate"
    creditCurveForm { b -> b.currency("?") }                                                                     | 412  | "Credit currency is not valid"
    creditCurveForm { b -> b.currency("AUD") }                                                                   | 412  | "Credit currency is not valid"
    creditCurveForm { b -> b.currency("CAD") }                                                                   | 412  | "Credit currency is not valid"
    creditCurveForm { b -> b.quoteConvention(null) }                                                             | 412  | "NotEmpty.cdsCurveForm.quoteConvention"
    creditCurveForm { b -> b.quoteConvention("Illegal") }                                                        | 412  | "ValidStringSet.cdsCurveForm.quoteConvention"
    creditCurveForm { b -> b.fixedCoupon(null) }                                                                 | 412  | "NotNull.cdsCurveForm.fixedCoupon"
    creditCurveForm { b -> b.fixedCoupon(0.11) }                                                                 | 412  | "com.solum.xplain.api.common.validation.ValidNumberSet.message"
    creditCurveForm { b -> b.seniority(null) }                                                                   | 412  | "NotEmpty.cdsCurveForm.seniority"
    creditCurveForm { b -> b.seniority("random") }                                                               | 412  | "ValidStringSet.cdsCurveForm.seniority"
    creditCurveForm { b -> b.docClause("random") }                                                               | 412  | "ValidStringSet.cdsCurveForm.docClause"
    creditCurveForm { b -> b.docClause(null) }                                                                   | 412  | "NotEmpty.cdsCurveForm.docClause"
    creditCurveForm { b -> b.sector("random") }                                                                  | 412  | "ValidStringSet.cdsCurveForm.sector"
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1Y")])
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y")])
    }                                                                                                            | 200  | "id"
    creditCurveForm { b ->
      b.fundingNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1D"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1W")
      ])
    }                                                                                                            | 412  | "Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y)."
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1M")])
    }                                                                                                            | 412  | "Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y)."
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "9M")])
    }                                                                                                            | 412  | "ValidCreditNodes.cdsCurveForm.fundingNodes"
    creditCurveForm { b ->
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "0M")])
    }                                                                                                            | 200  | "id"
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "0M")])
    }                                                                                                            | 412  | "Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y)."
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "9M")])
    }                                                                                                            | 200  | "id"
    creditCurveForm { b ->
      b.fundingNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1D"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1W")
      ])
    }                                                                                                            | 412  | "Invalid period. Only 0M/3M/6M/9M/Y allowed (6M, 2Y)."
    creditCurveForm { b ->
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "0Y")])
    }                                                                                                            | 412  | "Invalid period. Only 0M/3M/6M/9M/Y allowed (6M, 2Y)."
    creditCurveForm { b ->
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "10Y")])
    }                                                                                                            | 200  | "id"
    creditCurveForm { b ->
      b.fundingNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1Y"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1Y")
      ])
    }                                                                                                            | 412  | "UniqueValues.cdsCurveForm.fundingNodes"
    creditCurveForm { b ->
      b.cdsNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y")
      ])
    }                                                                                                            | 412  | "UniqueValues.cdsCurveForm.cdsNodes"
    creditCurveForm { b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1Y")])
      b.seniority("PREFT1")
    }                                                                                                            | 412  | "Selected seniority does not allow funding nodes"
  }

  @WithMockUser
  def "should return error when funding instrument not registered"() {
    setup:
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false
    instrumentResolver.assetGroupInstruments(CoreAssetGroup.CREDIT) >> []
    def form = creditCurveForm({ b ->
      b.fundingNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.FUNDING, tenor: "1Y")])
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y")])
    })

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/cds")
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 412
      getContentAsString().indexOf("Funding nodes only available with XVA") >= 0
    }
  }

  @WithMockUser
  def "should not return error when funding instrument not registered and funding nodes not passed"() {
    setup:
    service.createCdsCurve(_ as String, _ as CdsCurveForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false
    instrumentResolver.assetGroupInstruments(CoreAssetGroup.CREDIT) >> []
    def form = creditCurveForm({ b ->
      b.cdsNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y")])
    })

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/cds")
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("1") >= 0
    }
  }

  @Unroll
  @WithMockUser
  def "should perform validation when creating credit index credit curve with response #code #responseBody and form (#form)"() {
    setup:
    service.createCreditIndexCurve("curveGroupId", _ as CreditIndexCurveForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/index")
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                           | code | responseBody
    creditIndexCurveForm()                                                                         | 200  | "id"
    creditIndexCurveForm { b -> b.recoveryRate(1) }                                                | 200  | "id"
    creditIndexCurveForm { b -> b.recoveryRate(0) }                                                | 200  | "id"
    creditIndexCurveForm { b -> b.fixedCoupon(0.0025) }                                            | 200  | "id"
    creditIndexCurveForm { b -> b.docClause(null) }                                                | 200  | "id"
    creditIndexCurveForm { b -> b.sector("UNDEFINED") }                                            | 200  | "id"
    creditIndexCurveForm { b -> b.quoteConvention("QUOTED_SPREAD") }                               | 200  | "id"
    creditIndexCurveForm { b ->
      b.quoteConvention("QUOTED_SPREAD")
      b.fixedCoupon(null)
    }                                                                                              | 412  | "NotNull.creditIndexCurveForm.fixedCoupon"
    creditIndexCurveForm { b -> b.versionForm(null) }                                              | 412  | "NotNull.creditIndexCurveForm.versionForm"
    creditIndexCurveForm { b -> b.reference(null) }                                                | 412  | "NotEmpty.creditIndexCurveForm.reference"
    creditIndexCurveForm { b -> b.recoveryRate(null) }                                             | 412  | "NotNull.creditIndexCurveForm.recoveryRate"
    creditIndexCurveForm { b -> b.recoveryRate(2) }                                                | 412  | "Max.creditIndexCurveForm.recoveryRate"
    creditIndexCurveForm { b -> b.recoveryRate(-1) }                                               | 412  | "Min.creditIndexCurveForm.recoveryRate"
    creditIndexCurveForm { b -> b.recoveryRate(1.5) }                                              | 412  | "Max.creditIndexCurveForm.recoveryRate"
    creditIndexCurveForm { b -> b.recoveryRate(1.1) }                                              | 412  | "Max.creditIndexCurveForm.recoveryRate"
    creditIndexCurveForm { b -> b.currency(null) }                                                 | 412  | "NotEmpty.creditIndexCurveForm.currency"
    creditIndexCurveForm { b -> b.currency("?") }                                                  | 412  | "Credit currency is not valid"
    creditIndexCurveForm { b -> b.currency("AUD") }                                                | 412  | "Credit currency is not valid"
    creditIndexCurveForm { b -> b.currency("CAD") }                                                | 412  | "Credit currency is not valid"
    creditIndexCurveForm { b -> b.quoteConvention(null) }                                          | 412  | "NotEmpty.creditIndexCurveForm.quoteConvention"
    creditIndexCurveForm { b -> b.quoteConvention("Illegal") }                                     | 412  | "ValidStringSet.creditIndexCurveForm.quoteConvention"
    creditIndexCurveForm { b -> b.fixedCoupon(null) }                                              | 412  | "NotNull.creditIndexCurveForm.fixedCoupon"
    creditIndexCurveForm { b -> b.fixedCoupon(0.11) }                                              | 412  | "com.solum.xplain.api.common.validation.ValidNumberSet.message"
    creditIndexCurveForm { b -> b.sector("random") }                                               | 412  | "ValidStringSet.creditIndexCurveForm.sector"
    creditIndexCurveForm { b -> b.creditIndexFactor(null) }                                        | 412  | "NotNull.creditIndexCurveForm.creditIndexFactor"
    creditIndexCurveForm { b -> b.entityLongName("IDX") }                                          | 412  | "ValidStringSet.creditIndexCurveForm.entityLongName"
    creditIndexCurveForm { b -> b.creditIndexStartDate(null) }                                     | 412  | "NotNull.creditIndexCurveForm.creditIndexStartDate"
    creditIndexCurveForm { b ->
      b.indexNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX, tenor: "1D"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX, tenor: "1W")
      ])
    }                                                                                              | 412  | "Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y)."
    creditIndexCurveForm { b ->
      b.indexNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX, tenor: "3M")])
    }                                                                                              | 200  | "id"
  }

  @Unroll
  @WithMockUser
  def "should perform validation when creating credit index tranche curve with response #code #responseBody and form (#form)"() {
    setup:
    service.createCreditIndexTrancheCurve("curveGroupId", _ as CreditIndexTrancheCurveForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false

    def results = mockMvc
      .perform(post("/curve-group/curveGroupId/credit-curves/index-tranche")
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                   | code  | responseBody
    creditIndexTrancheCurveForm()                                                                          | 200   | "id"
    creditIndexTrancheCurveForm { b -> b.creditIndexTranche(null) }                                        | 412   | "NotEmpty.creditIndexTrancheCurveForm.creditIndexTranche"
    creditIndexTrancheCurveForm { b -> b.creditIndexTranche("0") }                                         | 412   | "Invalid tranche for credit index. Valid tranches are: [0-15, 15-25, 25-35, 35-100]"
    creditIndexTrancheCurveForm { b -> b.entityLongName(null) }                                            | 200   | "id"
    creditIndexTrancheCurveForm { b ->
      b.indexNodes([
        new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX, tenor: "1Y"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX, tenor: "2Y")
      ])
    }                                                                                                      | 412   | "All nodes must be of type: CREDIT_INDEX_TRANCHE"
    creditIndexTrancheCurveForm { b ->
      b.indexNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX_TRANCHE, tenor: "1M")])
    }                                                                                                      | 412   | "Invalid period. Only 3M/6M/9M/Y allowed (6M, 2Y)."
    creditIndexTrancheCurveForm { b ->
      b.indexNodes([new CreditCurveNodeForm(type: CreditCurveNodeType.CREDIT_INDEX_TRANCHE, tenor: "9M")])
    }                                                                                                      | 200   | "id"
  }

  @Unroll
  @WithMockUser
  def "should perform validation when updating credit index tranche curve with response #code #responseBody and form (#form)"() {
    setup:
    service.updateCreditIndexCurve("curveGroupId", "curveId", LocalDate.now(), _ as CreditIndexCurveUpdateForm) >> right(EntityId.entityId("1"))
    uniqueEntitySupport.existsByCriteria(_ as LocalDate, _ as Criteria, CreditCurve.class) >> false
    repository.getActiveCurveView("curveGroupId", "curveId", LocalDate.now()) >> right(new CreditCurveView(creditIndexTranche: "0-15"))

    def results = mockMvc
      .perform(put("/curve-group/{groupId}/credit-curves/index-tranche/{curveId}/{version}", "curveGroupId", "curveId", LocalDate.now())
      .with(csrf())
      .content(generator.toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                          | code | responseBody
    creditIndexUpdateForm()                                       | 200  | "id"
    creditIndexUpdateForm { b -> b.entityLongName(null) }         | 200  | "id"
    creditIndexUpdateForm { b -> b.entityLongName("ITRAXX_EUR") } | 412  | "Invalid tranche for credit index. Valid tranches are: [0-3, 3-6, 6-12, 12-100]."
  }

  @WithMockUser
  def "should archive credit curve"() {
    setup:
    1 * service.archiveCurve("curveGroupId",
      "curveId",
      parse("2000-01-01"),
      _) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/curve-group/curveGroupId/credit-curves/curveId/2000-01-01/archive")
      .content(generator.toJson([versionForm: [comment: "comment", validFrom: "2000-01-01", stateDate: "2000-01-01", futureVersionsAction: "DELETE"]]))
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should delete credit curve"() {
    setup:
    1 * service.deleteCurve("curveGroupId",
      "curveId",
      parse("2000-01-01")) >> right(EntityId.entityId("1"))

    when:
    def results = mockMvc
      .perform(put("/curve-group/curveGroupId/credit-curves/curveId/2000-01-01/delete")
      .with(csrf()))
      .andReturn()

    then:
    results.response.status == 200
  }


  @WithMockUser
  def "should get credit curves"() {
    setup:
    def stateDate = parse("2016-01-01")
    1 * service.getCurves("curveGroupId", {
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    },
    false,
    Sort.by(CreditCurve.Fields.curveType, CreditCurve.Fields.reference)) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves")
      .param("stateDate", "2016-01-01")
      .param("withArchived", "false"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get credit curve"() {
    setup:
    1 * service.getCurve("groupId",
      "1",
      parse("2016-01-01")) >> right(new CreditCurveView(seniority: "SNRFOR", corpTicker: "BARC", currency: "EUR", curveType: CreditCurveType.CDS, entityLongName: "LONG"))

    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/credit-curves/1?stateDate=2016-01-01"))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get credit curve CDS nodes"() {
    setup:
    def groupId = "groupId"
    def curveId = "1"
    def version = "2016-01-01"
    def valuationDate = "2016-01-02"
    def discountingType = CalculationDiscountingType.DISCOUNT_USD

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/credit-curves/$curveId/$version/cds-nodes")
      .param("valuationDate", valuationDate)
      .param("discountingType", discountingType.name())
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name()))

    then:
    1 * service.getCurveCdsNodes(groupId,
      curveId,
      parse(version),
      parse(valuationDate),
      MARKET_STATE_FORM,
      discountingType) >> right(emptyList())

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  @WithMockUser
  def "should get credit curve CDS nodes with price requirements"() {
    setup:
    def groupId = "groupId"
    def curveId = "1"
    def version = "2016-01-01"
    def valuationDate = "2016-01-02"
    def discountingType = CalculationDiscountingType.DISCOUNT_USD
    def stateForm =
      new CurveConfigMarketStateForm("marketId",
      "configId",
      RAW_PRIMARY,
      LocalDate.of(2023, 1, 2),
      LocalDate.of(2023, 1, 1),
      new InstrumentPriceRequirementsForm(curvesPriceType: BID_PRICE,
      dscCurvesPriceType: BID_PRICE,
      fxRatesPriceType: BID_PRICE,
      volsPriceType: BID_PRICE,
      volsSkewsPriceType: BID_PRICE))

    when:
    def results = mockMvc
      .perform(get("/curve-group/$groupId/credit-curves/$curveId/$version/cds-nodes")
      .param("valuationDate", valuationDate)
      .param("discountingType", discountingType.name())
      .param("marketDataGroupId", stateForm.marketDataGroupId)
      .param("stateDate", stateForm.stateDate.toString())
      .param("curveDate", stateForm.curveDate.toString())
      .param("configurationId", stateForm.configurationId)
      .param("marketDataSource", stateForm.marketDataSource.name())
      .param("priceRequirements.curvesPriceType", stateForm.priceRequirements.curvesPriceType.name())
      .param("priceRequirements.dscCurvesPriceType", stateForm.priceRequirements.dscCurvesPriceType.name())
      .param("priceRequirements.fxRatesPriceType", stateForm.priceRequirements.fxRatesPriceType.name())
      .param("priceRequirements.volsPriceType", stateForm.priceRequirements.volsPriceType.name())
      .param("priceRequirements.volsSkewsPriceType", stateForm.priceRequirements.volsSkewsPriceType.name()))

    then:
    1 * service.getCurveCdsNodes(groupId,
      curveId,
      parse(version),
      parse(valuationDate),
      stateForm,
      discountingType) >> right(emptyList())

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  @WithMockUser
  def "should get credit curve chart points"() {
    setup:
    when:
    def results = mockMvc
      .perform(get("/curve-group/groupId/credit-curves/1/cds-chart-points")
      .param("marketDataGroupId", MARKET_STATE_FORM.marketDataGroupId)
      .param("stateDate", MARKET_STATE_FORM.stateDate.toString())
      .param("curveDate", MARKET_STATE_FORM.curveDate.toString())
      .param("configurationId", MARKET_STATE_FORM.configurationId)
      .param("marketDataSource", MARKET_STATE_FORM.marketDataSource.name()))
      .andReturn()

    then:
    1 * service.getCurveCdsChartPoints("groupId", "1", MARKET_STATE_FORM, null) >> right(emptyList())

    and:
    results.response.status == 200
  }

  @WithMockUser
  @Unroll
  def "should search CDS curve future versions"() {
    setup:
    1 * service.getFutureVersions("curveGroupId",
      new CreditCurveSearch("BARC_EUR_SNRFOR_CR14", parse("2018-05-05"))) >> right(DateList.uniqueSorted([]))

    when:
    def results = mockMvc.perform(get("/curve-group/curveGroupId/credit-curves/cds/future-versions/search")
      .params(cdsCurveSearchParams())
      .param("stateDate", "2018-05-05"))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("[]") >= 0
    }
  }

  @WithMockUser
  @Unroll
  def "should search curve #curveName future versions for #pathSegment #params"() {
    setup:
    1 * service.getFutureVersions("curveGroupId",
      new CreditCurveSearch(curveName, parse("2018-05-05"))) >> right(DateList.uniqueSorted([]))

    when:
    def results = mockMvc.perform(get("/curve-group/curveGroupId/credit-curves/$pathSegment/future-versions/search")
      .params(params)
      .param("stateDate", "2018-05-05"))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("[]") >= 0
    }

    where:
    curveName              | pathSegment     | params
    "BARC_EUR_SNRFOR_CR14" | "cds"           | cdsCurveSearchParams()
    "REFERENCE_EUR"        | "index"         | cdsIndexCurveSearchParams()
    "REF_0-15_EUR"         | "index-tranche" | cdsIndexTrancheCurveSearchParams()
  }

  static MultiValueMap<String, String> cdsIndexCurveSearchParams() {
    def map = new LinkedMultiValueMap<String, String>()
    map.put("reference", ["REFERENCE"])
    map.put("currency", ["EUR"])
    return map
  }

  static MultiValueMap<String, String> cdsIndexTrancheCurveSearchParams() {
    def map = new LinkedMultiValueMap<String, String>()
    map.put("reference", ["REF"])
    map.put("tranche", ["0-15"])
    map.put("currency", ["EUR"])
    return map
  }

  static MultiValueMap<String, String> cdsCurveSearchParams() {
    def map = new LinkedMultiValueMap<String, String>()
    map.put("reference", ["BARC"])
    map.put("currency", ["EUR"])
    map.put("seniority", ["SNRFOR"])
    map.put("docClause", ["CR14"])
    return map
  }

  def "should get credit curves CSV"() {
    setup:
    def stateDate = parse("2018-05-05")
    1 * exportService.getAllCurvesCsv("curveGroupId", {
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    },
    TableFilter.emptyTableFilter(),
    null,
    Sort.by(CreditCurve.Fields.curveType, CreditCurve.Fields.reference)) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    when:
    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves/credit-curves-csv")
      .param("stateDate", stateDate.toString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.getResponse().getStatus() == 200
  }

  def "should get all credit curves CDS nodes CSV"() {
    setup:
    exportService.getAllCurvesNodesCsv(_ as String,
      _ as BitemporalDate,
      _ as TableFilter,
      _ as Sort) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves/nodes/csv")
      .param("stateDate", "2016-01-01")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    results.getResponse().getStatus() == 200
  }

  def "should get credit curve CDS nodes CSV"() {
    setup:
    exportService.getCurveCdsNodesCsv(_ as String,
      _ as String,
      _ as LocalDate,
      _ as LocalDate,
      _ as CurveConfigMarketStateForm,
      null) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves/id/2016-01-01/cds-nodes/csv")
      .param("curveGroupStateDate", "2018-05-05")
      .param("valuationDate", "2018-05-05")
      .param("marketDataGroupId", "id")
      .param("configurationId", "id")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    results.getResponse().getStatus() == 200
  }

  def "should get credit curve index nodes CSV"() {
    setup:
    exportService.getCurveIndexNodesCsv(_ as String,
      _ as String,
      _ as LocalDate,
      _ as LocalDate,
      _ as CurveConfigMarketStateForm,
      null) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    def results = mockMvc
      .perform(get("/curve-group/curveGroupId/credit-curves/id/2016-01-01/index-nodes/csv")
      .param("curveGroupStateDate", "2018-05-05")
      .param("valuationDate", "2018-05-05")
      .param("marketDataGroupId", "id")
      .param("configurationId", "id")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    results.getResponse().getStatus() == 200
  }

  @WithMockUser
  def "should upload curves CSV"() {
    setup:
    1 * importService.uploadCurves("curveGroupId", {
      it.stateDate == parse("2018-05-05") && it.duplicateAction == ERROR
    },
    _) >> right([EntityId.entityId("entityId")])

    when:
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    def results = mockMvc.perform(multipart("/curve-group/curveGroupId/credit-curves/upload")
      .file(file)
      .param("stateDate", "2018-05-05")
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("entityId") >= 0
    }
  }

  @WithMockUser
  def "should upload all curves nodes CSV"() {
    setup:
    1 * importService.uploadNodes("curveGroupId", {
      it.stateDate == parse("2018-05-05") && it.duplicateAction == ERROR
    },
    _) >> right([EntityId.entityId("entityId")])

    when:
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    def results = mockMvc.perform(multipart("/curve-group/curveGroupId/credit-curves/upload-nodes")
      .file(file)
      .param("stateDate", "2018-05-05")
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("entityId") >= 0
    }
  }

  @WithMockUser
  def "should upload curve nodes CSV"() {
    setup:
    1 * importService.uploadNodesForCurve("curveGroupId",
      "curveId",
      parse("1970-01-01"),
      CreditCurveNodeType.CDS, {
        it.stateDate == parse("2018-05-05") && it.duplicateAction == ERROR
      },
      _) >> right([EntityId.entityId("entityId")])

    when:
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    def results = mockMvc.perform(multipart("/curve-group/curveGroupId/credit-curves/curveId/1970-01-01/upload-nodes")
      .file(file)
      .param("nodeType", CreditCurveNodeType.CDS.name())
      .param("stateDate", "2018-05-05")
      .param("duplicateAction", ERROR.name())
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("entityId") >= 0
    }
  }
}
