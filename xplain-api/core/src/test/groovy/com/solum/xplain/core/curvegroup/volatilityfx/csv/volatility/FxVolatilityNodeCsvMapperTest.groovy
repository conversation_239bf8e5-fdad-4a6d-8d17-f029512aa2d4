package com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView
import spock.lang.Specification

class FxVolatilityNodeCsvMapperTest extends Specification {

  def "should return correctly mapped csv row"() {
    setup:
    def value = new FxVolatilityNodeValueView(domesticCurrency: "EUR", foreignCurrency:
    "USD", expiry: "1Y", delta1: 1, delta2: 2, value: 10D)
    when:
    def mapper = new FxVolatilityNodeCsvMapper(null)
    def row = mapper.toCsvRow(value)
    def csv = new CsvOutputFile(mapper.header(), [row])
    def result = csv.write()

    then:
    result == """Expiry,CcyPair
1Y,EUR/USD
"""
  }
}
