package com.solum.xplain.core.curvegroup.curve

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.nonDeleted
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupViewBuilder.curveGroupView
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions
import com.solum.xplain.core.curvegroup.curve.dto.GetCalibratedCurvesRequest
import com.solum.xplain.core.curvegroup.curve.value.CurveView
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import spock.lang.Specification
import spock.lang.Unroll

class CurveGroupCurveServiceTest extends Specification {
  def static CURVE_GROUP_ID = "curveGroupId"
  def static STATE_DATE = now()

  def curveGroupRepository = Mock(CurveGroupRepository)
  def curveGroupCurveRepository = Mock(CurveGroupCurveRepository)
  def marketDataQuotesSupport = Mock(MarketDataQuotesSupport)

  def service = new CurveGroupCurveService(curveGroupRepository, curveGroupCurveRepository, marketDataQuotesSupport)

  @Unroll
  def "should get curves with archived curves #withArchived"() {
    setup:
    def getCurveRequest = GetCalibratedCurvesRequest.newOf(STATE_DATE, withArchived, null, null, null, null, null, null, null)
    def stateDate = BitemporalDate.newOf(STATE_DATE)

    when:
    def result = service.getCurves(CURVE_GROUP_ID, stateDate, getCurveRequest)

    then:
    1 * curveGroupRepository.getEither(CURVE_GROUP_ID) >> right(curveGroupView(curveGroup()))
    1 * curveGroupCurveRepository.getCurveViews(CURVE_GROUP_ID, stateDate, getCurveRequest, filter) >>
      [new CurveView()]

    and:
    result.isRight()
    def views = result.right().get() as List<CurveView>
    views.size() == 1

    where:
    withArchived | filter
    true         | nonDeleted()
    false        | active()
  }

  def "should get curve nodes when resolved market data is duplicated"() {
    def version = BitemporalDate.newOfNow()
    def group = curveGroup()
    def groupView = curveGroupView(group)
    def marketStateForm = Mock(CurveConfigMarketStateForm)
    def calibrationOptions = new CalibratedCurvesOptions(now(), null, null)
    def quotes = [
      "k1": new CalculationMarketValueFullView(key: "k1"),
      "k2": new CalculationMarketValueFullView(key: "k1"),
    ]
    def marketStateQuotes = new CurveConfigMarketStateQuotes(marketStateForm, quotes)

    when:
    def result = service.getCurveNodes(
      "groupId",
      "curveId",
      version,
      marketStateForm,
      calibrationOptions)

    then:
    1 * curveGroupRepository.getEither("groupId") >> right(groupView)
    1 * marketDataQuotesSupport.getFullQuotes(marketStateForm) >> quotes
    1 * curveGroupCurveRepository.getCurveNodes(
      groupView, "curveId", version, marketStateQuotes, calibrationOptions) >> []

    and:
    result.isRight()
  }
}
