package com.solum.xplain.core.curvegroup.curvecredit.csv.node

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.ANY
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CDS
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CREDIT_INDEX
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CREDIT_INDEX_TRANCHE
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.FUNDING
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.IMPORT_ERROR
import static com.solum.xplain.core.error.Error.IMPORT_INFO
import static com.solum.xplain.core.error.Error.IMPORT_WARNING
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.csv.ItemsGroupCsvResult
import com.solum.xplain.core.common.csv.NamedList
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.curvegroup.curvecredit.CreditCurveMapper
import com.solum.xplain.core.curvegroup.curvecredit.CurveGroupCreditCurveRepository
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNode
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveFundingNode
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveUpdateForm
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.InfoItem
import com.solum.xplain.core.error.WarningItem
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class CreditCurveNodeCsvImportServiceTest extends Specification {
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static GROUP_ID = "curveGroupId"
  def static CURVE_ID = "curveId"
  def static FILE_CONTENT = [] as byte[]


  def auditEntryService = Mock(AuditEntryService)
  def csvLoaderFactory = Mock(CreditCurveNodesCsvLoaderFactory)
  def curveMapper = Mock(CreditCurveMapper)
  def repository = Mock(CurveGroupCreditCurveRepository)

  def csvLoader = Mock(CreditCurveNodesCsvLoader)

  def nodeImport = Spy(new CreditCurveNodeCsvImportService(
  auditEntryService,
  csvLoaderFactory,
  curveMapper,
  repository)
  )

  def setup() {
    csvLoaderFactory.get(_ as CreditCurveNodeType) >> csvLoader
  }

  def "should return errors when importing for curve and curve not found"() {
    setup:
    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> left(OBJECT_NOT_FOUND.entity())
    0 * repository.getFutureVersions(_, _)
    0 * csvLoader.parse(_, _, _)
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == OBJECT_NOT_FOUND

    where:
    nodeType << [CDS, FUNDING]
  }

  def "should return errors when importing for curve and csv parsing error"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult([PARSING_ERROR.entity()])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR

    where:
    nodeType << [CDS, FUNDING]
  }

  def "should return errors when importing for curve and csv parsing error and nodes"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [IMPORT_ERROR.entity()],
      curve.name,
      [nodeForm(nodeType, "1M")]
      )

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR

    where:
    nodeType << [CDS, FUNDING, CREDIT_INDEX]
  }

  def "should import nodes for curve and return success result when ERROR"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm(nodeType, "1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], nodeType, _, csvResult.namedLists) >>
    [InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)

    where:
    nodeType << [CDS, FUNDING, CREDIT_INDEX]
  }

  def "should import nodes for curve and return error result"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm(nodeType, "1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, REPLACE) >> csvResult
    1 * nodeImport.importNodes([curve], nodeType, _, csvResult.namedLists) >>
    [new ErrorItem(MISSING_ENTRY, "error description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "error description"

    where:
    nodeType << [CDS, FUNDING, CREDIT_INDEX]
  }

  def "should import nodes for curve with warnings"() {
    setup:
    CreditCurve curve = curve([], [])
    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "info description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [],
      [warning],
      curve.name,
      [nodeForm(nodeType, "1M")]
      )

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], nodeType, _, csvResult.namedLists) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT
      )

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)

    where:
    nodeType << [CDS, FUNDING, CREDIT_INDEX]
  }

  def "should fail import nodes for curve with warnings"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [new ErrorItem(IMPORT_ERROR, "error description")],
      [WarningItem.of(IMPORT_WARNING, "warning description")],
      curve.name,
      [nodeForm(nodeType, "1M")])

    1 * repository.getActiveCurve(GROUP_ID, CURVE_ID, STATE_DATE) >> right(curve)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parseForCurve(FILE_CONTENT, curve, ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForCurve(
      GROUP_ID,
      CURVE_ID,
      STATE_DATE,
      nodeType,
      importOptions,
      FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"

    where:
    nodeType << [CDS, FUNDING, CREDIT_INDEX]
  }

  def "should return errors when importing for all curves and csv parsing error"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult([PARSING_ERROR.entity()])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes([curve], _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors[0].reason == PARSING_ERROR
  }

  def "should return errors when importing for all curves and csv parsing error and nodes"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [IMPORT_ERROR.entity()],
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")]
      )

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
  }

  def "should import nodes for all curves and return success result"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")]
      )

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], ANY, _, csvResult.namedLists) >>
    [InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should fail import nodes for all curves and return errors"() {
    setup:
    CreditCurve curve = curve([], [])
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")]
      )

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], ANY, _, csvResult.namedLists) >>
    [new ErrorItem(MISSING_ENTRY, "error description")]
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "error description"
  }

  def "should import nodes for all curves and log info and warning items"() {
    setup:
    CreditCurve curve = curve([], [])
    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(curve.entityId), "info description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [],
      [warning],
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    1 * nodeImport.importNodes([curve], ANY, _, csvResult.namedLists) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, [info, warning])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(curve.entityId)
  }

  def "should fail import nodes for all curves and log error and warning items"() {
    setup:
    CreditCurve curve = curve([], [])
    ErrorItem error = new ErrorItem(IMPORT_ERROR, "error description")
    WarningItem warning = WarningItem.of(IMPORT_WARNING, "warning description")
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      [error],
      [warning],
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")])

    1 * repository.getActiveCurves(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [curve]
    0 * repository.getFutureVersions(_)
    1 * csvLoader.parse(FILE_CONTENT, [curve], ERROR) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, [error, warning])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importForAll(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"
  }

  def "should fail with unexpected action REPLACE_DELETE"() {
    setup:
    CreditCurve curve = curve([cdsNode("1M")], [fundingNode("1M")])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [
        nodeForm(CDS, "2M"),
        nodeForm(CDS, "3M"),
        nodeForm(FUNDING, "2M"),
        nodeForm(FUNDING, "3M"),
      ]
      )

    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  def "should fail with unexpected action REPLACE"() {
    setup:
    CreditCurve curve = curve([cdsNode("1M")], [fundingNode("1M")])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [
        nodeForm(CDS, "1M"),
        nodeForm(CDS, "3M"),
        nodeForm(FUNDING, "1M"),
        nodeForm(FUNDING, "3M"),
      ]
      )

    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  @Unroll
  def "should return MISSING_ENTRY with missing nodes when node type #nodeType and ERROR "() {
    setup:
    CreditCurve curve = curve([cdsNode("1M")], [fundingNode("1M")])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(CDS, "3M"), nodeForm(FUNDING, "3M")]
      )

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == expectedErrorCount
    for (idx in 0..<expectedErrorCount) {
      result[idx].reason == MISSING_ENTRY
      result[idx].description.endsWith("- 1M in curve ticker_currency_seniority_doc is missing")
    }

    where:
    nodeType | expectedErrorCount
    CDS      | 1
    FUNDING  | 1
    ANY      | 2
  }

  @Unroll
  def "should not update curve with only duplicate nodes when node type #nodeType and ERROR"() {
    setup:
    CreditCurve curve = curve([cdsNode("1M")], [fundingNode("1M")])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")]
      )

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 0

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  @Unroll
  def "should return NEW_VERSION_VIABLE when node type #nodeType and ERROR"() {
    setup:
    CreditCurve curve = curve([], [], PAST_VERSION_DATE)
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(CDS, "1M"), nodeForm(FUNDING, "1M")]
      )

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == NEW_VERSION_VIABLE
    result[0].description == "New version is viable for ticker_currency_seniority_doc"

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  @Unroll
  def "should return FUTURE_VERSION_EXISTS when node type #nodeType and ERROR"() {
    setup:
    CreditCurve curve = curve([], [])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(curve.name, [nodeForm(CDS, "1M")])

    1 * repository.getFutureVersions(_, _) >> new DateList([FUTURE_VERSION_DATE])
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    result[0].reason == FUTURE_VERSION_EXISTS
    result[0].description == "ticker_currency_seniority_doc has future version(s)"

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  @Unroll
  def "should return empty result when no errors when type #nodeType and ERROR"() {
    setup:
    CreditCurve curve = curve([cdsNode("1M")], [fundingNode("1M")])
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [
        nodeForm(CDS, "1M"),
        nodeForm(CDS, "3M"),
        nodeForm(FUNDING, "1M"),
        nodeForm(FUNDING, "3M")
      ]
      )

    returnNoFutureVersions()
    doNotUpdate(curveForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.isEmpty()

    where:
    nodeType << [CDS, FUNDING, ANY]
  }

  @Unroll
  def "should correctly update curve when node type #nodeType and APPEND"() {
    setup:
    CreditCurve curve = curve(
      [cdsNode("1M"), cdsNode("1W")],
      [fundingNode("1M"), fundingNode("1W")]
      )
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [
        nodeForm(CDS, "1M"),
        nodeForm(CDS, "3M"),
        nodeForm(FUNDING, "1M"),
        nodeForm(FUNDING, "3M")
      ])

    doUpdate(curve, curveForm, expectedCdsUpdateForms, expectedFundingUpdateForms)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(curve.entityId)

    where:
    nodeType | expectedCdsUpdateForms                                          | expectedFundingUpdateForms
    CDS      | [nodeForm(CDS, "1M"), nodeForm(CDS, "1W"), nodeForm(CDS, "3M")] | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "1W")]
    FUNDING  | [nodeForm(CDS, "1M"), nodeForm(CDS, "1W")]                      | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "1W"), nodeForm(FUNDING, "3M")]
    ANY      | [nodeForm(CDS, "1M"), nodeForm(CDS, "1W"), nodeForm(CDS, "3M")] | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "1W"), nodeForm(FUNDING, "3M")]
  }

  @Unroll
  def "should correctly update curve when node type #nodeType and APPEND - INDEX curves"() {
    setup:
    CreditCurve curve = curve([], [], STATE_DATE, curveType)

    CreditIndexCurveUpdateForm curveForm = curveIndexForm(curve)
    1 * curveForm.setIndexNodes({
      it.size() == expectedForms.size() && it.containsAll(expectedForms)
    })
    1 * curveForm.setVersionForm({ it.validFrom == curve.validFrom })
    1 * repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, curveForm) >>
      right(entityId(curve.entityId))

    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [nodeForm(nodeType, "1Y"), nodeForm(nodeType, "2Y"),])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(curve.entityId)

    where:
    curveType                            | nodeType             | expectedForms
    CreditCurveType.CREDIT_INDEX         | CREDIT_INDEX         | [nodeForm(CREDIT_INDEX, "1Y"), nodeForm(CREDIT_INDEX, "2Y"),]
    CreditCurveType.CREDIT_INDEX_TRANCHE | CREDIT_INDEX_TRANCHE | [nodeForm(CREDIT_INDEX_TRANCHE, "1Y"), nodeForm(CREDIT_INDEX_TRANCHE, "2Y")]
  }

  @Unroll
  def "should correctly update curve when node type #nodeType and APPEND_DELETE"() {
    setup:
    CreditCurve curve = curve(
      [cdsNode("1M"), cdsNode("1W")],
      [fundingNode("1M"), fundingNode("1W")]
      )
    CdsCurveUpdateForm curveForm = curveForm(curve)
    ItemsGroupCsvResult csvResult = nodeGroupCsvResult(
      curve.name,
      [
        nodeForm(CDS, "1M"),
        nodeForm(CDS, "3M"),
        nodeForm(FUNDING, "1M"),
        nodeForm(FUNDING, "3M"),
      ]
      )

    doUpdate(curve, curveForm, expectedCdsUpdateForms, expectedFundingUpdateForms)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes([curve], nodeType, importOptions, csvResult.namedLists)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(curve.entityId)

    where:
    nodeType | expectedCdsUpdateForms                     | expectedFundingUpdateForms
    CDS      | [nodeForm(CDS, "1M"), nodeForm(CDS, "3M")] | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "1W")]
    FUNDING  | [nodeForm(CDS, "1M"), nodeForm(CDS, "1W")] | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "3M")]
    ANY      | [nodeForm(CDS, "1M"), nodeForm(CDS, "3M")] | [nodeForm(FUNDING, "1M"), nodeForm(FUNDING, "3M")]
  }

  def curve(List<CreditCurveCdsNode> cdsNodes, List<CreditCurveFundingNode> fundingNodes, validFrom = STATE_DATE, CreditCurveType type = CreditCurveType.CDS) {
    def curve = Mock(CreditCurve)
    curve.curveType >> type
    curve.curveGroupId >> GROUP_ID
    curve.entityId >> entityId("curve_entityId").id
    curve.validFrom >> validFrom
    curve.name >> "ticker_currency_seniority_doc"
    curve.cdsNodes >> cdsNodes
    curve.fundingNodes >> fundingNodes
    curve.indexNodes >> []
    return curve
  }

  def cdsNode(String tenor) {
    def node = new CreditCurveCdsNode(tenor: tenor)
    def nodeForm = nodeForm(CDS, tenor)

    _ * curveMapper.toNodeForm(node) >> nodeForm
    return node
  }

  def fundingNode(String tenor) {
    def node = new CreditCurveFundingNode(tenor: tenor)
    def nodeForm = nodeForm(FUNDING, tenor)

    _ * curveMapper.toNodeForm(node) >> nodeForm
    return node
  }

  static CreditCurveNodeForm nodeForm(CreditCurveNodeType type, String tenor) {
    new CreditCurveNodeForm(type: type, tenor: tenor)
  }

  def curveForm(CreditCurve curve) {
    def curveForm = Mock(CdsCurveUpdateForm)
    _ * curveMapper.toCdsUpdateForm(curve) >> curveForm
    return curveForm
  }

  def curveIndexForm(CreditCurve curve) {
    def curveForm = Mock(CreditIndexCurveUpdateForm)
    _ * curveMapper.toIndexUpdateForm(curve) >> curveForm
    return curveForm
  }

  static ItemsGroupCsvResult nodeGroupCsvResult(
    List<ErrorItem> errors,
    List<WarningItem> warnings,
    String curveName,
    List<CreditCurveNodeForm> nodesForms) {
    return new ItemsGroupCsvResult(errors, warnings, [new NamedList<>(curveName, nodesForms)])
  }

  static ItemsGroupCsvResult nodeGroupCsvResult(List<ErrorItem> errors) {
    return new ItemsGroupCsvResult(errors, [], [])
  }

  def nodeGroupCsvResult(List<ErrorItem> errors,
    String curveName,
    List<CreditCurveNodeForm> nodesForms) {
    return nodeGroupCsvResult(errors, [], curveName, nodesForms)
  }

  def nodeGroupCsvResult(String curveName, List<CreditCurveNodeForm> nodesForms) {
    return nodeGroupCsvResult([], [], curveName, nodesForms)
  }

  def doNotUpdate(CdsCurveUpdateForm curveForm) {
    0 * curveForm.setCdsNodes(_)
    0 * curveForm.setFundingNodes(_)
    0 * curveForm.setVersionForm(_)
    0 * repository.updateCurve(_, _, _, _)
  }

  def returnNoFutureVersions() {
    1 * repository.getFutureVersions(_, _) >> new DateList([])
  }

  def doUpdate(
    CreditCurve curve,
    CdsCurveUpdateForm curveForm,
    List<CreditCurveNodeForm> cdsNodeForms,
    List<CreditCurveNodeForm> fundingNodeForms) {

    1 * curveForm.setCdsNodes({
      it.size() == cdsNodeForms.size() &&
        cdsNodeForms.withIndex().collect { elem, idx -> elem == cdsNodeForms[idx as Integer] }
    })
    1 * curveForm.setFundingNodes({
      it.size() == fundingNodeForms.size() &&
        fundingNodeForms.withIndex().collect { elem, idx -> elem == fundingNodeForms[idx as Integer] }
    })
    1 * curveForm.setVersionForm({ it.validFrom == curve.validFrom })
    1 * repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, curveForm) >>
      right(entityId(curve.entityId))
  }
}
