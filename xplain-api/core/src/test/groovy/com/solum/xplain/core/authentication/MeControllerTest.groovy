package com.solum.xplain.core.authentication

import static com.solum.xplain.core.users.UserBuilder.user
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.ObjectIdMapperImpl
import com.solum.xplain.core.roles.RoleRepository
import com.solum.xplain.core.roles.value.RoleView
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamListView
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.users.UserMapperImpl
import com.solum.xplain.core.users.value.CurrentUserView
import com.solum.xplain.core.users.value.UserRoleView
import com.solum.xplain.core.users.value.UserTeamView
import io.atlassian.fugue.Either
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.core.Authentication
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [MeController])
@ContextConfiguration(classes = [MeController.class, ObjectIdMapperImpl.class, UserMapperImpl.class])
class MeControllerTest extends Specification {

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper
  @SpringBean
  private AuthenticationContext userRepository = Mock()
  @SpringBean
  private TeamRepository teamRepository = Mock()
  @SpringBean
  private RoleRepository roleRepository = Mock()
  @SpringBean
  private AuthenticationCacheService authenticationCacheService = Mock()

  @WithMockUser
  @Unroll
  def "should get me user"() {
    setup:
    def user = user()
    def roleView = new RoleView(id: user.getRoles()[0].toString(), name: "ROLE_NAME")

    userRepository.userEither(_ as Authentication) >> Either.right(user)
    teamRepository.teamListView(user.getTeams()[0].toString()) >> Either.right(new TeamListView(name: "TEAM_NAME"))
    roleRepository.getView(user.getRoles()[0].toString()) >> Either.right(roleView)

    when:
    def results = mockMvc.perform(get('/me')
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    objectMapper.readValue(results.getResponse().getContentAsString(), CurrentUserView.class) == new CurrentUserView(
      id: user.getId(),
      username: user.getName(),
      teams: [new UserTeamView(user.getTeams()[0].toString(), "TEAM_NAME")],
      permissions: user.getPermissions(),
      roles: [new UserRoleView(roleView.getId(), roleView.getName())])
  }

  @WithMockUser
  @Unroll
  def "should logout user"() {
    setup:
    def user = user()
    userRepository.userEither(_ as Authentication) >> Either.right(user)
    1 * authenticationCacheService.revokeCachedPrincipal(user)

    when:
    def results = mockMvc.perform(post('/me/logout')
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString() == "logout successful"
  }
}
