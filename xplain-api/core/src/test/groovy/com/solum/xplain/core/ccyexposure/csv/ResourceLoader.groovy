package com.solum.xplain.core.ccyexposure.csv

import static com.google.common.io.ByteSource.wrap

import com.google.common.io.ByteStreams
import com.opengamma.strata.collect.io.CsvIterator
import com.opengamma.strata.collect.io.CsvRow
import com.opengamma.strata.collect.io.UnicodeBom

trait ResourceLoader {
  List<CsvRow> loadResourceAsCsvRow(String fileName) {
    byte[] content = loadResourceAsBytes(fileName)
    return CsvIterator.of(UnicodeBom.toCharSource(wrap(content)), true)
      .asStream()
      .toList()
  }

  byte[] loadResourceAsBytes(String fileName) {
    def content = ByteStreams.toByteArray(getClass().getResourceAsStream("/ccyexposure/csv/" + fileName))
    content
  }
}
