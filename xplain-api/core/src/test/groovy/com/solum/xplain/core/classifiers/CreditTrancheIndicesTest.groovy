package com.solum.xplain.core.classifiers

import static com.solum.xplain.core.classifiers.CdsIndex.CDX_NA_HY
import static com.solum.xplain.core.classifiers.CdsIndex.CDX_NA_IG
import static com.solum.xplain.core.classifiers.CdsIndex.ITRAXX_EUR
import static com.solum.xplain.core.classifiers.CdsIndex.ITRAXX_EUR_XOVER

import spock.lang.Specification

class CreditTrancheIndicesTest extends Specification {

  def "should return correct indices for #tranche"() {
    expect:
    var indices = CreditTrancheIndices.permissibleIndicesForTranche(tranche)
    indices.size() == expectedIndices.size()
    indices.containsAll(expectedIndices)

    where:
    tranche                       | expectedIndices
    CreditTranches.TRANCHE_0_15   | [CDX_NA_HY, ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_0_10   | [ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_15_25  | [CDX_NA_HY, ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_10_20  | [ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_20_35  | [ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_25_35  | [CDX_NA_HY, ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_35_100 | [CDX_NA_HY, ITRAXX_EUR_XOVER]
    CreditTranches.TRANCHE_0_3    | [CDX_NA_IG, ITRAXX_EUR]
    CreditTranches.TRANCHE_3_7    | [CDX_NA_IG]
    CreditTranches.TRANCHE_7_15   | [CDX_NA_IG]
    CreditTranches.TRANCHE_15_100 | [CDX_NA_IG]
    CreditTranches.TRANCHE_3_6    | [ITRAXX_EUR]
    CreditTranches.TRANCHE_6_12   | [ITRAXX_EUR]
    CreditTranches.TRANCHE_12_100 | [ITRAXX_EUR]
  }
}
