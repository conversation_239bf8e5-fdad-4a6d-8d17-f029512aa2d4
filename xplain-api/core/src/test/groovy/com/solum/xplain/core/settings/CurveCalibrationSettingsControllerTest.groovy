package com.solum.xplain.core.settings

import static com.solum.xplain.core.common.EntityId.entityId
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings
import com.solum.xplain.core.settings.form.ConvexityAdjustmentsSettingsForm
import com.solum.xplain.core.settings.form.InflationSeasonalitySettingsForm
import com.solum.xplain.core.settings.service.CalibrationSettingsControllerService
import com.solum.xplain.core.settings.service.ConvexitySettingsUploadService
import com.solum.xplain.core.settings.service.SeasonalityAdjustmentCalculationService
import com.solum.xplain.core.settings.service.SeasonalitySettingsUploadService
import com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType
import com.solum.xplain.core.settings.value.InflationSeasonalitySettingsView
import com.solum.xplain.core.test.MockMvcConfiguration
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [CurveCalibrationSettingsController])
class CurveCalibrationSettingsControllerTest extends Specification {

  @SpringBean
  CalibrationSettingsControllerService service = Mock()
  @SpringBean
  SeasonalitySettingsUploadService uploadService = Mock()
  @SpringBean
  ConvexitySettingsUploadService convexitySettingsImport = Mock()
  @SpringBean
  SeasonalityAdjustmentCalculationService seasonalityCalculationService = Mock()

  @Autowired
  private ObjectMapper mapper

  @Autowired
  private MockMvc mockMvc


  def "should get curve inflation settings"() {
    setup:
    service.getCalibrationInflationSettings(_) >> new InflationSeasonalitySettingsView()

    when:
    def results = mockMvc.perform(get("/settings/calibration/inflation")
      .param("stateDate", "1999-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get curve inflation settings versions"() {
    setup:
    service.getCalibrationInflationSettingsVersions() >> []

    when:
    def results = mockMvc.perform(get("/settings/calibration/inflation/versions")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  @Unroll
  def "should save curve inflation settings #form"() {
    setup:
    service.saveInflationSesionalitySettings(LocalDate.parse("1999-01-01"), _ as
      InflationSeasonalitySettingsForm) >> Either.right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/settings/calibration/inflation/1999-01-01")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response
    results.getResponse().getContentAsString().indexOf(responseBody) >= 0

    where:
    form                             | response | responseBody
    formInflationManual("GB-RPI", 1) | 200      | "id"
    emptyFormInflation()             | 200      | "id"
    formInflationManual("GB-RPI", 5) | 412      | "Additive factors for Inflation seasonality should add to zero"
    formInflationManual("XXX", 1)    | 412      | "ValidStringSet.inflationSeasonalitySettingsForm.curveSeasonalities[0].priceIndex"
    formInflationAuto("GB-RPI")      | 200      | "id"
    formInflationAuto("GB-RPI", 1)   | 412      | "Null.inflationSeasonalitySettingsForm.curveSeasonalities[0].jan"
  }

  def "should delete curve inflation settings version"() {
    setup:
    service.deleteInflationSesionalitySettingsVersion(_ as LocalDate) >> Either.right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/settings/calibration/inflation/1999-01-01/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get curve convexity adj settings"() {
    setup:
    service.getConvexitySettings(_ as LocalDate) >> new ConvexityAdjustmentsSettings()

    when:
    def results = mockMvc.perform(get("/settings/calibration/convexity")
      .param("stateDate", "1999-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get curve convexity adj settings versions"() {
    setup:
    1 * service.getConvexitySettingsVersions() >> []

    when:
    def results = mockMvc.perform(get("/settings/calibration/convexity/versions")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  @Unroll
  def "should save curve convexity adj #form"() {
    setup:
    service.saveConvexityAdjustmentsSettings(_ as LocalDate, _ as ConvexityAdjustmentsSettingsForm) >> Either
      .right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/settings/calibration/convexity/1999-01-01")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == response
    results.getResponse().getContentAsString().indexOf(responseBody) >= 0

    where:
    form                                     | response | responseBody
    formConvexity("EUR 3M", "1M", 0.1, 0.1)  | 200      | "id"
    emptyFormConvexity()                     | 200      | "id"
    formConvexity("XXX", "1M", 0.1, 0.1)     | 412      | "ValidStringMapKeys.convexityAdjustmentsSettingsForm" +
      ".curveConvexityAdjustments"
    formConvexity("EUR 3M", "5M", 0.1, 0.1)  | 412      | "ValidStringMapKeys.convexityAdjustmentsSettingsForm" +
      ".curveConvexityAdjustments.volatilities"
    formConvexity("EUR 3M", "1M", null, 0.1) | 412      | "NotNull.convexityAdjustmentsSettingsForm" +
      ".curveConvexityAdjustments[EUR 3M].mean"
    formConvexity("EUR 3M", "1M", 0.1, null) | 412      | "NotNull.convexityAdjustmentsSettingsForm" +
      ".curveConvexityAdjustments[EUR 3M].volatilities[1M]"
  }

  def "should delete curve convexity version"() {
    setup:
    1 * service.deleteConvexitySettingsVersion(_) >> Either.right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/settings/calibration/convexity/1999-01-01/delete")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def formInflationManual(String priceIndex, Integer jan) {
    [
      curveSeasonalities: [
        [
          priceIndex  : priceIndex,
          settingsType: InflationSeasonalitySettingsType.MANUAL,
          jan         : (jan),
          feb         : -1,
          mar         : 1,
          apr         : -1,
          may         : 1,
          jun         : -1,
          jul         : 1,
          aug         : -1,
          sep         : 1,
          oct         : -1,
          nov         : 1,
          dec         : -1]
      ],
      "versionForm"     : versionForm()

    ]
  }

  def formInflationAuto(String priceIndex, Integer jan = null) {
    [
      curveSeasonalities: [
        [
          priceIndex       : priceIndex,
          settingsType     : InflationSeasonalitySettingsType.AUTO,
          observationPeriod: 2,
          jan              : (jan),
        ]
      ],
      "versionForm"     : versionForm()

    ]
  }

  def emptyFormInflation() {
    [
      curveSeasonalities: [],
      "versionForm"     : versionForm()

    ]
  }

  def formConvexity(String name, String tenor, Double mean, BigDecimal v1) {
    [
      curveConvexityAdjustments: [(name): [
          mean        : (mean),
          zeroTenorVol: 0.05,
          volatilities: [(tenor): v1]]],
      "versionForm"            : versionForm()
    ]
  }

  def emptyFormConvexity() {
    [
      curveConvexityAdjustments: [:],
      "versionForm"            : versionForm()
    ]
  }

  def versionForm() {
    [
      comment             : "comment",
      validFrom           : LocalDate.now(),
      stateDate           : LocalDate.now(),
      futureVersionsAction: FutureVersionsAction.KEEP
    ]
  }
}
