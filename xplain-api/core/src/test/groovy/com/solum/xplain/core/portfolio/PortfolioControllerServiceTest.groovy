package com.solum.xplain.core.portfolio

import static PortfolioBuilder.portfolio
import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.core.portfolio.repository.PortfolioRepositoryFilter.activePortfolios
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.team.UserTeamEntity
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.portfolio.csv.TradeValueCsvLoaderFactory
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm
import com.solum.xplain.core.portfolio.form.PortfolioFilterForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.trade.TradeMapperImpl
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.teams.TeamMapperImpl
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.context.annotation.AnnotationConfigApplicationContext
import org.springframework.data.domain.Sort
import org.springframework.format.support.DefaultFormattingConversionService
import spock.lang.Shared
import spock.lang.Specification

class PortfolioControllerServiceTest extends Specification {

  def repository = Mock(PortfolioRepository)
  def portfolioItemRepository = Mock(PortfolioItemRepository)
  def portfolioItemWriteRepository = Mock(PortfolioItemWriteRepository)
  def authContext = Mock(AuthenticationContext)
  def portfolioItemsUpload = Mock(PortfolioItemsUploadService)
  def teamFilterProvider = Mock(PortfolioTeamFilterProvider)
  def loaderFactory = Mock(TradeValueCsvLoaderFactory)
  def mapperContext = new AnnotationConfigApplicationContext(PortfolioMapperImpl, TradeMapperImpl, TeamMapperImpl)
  def mapper = mapperContext.getBean(PortfolioMapper)
  def viewQueryTranslator = Mock(ViewQueryTranslator)
  def viewQueryTranslatorFactory = Mock(ViewQueryTranslatorFactory) {
    getTranslator(_ as Class<?>) >> viewQueryTranslator
  }
  def teamRepository = Mock(TeamRepository)
  def conversionService = new DefaultFormattingConversionService(true)
  def service = new PortfolioControllerService(
  repository,
  portfolioItemRepository,
  portfolioItemWriteRepository,
  authContext,
  portfolioItemsUpload,
  teamFilterProvider,
  mapper,
  loaderFactory,
  viewQueryTranslatorFactory,
  teamRepository,
  conversionService)

  @Shared
  def user = UserBuilder.user("userId")

  def "get should not allow not team"() {
    setup:
    def user = UserBuilder.user("userId")
    def portfolio = portfolio(AuditUser.of(UserBuilder.user("userId2")))
    portfolio.teamIds = [new ObjectId("000000000000000000000001")]
    portfolio.allowAllTeams = false

    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(
      user,
      "portfolioId") >> Either.left(OPERATION_NOT_ALLOWED.entity())

    when:
    def result = service.get("portfolioId", BitemporalDate.newOfNow())

    then:
    result.left().get() == OPERATION_NOT_ALLOWED.entity()
  }

  def "should not allow setting team that does not belong to user when creating"() {

    setup:
    def user = UserBuilder.user("user")
    def form = new PortfolioCreateForm()
    form.allowedTeamsForm = new AllowedTeamsForm(false, [new ObjectId().toString()])

    1 * authContext.currentUser() >> user

    when:
    def result = service.create(form)

    then:
    result.left().get() == OPERATION_NOT_ALLOWED.entity("No team access")
  }

  def "should not get allow items when not team"() {
    setup:
    def user = UserBuilder.user("userId")
    def portfolio = portfolio()
    portfolio.allowAllTeams = false
    portfolio.teamIds = [new ObjectId("000000000000000000000001")]


    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(user, "portfolioId") >> Either.left(OPERATION_NOT_ALLOWED.entity())

    when:
    def result = service.getItems(
      "portfolioId",
      BitemporalDate.newOfNow(),
      false,
      emptyTableFilter(),
      ScrollRequest.of(0, 10),
      GroupRequest.emptyGroupRequest())

    then:
    result.left().get() == OPERATION_NOT_ALLOWED.entity()
  }

  def "should get portfolio items with VDK"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def user = UserBuilder.user("userId")
    def portfolioItemWithKeyView = new PortfolioItemWithKeyView().tap {
      externalTradeId = "tradeId"
      key = "COMPANY_ID_ENTITY_ID_PORTFOLIO_ID_tradeId"
    }
    def portfolio = portfolio()
    portfolio.allowAllTeams = true
    portfolio.externalPortfolioId = "PORTFOLIO"
    portfolio.company = new CompanyReference(externalCompanyId: "COMPANY", entityId: "cId")
    portfolio.entity = new CompanyLegalEntityReference(entityId: "entityId", externalEntityId: "ENTITY", name: "name")

    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(user, "portfolioId") >> userPortfolioEither(user)
    1 * portfolioItemRepository.portfolioItemsWithKeyView("portfolioId", stateDate, _, _, _, _) >> ScrollableEntry.of([portfolioItemWithKeyView], ScrollRequest.of(0, 10))
    1 * viewQueryTranslator.translate(_ as TableFilter) >> { TableFilter filter -> filter }
    1 * viewQueryTranslator.translate(_ as ScrollRequest) >> { ScrollRequest scrollRequest -> scrollRequest }

    when:
    def result = service.getItems("portfolioId", stateDate, false, emptyTableFilter(), ScrollRequest.of(0, 10), GroupRequest.emptyGroupRequest())

    then:
    result.isRight()
    result.getOrNull().content[0].key == "COMPANY_ID_ENTITY_ID_PORTFOLIO_ID_tradeId"
    result.getOrNull().content[0].externalTradeId == "tradeId"
  }

  def "should get single portfolio item"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(user, "portfolioId") >> userPortfolioEither(user)
    1 * portfolioItemRepository.portfolioItemLatest("portfolioId", "tradeId", stateDate) >> Either.right(PortfolioItemBuilder.irsPortfolioItem())

    when:
    def result = service.getItem(
      "portfolioId",
      "tradeId",
      stateDate)

    then:
    result.isRight()
    // the key is build on top of externalCompanyId, externalEntityId, externalPortfolioId and
    // tradeId from com.solum.xplain.core.portfolio.PortfolioItemBuilder.groovy
    // The portfolio returned from userPortfolioEither(user) is not used.
    // It's only here for the user permission check.
    result.getOrNull().key == "externalCompanyId_externalEntityId_extPortfolioId_tradeId"
  }

  def "should get trade versions with VDK"() {
    setup:
    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(user, "portfolioId") >> userPortfolioEither(user)
    1 * portfolioItemRepository.getVersions("portfolioId", "tradeId") >> List.of(PortfolioItemBuilder.irsPortfolioItem())

    when:
    def result = service.getVersions("portfolioId", "tradeId")

    then:
    result.isRight()
    def items = result.getOrNull()
    items.size() == 1
    // the key is build on top of externalCompanyId, externalEntityId, externalPortfolioId and
    // tradeId from com.solum.xplain.core.portfolio.PortfolioItemBuilder.groovy
    // The portfolio returned from userPortfolioEither(user) is not used.
    // It's only here for the user permission check.
    items[0].key == "externalCompanyId_externalEntityId_extPortfolioId_tradeId"
  }

  def "should get portfolio counted view scrollable entry"() {
    setup:
    def portfolio = portfolio()
    portfolio.allowAllTeams = true
    portfolio.externalPortfolioId = "PORTFOLIO"
    portfolio.company = new CompanyReference(externalCompanyId: "COMPANY", entityId: "companyId")
    portfolio.entity = new CompanyLegalEntityReference(entityId: "entityId", externalEntityId: "ENTITY", name: "name")

    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * portfolioItemRepository.portfoliosTradesCount(["portfolioId", "portfolioId2"], { it.getActualDate() == LocalDate.now() }) >> [portfolioId: 7]
    1 * repository.portfolioList(ScrollRequest.unconstrained(), emptyTableFilter(), _, _,) >> ScrollableEntry.of(
      [
        portfolioView(),
        portfolioView().tap {
          id = "portfolioId2"; externalPortfolioId = "PORTFOLIO_ID_2"
        }
      ],
      ScrollRequest.unconstrained()
      )
    1 * viewQueryTranslator.translate(_ as TableFilter) >> { TableFilter filter -> filter }
    1 * viewQueryTranslator.translate(_ as ScrollRequest) >> { ScrollRequest scrollRequest -> scrollRequest }

    when:
    def result = service.getAll(
      ScrollRequest.unconstrained(),
      emptyTableFilter(),
      new PortfolioFilterForm(),
      BitemporalDate.newOfNow(),
      )
    then:
    result.content.size() == 2
    result.content[0].numberOfTrades == 7
    result.content[0].allowAllTeams
    result.content[0].externalPortfolioId == "PORTFOLIO_ID"
    result.content[0].entityId == "entityId"
    result.content[0].externalEntityId == "ENTITY_ID"
    result.content[0].companyId == "companyId"
    result.content[0].externalCompanyId == "COMPANY_ID"
    result.content[1].numberOfTrades == 0
    result.content[1].externalPortfolioId == "PORTFOLIO_ID_2"
  }

  def "should perform in-memory filtering of trade count on scrollable entry"() {
    def portfolio = portfolio()
    portfolio.allowAllTeams = true
    portfolio.externalPortfolioId = "PORTFOLIO"
    portfolio.company = new CompanyReference(externalCompanyId: "COMPANY", entityId: "companyId")
    portfolio.entity = new CompanyLegalEntityReference(entityId: "entityId", externalEntityId: "ENTITY", name: "name")

    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * portfolioItemRepository.portfoliosTradesCount(["portfolioId", "portfolioId2"], { it.getActualDate() == LocalDate.now() }) >> [portfolioId: 7]
    1 * repository.portfolioList(ScrollRequest.unconstrained(), _ as TableFilter, _, _,) >> ScrollableEntry.of(
      [
        portfolioView(),
        portfolioView().tap {
          id = "portfolioId2"; externalPortfolioId = "PORTFOLIO_ID_2"
        }
      ],
      ScrollRequest.unconstrained()
      )
    1 * viewQueryTranslator.translate(_ as TableFilter) >> { TableFilter filter -> filter }
    1 * viewQueryTranslator.translate(_ as ScrollRequest) >> { ScrollRequest scrollRequest -> scrollRequest }

    when:
    def result = service.getAll(
      ScrollRequest.unconstrained(),
      new TableFilter([new SimpleFilterClause("numberOfTrades", FilterOperation.GREATER_THAN, "6")]),
      new PortfolioFilterForm(),
      BitemporalDate.newOfNow(),
      )
    then:
    result.content.size() == 1
    result.content[0].numberOfTrades == 7
  }


  def "should perform in-memory sort of trade count on scrollable entry"() {
    def portfolio = portfolio()
    portfolio.allowAllTeams = true
    portfolio.externalPortfolioId = "PORTFOLIO"
    portfolio.company = new CompanyReference(externalCompanyId: "COMPANY", entityId: "companyId")
    portfolio.entity = new CompanyLegalEntityReference(entityId: "entityId", externalEntityId: "ENTITY", name: "name")

    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * portfolioItemRepository.portfoliosTradesCount(["portfolioId", "portfolioId2", "portfolioId3"], { it.getActualDate() == LocalDate.now() }) >> [portfolioId: 7, portfolioId3: 2]
    1 * repository.portfolioList(_ as ScrollRequest, emptyTableFilter(), _, _,) >> ScrollableEntry.of(
      [
        portfolioView(),
        portfolioView().tap {
          id = "portfolioId2"; externalPortfolioId = "PORTFOLIO_ID_2"
        },
        portfolioView().tap {
          id = "portfolioId3"; externalPortfolioId = "PORTFOLIO_ID_3"
        }
      ],
      ScrollRequest.unconstrained()
      )
    1 * viewQueryTranslator.translate(_ as TableFilter) >> { TableFilter filter -> filter }
    1 * viewQueryTranslator.translate(_ as ScrollRequest) >> { ScrollRequest scrollRequest -> scrollRequest }

    when:
    def result = service.getAll(
      ScrollRequest.of(0L, 0L, Sort.by(new Sort.Order(direction, "numberOfTrades", nullHandling))),
      emptyTableFilter(),
      new PortfolioFilterForm(),
      BitemporalDate.newOfNow(),
      )
    then:
    result.content*.numberOfTrades == outcome

    where:
    direction           | nullHandling                  || outcome
    Sort.Direction.ASC  | Sort.NullHandling.NATIVE      || [0, 2, 7]
    Sort.Direction.DESC | Sort.NullHandling.NATIVE      || [7, 2, 0]
    Sort.Direction.ASC  | Sort.NullHandling.NULLS_FIRST || [0, 2, 7]
    Sort.Direction.DESC | Sort.NullHandling.NULLS_FIRST || [7, 2, 0]
    Sort.Direction.ASC  | Sort.NullHandling.NULLS_LAST  || [0, 2, 7]
    Sort.Direction.DESC | Sort.NullHandling.NULLS_LAST  || [7, 2, 0]
  }

  def "should get all active portfolios"() {
    setup:
    1 * teamFilterProvider.provideFilter() >> PortfolioTeamFilter.emptyFilter()
    1 * repository.portfolioCondensedViewList(activePortfolios(), PortfolioTeamFilter.emptyFilter(), Sort.by(PortfolioCondensedView.Fields.externalCompanyId, PortfolioCondensedView.Fields.externalEntityId, PortfolioCondensedView.Fields.externalPortfolioId).ascending()) >> [
      new PortfolioView(
      allowAllTeams: true,
      id: "portfolioId1",
      externalPortfolioId: "EXTERNAL_PORTFOLIO_ID_1",
      entityId: "entityId",
      externalEntityId: "EXTERNAL_ENTITY_ID",
      companyId: "companyId",
      externalCompanyId: "EXTERNAL_COMPANY_ID"
      ),
      new PortfolioView(
      allowAllTeams: true,
      id: "portfolioId2",
      externalPortfolioId: "EXTERNAL_PORTFOLIO_ID_2",
      entityId: "entityId",
      externalEntityId: "EXTERNAL_ENTITY_ID",
      companyId: "companyId",
      externalCompanyId: "EXTERNAL_COMPANY_ID"
      )
    ]
    when:
    def result = service.getAllActivePortfolios()

    then:
    result.size() == 2
    with(result[0]) {
      id == "portfolioId1"
      externalPortfolioId == "EXTERNAL_PORTFOLIO_ID_1"
      entityId == "entityId"
      externalEntityId == "EXTERNAL_ENTITY_ID"
      companyId == "companyId"
      externalCompanyId == "EXTERNAL_COMPANY_ID"
    }
    with(result[1]) {
      id == "portfolioId2"
      externalPortfolioId == "EXTERNAL_PORTFOLIO_ID_2"
      entityId == "entityId"
      externalEntityId == "EXTERNAL_ENTITY_ID"
      companyId == "companyId"
      externalCompanyId == "EXTERNAL_COMPANY_ID"
    }
  }

  def "should get portfolio counted view"() {
    setup:
    1 * authContext.currentUser() >> user
    1 * repository.getUserPortfolioView(user, "portfolioId") >> userPortfolioEither(user)
    1 * portfolioItemRepository.portfolioTradesCount("portfolioId", { it.getActualDate() == LocalDate.now() }) >> 7
    when:
    def result = service.get("portfolioId", BitemporalDate.newOfNow())

    then:
    result.isRight()
    result.getOrNull().numberOfTrades == 7
    result.getOrNull().allowAllTeams
    result.getOrNull().externalPortfolioId == "PORTFOLIO_ID"
    result.getOrNull().entityId == "entityId"
    result.getOrNull().externalEntityId == "ENTITY_ID"
    result.getOrNull().companyId == "companyId"
    result.getOrNull().externalCompanyId == "COMPANY_ID"
  }

  static def portfolioView() {
    new PortfolioView(
      id: "portfolioId",
      allowAllTeams: true,
      externalPortfolioId: "PORTFOLIO_ID",
      entityId: "entityId",
      externalEntityId: "ENTITY_ID",
      companyId: "companyId",
      externalCompanyId: "COMPANY_ID"
      )
  }

  static def userPortfolioEither(XplainPrincipal user) {
    return Either.right(UserTeamEntity.userEntity(user, portfolioView()))
  }
}
