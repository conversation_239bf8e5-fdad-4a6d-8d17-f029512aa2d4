package com.solum.xplain.core.portfolio.value

import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.trade
import static java.time.LocalDate.parse

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import spock.lang.Specification

class CapFloorTradeViewTest extends Specification {
  def "should create from portfolio item"() {
    setup:
    def portfolioItem = trade(CoreProductType.CAP_FLOOR, TradeDetailsBuilder.capFloor())
    portfolioItem.tradeDetails.info.csaDiscountingGroup = "USD"

    when:
    def result = CapFloorTradeView.of(portfolioItem)

    then:
    result.isRight()
    def view = result.getOrNull()
    view.position == "BUY"
    view.type == "CAP"
    view.strike == 1
    view.accrualFrequency == "3M"
    view.paymentFrequency == "3M"
    view.premiumDateConvention == "NoAdjust"
    view.businessDayConvention == "NoAdjust"
    view.premiumDate == parse("2017-01-01")
    view.premiumValue == 10
    view.startDate == parse("2016-01-01")
    view.endDate == parse("2017-01-01")
    view.notionalCurrency == "EUR"
    view.calendar == "EUTA"
    view.notionalValue == 20
    view.calculationIborDayCount == "Act/360"
    view.calculationIborIndex == "EUR-EURIBOR-3M"
    view.calculationIborSpreadInitialValue == 30
    view.calculationIborFixingDateOffsetDays == -2
    view.extLegIdentifier == "LEG_ID"
    !view.isOffshore
    view.csaDiscountingGroup == "USD"
  }
}
