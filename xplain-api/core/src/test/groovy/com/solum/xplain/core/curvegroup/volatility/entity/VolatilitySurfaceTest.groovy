package com.solum.xplain.core.curvegroup.volatility.entity

import static com.solum.xplain.core.market.mapping.MarketDataUtils.quoteId
import static java.time.LocalDate.now

import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.market.ValueType
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.SkewType
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter
import com.solum.xplain.core.error.ErrorItem
import spock.lang.Specification

class VolatilitySurfaceTest extends Specification {

  def "raw option data should return correct values"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .capletVolatilities([
        new CapletVolatilityNode(tenor: "1M", strike: 0.57),
        new CapletVolatilityNode(tenor: "2M", strike: 0.57),
        new CapletVolatilityNode(tenor: "3M", strike: 0.57),
        new CapletVolatilityNode(tenor: "1M", strike: 0.67),
        new CapletVolatilityNode(tenor: "2M", strike: 0.67),
        new CapletVolatilityNode(tenor: "3M", strike: 0.67),
        new CapletVolatilityNode(tenor: "4M", strike: 0.67),
        new CapletVolatilityNode(tenor: "5M", strike: 0.67),
      ])
      .capletValuationModel(capletValuationModel)
      .build()
    def marketData = ImmutableMarketData.builder(now())
      .addValue(quoteId("1M_57.00%_CF_EUR-EURIBOR-3M"), 0.11 as double)
      .addValue(quoteId("2M_57.00%_CF_EUR-EURIBOR-3M"), 0.12 as double)
      .addValue(quoteId("3M_57.00%_CF_EUR-EURIBOR-3M"), 0.13 as double)
      .addValue(quoteId("1M_67.00%_CF_EUR-EURIBOR-3M"), 0.21 as double)
      .addValue(quoteId("2M_67.00%_CF_EUR-EURIBOR-3M"), 0.22 as double)
      .addValue(quoteId("3M_67.00%_CF_EUR-EURIBOR-3M"), 0.23 as double)
      .build()

    when:
    def result = surface.rawOptionData(marketData, ValidNodesFilter.EMPTY_FILTER).getOrNull()

    then:
    result.expiries.collect({ it -> it.toString() }) == ["P1M", "P2M", "P3M"]
    result.strikes.toArray() == [0.57, 0.67]
    result.data.toArray() == [[0.11, 0.21], [0.12, 0.22], [0.13, 0.23],]
    result.dataType == valueType

    where:
    capletValuationModel        | valueType
    CapletValuationModel.BLACK  | ValueType.BLACK_VOLATILITY
    CapletValuationModel.NORMAL | ValueType.NORMAL_VOLATILITY
    null                        | ValueType.NORMAL_VOLATILITY
  }

  def "isOvernight should return true when volatility surface has an overnight index"() {
    setup:
    def surfaceWithIborIndex = new VolatilitySurfaceBuilder()
      .build()
    def surfaceWithOvernightIndex = new VolatilitySurfaceBuilder()
      .name("THB THOR Vols")
      .build()

    expect: "The ibor index should be false"
    !surfaceWithIborIndex.isOvernight()

    and: "The overnight index should be true"
    surfaceWithOvernightIndex.isOvernight()
  }

  def "should return error when not enough caplet nodes"() {
    setup:
    def surface = new VolatilitySurfaceBuilder()
      .capletVolatilities([new CapletVolatilityNode(tenor: "1M", strike: 0.57),])
      .build()

    def marketData = ImmutableMarketData.builder(now())
      .addValue(quoteId("1M_57.00%_CF_EUR-EURIBOR-3M"), 0.11 as double)
      .addValue(quoteId("2M_57.00%_CF_EUR-EURIBOR-3M"), 0.12 as double)
      .build()

    when:
    def result = surface.rawOptionData(marketData, ValidNodesFilter.EMPTY_FILTER)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.description == "Error creating caplet value matrix for surface EUR 3M Vols: Surface must have at least 2 valid caplet nodes"
  }

  def "should return correct instrument"() {
    setup:
    def node = Mock(VolatilitySurfaceNode)
    def capletNode = Mock(CapletVolatilityNode)
    def skew = Mock(VolatilitySurfaceSkew)
    def volatility = new VolatilitySurfaceBuilder()
      .nodes([node])
      .capletVolatilities([capletNode])
      .skewNodes([skew])
      .build()

    def i1 = Mock(InstrumentDefinition)
    1 * node.atmInstrument("EUR 3M Vols", "EUR-EURIBOR-3M", "EUR", _) >> i1

    def i2 = Mock(InstrumentDefinition)
    1 * skew.allInstruments("EUR 3M Vols", "EUR-EURIBOR-3M", "EUR", SkewType.STRIKE, _, _) >> [i2]

    def i3 = Mock(InstrumentDefinition)
    1 * capletNode.instrument("EUR 3M Vols", "EUR-EURIBOR-3M", "EUR", _) >> i3


    when:
    def result = volatility.allInstruments()

    then:
    result.size() == 3
    result[0] == i1
    result[1] == i2
    result[2] == i3
  }
}
