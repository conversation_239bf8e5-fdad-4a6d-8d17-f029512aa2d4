package com.solum.xplain.core.portfolio

import com.solum.xplain.core.portfolio.trade.TradeValue
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class PortfolioMapperTest extends Specification {

  def "Should generate valuationDataKey for PortfolioItem from existing fields"() {
    setup:
    def portfolioId = "000000000000000000000001"
    def externalTradeId = "extTradeId"
    def portfolioItemEntity = new PortfolioItemEntity(PortfolioItemUniqueKey.newOf(portfolioId, externalTradeId))
    def portfolioCondensedView = new PortfolioCondensedView(
      id: portfolioId,
      externalPortfolioId: "extPortfolioId",
      entityId: "entityId1",
      externalEntityId: "extEntityId",
      companyId: "companyId1",
      externalCompanyId: "extCompany"
      )
    def tradeValue = new TradeValue()

    when:
    def portfolioItem = Mappers.getMapper(PortfolioMapper.class).generateRead(portfolioItemEntity, tradeValue, portfolioCondensedView)

    then:
    portfolioItem.valuationDataKey == "extCompany_extEntityId_extPortfolioId_extTradeId"
  }
}
