package com.solum.xplain.core.portfolio.csv.loader

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_IDENTIFIER
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PAYMENT_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.io.CsvFile
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class FxSingleTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {

  def FX_FWD_HEADERS = [
    LEG_1 + "." + PAY_RECEIVE,
    LEG_1 + "." + LEG_CURRENCY,
    LEG_1 + "." + LEG_IDENTIFIER,
    LEG_1 + "." + LEG_NOTIONAL,
    LEG_2 + "." + PAY_RECEIVE,
    LEG_2 + "." + LEG_CURRENCY,
    LEG_2 + "." + LEG_IDENTIFIER,
    LEG_2 + "." + LEG_NOTIONAL,
    TRADE_BUSINESS_DAY_CONVENTION,
    TRADE_PAYMENT_DATE,
    TRADE_CURRENCY,
    TRADE_REF_SEC_FX_RATE,
  ]
  def LOADER = new FxSingleTradeCsvLoader()

  @Unroll
  def "should parse fx expected #success when #csvPaymentDate"() {
    setup:
    def csvRow = ["Pay", "EUR", "", "10", "Rec", "USD", "", "20", "", csvPaymentDate, "EUR", ""]

    def firstRow = CsvFile.of(FX_FWD_HEADERS, [csvRow]).row(0)

    expect:
    LOADER.parse(firstRow, false).isRight() == success

    where:
    csvPaymentDate | success
    "2023-01-15"   | true
    ""             | false
  }

  def "should return correct product type"() {
    expect:
    LOADER.productTypes() == List.of(CoreProductType.FXFWD)
  }

  def "should correctly parse FXFWD trade"() {
    setup:
    def rows = loadResource("FxSingleTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 3
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result1 = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result1.businessDayAdjustmentType == null
    result1.endDate == LocalDate.parse("2014-10-10")
    result1.businessDayConvention == "Following"
    result1.payLeg.currency == "AED"
    result1.payLeg.notional == -100
    result1.payLeg.extLegIdentifier == "TRADEFX_LEG1"
    result1.receiveLeg.currency == "CNY"
    result1.receiveLeg.notional == 200
    result1.receiveLeg.extLegIdentifier == "TRADEFX_LEG2"

    def result2 = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "AED"))
    result2.payLeg.currency == "CNY"
    result2.payLeg.notional == -200
    result2.payLeg.extLegIdentifier == null
    result2.receiveLeg.currency == "AED"
    result2.receiveLeg.notional == 100
    result2.receiveLeg.extLegIdentifier == null
  }

  def "should return FXFWD parse errors"() {
    setup:
    def rows = loadResource("FxSingleTradesInvalid.csv")

    when:
    def parsedRow = LOADER.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Value must be positive for field: Leg2.Notional"
  }

  def "should parse FXFWD trade currency"() {
    setup:
    def rows = loadResource("FxSingleTrades.csv")

    when:
    def parsedRows = rows.stream().map { LOADER.parseTradeCcy(it) }.toList()

    then:
    parsedRows.size() == 3
    parsedRows[0] == Currency.AED
    parsedRows[1] == Currency.CNY
    parsedRows[2] == Currency.AED
  }
}
