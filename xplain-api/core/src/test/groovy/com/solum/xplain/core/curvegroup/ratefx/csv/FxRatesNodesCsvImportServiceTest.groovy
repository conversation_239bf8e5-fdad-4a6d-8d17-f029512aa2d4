package com.solum.xplain.core.curvegroup.ratefx.csv

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.*
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.IMPORT_ERROR
import static com.solum.xplain.core.error.Error.IMPORT_INFO
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.error.Error.PARSING_ERROR

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.csv.ParsingMode
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRateMapper
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRatesRepository
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.InfoItem
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class FxRatesNodesCsvImportServiceTest extends Specification {
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static GROUP_ID = "volatilityGroupId"
  def static FILE_CONTENT = [] as byte[]

  def auditEntryService = Mock(AuditEntryService)
  def csvLoader = Mock(FxRatesNodeCsvLoader)
  def mapper = Mock(CurveGroupFxRateMapper)
  def repository = Mock(CurveGroupFxRatesRepository)

  def nodeImport = Spy(new FxRatesNodeCsvImportService(
  auditEntryService,
  csvLoader,
  mapper,
  repository)
  )

  def "should return errors when importing rates and csv parsing error"() {
    setup:
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      leftCsvResult([PARSING_ERROR.entity()])

    0 * repository.getActiveRates(GROUP_ID, STATE_DATE)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    0 * nodeImport.importNodes(_, _, _)
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importRates(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
  }

  def "should create rates if no version"() {
    setup:
    1 * repository.getActiveRates(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.left(OBJECT_NOT_FOUND.entity())
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> rightCsvResult([nodeForm("USD")])
    1 * nodeImport.importNodes({ CurveGroupFxRates.newOf(GROUP_ID).valueEquals(it) }, _, _) >> [InfoItem.of(IMPORT_INFO, entityId(GROUP_ID), "")]

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importRates(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
  }

  def "should not import rates and return success result"() {
    setup:
    CurveGroupFxRates rates = rates([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    1 * repository.getActiveRates(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(rates)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(rates, _, csvResult.getOrNull().parsedLines) >> []
    1 * auditEntryService.newEntryWithLogs(_, [])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importRates(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as EntityId
    ids == entityId(rates.entityId)
  }

  def "should import rates, log and return success result"() {
    setup:
    CurveGroupFxRates rates = rates([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    InfoItem info = InfoItem.of(IMPORT_INFO, entityId(rates.entityId), "info description")

    1 * repository.getActiveRates(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(rates)
    0 * repository.getFutureVersions(_, _)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(rates, _, csvResult.getOrNull().parsedLines) >> [info]
    1 * auditEntryService.newEntryWithLogs(_, [info])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importRates(GROUP_ID, importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as EntityId
    ids == entityId(rates.entityId)
  }

  def "should fail import rates, log and return errors"() {
    setup:
    CurveGroupFxRates rates = rates([])
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    ErrorItem error = new ErrorItem(IMPORT_ERROR, "error description")


    1 * repository.getActiveRates(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> Either.right(rates)
    0 * repository.getFutureVersions(_)
    1 * csvLoader.parse(FILE_CONTENT, STRICT) >> csvResult
    1 * nodeImport.importNodes(_, _, _) >> [error]
    1 * auditEntryService.newEntryWithLogs(_, [error])

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importRates(GROUP_ID, importOptions, FILE_CONTENT)


    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == IMPORT_ERROR
    errors[0].description == "error description"
  }

  def "should fail with unexpected action REPLACE_DELETE"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should fail with unexpected action REPLACE"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == IMPORT_ERROR
  }

  def "should return MISSING_ENTRY with missing nodes when ERROR"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == MISSING_ENTRY
    result[0].description == "Rate EUR/USD in rates FX Rates is missing"
  }

  def "should not update rates with only duplicate nodes when ERROR"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    returnNoFutureVersions()
    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 0
  }

  @Unroll
  def "should return NEW_VERSION_VIABLE when ERROR and rates exists #exists"() {
    setup:
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    returnNoFutureVersions()
    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == NEW_VERSION_VIABLE
    result[0].description == "New version is viable for FX Rates"

    where:
    exists | rates
    true   | rates([], PAST_VERSION_DATE)
    false  | CurveGroupFxRates.newOf(GROUP_ID)
  }


  def "should return FUTURE_VERSION_EXISTS when ERROR"() {
    setup:
    CurveGroupFxRates rates = rates([])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD")])

    1 * repository.getFutureVersions(_, _) >> new DateList([FUTURE_VERSION_DATE])
    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    result[0].reason == FUTURE_VERSION_EXISTS
    result[0].description == "FX Rates has future version(s)"
  }

  def "should return empty result when no errors when ERROR"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD"), nodeForm("3M")])

    returnNoFutureVersions()
    doNotUpdate(ratesForm)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.isEmpty()
  }

  def "should correctly update rates when APPEND"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD"), node("1W")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD"), nodeForm("3M")])

    doUpdate(rates, ratesForm, [nodeForm("USD"), nodeForm("1W"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(rates.entityId)
  }

  def "should correctly update rates when APPEND_DELETE"() {
    setup:
    CurveGroupFxRates rates = rates([node("USD"), node("1W")])
    CurveGroupFxRatesForm ratesForm = ratesForm(rates)
    Either<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> csvResult =
      rightCsvResult([nodeForm("USD"), nodeForm("3M")])

    doUpdate(rates, ratesForm, [nodeForm("USD"), nodeForm("3M")])

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = nodeImport.importNodes(rates, importOptions, csvResult.getOrNull().parsedLines)

    then:
    result.size() == 1
    def infoItems = result as List<InfoItem>
    infoItems[0].entityId == entityId(rates.entityId)
  }

  def rates(List<CurveGroupFxRatesNode> nodes, validFrom = STATE_DATE) {
    def rates = Mock(CurveGroupFxRates)
    rates.entityId >> GROUP_ID
    rates.validFrom >> validFrom
    rates.nodes >> nodes
    return rates
  }

  def node(String foreignCcy) {
    def node = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: foreignCcy)
    _ * mapper.toNodeForm(node) >> nodeForm(foreignCcy)
    return node
  }

  def nodeForm(String foreignCcy) {
    new CurveGroupFxRatesNodeForm(domesticCurrency: "EUR", foreignCurrency: foreignCcy)
  }

  def ratesForm(CurveGroupFxRates rates) {
    def ratesForm = Mock(CurveGroupFxRatesForm)
    _ * mapper.toForm(rates) >> ratesForm
    return ratesForm
  }

  def leftCsvResult(List<ErrorItem> errors) {
    return Either.<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> left(errors)
  }

  def rightCsvResult(List<CurveGroupFxRatesNodeForm> nodeForms) {
    return Either.<List<ErrorItem>, CsvParserResult<CurveGroupFxRatesNodeForm>> right(new CsvParserResult<>(nodeForms, []))
  }

  def doNotUpdate(CurveGroupFxRatesForm ratesForm) {
    0 * ratesForm.setNodes(_)
    0 * ratesForm.setVersionForm(_)
    0 * repository.createRates(_, _, _)
  }

  def returnNoFutureVersions() {
    1 * repository.getFutureVersions(_, _) >> new DateList([])
  }

  def doUpdate(CurveGroupFxRates rates, CurveGroupFxRatesForm ratesForm, List<CurveGroupFxRatesNodeForm> nodeForms) {
    1 * ratesForm.setNodes({
      it.size() == nodeForms.size() &&
        nodeForms.withIndex().collect { elem, idx -> elem == nodeForms[idx as Integer] }
    })
    1 * ratesForm.setVersionForm({ it.validFrom == rates.validFrom })
    1 * repository.createRates(rates.entityId, ratesForm) >> Either.right(entityId(rates.entityId))
  }
}
