package com.solum.xplain.core.portfolio.value.view

import com.solum.xplain.core.portfolio.value.PortfolioCountedView
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.teams.TeamRepository
import com.solum.xplain.core.teams.value.TeamListView
import com.solum.xplain.shared.utils.filter.FilterClause
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import org.springframework.core.convert.ConversionService
import spock.lang.Specification

class TeamNameFilterClauseTranslatorTest extends Specification {
  def teamRepository = Mock(TeamRepository)
  def conversionService = Mock(ConversionService)

  def "should translate 'Shared' to allow all teams"() {
    given:
    def translator = new TeamNameFilterClauseTranslator(teamRepository)
    1 * teamRepository.getTeamsById() >> [:]

    when:
    def output = new LinkedList<FilterClause>()
    def replaced = translator.replaceFilterClause(new SimpleFilterClause(
    PortfolioCountedView.Fields.teamNames,
    FilterOperation.EQUAL,
    "Shared"
    ), output::add)

    then:
    replaced
    output.size() == 1
    output.getFirst() == new SimpleFilterClause(
    PortfolioView.Fields.allowAllTeams,
    FilterOperation.EQUAL,
    "true"
    )
  }

  def "should translate matching team to team ID"() {
    given:
    def translator = new TeamNameFilterClauseTranslator(teamRepository)
    def teamId = "team123"
    1 * teamRepository.getTeamsById() >> [(teamId): new TeamListView(id: teamId, name: "Team Bob Name")]

    when:
    def output = new LinkedList<FilterClause>()
    def replaced = translator.replaceFilterClause(new SimpleFilterClause(
    PortfolioCountedView.Fields.teamNames,
    FilterOperation.CONTAINS,
    "bob"
    ), output::add)

    then:
    replaced
    output.size() == 1
    output.getFirst() == new SimpleFilterClause(
    PortfolioView.Fields.teamIds,
    FilterOperation.EQUAL_STRICT,
    teamId
    )
  }

  def "should translate multiple matching teams to team IDs"() {
    given:
    def translator = new TeamNameFilterClauseTranslator(teamRepository)
    def teamId1 = "team123"
    def teamId2 = "team456"
    1 * teamRepository.getTeamsById() >> [(teamId1): new TeamListView(id: teamId1, name: "Team Bob Name"), (teamId2): new TeamListView(id: teamId2, name: "Bobbins Name")]

    when:
    def output = new LinkedList<FilterClause>()
    def replaced = translator.replaceFilterClause(new SimpleFilterClause(
    PortfolioCountedView.Fields.teamNames,
    FilterOperation.CONTAINS,
    "bob"
    ), output::add)

    then:
    replaced
    output.size() == 1
    output.getFirst() == new SimpleFilterClause(
    PortfolioView.Fields.teamIds,
    FilterOperation.EQUAL_STRICT,
    [teamId1, teamId2]
    )
  }

  def "should return OR-criteria if we match both Shared and a team name"() {
    given:
    def translator = new TeamNameFilterClauseTranslator(teamRepository)
    def teamId = "team123"
    1 * teamRepository.getTeamsById() >> [(teamId): new TeamListView(id: teamId, name: "Shark Team")]
    1 * conversionService.convert("true", Boolean) >> Boolean.TRUE

    when:
    def output = new LinkedList<FilterClause>()
    def replaced = translator.replaceFilterClause(new SimpleFilterClause(
    PortfolioCountedView.Fields.teamNames,
    FilterOperation.STARTS_WITH,
    "shar"
    ), output::add)

    then:
    replaced
    output.size() == 1
    with (output.getFirst().criteria(null, PortfolioCountedView.class, [], conversionService).getCriteriaObject().toBsonDocument()) {
      containsKey("\$or")
      with (getArray("\$or")) {
        size() == 2
        get(0).asDocument().getBoolean(PortfolioView.Fields.allowAllTeams) == org.bson.BsonBoolean.TRUE
        with (get(1).asDocument().getDocument(PortfolioView.Fields.teamIds).getArray("\$in")) {
          size() == 1
          get(0).asString().getValue() == teamId
        }
      }
    }
  }
}
