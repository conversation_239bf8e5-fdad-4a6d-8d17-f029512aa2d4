package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.cdsTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.cdsTradeFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.basics.date.DayCounts
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.externalsource.IdentifierSourceRepository
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.CdsTradeForm
import com.solum.xplain.core.portfolio.form.PaymentDateForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [CdsController])
@Import([TestSecurityConfig])
class CdsControllerTest extends Specification {
  private static final String URI = "/portfolio/{id}/trades/cds"
  private static final String PORTFOLIO_ID = "portfolio"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  IdentifierSourceRepository identifierSourceRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should get cds"() {
    setup:
    def trade = PortfolioItemBuilder.trade(CoreProductType.CDS, TradeDetailsBuilder.cdsTradeDetails(now()))
    1 * service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(trade)

    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
      .param("stateDate", "2020-01-01")
      .with(userWithAuthority(VIEW_TRADE))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new cds with form #form, role #role and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as CdsTradeForm) >> right(entityId("1"))
    identifierSourceRepository.existsByExternalId("SRC") >> true

    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                                                                                                          | code | response
    VIEW_TRADE   | cdsTradeForm()                                                                                                                                | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | cdsTradeFormWith(["upfrontFee": null, tradeDate: null])                                                                                       | 200  | """{"id":"1"}"""
    MODIFY_TRADE | cdsTradeFormWith(["dayCount": DayCounts.ACT_360.name])                                                                                        | 200  | """{"id":"1"}"""
    MODIFY_TRADE | cdsTradeFormWith(["externalIdentifiers": [["identifier": "ID", "externalSourceId": "SRC"]]])                                                  | 200  | """{"id":"1"}"""
    MODIFY_TRADE | cdsTradeFormWith(["externalIdentifiers": [["identifier": "ID", "externalSourceId": "SRC"], ["identifier": "ID", "externalSourceId": "SRC"]]]) | 412  | "External Trade Id Source must be unique"
    MODIFY_TRADE | cdsTradeFormWith(["externalIdentifiers": [["identifier": "id", "externalSourceId": "SRC"]]])                                                  | 200  | """{"id":"1"}"""
    MODIFY_TRADE | cdsTradeFormWith(["externalIdentifiers": [["identifier": "ID", "externalSourceId": "src"]]])                                                  | 412  | "External source does not exist!"
    MODIFY_TRADE | cdsTradeFormWith(["dayCount": DayCounts.ACT_365L.name])                                                                                       | 412  | "Pattern.cdsTradeForm.dayCount"
    MODIFY_TRADE | cdsTradeFormWith(["docClause": null])                                                                                                         | 412  | "NotEmpty.cdsTradeForm.docClause"
    MODIFY_TRADE | cdsTradeFormWith(["externalTradeId": null])                                                                                                   | 412  | "NotEmpty.cdsTradeForm.externalTradeId"
    MODIFY_TRADE | cdsTradeFormWith(["externalTradeId": "WITH SPACES"])                                                                                          | 412  | "ValidIdentifier.cdsTradeForm.externalTradeId"
    MODIFY_TRADE | cdsTradeFormWith(["externalTradeId": "lowercaseS"])                                                                                           | 412  | "ValidIdentifier.cdsTradeForm.externalTradeId"
    MODIFY_TRADE | cdsTradeFormWith(["upfrontFee": new PaymentDateForm(amount: 10)])                                                                             | 412  | "NotNull.cdsTradeForm.upfrontFee.date"
    MODIFY_TRADE | cdsTradeFormWith(["upfrontFee": new PaymentDateForm(date: now())])                                                                            | 412  | "NotNull.cdsTradeForm.upfrontFee.amount"
    MODIFY_TRADE | cdsTradeFormWith(["tradeDate": null])                                                                                                         | 200  | """{"id":"1"}"""
    MODIFY_TRADE | cdsTradeFormWith(["position": null])                                                                                                          | 412  | "NotEmpty.cdsTradeForm.position"
    MODIFY_TRADE | cdsTradeFormWith(["position": "random"])                                                                                                      | 412  | "ValidStringSet.cdsTradeForm.position"
    MODIFY_TRADE | cdsTradeFormWith(["reference": null])                                                                                                         | 412  | "NotEmpty.cdsTradeForm.reference"
    MODIFY_TRADE | cdsTradeFormWith(["startDate": null])                                                                                                         | 412  | "NotNull.cdsTradeForm.startDate"
    MODIFY_TRADE | cdsTradeFormWith(["endDate": null])                                                                                                           | 412  | "NotNull.cdsTradeForm.endDate"
    MODIFY_TRADE | cdsTradeFormWith(["endDate": now()])                                                                                                          | 412  | "ValidPaymentPeriod.cdsTradeForm.endDate"
    MODIFY_TRADE | cdsTradeFormWith(["currency": null])                                                                                                          | 412  | "NotEmpty.cdsTradeForm.currency"
    MODIFY_TRADE | cdsTradeFormWith(["notionalValue": null])                                                                                                     | 412  | "NotNull.cdsTradeForm.notionalValue"
    MODIFY_TRADE | cdsTradeFormWith(["notionalValue": Double.valueOf(-10)])                                                                                      | 412  | "Positive.cdsTradeForm.notionalValue"
    MODIFY_TRADE | cdsTradeFormWith(["frequency": null])                                                                                                         | 412  | "NotEmpty.cdsTradeForm.frequency"
    MODIFY_TRADE | cdsTradeFormWith(["frequency": "random"])                                                                                                     | 412  | "ValidStringSet.cdsTradeForm.frequency"
    MODIFY_TRADE | cdsTradeFormWith(["fixedRate": null])                                                                                                         | 412  | "NotNull.cdsTradeForm.fixedRate"
    MODIFY_TRADE | cdsTradeFormWith(["seniority": null])                                                                                                         | 412  | "NotEmpty.cdsTradeForm.seniority"
  }

  @Unroll
  def "should update cds when role #role then responseStatus #responseStatus and response #response"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2020-01-01"), _ as CdsTradeForm, "1") >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put(URI + "/1/2020-01-01", PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(cdsTradeForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseStatus
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | responseStatus | response
    VIEW_TRADE   | 403            | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | 200            | """{"id":"1"}"""
  }
}
