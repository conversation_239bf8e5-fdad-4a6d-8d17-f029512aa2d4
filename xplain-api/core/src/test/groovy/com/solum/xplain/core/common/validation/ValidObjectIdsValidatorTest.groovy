package com.solum.xplain.core.common.validation

import org.bson.types.ObjectId
import spock.lang.Specification
import spock.lang.Unroll

class ValidObjectIdsValidatorTest extends Specification {

  @Unroll
  "should return #result for input #input"() {
    setup:
    def validator = new ValidObjectIdsValidator()

    expect:
    validator.isValid(input, null) == result

    where:
    input                             | result
    []                                | true
    null                              | true
    ["0"]                             | false
    ["000000000000000000000000"]      | true
    [new ObjectId().toString()]       | true
    [null]                            | false
    [null, new ObjectId().toString()] | false
  }
}
