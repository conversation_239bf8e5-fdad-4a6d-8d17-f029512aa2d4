package com.solum.xplain.core.math


import com.opengamma.strata.collect.array.DoubleMatrix
import spock.lang.Specification

class EjmlSvdDecompositionTest extends Specification {

  def "should create decomposition"() {
    setup:
    def decomposition = new EjmlSvdDecomposition()
    double[][] array = [[0d, 1d, 2d], [0d, 1d, 2d], [0d, 1d, 2d]]
    def matrix = DoubleMatrix.copyOf(array)

    when:
    def res = decomposition.apply(matrix)

    then:
    double[] expectedSingularValues = [0.0, 0.2581988897471611, 0.0]
    res.getSingularValues() == expectedSingularValues
  }
}
