package com.solum.xplain.core.curvegroup.curvebond.csv.node

import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.error.Error.PARSING_ERROR

import com.google.common.io.ByteStreams
import com.solum.xplain.core.common.csv.NamedList
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveBuilder
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm
import java.time.LocalDate
import spock.lang.Specification

class BondCurveNodesCsvLoaderTest extends Specification {

  def static CURVES = [new BondCurveBuilder().build()]

  BondCurveNodesCsvLoader loader = new BondCurveNodesCsvLoader()

  def "should return correctly parsed CSV rows"() {
    setup:
    def bytes = loadResource("CurveNodes.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 0
    result.warnings.size() == 0
    result.namedLists.size() == 1

    Map<String, NamedList<BondCurveNodeForm>> nodesByNames = result.namedLists.collectEntries {
      [(it.name): it]
    }

    nodesByNames.containsKey("UKGT")
    nodesByNames.get("UKGT").name == "UKGT"
    nodesByNames.get("UKGT").items.size() == 2
    nodesByNames.get("UKGT").items[0].maturityDate == LocalDate.of(2023, 1, 1)
    nodesByNames.get("UKGT").items[0].cusip == "CUSIP1"
    nodesByNames.get("UKGT").items[0].coupon == 0.2
    nodesByNames.get("UKGT").items[1].maturityDate == LocalDate.of(2024, 1, 1)
    nodesByNames.get("UKGT").items[1].cusip == "CUSIP2"
    nodesByNames.get("UKGT").items[1].coupon == null
  }

  def "should return parse errors"() {
    setup:
    def bytes = loadResource("CurveNodesInvalid.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 3
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].description == "Error parsing line 2: Unknown date format, must be formatted as 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy/M/d', 'd/M/yyyy', 'd-MMM-yyyy', 'dMMMyyyy', 'd/M/yy', 'd-MMM-yy' or 'dMMMyy' but was: XXX"
    errors[1].description == "Error parsing line 3: No value was found for 'CUSIP'"
    errors[2].description == "Error parsing line 4: Error parsing field Bond Coupon. Invalid number format for string: 'XXX'"
  }

  def "should check unique node name"() {
    setup:
    def bytes = loadResource("CurveNodesDuplicate.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 1
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].reason == PARSING_ERROR
    errors[0].description == "Error parsing line 3: Duplicate found: UKGT 2023-01-01"
  }

  byte[] loadResource(String fileName) {
    return ByteStreams.toByteArray(getClass().getResourceAsStream("/curvegroup/curvebond/csv/node/" + fileName))
  }
}
