package com.solum.xplain.core.curvegroup.volatilityfx.csv.volatility

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT

import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class FxVolatilityNodeCsvLoaderTest extends Specification {

  def "should return correctly parsed CSV rows"() {
    setup:
    def bytes = """\
        "Expiry","CcyPair"
        "1Y","EUR/USD"
        "1D","EUR/GBP"
        "1Y","EUR/GBP"
        """.stripIndent().bytes

    when:
    def result = new FxVolatilityNodeCsvLoader().parse(bytes, STRICT)

    then:
    result.isRight()

    def parserResult = result.right().get() as CsvParserResult<CurveGroupFxVolatilityNodeForm>
    def parsedForms = parserResult.parsedLines
    parsedForms.size() == 3

    parsedForms[0].expiry == "1Y"
    parsedForms[0].domesticCurrency == "EUR"
    parsedForms[0].foreignCurrency == "USD"

    parsedForms[1].expiry == "1D"
    parsedForms[1].domesticCurrency == "EUR"
    parsedForms[1].foreignCurrency == "GBP"

    parsedForms[2].expiry == "1Y"
    parsedForms[2].domesticCurrency == "EUR"
    parsedForms[2].foreignCurrency == "GBP"
  }

  def "should fail parsing CSV with duplicate rows"() {
    setup:
    def bytes = """\
        "Expiry","CcyPair"
        "1Y","EUR/USD"
        "1Y","EUR/USD"
        """.stripIndent().bytes

    when:
    def result = new FxVolatilityNodeCsvLoader().parse(bytes, STRICT)

    then:
    result.isLeft()

    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == Error.PARSING_ERROR
    errors[0].description == "Error parsing lines [2,3]: Duplicate entry found: 1Y - EUR/USD"
  }

  def "should return parse errors"() {
    setup:
    def bytes = """\
        "Expiry","CcyPair"
        1Y,EUR/USS
        1Y,USS/EUR
         ",EUR/EUR"
        """.stripIndent().bytes

    expect:
    def result = new FxVolatilityNodeCsvLoader().parse(bytes, STRICT)
    result.isLeft()

    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 3

    errors[0].reason == Error.PARSING_ERROR
    errors[0].description == "Error parsing line 2. Field CcyPair is invalid: Unknown currency USS"

    errors[1].reason == Error.PARSING_ERROR
    errors[1].description == "Error parsing line 3. Field CcyPair is invalid: Unknown currency USS"

    errors[2].reason == Error.PARSING_ERROR
    errors[2].description == "Error parsing line 4. Field Expiry is invalid: Unsupported value: ,EUR/EUR"
  }
}
