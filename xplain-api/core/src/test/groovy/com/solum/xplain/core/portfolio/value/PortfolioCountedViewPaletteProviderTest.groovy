package com.solum.xplain.core.portfolio.value

import com.solum.xplain.core.viewconfig.PaletteProviderSpecification
import com.solum.xplain.core.viewconfig.value.FieldType
import spock.lang.Unroll

class PortfolioCountedViewPaletteProviderTest extends PaletteProviderSpecification {

  static classes = [PortfolioCountedView]

  @Unroll
  def "should provide palette for #clazz"() {
    when:
    def palettes = provider.providePalettes()

    then:
    assert null != palettes.find {
      it.scope().viewClass() == clazz
    }

    where:
    clazz << classes
  }

  def "enum fields for #clazz should all have classifier reference"() {
    when:
    def fields = fieldDefinitionsForClass(clazz)
    def missingClassifier = fields
      .findAll { it.type() == FieldType.ENUM && null == it.enumClassifierName() }
      .collect { it.name() }

    then:
    missingClassifier == []
    where:
    clazz << classes
  }

  def "palette view should not contain any duplicate fields"() {
    when:
    def fields = fieldDefinitionsForClass(PortfolioCountedView)

    def fieldByName = fields.groupBy { it.name() }
    def duplicates = fieldByName.entrySet().stream().filter { it.value.size() > 1 }.toList()

    then:
    duplicates == []
  }

  def "palette view should include expected fields"() {

    when:
    def fields = fieldDefinitionsForClass(PortfolioCountedView)

    then:
    def field = fields.find {
      it.name() == fieldName
    }
    field != null
    field.sortable() == sortable


    where:
    fieldName             | sortable
    "companyName"         | true
    "externalCompanyId"   | true
    "entityName"          | true
    "externalEntityId"    | true
    "name"                | true
    "externalPortfolioId" | true
    "numberOfTrades"      | true
    "teamNames"           | false
    "description"         | true
    "valuationDate"       | true
    "calculatedBy"        | true
    "calculatedAt"        | true
  }

  def "temporal fields should be detected correctly"() {
    when:
    def field = fieldDefinitionsForClass(PortfolioCountedView).find {
      it.name() == fieldName
    }

    then:
    field.type() == type

    where:
    fieldName       | type
    "valuationDate" | FieldType.DATE
    "calculatedAt"  | FieldType.DATETIME
  }
}
