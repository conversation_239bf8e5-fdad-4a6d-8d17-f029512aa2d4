package com.solum.xplain.core.curvemarket

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.instrument.AssetGroup
import com.solum.xplain.core.mdvalue.MarketDataValueService
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class MarketDataExtractionParamsTest extends Specification {
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 01))
  private static def CURVE_DATE = LocalDate.of(2022, 01, 02)
  private static final MDG_ID = "md"

  def "should resolve values by key for SingleDateMarketDataExtractionParams"() {
    setup:
    CurveConfigMarketStateKey stateKey = Mock()
    List<String> discountCurves = ["curve1", "curve2"]
    InstrumentOverrideCurveDateDetails overridecurveDateDetails = Mock()
    MarketDataValueService marketDataValueService = Mock()

    SingleDateMarketDataExtractionParams params = SingleDateMarketDataExtractionParams.mdParamsWithCurves(
      stateKey,
      discountCurves,
      )

    1 * stateKey.getMarketDataGroupId() >> MDG_ID
    1 * stateKey.getStateDate() >> STATE_DATE
    1 * stateKey.getCurveDate() >> CURVE_DATE

    1 * marketDataValueService.getResolvedValuesByKey(
      MDG_ID,
      STATE_DATE,
      CURVE_DATE,
      _ as List<AssetGroup>
      ) >> Either.right([:])

    when:
    def result = params.resolvedValuesByKey(
      marketDataValueService
      )

    then:
    result.isRight()
  }


  def "should resolve values by key for InstrumentTypeDateMarketDataExtractionParams"() {
    setup:
    CurveConfigMarketStateKey stateKey = Mock()
    List<String> discountCurves = ["curve1", "curve2"]
    InstrumentOverrideCurveDateDetails overridecurveDateDetails = Mock()
    MarketDataValueService marketDataValueService = Mock()

    InstrumentTypeDateMarketDataExtractionParams params = InstrumentTypeDateMarketDataExtractionParams.mdParamsWithCurves(
      stateKey,
      discountCurves,
      overridecurveDateDetails
      )

    1 * stateKey.getMarketDataGroupId() >> MDG_ID
    1 * stateKey.getStateDate() >> STATE_DATE
    0 * stateKey.getCurveDate()

    1 * marketDataValueService.getResolvedValuesByKey(
      MDG_ID,
      STATE_DATE,
      overridecurveDateDetails,
      _ as List<AssetGroup>
      ) >> Either.right([:])

    when:
    def result = params.resolvedValuesByKey(
      marketDataValueService
      )

    then:
    result.isRight()
  }
}
