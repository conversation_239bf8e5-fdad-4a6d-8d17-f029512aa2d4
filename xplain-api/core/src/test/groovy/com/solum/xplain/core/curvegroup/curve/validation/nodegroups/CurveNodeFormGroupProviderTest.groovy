package com.solum.xplain.core.curvegroup.curve.validation.nodegroups

import com.solum.xplain.core.classifiers.CurveNodeTypes
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm
import spock.lang.Specification

class CurveNodeFormGroupProviderTest extends Specification {

  def "should return correct validation groups for each node type"() {
    setup:
    CurveNodeForm form = new CurveNodeForm()
    CurveNodeFormGroupProvider provider = new CurveNodeFormGroupProvider()
    form.setType(type)

    when:
    List<Class<?>> result = provider.getValidationGroups(form)

    then:
    result == expected

    where:
    type                                            | expected
    CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE           | [CurveNodeForm.class, CurveNodeFormIborFixingDepositGroup.class]
    CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE          | [CurveNodeForm.class, CurveNodeFormFixedOvernightSwapGroup.class]
    CurveNodeTypes.FIXED_IBOR_SWAP_NODE               | [CurveNodeForm.class, CurveNodeFormFixedIborSwapGroup.class]
    CurveNodeTypes.IBOR_IBOR_SWAP_NODE                | [CurveNodeForm.class, CurveNodeFormIborIborSwapGroup.class]
    CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE     | [CurveNodeForm.class, CurveNodeFormOvernightIborBasisSwapGroup.class]
    CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE           | [CurveNodeForm.class, CurveNodeFormXCcyIborIborSwapGroup.class]
    CurveNodeTypes.XCCY_OIS_OIS_SWAP_NODE             | [CurveNodeForm.class, CurveNodeFormXCcyOOSwapGroup.class]
    CurveNodeTypes.XCCY_FIXED_OVERNIGHT_SWAP_NODE     | [CurveNodeForm.class, CurveNodeFormXCcyFixedOvernightSwapGroup.class]
    CurveNodeTypes.TERM_OIS_FIXING_DEPOSIT_NODE       | [CurveNodeForm.class, CurveNodeFormTermOisFixingDepositGroup.class]
    CurveNodeTypes.OVERNIGHT_TERM_OVERNIGHT_SWAP_NODE | [CurveNodeForm.class, CurveNodeFormOvernightTermOvernightSwapGroup.class]
  }
}
