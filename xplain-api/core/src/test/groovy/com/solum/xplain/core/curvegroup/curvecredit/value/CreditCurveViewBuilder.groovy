package com.solum.xplain.core.curvegroup.curvecredit.value

import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy

@Builder(builderStrategy = ExternalStrategy, forClass = CreditCurveView)
class CreditCurveViewBuilder {
  static CreditCurveView fromEntity(CreditCurve entity) {
    return new CreditCurveViewBuilder()
      .seniority(entity.seniority)
      .corpTicker(entity.corpTicker)
      .currency(entity.currency)
      .quoteConvention(entity.getQuoteConvention())
      .docClause(entity.getDocClause())
      .build()
  }
}
