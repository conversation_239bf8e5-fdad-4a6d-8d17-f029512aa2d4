package com.solum.xplain.core.users

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.AuditContext
import java.time.LocalDateTime

class AuditContextBuilder {
  static auditContext() {
    auditContext(UserBuilder.user(), LocalDateTime.parse("2017-01-01T00:00:00"))
  }

  static auditContext(XplainPrincipal user, LocalDateTime date) {
    new AuditContext(AuditUser.of(user), date)
  }
}
