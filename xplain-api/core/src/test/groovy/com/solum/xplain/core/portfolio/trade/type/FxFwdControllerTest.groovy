package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fxForwardForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fxForwardFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.FxForwardTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [FxFwdController])
@Import([TestSecurityConfig])
class FxFwdControllerTest extends Specification {
  private static final String URI = "/portfolio/{id}/trades/fxfwd"
  private static final String PORTFOLIO_ID = "portfolio"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should get fx forward"() {
    setup:
    def trade = PortfolioItemBuilder.trade(CoreProductType.FXFWD, TradeDetailsBuilder.fxSingle(now()))
    1 * service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(trade)

    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
      .param("stateDate", "2020-01-01")
      .with(userWithAuthority(VIEW_TRADE))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new fx forward with form #form, role #role, and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as FxForwardTradeForm) >> right(entityId("1"))
    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                                           | code | response
    VIEW_TRADE   | fxForwardForm()                                                                | 403  | "OPERATION_NOT_ALLOWED"
    VIEW_TRADE   | fxForwardFormWith("tradeDate", null)                                           | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | fxForwardForm()                                                                | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fxForwardFormWith("tradeDate", null)                                           | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fxForwardFormWith("foreignCurrencyAmount", null)                               | 412  | "NotNull.fxForwardTradeForm.foreignCurrencyAmount"
    MODIFY_TRADE | fxForwardFormWith("domesticCurrencyAmount", null)                              | 412  | "NotNull.fxForwardTradeForm.domesticCurrencyAmount"
    MODIFY_TRADE | fxForwardFormWith("businessDayConvention", null)                               | 412  | "NotEmpty.fxForwardTradeForm.businessDayConvention"
    MODIFY_TRADE | fxForwardFormWith("businessDayConvention", "random")                           | 412  | "ValidStringSet.fxForwardTradeForm.businessDayConvention"
    MODIFY_TRADE | fxForwardFormWith("externalTradeId", null)                                     | 412  | "NotEmpty.fxForwardTradeForm.externalTradeId"
    MODIFY_TRADE | fxForwardFormWith("externalTradeId", "WITH SPACES")                            | 412  | "ValidIdentifier.fxForwardTradeForm.externalTradeId"
    MODIFY_TRADE | fxForwardFormWith("externalTradeId", "lowercase")                              | 412  | "ValidIdentifier.fxForwardTradeForm.externalTradeId"
    MODIFY_TRADE | fxForwardFormWith("foreignCurrencyAmount", CurrencyAmountForm.of("USD", null)) | 412  | "NotNull.fxForwardTradeForm.foreignCurrencyAmount.amount"
    MODIFY_TRADE | fxForwardFormWith("foreignCurrencyAmount", CurrencyAmountForm.of("USD", 1.0))  | 412  | "RequiredDifferentAmountSigns.fxForwardTradeForm.foreignCurrencyAmount.amount"
    MODIFY_TRADE | fxForwardFormWith("foreignCurrencyAmount", CurrencyAmountForm.of("EUR", -1.0)) | 412  | "RequiredDifferentCurrencies.fxForwardTradeForm.foreignCurrencyAmount.currency"
    MODIFY_TRADE | fxForwardFormWith("payLegExtIdentifier", "REC_LEG_ID")                         | 412  | "RequiredDifferentFxLegIdentifiers.fxForwardTradeForm"
  }

  @Unroll
  def "should update fx forward when role #role then responseStatus #responseStatus and response #response"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2020-01-01"), _ as FxForwardTradeForm, "1") >> right(entityId("1"))
    when:
    def results = mockMvc.perform(put(URI + "/1/2020-01-01", PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(fxForwardForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseStatus
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | responseStatus | response
    VIEW_TRADE   | 403            | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | 200            | """{"id":"1"}"""
  }
}
