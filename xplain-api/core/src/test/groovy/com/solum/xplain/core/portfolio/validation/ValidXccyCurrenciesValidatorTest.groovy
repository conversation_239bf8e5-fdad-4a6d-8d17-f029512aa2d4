package com.solum.xplain.core.portfolio.validation

import static com.solum.xplain.core.portfolio.validation.ValidXccyCurrenciesValidator.DIFFERENT_CURRENCIES_REQUIRED
import static com.solum.xplain.core.portfolio.validation.ValidXccyCurrenciesValidator.TRADE_CCY_MUST_MATCH_LEG_CURRENCIES

import com.solum.xplain.core.portfolio.form.SwapLegForm
import com.solum.xplain.core.portfolio.form.XccyTradeForm
import jakarta.validation.ConstraintValidatorContext
import spock.lang.Specification
import spock.lang.Unroll

class ValidXccyCurrenciesValidatorTest extends Specification {
  @Unroll
  "validation should return result for form #form"() {
    setup:
    def validator = new ValidXccyCurrenciesValidator()
    def context = Mock(ConstraintValidatorContext)
    def constraintBuilder = Mock(ConstraintValidatorContext.ConstraintViolationBuilder)
    def nodeContext = Mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext)
    (result ? 0 : _) * context.buildConstraintViolationWithTemplate(template) >> constraintBuilder
    (result ? 0 : _) * constraintBuilder.addPropertyNode(_) >> nodeContext
    (result ? 0 : _) * nodeContext.addPropertyNode(_) >> nodeContext
    (result ? 0 : _) * nodeContext.addConstraintViolation() >> context

    expect:
    validator.isValid(form, context) == result
    where:
    form                                                | template                             | result
    new XccyTradeForm(
      tradeCurrency: "EUR",
      leg1: new SwapLegForm(notionalCurrency: "EUR"),
      leg2: new SwapLegForm(notionalCurrency: "EUR")
      )                                                   | DIFFERENT_CURRENCIES_REQUIRED       | false
    new XccyTradeForm(
      tradeCurrency: "GBP",
      leg1: new SwapLegForm(notionalCurrency: "EUR"),
      leg2: new SwapLegForm(notionalCurrency: "USD")
      )                                                   | TRADE_CCY_MUST_MATCH_LEG_CURRENCIES | false
    new XccyTradeForm(
      tradeCurrency: "EUR",
      leg1: new SwapLegForm(notionalCurrency: "EUR"),
      leg2: new SwapLegForm(notionalCurrency: "USD")
      )                                                   | null                                | true
    new XccyTradeForm(
      tradeCurrency: "EUR",
      leg1: new SwapLegForm(),
      leg2: new SwapLegForm()
      )                                                   | null                                | true
    new XccyTradeForm()                                 | null                                | true
  }
}
