package com.solum.xplain.core.permissions

import com.solum.xplain.core.permissions.extension.XplainPermission
import com.solum.xplain.core.permissions.provider.PermissionsProvider
import spock.lang.Specification

class PermissionResolverTest extends Specification {
  def provider1 = new PermissionsProvider() {
    @Override
    List<XplainPermission> availablePermissions() {
      return [
        new XplainPermission("VIEW_PORTFOLIO", "V_PF", UserType.BASIC_USER, CorePermissionCategory.PORTFOLIO_LIST),
        new XplainPermission("MODIFY_PORTFOLIO", "M_PF", UserType.ADMIN_USER, CorePermissionCategory.PORTFOLIO_LIST)
      ]
    }
  }
  def provider2 = new PermissionsProvider() {
    @Override
    List<XplainPermission> availablePermissions() {
      return [
        new XplainPermission("EMBELLISH_PORTFOLIO", "E_PF", UserType.SUPER_USER, CorePermissionCategory.PORTFOLIO_LIST)
      ]
    }
  }
  def permissionResolver = new PermissionResolver([provider1, provider2])

  def "should return matching permissions for #userType"() {
    when:
    def result = permissionResolver.getBySubCategoryAndUserType(CorePermissionCategory.PORTFOLIO_LIST.name(), userType).collect {it.name }

    then:
    result.size() == permissions.size()
    result.containsAll(permissions)

    where:
    userType            | permissions
    UserType.BASIC_USER | ["VIEW_PORTFOLIO"]
    UserType.SUPER_USER | ["VIEW_PORTFOLIO", "EMBELLISH_PORTFOLIO"]
    UserType.ADMIN_USER | ["VIEW_PORTFOLIO", "EMBELLISH_PORTFOLIO", "MODIFY_PORTFOLIO"]
  }
}
