package com.solum.xplain.core.viewconfig.provider

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.viewconfig.ViewConfigurationMapper
import com.solum.xplain.core.viewconfig.ViewConfigurationRepository
import com.solum.xplain.core.viewconfig.entity.ViewConfiguration
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupView
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionView
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView
import com.solum.xplain.core.viewconfig.value.FieldType
import com.solum.xplain.core.viewconfig.value.View
import com.solum.xplain.core.viewconfig.value.ViewConfigurationCreateForm
import com.solum.xplain.core.viewconfig.value.ViewConfigurationUpdateForm
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView
import io.atlassian.fugue.Either
import org.bson.types.ObjectId
import org.springframework.security.core.Authentication
import spock.lang.Specification

class ViewConfigurationServiceTest extends Specification {
  static class ViewClass1 {
    static View<ViewClass1> view = new View<>(ViewClass1)
  }

  static class ViewClass2 {
    static View<ViewClass2> view = new View<>(ViewClass2)
  }

  static class ViewClass3 {
  }

  static FieldDefinitionView fieldA = FieldDefinitionView.ofNamedProperty("a", FieldType.STRING, "a", false)
  static FieldDefinitionView fieldB = FieldDefinitionView.ofNamedProperty("b", FieldType.STRING, "b", false)
  static ColumnDefinitionView colA = new ColumnDefinitionView(fieldA, true, true, null, null)
  static ColumnDefinitionView colB = new ColumnDefinitionView(fieldB, true, true, null, null)
  static ColumnDefinitionGroupView colGroupA = new ColumnDefinitionGroupView("A", [colA])
  static ColumnDefinitionGroupView colGroupB = new ColumnDefinitionGroupView("B", [colB])
  static ViewConfigurationView<ViewClass1> defaultView = new ViewConfigurationView<ViewClass1>("defaultId", ViewClass1.view, ViewConfigurationProvider.DEFAULT_VIEW_NAME, false, [colGroupA], true, null, null)
  static ViewConfigurationView<ViewClass2> customView = new ViewConfigurationView<ViewClass2>("customId", ViewClass2.view, "Custom View", false, [colGroupB], true, null, null)

  ViewConfigurationMapper mapper = Mock()
  ViewConfigurationRepository repository = Mock(ViewConfigurationRepository)
  AuthenticationContext authenticationContext = Mock()

  def "should create view configurations"() {
    given:
    def objectId = ObjectId.get()
    def configuration = new ViewConfiguration(id: objectId)
    def form = Mock(ViewConfigurationCreateForm)
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def entityId = viewConfigService.saveViewConfiguration(form)

    then:
    1 * mapper.fromForm(form) >> configuration
    1 * repository.save(configuration) >> configuration

    and:
    entityId == EntityId.entityId(objectId)
  }

  def "should find view configurations by class"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def viewConfigs = viewConfigService.findAvailableViewConfigurations(null, ViewClass1)

    then:
    !viewConfigs.isEmpty()
    viewConfigs.get(0) == defaultView
  }

  def "should find view configurations from multiple providers"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView]), viewConfigProvider([customView])], repository, mapper, authenticationContext)

    when:
    def viewConfigs = viewConfigService.findAvailableViewConfigurations(null, viewClass)

    then:
    !viewConfigs.isEmpty()
    viewConfigs.get(0) == expected

    where:
    viewClass  || expected
    ViewClass1 || defaultView
    ViewClass2 || customView
  }

  def "should find view configuration by name from multiple providers"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView]), viewConfigProvider([customView])], repository, mapper, authenticationContext)

    when:
    def viewConfig = viewConfigService.findViewConfiguration(null, viewClass, name)

    then:
    viewConfig.isEmpty() == (expected == null)
    viewConfig.orElse(null) == expected

    where:
    viewClass  | name                                        || expected
    ViewClass1 | "defaultId"                                 || defaultView
    ViewClass1 | "customId"                                  || null
    ViewClass2 | "customId"                                  || customView
  }

  def "should find multiple view configurations from single provider"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView, customView])], repository, mapper, authenticationContext)

    when:
    def viewConfigs = viewConfigService.findAvailableViewConfigurations(null, viewClass)

    then:
    !viewConfigs.isEmpty()
    viewConfigs.get(0) == expected

    where:
    viewClass  || expected
    ViewClass1 || defaultView
    ViewClass2 || customView
  }

  def "should find view configuration by name from single provider"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView, customView])], repository, mapper, authenticationContext)

    when:
    def viewConfig = viewConfigService.findViewConfiguration(null, viewClass, name)

    then:
    viewConfig.isEmpty() == (expected == null)
    viewConfig.orElse(null) == expected

    where:
    viewClass  | name                                        || expected
    ViewClass1 | "defaultId"                                 || defaultView
    ViewClass1 | "Missing id"                                || null
    ViewClass2 | "customId"                                  || customView
    ViewClass2 | "defaultId"                                 || null
    ViewClass3 | "defaultId"                                 || null
  }

  def "should find view configuration by id with fallback to default"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def viewConfig = viewConfigService.getViewConfigurationOrDefault(null, ViewClass1, "Missing View id")

    then:
    viewConfig.isRight()
    viewConfig.right().get() == defaultView
  }

  def "should return empty list if view configuration not provided for type"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def viewConfigs = viewConfigService.findAvailableViewConfigurations(null, ViewClass2)

    then:
    viewConfigs.isEmpty()
  }

  def "should return error if default view is missing"() {
    given:
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def viewConfig = viewConfigService.getViewConfigurationOrDefault(null, ViewClass2, "Missing View id")

    then:
    viewConfig.isLeft()
    viewConfig.left().get().reason == Error.OBJECT_NOT_FOUND
  }

  def "should update view configurations"() {
    given:
    def user = user()
    def auth = Mock(Authentication)
    def configuration = new ViewConfiguration(id: ObjectId.get(), createdBy: AuditUser.of(user))
    def form = Mock(ViewConfigurationUpdateForm)

    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def result = viewConfigService.updateViewConfiguration(auth, configuration.id.toHexString(), form)

    then:
    1 * authenticationContext.user(auth) >> user
    1 * repository.findById(configuration.id) >> Optional.of(configuration)
    1 * mapper.fromForm(form, configuration) >> configuration
    1 * repository.save(configuration) >> configuration

    and:
    result.isRight()
    result.getOrNull() == EntityId.entityId(configuration.id)
  }

  def "should return error when updating and not owner"() {
    given:
    def user = user()
    def auth = Mock(Authentication)
    def configuration = new ViewConfiguration(id: ObjectId.get(), createdBy: AuditUser.of(user))
    def form = Mock(ViewConfigurationUpdateForm)

    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def result = viewConfigService.updateViewConfiguration(auth, configuration.id.toHexString(), form)

    then:
    1 * authenticationContext.user(auth) >> Mock(XplainPrincipal)
    1 * repository.findById(configuration.id) >> Optional.of(configuration)
    0 * mapper._
    0 * repository.save(_ as ViewConfiguration)

    and:
    result.isLeft()
    def error = result.swap().getOrNull()
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "View configuration can only be modified by the user who created it."
  }

  def "should delete view"() {
    given:
    def user = user()
    def auth = Mock(Authentication)
    def configuration = new ViewConfiguration(id: ObjectId.get(), createdBy: AuditUser.of(user))

    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def result = viewConfigService.deleteViewConfiguration(auth, configuration.id.toHexString())

    then:
    1 * authenticationContext.user(auth) >> user
    1 * repository.findById(configuration.id) >> Optional.of(configuration)
    1 * repository.delete(_ as ViewConfiguration)

    and:
    result.isRight()
    result.getOrNull() == EntityId.entityId(configuration.id)
  }

  def "should return error when deleting view when not owner"() {
    given:
    def user = user()
    def auth = Mock(Authentication)
    def configuration = new ViewConfiguration(id: ObjectId.get(), createdBy: AuditUser.of(user))

    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)

    when:
    def result = viewConfigService.deleteViewConfiguration(auth, configuration.id.toHexString())

    then:
    1 * authenticationContext.user(auth) >> Mock(XplainPrincipal)
    1 * repository.findById(configuration.id) >> Optional.of(configuration)
    0 * repository.delete(_ as ViewConfiguration)

    and:
    result.isLeft()
    def error = result.swap().getOrNull()
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "View configuration can only be modified by the user who created it."
  }

  private static ViewConfigurationProvider viewConfigProvider(List<ViewConfigurationView> views) {
    return (user, viewCls) -> views.stream().filter {
      v -> v.scope().viewClass() == viewCls
    }.toList()
  }

  def "should clone view configuration"() {
    given:
    def user = user("id")
    def auth = Mock(Authentication)
    def viewConfigService = new ViewConfigurationService([viewConfigProvider([defaultView])], repository, mapper, authenticationContext)
    def expectedConfiguration = new ViewConfiguration(id: ObjectId.get(), name: "Copy of viewName", createdBy: AuditUser.of(user))

    mapper.clone(_ as ViewConfigurationView) >> new ViewConfiguration()
    authenticationContext.userEither(auth) >> Either.right(user)
    repository.save(_ as ViewConfiguration) >> { ViewConfiguration vc ->
      vc.id == null
      return vc.with {
        id = expectedConfiguration.id
        return it
      }
    }

    when:
    def result = viewConfigService.cloneViewConfiguration(auth, "defaultId", ViewClass1)

    then:
    result.isRight()
    result.getOrNull() == EntityId.entityId(expectedConfiguration.id)
  }
}
