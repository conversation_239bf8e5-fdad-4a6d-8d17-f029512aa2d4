package com.solum.xplain.core.common.validation.identifier

import spock.lang.Specification
import spock.lang.Unroll

class IdentifierValidationUtilsTest extends Specification {

  @Unroll
  def "should return #valid when identifier name #identifier"() {
    when:
    def isValidIdentifier = IdentifierValidationUtils.isValid(identifier)

    then:
    isValidIdentifier == valid

    where:
    identifier               | valid
    "identifier"             | false
    "IDENTIFIER WITH SPACES" | false
    "IDENTIFIER"             | true
    "IDENTIFIER1"            | true
    "IDENTIFIER*"            | true
    "IDENTIFIER!@#&*"        | true
  }

  def "should return #valid when external identifier name #externalIdentifier"() {
    when:
    def isValidExternalIdentifier = IdentifierValidationUtils.isValidExternalIdentifier(externalIdentifier)

    then:
    isValidExternalIdentifier == valid

    where:
    externalIdentifier       | valid
    "identifier"             | true
    "IDENTIFIER WITH SPACES" | true
    "IDENTIFIER"             | true
    "IDENTIFIER1"            | true
    "IDENTIFIER*"            | true
    "IDENTIFIER!@#&*_§"      | true
    "IDENTIFIER\u00a0"       | false
  }
}
