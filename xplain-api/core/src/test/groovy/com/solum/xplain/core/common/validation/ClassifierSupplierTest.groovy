package com.solum.xplain.core.common.validation

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.STRIPPING_TYPE_CLASSIFIER

import spock.lang.Specification

class ClassifierSupplierTest extends Specification {

  def "should supply correct values"() {
    setup:
    def validator = new ClassifierSupplier(STRIPPING_TYPE_CLASSIFIER)

    when:
    def result = validator.get()

    then:
    result == ['OIS', 'LIBOR']
  }
}
