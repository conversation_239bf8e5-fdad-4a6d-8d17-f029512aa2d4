package com.solum.xplain.core.portfolio.csv.loader

import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.basics.schedule.StubConvention
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Collectors
import spock.lang.Specification

class LoanNoteTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def loader = new LoanNoteTradeCsvLoader()

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CoreProductType.LOAN_NOTE)
  }

  def "should parse LOAN_NOTE trade"() {
    setup:
    def rows = loadResource("LoanNoteTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 4
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails())
    result.businessDayConvention == "NoAdjust"
    result.businessDayAdjustmentType == null
    result.startDate == LocalDate.parse("2017-12-20")
    result.endDate == LocalDate.parse("2018-12-20")
    result.firstRegularStartDate == LocalDate.parse("2017-12-30")
    result.lastRegularEndDate == LocalDate.parse("2018-01-20")
    result.stubConvention == "Both"

    result.receiveLeg.notional == 1000000d
    result.receiveLeg.currency == "USD"
    result.receiveLeg.paymentFrequency == "3M"
    result.receiveLeg.dayCount == "Act/365F"

    result.loanNoteTradeDetails.creditSpread == 1d
    result.loanNoteTradeDetails.reference == "UST"

    def result1 = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails())
    result1.businessDayConvention == "NoAdjust"
    result1.businessDayAdjustmentType == null
    result1.stubConvention == StubConvention.SMART_INITIAL.toString()
    result1.rollConvention == RollConventions.NONE.name

    def result2 = parsedRows[2].getOrNull().toTradeDetails(new TradeInfoDetails())
    result2.loanNoteTradeDetails.reference == "JPGT"
    result2.receiveLeg.dayCount == DayCounts.ACT_365F.getName()

    def result3 = parsedRows[3].getOrNull().toTradeDetails(new TradeInfoDetails())
    result3.loanNoteTradeDetails.reference == "JPGT"
    result3.receiveLeg.currency == "JPY"
    result3.receiveLeg.dayCount == DayCounts.ACT_365F.getName()
  }

  def "should return LOAN_NOTE parse error"() {
    setup:
    def rows = loadResource("LoanNoteTradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Error parsing field End Date. Must be after Start Date"
    1   | "Error at line number 3. Error: Trade must define field 'Regular Start Date' if stub convention is 'Both'"
    2   | "Error at line number 4. Error: Trade must define field 'Regular End Date' if stub convention is 'Both'"
    3   | "Error at line number 5. Error: Trade must not define field 'Regular Start Date' if stub convention is not 'Both'"
    4   | "Error at line number 6. Error: Unsupported value: KRW. Supported values: [EUR, GBP, JPY, USD]"
    5   | "Error at line number 7. Error: Value must be positive for field: Loan Fixed Rate"
    6   | "Error at line number 8. Error: Value must be positive for field: Loan Notional"
    7   | "Error at line number 9. Error: Unsupported value: 1D for field Loan Freq. Supported values: [1W, 4W, 13W, 26W, 1M, 3M, 6M, 12M, 1Y, TERM]"
    8   | "Error at line number 10. Error: Unsupported value: test for field Loan Day Count. Supported values: [One/One, 30/360 ISDA, 30E/360, 30E/360 ISDA, 30U/360, Act/360, Act/365 Actual, Act/365F, Act/365L, Act/Act ISDA, Act/Act Year, Act/Act ICMA]"
    9   | "Error at line number 11. Error: Unsupported value: UST for field Loan Reference. Supported values: [DEGT]"
    10  | "Error at line number 12. Error: Error parsing field Loan Settlement Days Offset. Value must be positive or zero."
    11  | "Error at line number 13. Error: Unsupported value: ModifiedFollowing for field Business Day Convention. Supported values: [NoAdjust]"
    12  | "Error at line number 14. Error: Unsupported value: Act/Act ICMA for field Loan Day Count. Supported values: [One/One, 30/360 ISDA, 30E/360, 30E/360 ISDA, 30U/360, Act/360, Act/365 Actual, Act/365F, Act/365L, Act/Act ISDA, Act/Act Year]"
    13  | "Error at line number 15. Error: Trade currency must match one of leg currencies"
  }
}
