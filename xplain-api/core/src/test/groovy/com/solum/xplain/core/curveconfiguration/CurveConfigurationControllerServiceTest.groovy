package com.solum.xplain.core.curveconfiguration

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.filter.VersionedEntityFilter
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationOverrideForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.instrument.InstrumentType
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.Map.Entry
import org.springframework.data.domain.Sort
import spock.lang.Specification

class CurveConfigurationControllerServiceTest extends Specification {

  private static final BitemporalDate STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  def repository = Mock(CurveConfigurationRepository)
  def curveGroupCurveRepository = Mock(CurveGroupCurveRepository)
  def valuationsResolver = Mock(CurveConfigurationValuationsResolver)

  def service = new CurveConfigurationControllerService(repository, curveGroupCurveRepository, valuationsResolver)

  def "should create config"() {
    setup:
    1 * repository.insert(null) >> Either.right(new EntityId("1"))

    when:
    def result = service.create(null)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should create config with null overrides"() {
    setup:
    def curveConfigForm = curveConfigurationForm()

    1 * repository.insert(curveConfigForm) >> Either.right(new EntityId("1"))

    when:
    def result = service.create(curveConfigForm)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should create config with overrides"() {
    setup:
    def curveConfigForm = curveConfigurationForm(
      [
        instrumentsMapEntry(CoreInstrumentType.IBOR_FIXING_DEPOSIT, ["CNY 1W Offshore", "CNY 1W", "THB 6M Offshore", "THB 6M"]),
        instrumentsMapEntry(CoreInstrumentType.FIXED_IBOR_SWAP, ["CNY 1W Offshore", "CNY 1W", "THB 6M Offshore"])
      ]
      )

    1 * repository.insert(curveConfigForm) >> Either.right(new EntityId("1"))

    when:
    def result = service.create(curveConfigForm)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should fail to create curve config form with missing offshore/onshore curve in override for IborFixingDeposit"() {
    setup:
    def curveConfigForm = curveConfigurationForm([instrumentsMapEntry(CoreInstrumentType.IBOR_FIXING_DEPOSIT, assetNames)])

    0 * repository.insert(_) >> Either.right(new EntityId("1"))

    when:
    def result = service.create(curveConfigForm)

    then:
    result.isLeft()
    def error = result.left().getOrNull() as ErrorItem
    error.reason == Error.VALIDATION_ERROR
    error.description == errMsg

    where:
    assetNames                                        | errMsg
    ["CNY 1W Offshore"]                               | "Offshore curve CNY 1W Offshore must have onshore equivalent in overrides for IborFixingDeposit"
    ["CNY 1W"]                                        | "Onshore curve CNY 1W must have offshore equivalent in overrides for IborFixingDeposit"
    ["CNY 1W Offshore", "GBP 6M"]                     | "Offshore curve CNY 1W Offshore must have onshore equivalent in overrides for IborFixingDeposit"
    ["CNY 1W Offshore", "THB 6M"]                     | "Offshore curve CNY 1W Offshore must have onshore equivalent in overrides for IborFixingDeposit"
    ["CNY 1W Offshore", "CNY 1W", "THB 6M"]           | "Onshore curve THB 6M must have offshore equivalent in overrides for IborFixingDeposit"
    ["CNY 1W Offshore", "CNY 1W", "THB 6M Offshore"]  | "Offshore curve THB 6M Offshore must have onshore equivalent in overrides for IborFixingDeposit"
  }

  def "should get config"() {
    setup:
    1 * repository.getView("id", STATE_DATE) >> Either.right(new CurveConfigurationView())

    when:
    def result = service.getOne("id", STATE_DATE)

    then:
    result.isRight()
  }

  def "should get curve group curves"() {
    setup:
    1 * repository.getView("id", _) >> Either.right(new CurveConfigurationView(curveGroupId: "id"))
    1 * curveGroupCurveRepository.getCurveViews("id", STATE_DATE, _) >> []

    when:
    def result = service.getCurveGroupCurves("id", STATE_DATE)

    then:
    result.isRight()
  }

  def "should get all configs for MD"() {
    setup:
    def resp = [
      new CurveConfigurationView(entityId: "1"),
      new CurveConfigurationView(entityId: "2")
    ]

    1 * repository.curveConfigurationViews(STATE_DATE, active(), Sort.unsorted()) >> resp

    1 * valuationsResolver.curveConfigsIdsByMarketData(STATE_DATE) >> [
      "md1": Set.of("1", "3"),
      "md2": Set.of("2", "4")
    ]

    when:
    def result = service.getAllForMD(STATE_DATE, "md1")

    then:
    result.size() == 1
    result[0].entityId == "1"
  }

  def "should update config"() {
    setup:
    1 * repository.update(
      _ as String,
      STATE_DATE.actualDate,
      null
      ) >> Either.right(new EntityId("1"))

    when:
    def result = service.update("id", STATE_DATE.actualDate, null)

    then:
    result.isRight()
    result.right().get() == new EntityId("1")
  }

  def "should get all curve configuration views"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def filter = VersionedEntityFilter.all()
    def sort = Sort.unsorted()
    def views = [new CurveConfigurationView(entityId: "1")]

    1 * repository.curveConfigurationViews(stateDate, filter, sort) >> views

    when:
    def result = service.getAll(stateDate, filter, sort)

    then:
    result == views
  }

  def instrumentsMapEntry(InstrumentType instrumentType, List<String> assetNames) {
    return Map.entry(instrumentType, new CurveConfigurationProviderOverrideForm(assetNames: assetNames))
  }

  def curveConfigurationForm(List<Entry<InstrumentType, CurveConfigurationProviderOverrideForm>> entries) {
    HashMap<InstrumentType, CurveConfigurationProviderOverrideForm> map = new HashMap<>()
    map.putAll(entries)
    return new CurveConfigurationForm(
      name: "curveConfig1",
      curveGroupId: "curveGroupId",
      overrides: [
        new CurveConfigurationOverrideForm(
        priority: 1,
        enabled: true,
        instruments: map
        )
      ]
      )
  }

  def curveConfigurationForm() {
    HashMap<InstrumentType, CurveConfigurationProviderOverrideForm> map = new HashMap<>()
    return new CurveConfigurationForm(
      name: "curveConfig1",
      curveGroupId: "curveGroupId",
      overrides: null
      )
  }
}
