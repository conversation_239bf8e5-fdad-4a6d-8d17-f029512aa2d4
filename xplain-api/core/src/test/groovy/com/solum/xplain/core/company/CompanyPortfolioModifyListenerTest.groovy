package com.solum.xplain.core.company

import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.portfolio.PortfolioBuilder
import com.solum.xplain.core.portfolio.event.PortfolioAdded
import org.bson.types.ObjectId
import spock.lang.Specification

class CompanyPortfolioModifyListenerTest extends Specification {

  CompanyPortfolioModifyListener testObject
  CompanyRepository companyRepository = Mock()
  CompanyPortfolioProvider providerOne = Mock()
  CompanyPortfolioProvider providerTwo = Mock()
  def companyPortfolioProviders = [providerOne, providerTwo]

  def setup() {
    testObject = new CompanyPortfolioModifyListener(companyRepository, companyPortfolioProviders)
  }

  def "should sum returned values from company portfolio providers"() {
    setup:
    def providerOneCount = 2
    def providerTwoCount = 3
    def expectedCount = 5
    def portfolio = PortfolioBuilder.portfolio()
    def entityId = portfolio.getCompany().getEntityId()
    def portfolioAddedEvent = PortfolioAdded.newOf(portfolio.company)

    when:
    testObject.onCompanyPortfolioModify(portfolioAddedEvent)

    then:
    1 * providerOne.countAll([entityId]) >> [(entityId): providerOneCount]
    1 * providerTwo.countAll([entityId]) >> [(entityId): providerTwoCount]
    1 * companyRepository.updatePortfolioCounts([(entityId) : (expectedCount)])
  }

  def "should sum returned values from company portfolio providers for multiple companies"() {
    setup:
    def company2 = ObjectId.get().toHexString()
    def providerOneCount = 2
    def providerTwoCount = 3
    def expectedCount = 5
    def portfolio = PortfolioBuilder.portfolio()
    def entityId = portfolio.getCompany().getEntityId()
    def portfolioAddedEvent = new PortfolioAdded([entityId, company2])

    when:
    testObject.onCompanyPortfolioModify(portfolioAddedEvent)

    then:
    1 * providerOne.countAll([entityId, company2]) >> [(entityId): providerOneCount, (company2): providerOneCount]
    1 * providerTwo.countAll([entityId, company2]) >> [(entityId): providerTwoCount, (company2): providerTwoCount]

    1 * companyRepository.updatePortfolioCounts([(entityId): expectedCount, (company2): expectedCount])
  }
}
