package com.solum.xplain.core.curvegroup.volatility.csv.surface

import com.google.common.io.ByteStreams
import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView
import spock.lang.Specification

class VolatilitySurfaceCsvMapperTest extends Specification {

  def "should return correctly mapped csv row"() {
    setup:
    def view1 = new VolatilitySurfaceView(name: "CHF 1M Vols",
    xInterpolator: "Linear",
    xExtrapolatorLeft: "Flat",
    xExtrapolatorRight: "Flat",
    yInterpolator: "Linear",
    yExtrapolatorLeft: "Flat",
    yExtrapolatorRight: "Flat",
    skewType: VolatilitySurfaceType.ATM_ONLY,
    sabr: false,
    sabrBeta: null,
    sabrShift: null,
    capletValuationModel: CapletValuationModel.NORMAL,
    numberOfSwaptionVolatilityNodes: 0,
    numberOfCapletVolatilityNodes: 0,
    numberOfSkewNodes: 0)

    def view2 = new VolatilitySurfaceView(name: "CHF 3M Vols",
    xInterpolator: "DoubleQuadratic",
    xExtrapolatorLeft: "Exception",
    xExtrapolatorRight: "Exponential",
    yInterpolator: "Linear",
    yExtrapolatorLeft: "Interpolator",
    yExtrapolatorRight: "Linear",
    skewType: VolatilitySurfaceType.STRIKE,
    sabr: true,
    sabrBeta: 0.1,
    sabrShift: 0.2,
    capletValuationModel: CapletValuationModel.NORMAL,
    numberOfSwaptionVolatilityNodes: 1,
    numberOfCapletVolatilityNodes: 2,
    numberOfSkewNodes: 3)

    def view3 = new VolatilitySurfaceView(name: "CHF 6M Vols",
    xInterpolator: "LogLinear",
    xExtrapolatorLeft: "LogLinear",
    xExtrapolatorRight: "ProductLinear",
    yInterpolator: "LogNaturalSplineDiscountFactor",
    yExtrapolatorLeft: "QuadraticLeft",
    yExtrapolatorRight: "Flat",
    skewType: VolatilitySurfaceType.MONEYNESS,
    sabr: false,
    sabrBeta: null,
    sabrShift: null,
    capletValuationModel: CapletValuationModel.BLACK,
    numberOfSwaptionVolatilityNodes: 3,
    numberOfCapletVolatilityNodes: 2,
    numberOfSkewNodes: 1)

    def view4 = new VolatilitySurfaceView(name: "AUD 3M Vols",
    xInterpolator: "Linear",
    xExtrapolatorLeft: "Flat",
    xExtrapolatorRight: "Flat",
    yInterpolator: "Linear",
    yExtrapolatorLeft: "Flat",
    yExtrapolatorRight: "Flat",
    skewType: VolatilitySurfaceType.ATM_ONLY,
    sabr: false,
    sabrBeta: null,
    sabrShift: null,
    capletValuationModel: CapletValuationModel.NORMAL,
    numberOfSwaptionVolatilityNodes: 0,
    numberOfCapletVolatilityNodes: 0,
    numberOfSkewNodes: 0)

    when:
    def mapper = new VolatilitySurfaceCsvMapper(null)
    def rows = [mapper.toCsvRow(view1), mapper.toCsvRow(view2), mapper.toCsvRow(view3), mapper.toCsvRow(view4)]
    def csv = new CsvOutputFile(mapper.header(), rows)
    def result = csv.write()

    then:
    def expectedCsv = ByteStreams.toByteArray(getClass().getResourceAsStream("/curvegroup/volatility/csv/surface/VolatilitySurfaces.csv"))
    new String(result.bytes, "UTF-8") == new String(expectedCsv, "UTF-8")
  }

  def "should return correctly mapped csv rows with selected columns"() {
    setup:
    def view = new VolatilitySurfaceView(name: "CHF 3M Vols",
    xInterpolator: "Linear",
    xExtrapolatorLeft: "Flat",
    xExtrapolatorRight: "Flat",
    yInterpolator: "Linear",
    yExtrapolatorLeft: "Flat",
    yExtrapolatorRight: "Flat",
    skewType: VolatilitySurfaceType.STRIKE,
    sabr: true,
    sabrBeta: 0.1,
    sabrShift: 0.2,
    capletValuationModel: CapletValuationModel.NORMAL,
    numberOfSwaptionVolatilityNodes: 1,
    numberOfCapletVolatilityNodes: 2,
    numberOfSkewNodes: 3)

    when:
    def mapper = new VolatilitySurfaceCsvMapper(selectedColumns)
    def row = mapper.toCsvRow(view)
    def csv = new CsvOutputFile(mapper.header(), [row])
    def result = csv.write()

    then:
    result == output

    where:
    selectedColumns                     | output
    ["name"]                            | "Name\nCHF 3M Vols\n"
    ["swapConvention"]                  | "Swap Convention\nCHF-FIXED-1Y-LIBOR-3M\n"
    ["numberOfSwaptionVolatilityNodes"] | "Swaption Volatility Nodes\n1\n"
    ["numberOfCapletVolatilityNodes"]   | "Caplet Volatility Nodes\n2\n"
  }
}
