package com.solum.xplain.core.portfolio.csv.loader

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class InflationTradeCsvLoaderTest extends Specification implements TradeLoaderHelper {
  def commonLoader = new FullSwapTradeCsvLoader()
  def loader = new InflationTradeCsvLoader(commonLoader)

  def "should return correct product type"() {
    expect:
    loader.productTypes() == List.of(CoreProductType.INFLATION)
  }

  def "should parse INFLATION trade"() {
    setup:
    def rows = loadResource("InflationSwapTrades.csv")

    when:
    def parsedRows = rows.stream().map {
      loader.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 3
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "EUR"))
    result.startDate == LocalDate.parse("2014-04-08")
    result.endDate == LocalDate.parse("2019-04-08")
    result.businessDayConvention == "Following"

    def payLeg = result.getPayLeg()
    payLeg.payReceive == PayReceive.PAY
    payLeg.extLegIdentifier == "TRADE001_LEG1"
    payLeg.accrualFrequency == "3M"
    payLeg.paymentFrequency == "3M"
    payLeg.paymentOffsetDays == 0
    payLeg.currency == "EUR"
    payLeg.notional == 16000000
    payLeg.index == "EU-AI-CPI"
    payLeg.inflationLag == "1M"
    payLeg.indexCalculationMethod == "Monthly"

    def receiveLeg = result.getReceiveLeg()
    receiveLeg.payReceive == PayReceive.RECEIVE
    receiveLeg.extLegIdentifier == "TRADE001_LEG2"
    receiveLeg.accrualFrequency == "1Y"
    receiveLeg.paymentFrequency == "1Y"
    receiveLeg.paymentOffsetDays == 0
    receiveLeg.currency == "EUR"
    receiveLeg.notional == 16000000
    receiveLeg.dayCount == "Act/Act ISDA"
    receiveLeg.initialValue == 0.00875

    def payLeg2 = parsedRows[1].getOrNull().toTradeDetails(new TradeInfoDetails(tradeCurrency: "EUR")).getPayLeg()
    payLeg2.index == "EU-AI-CPI"
    payLeg2.inflationLag == "1M"
    payLeg2.indexCalculationMethod == "Interpolated"
    payLeg2.extLegIdentifier == null
  }

  def "should return INFLATION parse errors"() {
    setup:
    def rows = loadResource("InflationSwapTradesInvalid.csv")

    when:
    def parsedRow = loader.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0   | "Error at line number 2. Error: Error parsing field End Date. Must be after Start Date"
    1   | "Error at line number 3. Error: Unsupported value: Inter. Supported values: [Interpolated, InterpolatedJapan, Monthly]"
    2   | "Error at line number 4. Error: Error parsing field Leg1.Ccy. Invalid currency USD for index EU-AI-CPI"
    3   | "Error at line number 5. Error: Unsupported value: ACCRUAL_AND_PAYMENT. Supported values: [PAYMENT_ONLY]"
    4   | "Error at line number 6. Error: Error parsing field Leg1.Inflation.IndexLag. Inflation lag must be positive number or valid Y/M tenor"
    5   | "Error at line number 7. Error: Error parsing field Leg1.Inflation.IndexLag. Inflation lag must be positive number or valid Y/M tenor"
    6   | "Error at line number 8. Error: Error parsing field Leg1.Inflation.IndexLag. Inflation lag must be positive number or valid Y/M tenor"
    7   | "Error at line number 9. Error: Required different LEG IDENTIFIERS. Got: LEG1ID, LEG1ID"
  }
}
