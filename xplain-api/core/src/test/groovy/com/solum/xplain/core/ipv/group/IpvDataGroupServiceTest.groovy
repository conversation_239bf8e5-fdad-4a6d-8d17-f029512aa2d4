package com.solum.xplain.core.ipv.group

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.ipv.group.filter.IpvDataGroupFilter
import com.solum.xplain.core.ipv.group.form.IpvDataGroupForm
import com.solum.xplain.core.ipv.group.value.IpvDataCountService
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCountedView
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.inject.Provider
import org.bson.types.ObjectId
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class IpvDataGroupServiceTest extends Specification {
  static AUTHENTICATION = new TestingAuthenticationToken(user(), null)

  static final EXCLUDE_NAV = "EXCLUDE_NAV"
  static final NAV = "NAV"

  AuthenticationContext userRepository = Mock()
  IpvDataGroupRepository groupRepository = Mock()
  Provider<IpvDataCountService> ipvDataCountService = Mock()
  IpvDataGroupMapper groupMapper = Mock()

  IpvDataCountService mockIpvDataCountService = Mock()

  IpvDataGroupService service = new IpvDataGroupService(userRepository,
  groupRepository,
  groupMapper,
  ipvDataCountService)

  def "should create IPV data group - allow all teams"() {
    setup:
    def form = Mock(IpvDataGroupForm)
    def teamsForm = Mock(AllowedTeamsForm)
    1 * teamsForm.getAllowAll() >> true
    1 * form.getAllowedTeamsForm() >> teamsForm

    1 * userRepository.userEither(AUTHENTICATION) >> right(user("id"))
    1 * groupRepository.insert(form) >> right(entityId("id"))

    expect:
    service.create(AUTHENTICATION, form) == right(entityId("id"))
  }

  def "should get IPV data group counted view"() {
    setup:
    def user = user("id")
    def groupId = ObjectId.get().toHexString()
    def date = BitemporalDate.newOfNow()
    def uncountedView = Mock(IpvDataGroupView, {
      getId() >> groupId
    })

    1 * userRepository.userEither(AUTHENTICATION) >> right(user)
    1 * groupRepository.ipvDataGroupView(user, groupId) >> right(uncountedView)
    2 * ipvDataCountService.get() >> mockIpvDataCountService
    1 * mockIpvDataCountService.getValuesCount(groupId, date, EXCLUDE_NAV) >> 10
    1 * mockIpvDataCountService.getValuesCount(groupId, date, NAV) >> 3
    1 * groupMapper.toCountedView(uncountedView, 10L, 3L) >> new IpvDataGroupCountedView(
      id: groupId,
      valuesCount: 10,
      navValuesCount: 3
      )

    when:
    def result = service.get(AUTHENTICATION, groupId, date)

    then:
    result.isRight()
    with(result.getOrNull()) {
      valuesCount == 10
      navValuesCount == 3
    }
  }

  def "should get IPV data group list with counts"() {
    setup:
    def now = BitemporalDate.newOfNow()
    def user = user("id")
    1 * userRepository.userEither(AUTHENTICATION) >> right(user)
    1 * groupRepository.ipvDataGroupList(user, ScrollRequest.unconstrained(), _ as IpvDataGroupFilter, TableFilter.emptyTableFilter()) >>
      ScrollableEntry.of([
        new IpvDataGroupView(id: "group1", name: "Group 1"),
        new IpvDataGroupView(id: "group2", name: "Group 2")
      ], ScrollRequest.unconstrained())

    2 * ipvDataCountService.get() >> mockIpvDataCountService
    1 * mockIpvDataCountService.getValuesCounts(["group1", "group2"], now, EXCLUDE_NAV) >> [
      "group1": 7
    ]
    1 * mockIpvDataCountService.getValuesCounts(["group1", "group2"], now, NAV) >> [
      "group1": 3,
      "group2": 1
    ]
    2 * groupMapper.toCountedView(_ as IpvDataGroupView, _ as Integer, _ as Integer) >> { args ->
      def view = args[0] as IpvDataGroupView
      def count = args[1] as Integer
      def navCount = args[2] as Integer
      new IpvDataGroupCountedView(
        id: view.id,
        valuesCount: count,
        navValuesCount: navCount
        )
    }

    when:
    def result = service.getAll(
      ScrollRequest.unconstrained(),
      AUTHENTICATION,
      new IpvDataGroupFilter(false, null),
      TableFilter.emptyTableFilter(),
      now
      )

    then:
    result.content.size() == 2
    result.content[0].valuesCount == 7
    result.content[1].valuesCount == 0
  }
}
