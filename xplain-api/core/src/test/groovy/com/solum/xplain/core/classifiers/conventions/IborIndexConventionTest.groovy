package com.solum.xplain.core.classifiers.conventions

import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import spock.lang.Specification

class IborIndexConventionTest extends Specification {

  def "should create KRW-CD-13W convention"() {
    setup:
    def index = IborIndexConvention.newOf(IborIndex.of("KRW-CD-13W"))

    expect:
    with(index) {
      name == "KRW-CD-13W"
      tenor == "3M"
      currency == "KRW"
      dayCount == "Act/365F"
      fixingCalendar == "KRSE"
      fixingDateOffset == -1
    }
  }

  def "should create EUR-EURIBOR-1M convention"() {
    setup:
    def index = IborIndexConvention.newOf(IborIndices.EUR_EURIBOR_1M)

    expect:
    with(index) {
      name == "EUR-EURIBOR-1M"
      tenor == "1M"
      currency == "EUR"
      dayCount == "Act/360"
      fixingCalendar == "EUTA"
      fixingDateOffset == -2
    }
  }
}
