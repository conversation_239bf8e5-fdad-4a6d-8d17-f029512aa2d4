package com.solum.xplain.core.sockets.constants

import static com.solum.xplain.core.sockets.WrappedEmitter.defaultEmitter
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.forGroup
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.global
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.ipvDataResolutionStatus
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.user
import static com.solum.xplain.core.sockets.constants.CoreSocketEvents.valuationProgress
import static com.solum.xplain.core.sockets.constants.CoreSocketReferences.forIpvResolutionStatus
import static com.solum.xplain.core.sockets.constants.CoreSocketReferences.forUser
import static com.solum.xplain.core.sockets.constants.CoreSocketReferences.forValuation

import spock.lang.Specification

class CoreSocketEventsTest extends Specification {

  def "should construct correct user event"() {
    when:
    def result = user("id", "type", "data")

    then:
    result != null
    result.shouldSend(defaultEmitter(forUser("id")))
    !result.shouldSend(defaultEmitter(forUser("another")))
  }

  def "should construct correct group event"() {
    when:
    def result = forGroup(["id"], "type", "data")

    then:
    result != null
    result.shouldSend(defaultEmitter(forUser("id")))
    !result.shouldSend(defaultEmitter(forUser("another")))
  }

  def "should construct correct global event"() {
    when:
    def result = global("type", "data")

    then:
    result != null
    result.shouldSend(defaultEmitter(forUser("id")))
    result.shouldSend(defaultEmitter(forUser("another")))
  }

  def "should construct correct ipvDataResolutionStatus event"() {
    when:
    def result = ipvDataResolutionStatus("id", "type")

    then:
    result != null
    result.shouldSend(defaultEmitter(forIpvResolutionStatus("id")))
    result.shouldSend(defaultEmitter(forIpvResolutionStatus("another")))
  }

  def "should construct correct valuation event"() {
    when:
    def result = valuationProgress("id", null)

    then:
    result != null
    result.shouldSend(defaultEmitter(forValuation("id")))
    result.shouldSend(defaultEmitter(forValuation("another")))
  }
}
