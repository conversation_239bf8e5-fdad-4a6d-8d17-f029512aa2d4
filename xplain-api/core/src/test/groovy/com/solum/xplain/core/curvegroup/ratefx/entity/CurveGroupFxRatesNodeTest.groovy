package com.solum.xplain.core.curvegroup.ratefx.entity


import spock.lang.Specification

class CurveGroupFxRatesNodeTest extends Specification {

  def "should return correct FX Rate node key and name"() {
    setup:
    def instruments = new CurveGroupFxRatesNodeBuilder()
      .build()
      .allInstruments()

    expect:
    instruments.size() == 1
    instruments[0].key == "EUR/USD"
    instruments[0].mdkName == "EUR/USD SPOT"
  }
}
