package com.solum.xplain.core.curvegroup.volatility.entity

import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve

import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvegroup.volatility.classifier.SkewType
import spock.lang.Specification

class VolatilitySurfaceSkewTest extends Specification {


  def "should return correct instrument"() {
    setup:
    def node = Mock(VolatilitySurfaceNode)
    def skew = new VolatilitySurfaceSkewBuilder().build()

    def i = ofIrCurve("EUR", "C", CoreInstrumentType.FIXED_IBOR_SWAP, "1Y", "1Y", "K", "name")
    1 * node.skewInstrument("EUR 3M Vols", "I", "EUR", skew.skewValue, SkewType.STRIKE, "EUR 3M") >> i

    when:
    def result = skew.allInstruments("EUR 3M Vols", "I", "EUR", SkewType.STRIKE, "EUR 3M", [node])

    then:
    result.size() == 1
    result[0] == i
  }
}
