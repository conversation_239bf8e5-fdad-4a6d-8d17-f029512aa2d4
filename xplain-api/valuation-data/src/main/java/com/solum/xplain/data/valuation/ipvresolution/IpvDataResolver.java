package com.solum.xplain.data.valuation.ipvresolution;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.merge;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.Fields.field;
import static org.springframework.data.mongodb.core.aggregation.MergeOperation.WhenDocumentsMatch.replaceDocument;

import com.google.common.collect.Iterables;
import com.solum.xplain.core.common.daterange.DateRange;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.portfolio.event.PortfolioItemsStatesUpdated;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.data.valuation.ipv.entity.BaseIpvDataValue;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue;
import com.solum.xplain.data.valuation.ipvresolution.IpvDataGroupDateKey.IpvDataGroupDate;
import com.solum.xplain.data.valuation.nav.entity.CompanyLegalEntityNav;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;
import org.bson.Document;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

@Service
@NullMarked
public class IpvDataResolver {

  private static final Logger LOGGER = getLogger(IpvDataResolver.class);

  private final IpvDataResolutionRepository ipvDataResolutionRepository;
  private final PortfolioItemRepository portfolioItemRepository;
  private final CompanyLegalEntityRepository companyLegalEntityRepository;
  private final MongoOperations mongoOperations;

  public IpvDataResolver(
      IpvDataResolutionRepository ipvDataResolutionRepository,
      PortfolioItemRepository portfolioItemRepository,
      CompanyLegalEntityRepository companyLegalEntityRepository,
      MongoOperations mongoOperations) {
    this.ipvDataResolutionRepository = ipvDataResolutionRepository;
    this.portfolioItemRepository = portfolioItemRepository;
    this.companyLegalEntityRepository = companyLegalEntityRepository;
    this.mongoOperations = mongoOperations;
  }

  public void resolveIpvData() {
    resolveIpvData(ipvDataResolutionRepository.getNonResolvedKeys());
  }

  public void resolveIpvData(String groupId) {
    resolveIpvData(ipvDataResolutionRepository.getNonResolvedKeys(groupId));
  }

  public void resolveCompanyLegalEntityNavData(String groupId) {
    resolveCompanyLegalEntityNavData(Optional.of(groupId));
  }

  protected void resolveCompanyLegalEntityNavData() {
    resolveCompanyLegalEntityNavData(Optional.empty());
  }

  /**
   * Resolves companyLegalEntityNav records by linking them to matching companyLegalEntity entries.
   * Sets resolved=true and updates companyId and legalEntityId. Filters by groupId if present.
   */
  protected void resolveCompanyLegalEntityNavData(Optional<String> groupId) {
    final String ENTITY = "entity";

    Criteria matchCriteria = Criteria.where(BaseIpvDataValue.Fields.resolved).ne(true);
    if (groupId.isPresent()) {
      matchCriteria = matchCriteria.and(BaseIpvDataValue.Fields.groupId).is(groupId.get());
    }

    var aggregation =
        Aggregation.newAggregation(
            // Match records based on criteria (resolved and optionally groupId)
            match(matchCriteria),

            // Lookup matching entity from companyLegalEntity
            lookup(
                CompanyLegalEntity.COMPANY_LEGAL_ENTITY_COLLECTION,
                BaseIpvDataValue.Fields.key,
                CompanyLegalEntity.Fields.vdkPrefix,
                ENTITY),
            unwind(ENTITY),
            match(Criteria.where(joinPaths(ENTITY, CompanyLegalEntity.Fields.archived)).is(false)),

            // Add resolved=true and company/entity ids from the matched entity
            addFields()
                .addField(BaseIpvDataValue.Fields.resolved)
                .withValue(true)
                .addField(CompanyLegalEntityNav.Fields.companyId)
                .withValueOf(field(joinPaths(ENTITY, CompanyLegalEntityNav.Fields.companyId)))
                .addField(CompanyLegalEntityNav.Fields.legalEntityId)
                .withValueOf(field(joinPaths(ENTITY, UNDERSCORE_ID)))
                .addField(ENTITY)
                .withValue(Aggregation.REMOVE)
                .build(),

            // Merge back into entity nav collection
            merge()
                .intoCollection(CompanyLegalEntityNav.COLLECTION_NAME)
                .whenMatched(replaceDocument())
                .build());

    mongoOperations.aggregate(
        aggregation.withOptions(
            Aggregation.newAggregationOptions().allowDiskUse(true).skipOutput().build()),
        CompanyLegalEntityNav.class,
        Document.class);
  }

  public void resolveIpvData(Stream<IpvDataGroupDateKey> keys) {
    keys.collect(
            groupingBy(
                IpvDataGroupDateKey::groupDate, mapping(IpvDataGroupDateKey::getKey, toSet())))
        .forEach(this::resolveIpvData);
  }

  private void resolveIpvData(IpvDataGroupDate groupDate, Set<String> groupDateKeys) {
    var groupId = groupDate.getGroupId();
    var date = groupDate.getDate();
    var stateDate = BitemporalDate.newOf(date);
    var resolvedKeys = portfolioItemRepository.resolvedValuationDataKeys(stateDate, groupDateKeys);

    updateIpvDataAsResolved(groupId, resolvedKeys, date);

    updateIpvDataAsNonResolved(groupId, date);
  }

  private void updateIpvDataAsResolved(String groupId, Set<String> keys, LocalDate date) {
    if (!Iterables.isEmpty(keys)) {
      long updateCount =
          ipvDataResolutionRepository.updateValuesResolved(groupId, keys, date, true);
      log(updateCount, true);
    }
  }

  private void updateIpvDataAsNonResolved(String groupId, LocalDate date) {
    long modifiedCount = ipvDataResolutionRepository.updateValuesAsUnresolved(groupId, date);
    log(modifiedCount, false);
  }

  public void updateIpvGroupDataNonResolved(String ipvGroupId) {
    long updateCount = ipvDataResolutionRepository.updateValuesNonResolved(ipvGroupId);
    log(updateCount, null);
  }

  public void updateArchivedPortfolioIpvDataNonResolved(List<String> portfolioIds) {
    var portfolioVdks =
        portfolioItemRepository.allValuationDataKeys(portfolioIds, BitemporalDate.newOfNow());
    long updateCount = ipvDataResolutionRepository.updateAllValuesUnresolved(portfolioVdks);
    log(updateCount, false);
  }

  public void updatePortfolioItemsIpvDataNonResolved(
      Set<PortfolioItemsStatesUpdated.VDKUpdate> changes) {
    var valuationKeysByDateRange =
        changes.stream()
            .collect(
                groupingBy(
                    vdkUpdate -> DateRange.newOf(vdkUpdate.fromDate(), vdkUpdate.toDate()),
                    mapping(PortfolioItemsStatesUpdated.VDKUpdate::valuationDataKey, toSet())));

    for (var entry : valuationKeysByDateRange.entrySet()) {
      var dateRange = entry.getKey();
      var valuationKeys = entry.getValue();
      updateIpvDataNonResolved(dateRange.getStartDate(), dateRange.getEndDate(), valuationKeys);
    }
  }

  public void updateCompanyLegalEntityNavDataNonResolved(String companyId, String entityId) {
    List<CompanyLegalEntity> archivedEntities =
        companyLegalEntityRepository.archivedLegalEntities(companyId, entityId);
    List<String> vdkPrefixes =
        archivedEntities.stream().map(CompanyLegalEntity::getVdkPrefix).toList();
    long updateCount =
        ipvDataResolutionRepository.updateCompanyLegalEntityNavValuesAsNonResolved(vdkPrefixes);
    log(updateCount, null);
  }

  private void updateIpvDataNonResolved(LocalDate from, LocalDate to, Set<String> valuationKeys) {
    long updateCount =
        ipvDataResolutionRepository.updateAllValuesNonResolved(valuationKeys, from, to);
    log(updateCount, null);
  }

  private void log(long updatedCount, @Nullable Boolean resolved) {
    LOGGER.debug(
        "Marked {} {} as resolved = {}",
        updatedCount,
        IpvDataValue.class.getSimpleName(),
        resolved);
  }
}
