package com.solum.xplain.data.valuation.ipv;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VALUATION_DATA;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_DATA;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.RequireLock.Type.PATH_VARIABLE;
import static com.solum.xplain.core.lock.XplainLock.IPV_DATA_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.ArchiveForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.data.valuation.ipv.csv.IpvDataExportService;
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueFilter;
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueForm;
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueUpdateForm;
import com.solum.xplain.data.valuation.ipv.value.IpvDataNavValueView;
import com.solum.xplain.data.valuation.ipv.value.IpvDataProviderValueView;
import com.solum.xplain.data.valuation.ipvresolution.IpvDataResolutionJobService;
import com.solum.xplain.data.valuation.ipvresolution.value.IpvDataResolutionStatus;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/ipv-data-groups/{groupId}/values")
public class IpvDataController {

  private final IpvDataService ipvDataService;
  private final IpvDataExportService ipvDataExportService;
  private final IpvDataResolutionJobService ipvDataResolutionJobService;

  public IpvDataController(
      IpvDataService ipvDataService,
      IpvDataExportService ipvDataExportService,
      IpvDataResolutionJobService ipvDataResolutionJobService) {
    this.ipvDataService = ipvDataService;
    this.ipvDataExportService = ipvDataExportService;
    this.ipvDataResolutionJobService = ipvDataResolutionJobService;
  }

  @Operation(summary = "Creates a new value")
  @PostMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA)
  @RequireLock(type = PATH_VARIABLE, name = "groupId", prefix = IPV_DATA_LOCK_ID)
  public ResponseEntity<EntityId> createValue(
      @PathVariable("groupId") String groupId,
      @Valid @RequestBody IpvDataProviderValueForm form,
      Authentication auth) {
    return eitherErrorItemResponse(ipvDataService.createValue(auth, groupId, form));
  }

  @Operation(summary = "Gets values")
  @CommonErrors
  @ScrolledFiltered
  @Sorted
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ScrollableEntry<IpvDataProviderValueView>> getValues(
      @PathVariable("groupId") String groupId,
      @Valid IpvDataProviderValueFilter valuesFilter,
      TableFilter tableFilter,
      @SortDefault(
              sort = {
                IpvDataProviderValueView.Fields.key,
                IpvDataProviderValueView.Fields.provider
              })
          ScrollRequest scrollRequest,
      Authentication auth) {
    return eitherErrorItemResponse(
        ipvDataService.getValues(auth, groupId, valuesFilter, tableFilter, scrollRequest));
  }

  @Operation(summary = "Gets nav values")
  @GetMapping("/nav")
  @CommonErrors
  @ScrolledFiltered
  @Sorted
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ScrollableEntry<IpvDataNavValueView>> getNavValues(
      @PathVariable("groupId") String groupId,
      @Valid IpvDataProviderValueFilter valuesFilter,
      TableFilter tableFilter,
      @SortDefault(sort = {IpvDataNavValueView.Fields.key}) ScrollRequest scrollRequest,
      Authentication auth) {
    return eitherErrorItemResponse(
        ipvDataService.getNavValues(auth, groupId, valuesFilter, tableFilter, scrollRequest));
  }

  @Operation(summary = "Gets value")
  @GetMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<IpvDataProviderValueView> getValue(
      @PathVariable("groupId") String groupId,
      @PathVariable("id") String id,
      Authentication auth,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType) {
    var stateDate = BitemporalDate.newOfNow();
    return eitherErrorItemResponse(ipvDataService.getValue(auth, groupId, id, stateDate, dataType));
  }

  @Operation(summary = "Updates value")
  @PutMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA)
  @RequireLock(type = PATH_VARIABLE, name = "groupId", prefix = IPV_DATA_LOCK_ID)
  public ResponseEntity<EntityId> updateValue(
      @PathVariable("groupId") String groupId,
      @PathVariable("id") String id,
      @Valid @RequestBody IpvDataProviderValueUpdateForm form,
      Authentication auth,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        ipvDataService.updateValue(auth, groupId, id, form, dataType, stateDate));
  }

  @Operation(summary = "Archives value")
  @PutMapping("/{id}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA)
  @RequireLock(type = PATH_VARIABLE, name = "groupId", prefix = IPV_DATA_LOCK_ID)
  public ResponseEntity<EntityId> archiveValue(
      @PathVariable("groupId") String groupId,
      @PathVariable("id") String id,
      @Valid @RequestBody ArchiveForm form,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType,
      @RequestParam LocalDate stateDate,
      Authentication authentication) {
    return eitherErrorItemResponse(
        ipvDataService.archiveValue(authentication, groupId, id, form, dataType, stateDate));
  }

  @Operation(summary = "Uploads values CSV")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA)
  @RequireLock(type = PATH_VARIABLE, name = "groupId", prefix = IPV_DATA_LOCK_ID)
  public ResponseEntity<EntityId> uploadValues(
      @PathVariable("groupId") String groupId,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file,
      Authentication auth)
      throws IOException {
    return eitherErrorItemsResponse(
        ipvDataService.upload(auth, groupId, file.getBytes(), importOptions, dataType));
  }

  @Operation(summary = "Validate values CSV")
  @PostMapping(value = "/upload/validate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VALUATION_DATA)
  @RequireLock(type = PATH_VARIABLE, name = "groupId", prefix = IPV_DATA_LOCK_ID)
  public ResponseEntity<ValidationResponse> validateValues(
      @PathVariable("groupId") String groupId,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType,
      @RequestPart MultipartFile file,
      Authentication auth)
      throws IOException {
    return eitherErrorItemsResponse(
        ipvDataService.validateFile(auth, groupId, parsingMode, file.getBytes(), dataType));
  }

  @Operation(summary = "Gets values CSV")
  @CommonErrors
  @GetMapping("/csv")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ByteArrayResource> getValuesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam(required = false, defaultValue = "ALL") IpvDataType dataType,
      @Valid IpvDataProviderValueFilter filter,
      @RequestParam LocalDate stateDate,
      Authentication auth) {
    return eitherErrorItemFileResponse(
        ipvDataExportService.getProviderValuesCsv(auth, groupId, filter, stateDate, dataType));
  }

  @Operation(summary = "Get Valuation Data resolution process status")
  @CommonErrors
  @GetMapping("/resolution-process")
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<IpvDataResolutionStatus> resolutionProcessStatus(
      @PathVariable String groupId) {
    return eitherErrorItemResponse(ipvDataResolutionJobService.getResolutionProcessStatus(groupId));
  }
}
