package com.solum.xplain.data.valuation.nav.entity;

import com.solum.xplain.data.valuation.ipv.entity.BaseIpvDataValue;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.Nullable;

@Data
@Document(collection = CompanyLegalEntityNav.COLLECTION_NAME)
@FieldNameConstants
public class CompanyLegalEntityNav extends BaseIpvDataValue<NavVersion> {
  public static final String COLLECTION_NAME = "companyLegalEntityNav";

  @Nullable private String companyId; // Populated after resolution
  @Nullable private String legalEntityId; // Populated after resolution
}
