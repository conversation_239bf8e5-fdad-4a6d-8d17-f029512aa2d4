package com.solum.xplain.data.valuation.ipvresolution;

import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.Iterables;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.data.valuation.ipv.entity.BaseIpvDataValue;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue;
import com.solum.xplain.data.valuation.nav.entity.CompanyLegalEntityNav;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationUpdate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public class IpvDataResolutionRepository {

  private static final int BATCH_SIZE = 10000;

  private final MongoOperations mongoOperations;

  public IpvDataResolutionRepository(MongoOperations mongoOperations) {
    this.mongoOperations = mongoOperations;
  }

  public Stream<IpvDataGroupDateKey> getNonResolvedKeys(@NonNull String groupId) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.archivedAt)
            .isNull()
            .and(BaseIpvDataValue.Fields.resolved)
            .isNull();
    return nonResolvedKeys(criteria);
  }

  public Stream<IpvDataGroupDateKey> getNonResolvedKeys() {
    var criteria =
        where(DataValuesHolder.Fields.archivedAt)
            .isNull()
            .and(BaseIpvDataValue.Fields.resolved)
            .isNull();
    return nonResolvedKeys(criteria);
  }

  private Stream<IpvDataGroupDateKey> nonResolvedKeys(Criteria criteria) {
    return mongoOperations
        .aggregateAndReturn(IpvDataGroupDateKey.class)
        .by(Aggregation.newAggregation(IpvDataValue.class, Aggregation.match(criteria)))
        .stream();
  }

  public long updateValuesNonResolved(String groupId) {
    return updateValuesResolved(where(BaseIpvDataValue.Fields.groupId).is(groupId), null);
  }

  public long updateAllValuesUnresolved(Set<String> keys) {
    return updateValuesResolved(
        keys,
        (List<String> keysPartition) -> where(BaseIpvDataValue.Fields.key).in(keysPartition),
        false);
  }

  public long updateValuesResolved(
      String groupId, Set<String> keys, LocalDate date, boolean resolved) {
    return updateValuesResolved(
        keys,
        (List<String> keysPartition) ->
            where(BaseIpvDataValue.Fields.groupId)
                .is(groupId)
                .and(DataValuesHolder.Fields.date)
                .is(date)
                .and(BaseIpvDataValue.Fields.key)
                .in(keysPartition),
        resolved);
  }

  public long updateValuesAsUnresolved(String groupId, LocalDate date) {
    var query =
        new Query(
            where(BaseIpvDataValue.Fields.groupId)
                .is(groupId)
                .and(DataValuesHolder.Fields.date)
                .is(date)
                .and(BaseIpvDataValue.Fields.resolved)
                .isNull());
    var update = AggregationUpdate.update().set(BaseIpvDataValue.Fields.resolved).toValue(false);
    return mongoOperations.updateMulti(query, update, IpvDataValue.class).getModifiedCount();
  }

  public long updateCompanyLegalEntityNavValuesAsNonResolved(List<String> keys) {
    var query = new Query(where(BaseIpvDataValue.Fields.key).in(keys));
    var update = AggregationUpdate.update().set(BaseIpvDataValue.Fields.resolved).toValue(null);
    return mongoOperations
        .updateMulti(query, update, CompanyLegalEntityNav.class)
        .getModifiedCount();
  }

  public long updateAllValuesNonResolved(Set<String> keys, LocalDate fromDate, LocalDate toDate) {
    return updateValuesResolved(
        keys,
        (List<String> keysPartition) ->
            where(DataValuesHolder.Fields.date)
                .gte(fromDate)
                .lte(toDate)
                .and(BaseIpvDataValue.Fields.key)
                .in(keysPartition),
        null);
  }

  private long updateValuesResolved(
      Set<String> keys, Function<List<String>, Criteria> criteriaFn, Boolean resolved) {
    final var count = new AtomicLong(0L);
    Iterables.partition(keys, BATCH_SIZE)
        .forEach(
            kk -> {
              var criteria = criteriaFn.apply(kk);
              count.addAndGet(updateValuesResolved(criteria, resolved));
            });
    return count.longValue();
  }

  private long updateValuesResolved(Criteria criteria, Boolean resolved) {
    return mongoOperations
        .updateMulti(
            Query.query(criteria),
            Update.update(BaseIpvDataValue.Fields.resolved, resolved),
            IpvDataValue.class)
        .getModifiedCount();
  }
}
