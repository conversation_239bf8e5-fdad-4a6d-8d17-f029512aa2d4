package com.solum.xplain.data.valuation.ipv.csv;

import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.detailedDescription;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.datavalue.csv.DataValueImportItemsValidatorUtils.validateImportItems;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ImportOverview;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.NewMinorVersionForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.datavalue.csv.DataUpdateSummary;
import com.solum.xplain.core.datavalue.csv.DataValueFileImporter;
import com.solum.xplain.core.datavalue.csv.DataValueFileImporterProvider;
import com.solum.xplain.core.datavalue.csv.DataValueImportItems;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.data.valuation.ipv.IpvDataMapper;
import com.solum.xplain.data.valuation.ipv.IpvDataRepository;
import com.solum.xplain.data.valuation.ipv.IpvDataType;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValueVersion;
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueForm;
import com.solum.xplain.data.valuation.nav.entity.CompanyLegalEntityNav;
import com.solum.xplain.data.valuation.nav.entity.NavVersion;
import com.solum.xplain.data.valuation.nav.repository.CompanyLegalEntityNavRepository;
import io.atlassian.fugue.Either;
import jakarta.inject.Provider;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class IpvDataUploadService {

  private static final String OBJECT_NAME = "Valuation data values";

  private final AuditEntryService auditEntryService;
  private final IpvDataRepository ipvDataRepository;
  private final CompanyLegalEntityNavRepository companyLegalEntityNavRepository;
  private final IpvDataMapper ipvDataMapper;
  private final Provider<IpvDataCsvLoader> ipvDataCsvLoaderProvider;
  private final DataValueFileImporterProvider importerProvider;

  public IpvDataUploadService(
      AuditEntryService auditEntryService,
      IpvDataRepository ipvDataRepository,
      IpvDataMapper ipvDataMapper,
      Provider<IpvDataCsvLoader> ipvDataCsvLoaderProvider,
      DataValueFileImporterProvider importerProvider,
      CompanyLegalEntityNavRepository companyLegalEntityNavRepository) {
    this.auditEntryService = auditEntryService;
    this.ipvDataRepository = ipvDataRepository;
    this.ipvDataMapper = ipvDataMapper;
    this.ipvDataCsvLoaderProvider = ipvDataCsvLoaderProvider;
    this.importerProvider = importerProvider;
    this.companyLegalEntityNavRepository = companyLegalEntityNavRepository;
  }

  public Either<List<ErrorItem>, ValidationResponse> validateFile(
      String groupId, ParsingMode parsingMode, byte[] csvBytes, IpvDataType dataType) {
    return ipvDataCsvLoaderProvider
        .get()
        .parseIpvCSV(csvBytes, parsingMode, dataType)
        .map(forms -> errors(groupId, forms.getParsedLines(), dataType));
  }

  private ValidationResponse errors(
      String groupId, List<IpvDataProviderValueForm> forms, IpvDataType dataType) {
    var errors =
        CollectionUtils.parallelToGroupMap(forms, IpvDataProviderValueForm::getDate)
            .entrySet()
            .stream()
            .map(e -> dailyErrors(groupId, e.getKey(), e.getValue(), dataType))
            .flatMap(Collection::stream)
            .toList();
    return ValidationResponse.newOf(errors);
  }

  public Either<List<ErrorItem>, EntityId> upload(
      String groupId, byte[] csvBytes, ImportOptions importOptions, IpvDataType dataType) {
    return validImportOptions(groupId, importOptions)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(options -> ipvDataCsvLoaderProvider.get())
        .flatMap(p -> p.parse(csvBytes, importOptions.parsingMode()))
        .map(parserResult -> process(groupId, importOptions, parserResult, dataType));
  }

  private Either<ErrorItem, ImportOptions> validImportOptions(
      String groupId, ImportOptions options) {
    if (ipvDataRepository.hasChangesAfter(groupId, options.getValidationTimestamp())) {
      return Either.left(stateChanged(OBJECT_NAME));
    }
    return Either.right(options);
  }

  private EntityId process(
      String groupId,
      ImportOptions importOptions,
      CsvParserResult<IpvDataProviderValueForm> parserResult,
      IpvDataType dataType) {
    var forms = parserResult.getParsedLines();
    var importOverview =
        ImportOverview.builder().identifier(OBJECT_NAME).warnings(parserResult.logItems()).build();

    var importOverviews =
        CollectionUtils.parallelToGroupMap(forms, IpvDataProviderValueForm::getDate)
            .entrySet()
            .stream()
            .map(
                entry ->
                    processDate(groupId, importOptions, entry.getKey(), entry.getValue(), dataType))
            .reduce(importOverview, ImportOverview::sum);
    logImport(importOverviews);
    return EntityId.entityId(groupId);
  }

  private ImportOverview processDate(
      String groupId,
      ImportOptions importOptions,
      LocalDate date,
      List<IpvDataProviderValueForm> dateForms,
      IpvDataType dataType) {
    String navLevel = ipvDataRepository.getGlobalNavLevelSettings(BitemporalDate.newOf(date));

    if (dataType == IpvDataType.NAV && navLevel.equals("ENTITY_LEVEL")) {
      return processEntityLevelNavData(groupId, date, dateForms, importOptions);
    } else {
      return processData(groupId, date, dateForms, importOptions, dataType);
    }
  }

  private ImportOverview processEntityLevelNavData(
      String groupId,
      LocalDate date,
      List<IpvDataProviderValueForm> dateForms,
      ImportOptions importOptions) {
    var importItems =
        buildEntityLevelNavItems(groupId, date, dateForms, importOptions.getComment());
    var result = navImporter().importItems(importOptions.getDuplicateAction(), importItems);
    return buildImportOverview(result);
  }

  private ImportOverview processData(
      String groupId,
      LocalDate date,
      List<IpvDataProviderValueForm> dateForms,
      ImportOptions importOptions,
      IpvDataType dataType) {
    var importItems = buildItems(groupId, date, dateForms, importOptions.getComment(), dataType);
    var result = importer().importItems(importOptions.getDuplicateAction(), importItems);
    return buildImportOverview(result);
  }

  private ImportOverview buildImportOverview(DataUpdateSummary result) {
    return ImportOverview.builder()
        .identifier(OBJECT_NAME)
        .insertedCount(result.getNewEntriesCount())
        .updatedCount(result.getReplacedEntriesCount())
        .archivedCount(result.getArchivedEntriesCount())
        .build();
  }

  private DataValueFileImporter<IpvDataValueVersion, IpvDataProviderValueKey, IpvDataValue>
      importer() {
    return importerProvider.importer(ipvDataRepository);
  }

  private DataValueFileImporter<NavVersion, IpvDataProviderValueKey, CompanyLegalEntityNav>
      navImporter() {
    return importerProvider.importer(companyLegalEntityNavRepository);
  }

  private List<ErrorItem> dailyErrors(
      String groupId, LocalDate date, List<IpvDataProviderValueForm> forms, IpvDataType dataType) {
    String navLevel = ipvDataRepository.getGlobalNavLevelSettings(BitemporalDate.newOf(date));
    var data =
        (dataType == IpvDataType.NAV && navLevel.equals("ENTITY_LEVEL"))
            ? buildEntityLevelNavItems(groupId, date, forms, null)
            : buildItems(groupId, date, forms, null, dataType);

    return validateImportItems(data, date);
  }

  private DataValueImportItems<IpvDataValueVersion, IpvDataProviderValueKey, IpvDataValue>
      buildItems(
          String groupId,
          LocalDate date,
          List<IpvDataProviderValueForm> forms,
          String importComment,
          IpvDataType dataType) {
    var entitiesByKey = entitiesByKey(groupId, new BitemporalDate(date), dataType);
    var importItemsByKey = importItemsByKey(groupId, forms, importComment);
    return DataValueImportItems
        .<IpvDataValueVersion, IpvDataProviderValueKey, IpvDataValue>builder()
        .importItemsByKeys(importItemsByKey)
        .existingItemsByKey(entitiesByKey)
        .importComment(importComment)
        .build();
  }

  private DataValueImportItems<NavVersion, IpvDataProviderValueKey, CompanyLegalEntityNav>
      buildEntityLevelNavItems(
          String groupId,
          LocalDate date,
          List<IpvDataProviderValueForm> forms,
          String importComment) {
    var entitiesByKey = entityLevelNavEntitiesByKey(groupId, new BitemporalDate(date));
    var importItemsByKey = importEntityLevelNavItemsByKey(groupId, forms, importComment);
    return DataValueImportItems
        .<NavVersion, IpvDataProviderValueKey, CompanyLegalEntityNav>builder()
        .importItemsByKeys(importItemsByKey)
        .existingItemsByKey(entitiesByKey)
        .importComment(importComment)
        .build();
  }

  private Map<IpvDataProviderValueKey, IpvDataValue> importItemsByKey(
      String groupId, List<IpvDataProviderValueForm> forms, String importComment) {
    return forms.stream()
        .map(f -> withVersionForm(f, importComment))
        .map(f -> ipvDataMapper.newProviderValue(groupId, f))
        .collect(Collectors.toMap(IpvDataProviderValueKey::from, Function.identity()));
  }

  private Map<IpvDataProviderValueKey, CompanyLegalEntityNav> importEntityLevelNavItemsByKey(
      String groupId, List<IpvDataProviderValueForm> forms, String importComment) {
    return forms.stream()
        .map(f -> withVersionForm(f, importComment))
        .map(f -> ipvDataMapper.newEntityLevelNavValue(groupId, f))
        .collect(
            Collectors.toMap(IpvDataProviderValueKey::fromEntityLevelNav, Function.identity()));
  }

  private IpvDataProviderValueForm withVersionForm(
      IpvDataProviderValueForm form, String importComment) {
    form.setVersionForm(NewMinorVersionForm.of(importComment));
    return form;
  }

  private Map<IpvDataProviderValueKey, IpvDataValue> entitiesByKey(
      String groupId, BitemporalDate date, IpvDataType dataType) {
    var entities = ipvDataRepository.valuesStream(groupId, date, dataType).toList();
    return CollectionUtils.parallelToMap(entities, IpvDataProviderValueKey::from);
  }

  private Map<IpvDataProviderValueKey, CompanyLegalEntityNav> entityLevelNavEntitiesByKey(
      String groupId, BitemporalDate date) {
    var entities =
        companyLegalEntityNavRepository.entityLevelNavValuesStream(groupId, date).toList();
    return CollectionUtils.parallelToMap(entities, IpvDataProviderValueKey::fromEntityLevelNav);
  }

  private void logImport(ImportOverview importOverview) {
    var description = detailedDescription(importOverview);
    auditEntryService.newEntryWithLogs(
        AuditEntry.of(IpvDataValue.class.getSimpleName(), description),
        importOverview.getWarnings());
  }
}
