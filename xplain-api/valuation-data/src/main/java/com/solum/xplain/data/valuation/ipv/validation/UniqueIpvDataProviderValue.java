package com.solum.xplain.data.valuation.ipv.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueIpvDataProviderValueValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface UniqueIpvDataProviderValue {

  String MESSAGE_KEY =
      "com.solum.xplain.api.ipv.data.validation.UniqueIpvDataProviderValue.message";

  String message() default "{" + MESSAGE_KEY + "}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
