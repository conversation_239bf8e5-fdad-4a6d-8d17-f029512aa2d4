package com.solum.xplain.data.valuation.ipv.value;

import static java.util.function.Predicate.not;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.viewconfig.provider.AbstractColumnDefinitionGroupBuilder;
import com.solum.xplain.core.viewconfig.provider.AbstractColumnsBuilder;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.provider.ViewConfigurationProvider;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupView;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldType;
import com.solum.xplain.core.viewconfig.value.PaletteView;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IpvDataViewConfigurationProvider implements ViewConfigurationProvider {
  private static final Set<String> IDENTIFIER_FIELDS =
      Set.of(
          IpvDataProviderValueView.Fields.key,
          IpvDataProviderValueView.Fields.provider,
          IpvDataProviderValueView.Fields.date,
          IpvDataProviderValueView.Fields.tradeCcy);
  private static final Set<String> NAV_FIELDS =
      Set.of(IpvDataProviderValueView.Fields.navValue, IpvDataProviderValueView.Fields.navCurrency);
  private static final Predicate<String> IS_IDENTIFIER = IDENTIFIER_FIELDS::contains;
  private static final Predicate<String> IS_NAV_FIELD = NAV_FIELDS::contains;
  private static final Predicate<String> IS_VALUE = not(IS_IDENTIFIER);
  private static final Predicate<String> ALWAYS_SHOWN = field -> false;

  private static final int DEFAULT_PRECISION = 2;
  private final PaletteService paletteService;

  @Override
  public <T> List<ViewConfigurationView<T>> provideViewConfigurations(
      Authentication user, Class<T> viewClass) {
    ImmutableList.Builder<ViewConfigurationView<T>> viewConfigurationViews =
        ImmutableList.builder();

    if (viewClass == IpvDataProviderValueView.class) {
      addDefaultViewConfiguration(viewConfigurationViews, (Class) IpvDataProviderValueView.class);
    } else if (viewClass == IpvDataNavValueView.class) {
      addDefaultViewConfiguration(viewConfigurationViews, (Class) IpvDataNavValueView.class);
    }

    return viewConfigurationViews.build();
  }

  private <V> void addDefaultViewConfiguration(
      ImmutableList.Builder<ViewConfigurationView<V>> viewConfigurationViews,
      Class<V> specificViewClass) {
    paletteService
        .findPaletteView(specificViewClass)
        .map(this::createDefaultViewConfiguration)
        .ifPresent(viewConfigurationViews::add);
  }

  private <T> ViewConfigurationView<T> createDefaultViewConfiguration(PaletteView<T> palette) {
    return new ViewConfigurationView<>(
        "670fde3ab1ea694feaca6be7",
        palette.scope(),
        DEFAULT_VIEW_NAME,
        false,
        createColumns(palette),
        false,
        null,
        null);
  }

  private <T> List<ColumnDefinitionGroupView> createColumns(PaletteView<T> palette) {
    IpvDataViewColumnsBuilder<T> builder = new IpvDataViewColumnsBuilder<>(palette);
    return builder.build();
  }

  private static class IpvDataViewColumnsBuilder<T> extends AbstractColumnsBuilder {
    IpvDataViewColumnsBuilder(PaletteView<T> palette) {
      super(
          List.of(
              new ColumnDefinitionGroupBuilder(IS_IDENTIFIER, "Details", ALWAYS_SHOWN),
              new ColumnDefinitionGroupBuilder(IS_NAV_FIELD, "NAV", ALWAYS_SHOWN),
              new ColumnDefinitionGroupBuilder(IS_VALUE, "Value", ALWAYS_SHOWN)));
      palette.fieldDefinitions().forEach(this::addField);
    }
  }

  private static class ColumnDefinitionGroupBuilder extends AbstractColumnDefinitionGroupBuilder {
    ColumnDefinitionGroupBuilder(
        Predicate<String> matcher, String label, Predicate<String> hidden) {
      super(matcher, label, hidden);
    }

    /**
     * Simple implementation to determine the default precision for a field.
     *
     * <p>Numeric fields have a default precision of 5 decimal places, but should generally be
     * overridden to have a default value of 2 decimal places. Numeric fields such as Par Rate
     * should be displayed as real numbers to be in line with other parts of Xplain, and therefore
     * keep the 5 decimal places
     *
     * @param field the field to return precision for.
     * @return precision in decimal places, or null if a non-numeric field
     */
    @Override
    protected Integer precision(FieldDefinitionView field) {
      if (field.type() == FieldType.NUMBER) {
        return switch (field.name()) {
          case IpvDataProviderValueView.Fields.parRate -> 5;
          default -> DEFAULT_PRECISION;
        };
      }
      return null;
    }
  }
}
