package com.solum.xplain.data.valuation.ipv.repository.fragment;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.ArrayElemAt.arrayOf;
import static org.springframework.data.mongodb.core.aggregation.BooleanOperators.And.and;
import static org.springframework.data.mongodb.core.query.Criteria.expr;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.AggregateOptions;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.VersionedValue;
import com.solum.xplain.data.valuation.ipv.IpvDataViews;
import com.solum.xplain.data.valuation.ipv.entity.BaseIpvDataValue;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue;
import com.solum.xplain.data.valuation.ipv.value.IpvDataNavValueView;
import com.solum.xplain.data.valuation.ipv.value.IpvDataProviderValueView;
import com.solum.xplain.data.valuation.nav.entity.NavVersion;
import java.time.LocalDate;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IpvResolvedDataProviderValueViewQueriesImpl
    implements IpvResolvedDataProviderValueViewQueries {
  private static final String VDK = "vdk";

  private final MongoOperations mongoOperations;

  /**
   * Finds the resolved NAV for a trade using the matching groupId, valuationDate, and key.
   *
   * <p>This query is similar to the lookup defined in {@link IpvDataViews} but the key is known in
   * advance so it does not need a self-join.
   *
   * @param groupId the group ID
   * @param valuationDate the valuation date
   * @param key the data provider value key
   * @return the resolved NAV data provider value view or null if not found
   */
  @Override
  @Nullable
  public Optional<IpvDataProviderValueView> findResolvedNavByGroupIdAndDateAndKey(
      @NonNull String groupId, @NonNull LocalDate valuationDate, @NonNull String key) {
    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(
                    match(
                        where(DataValuesHolder.Fields.archivedAt)
                            .isNull()
                            .and(IpvDataValue.Fields.provider)
                            .is("NAV")
                            .and(DataValuesHolder.Fields.date)
                            .is(valuationDate)
                            .and(BaseIpvDataValue.Fields.groupId)
                            .is(groupId)),
                    addFields().addField(VDK).withValue(key).build(),
                    match(
                        expr(
                            and(
                                StringOperators.valueOf(VDK)
                                    .regexMatch(
                                        StringOperators.Concat.stringValue("^")
                                            .concatValueOf(BaseIpvDataValue.Fields.key))))),
                    limit(1),
                    project()
                        .and(BaseIpvDataValue.Fields.groupId)
                        .as(IpvDataProviderValueView.Fields.groupId)
                        .and(DataValuesHolder.Fields.date)
                        .as(IpvDataProviderValueView.Fields.date)
                        .and(VDK)
                        .as(IpvDataProviderValueView.Fields.key)
                        .and(IpvDataValue.Fields.provider)
                        .as(IpvDataProviderValueView.Fields.provider)
                        .and(BaseIpvDataValue.Fields.resolved)
                        .as(IpvDataProviderValueView.Fields.resolved)
                        .and(
                            arrayOf(
                                    propertyName(
                                        DataValuesHolder.Fields.values,
                                        VersionedValue.Fields.value))
                                .elementAt(-1))
                        .as(IpvDataProviderValueView.Fields.value)
                        .and(
                            arrayOf(
                                    propertyName(
                                        DataValuesHolder.Fields.values, NavVersion.Fields.currency))
                                .elementAt(-1))
                        .as(IpvDataNavValueView.Fields.currency)
                        .and(
                            arrayOf(
                                    propertyName(
                                        DataValuesHolder.Fields.values,
                                        VersionedValue.Fields.comment))
                                .elementAt(-1))
                        .as(IpvDataProviderValueView.Fields.comment))
                .withOptions(AggregateOptions.ALLOW_DISK_USE),
            IpvDataValue.class,
            IpvDataProviderValueView.class)
        .getMappedResults()
        .stream()
        .findFirst();
  }
}
