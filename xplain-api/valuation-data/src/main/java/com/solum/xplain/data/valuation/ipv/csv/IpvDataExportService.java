package com.solum.xplain.data.valuation.ipv.csv;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.portfolio.repository.PortfolioRepositoryFilter.activePortfolios;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.ValuationDataKeyUtils;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupView;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.PortfolioTeamFilterProvider;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.data.valuation.ipv.IpvDataRepository;
import com.solum.xplain.data.valuation.ipv.IpvDataType;
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueFilter;
import com.solum.xplain.data.valuation.ipv.value.IpvDataProviderValueView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class IpvDataExportService {

  private static final String RESOLVED_DATA_FILENAME_TEMPLATE = "%s_ValuationData";
  private static final String UNRESOLVED_DATA_FILENAME_TEMPLATE = "UnresolvedValuationData";

  private final AuthenticationContext authenticationContext;
  private final IpvDataRepository repository;
  private final IpvDataGroupRepository groupRepository;
  private final PortfolioRepository portfolioRepository;
  private final PortfolioItemRepository itemRepository;
  private final PortfolioTeamFilterProvider filterProvider;

  public IpvDataExportService(
      AuthenticationContext authenticationContext,
      IpvDataRepository repository,
      IpvDataGroupRepository groupRepository,
      PortfolioRepository portfolioRepository,
      PortfolioItemRepository itemRepository,
      PortfolioTeamFilterProvider filterProvider) {
    this.authenticationContext = authenticationContext;
    this.repository = repository;
    this.groupRepository = groupRepository;
    this.portfolioRepository = portfolioRepository;
    this.itemRepository = itemRepository;
    this.filterProvider = filterProvider;
  }

  public Either<ErrorItem, FileResponseEntity> getProviderValuesCsv(
      Authentication auth,
      String groupId,
      IpvDataProviderValueFilter filter,
      LocalDate stateDate,
      IpvDataType dataType) {
    return ipvDataGroup(auth, groupId)
        .map(g -> valuesCsv(g.getId(), filter, dataType))
        .map(bytes -> FileResponseEntity.csvFile(bytes, resolveFileName(stateDate, filter)));
  }

  private ByteArrayResource valuesCsv(
      String groupId, IpvDataProviderValueFilter filter, IpvDataType dataType) {
    final var mapper = new IpvDataCsvMapper(dataType);
    return repository
        .getValueViewsStream(groupId, filter, dataType)
        .map(mapper::toCsvRow)
        .collect(
            Collectors.collectingAndThen(
                toList(), rows -> new CsvOutputFile(mapper.header(), rows).writeToByteArray()));
  }

  private String resolveFileName(LocalDate stateDate, IpvDataProviderValueFilter filter) {
    var resolved = filter.getResolved();
    var prefix = UNRESOLVED_DATA_FILENAME_TEMPLATE;
    if (Objects.isNull(resolved) || BooleanUtils.isTrue(resolved)) {
      prefix = String.format(RESOLVED_DATA_FILENAME_TEMPLATE, filter.getValuationDate());
    }
    return nameWithTimeStamp(prefix, stateDate);
  }

  private Either<ErrorItem, IpvDataGroupView> ipvDataGroup(Authentication auth, String groupId) {
    return authenticationContext
        .userEither(auth)
        .flatMap(u -> groupRepository.ipvDataGroupView(u, groupId));
  }

  public Either<ErrorItem, FileResponseEntity> exportPortfolioTradeDataKeys(
      String id, BitemporalDate stateDate) {
    return getUserPortfolio(id)
        .map(UserTeamEntity::getView)
        .map(
            portfolio -> {
              var fileName =
                  nameWithTimeStamp(
                      portfolio.getExternalPortfolioId(),
                      "ValuationDataDefinitions",
                      stateDate.getActualDate());
              return FileResponseEntity.csvFile(file(portfolio, stateDate), fileName);
            });
  }

  private ByteArrayResource file(PortfolioView view, BitemporalDate stateDate) {
    var mapper = new IpvDataCsvMapper(IpvDataType.ALL);
    var rows =
        itemRepository
            .activePortfolioItemsStream(view.getId(), stateDate)
            .map(i -> toIpvDataValueView(view, i.getExternalTradeId()))
            .sorted(Comparator.comparing(IpvDataProviderValueView::getKey))
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return new CsvOutputFile(mapper.header(), rows).writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> exportAllPortfolioTradeDataKeys(
      BitemporalDate stateDate) {
    var mapper = new IpvDataCsvMapper(IpvDataType.ALL);
    var portfoliosMap =
        userPortfolios().stream().collect(toMap(PortfolioCondensedView::getId, v -> v));

    var rows =
        itemRepository
            .portfoliosItemsStream(portfoliosMap.keySet(), stateDate)
            .map(i -> toIpvDataValueView(portfoliosMap, i))
            .sorted(Comparator.comparing(IpvDataProviderValueView::getKey))
            .map(mapper::toCsvRow)
            .toList();
    var prefix =
        getPrefix(
            "ValuationDataDefinitions",
            new ArrayList<>(portfoliosMap.values()),
            PortfolioCondensedView::getExternalPortfolioId);
    var csvFileName = nameWithTimeStamp(prefix, stateDate);

    var csvFile = new CsvOutputFile(mapper.header(), rows);
    return Either.right(FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName));
  }

  private IpvDataProviderValueView toIpvDataValueView(
      Map<String, PortfolioView> portfolioViewMap, PortfolioItem item) {

    return toIpvDataValueView(portfolioViewMap.get(item.getPortfolioId().toHexString()), item);
  }

  private IpvDataProviderValueView toIpvDataValueView(
      PortfolioView view, PortfolioItem portfolioItem) {
    return toIpvDataValueView(view, portfolioItem.getExternalTradeId());
  }

  private IpvDataProviderValueView toIpvDataValueView(PortfolioView view, String externalTradeId) {
    var key =
        ValuationDataKeyUtils.toValuationDataKey(
            view.getExternalCompanyId(),
            view.getExternalEntityId(),
            view.getExternalPortfolioId(),
            externalTradeId);
    var v = new IpvDataProviderValueView();
    v.setKey(key);
    return v;
  }

  private Either<ErrorItem, UserTeamEntity<PortfolioView>> getUserPortfolio(String id) {
    return portfolioRepository.getUserPortfolioView(authenticationContext.currentUser(), id);
  }

  private List<PortfolioView> userPortfolios() {
    var filter = filterProvider.provideFilter();
    return portfolioRepository.portfolioViewList(activePortfolios(), filter);
  }
}
