package com.solum.xplain.data.valuation.ipv.form;

import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.data.valuation.ipv.IpvDataType;
import com.solum.xplain.data.valuation.ipv.entity.BaseIpvDataValue;
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue;
import java.time.LocalDate;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Data
@ParameterObject
public class IpvDataProviderValueFilter {

  @NonNull private final LocalDate valuationDate;
  @Nullable private final Boolean resolved;
  @NonNull private final Boolean archived;

  public static IpvDataProviderValueFilter newOfValuationDate(@NonNull LocalDate valuationDate) {
    return new IpvDataProviderValueFilter(valuationDate, null, false);
  }

  public Criteria criteria(@NonNull String groupId) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.date)
            .is(valuationDate);
    //noinspection PointlessBooleanExpression
    if (Boolean.TRUE.equals(archived)) {
      criteria.and(DataValuesHolder.Fields.archivedAt).ne(null);
    } else {
      criteria.and(DataValuesHolder.Fields.archivedAt).isNull();
    }
    return withResolvedCriteria(criteria);
  }

  public Criteria criteria(@NonNull String groupId, @NonNull IpvDataType dataType) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.date)
            .is(valuationDate);

    //noinspection PointlessBooleanExpression
    if (Boolean.TRUE.equals(archived)) {
      criteria.and(DataValuesHolder.Fields.archivedAt).ne(null);
    } else {
      criteria.and(DataValuesHolder.Fields.archivedAt).isNull();
    }

    criteria = withResolvedCriteria(criteria);

    return switch (dataType) {
      case NAV -> criteria.and(IpvDataValue.Fields.provider).is("NAV");
      case EXCLUDE_NAV -> criteria.and(IpvDataValue.Fields.provider).ne("NAV");
      default -> criteria;
    };
  }

  private Criteria withResolvedCriteria(Criteria criteria) {
    if (resolved == null) {
      return criteria;
    }

    if (resolved) {
      return criteria.and(BaseIpvDataValue.Fields.resolved).is(true);
    } else {
      return criteria.and(BaseIpvDataValue.Fields.resolved).ne(true);
    }
  }
}
