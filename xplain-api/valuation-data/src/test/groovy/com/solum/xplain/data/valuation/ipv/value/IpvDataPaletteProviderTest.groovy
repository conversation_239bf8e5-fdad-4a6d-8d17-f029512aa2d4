package com.solum.xplain.data.valuation.ipv.value

import com.solum.xplain.core.viewconfig.PaletteProviderSpecification
import com.solum.xplain.core.viewconfig.value.FieldType
import spock.lang.Unroll

class IpvDataPaletteProviderTest extends PaletteProviderSpecification {
  static classes = [IpvDataProviderValueView]
  @Unroll
  def "should provide palette for #clazz"() {
    when:
    def palettes = provider.providePalettes()

    then:
    assert null != palettes.find {
      it.scope().viewClass() == clazz
    }

    where:
    clazz << classes
  }

  @Unroll
  def "enum fields for #clazz should all have classifier reference"() {
    when:
    def fields = fieldDefinitionsForClass(clazz)
    def missingClassifier = fields
      .findAll { it.type() == FieldType.ENUM && null == it.enumClassifierName() }
      .collect { it.name() }

    then:
    missingClassifier == []

    where:
    clazz << classes
  }

  def "IpvDataProviderValueView palette should include expected fields"() {
    when:
    def fields = fieldDefinitionsForClass(IpvDataProviderValueView)

    then:
    assert null != fields.find { it.name() == fieldName }

    where:
    fieldName << [
      "key",
      "provider",
      "date",
      "value",
      "delta",
      "vega",
      "gamma",
      "theta",
      "rho",
      "parRate",
      "spotRate",
      "impliedVol",
      "atmImpliedVol",
      "realisedVol",
      "fairVol",
    ]
  }
}
