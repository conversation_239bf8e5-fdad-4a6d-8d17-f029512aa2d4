package com.solum.xplain.data.valuation.ipv.entity

import spock.lang.Specification

class IpvDataValueVersionTest extends Specification {

  def "valueEquals returns true when all fields are equal"() {
    given:
    def v1 = new IpvDataValueVersion(
      value: 100,
      delta: 1,
      vega: 2,
      gamma: 3,
      theta: 4,
      rho: 5,
      parRate: 6,
      spotRate: 7,
      impliedVol: 8,
      atmImpliedVol: 9,
      realisedVol: 10,
      fairVol: 11,
      )
    def v2 = new IpvDataValueVersion(
      value: 100,
      delta: 1,
      vega: 2,
      gamma: 3,
      theta: 4,
      rho: 5,
      parRate: 6,
      spotRate: 7,
      impliedVol: 8,
      atmImpliedVol: 9,
      realisedVol: 10,
      fairVol: 11,
      )

    expect:
    v1.valueEquals(v2)
  }

  def "valueEquals returns false when value is different"() {
    given:
    def v1 = new IpvDataValueVersion(value: 100)
    def v2 = new IpvDataValueVersion(value: 200)

    expect:
    !v1.valueEquals(v2)
  }

  def "valueEquals returns false when delta is different"() {
    given:
    def v1 = new IpvDataValueVersion(value: 100, delta: 1)
    def v2 = new IpvDataValueVersion(value: 100, delta: 2)

    expect:
    !v1.valueEquals(v2)
  }

  def "valueEquals returns false when compared to different type"() {
    given:
    def v1 = new IpvDataValueVersion(value: 100G)

    expect:
    !v1.valueEquals("Not an IpvDataValueVersion")
  }
}
