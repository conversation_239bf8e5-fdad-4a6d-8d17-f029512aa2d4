package com.solum.xplain.data.valuation

import com.solum.xplain.data.valuation.ipv.IpvDataViews
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.SpringBootConfiguration
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Bean

@EnableAutoConfiguration
@SpringBootConfiguration
@ImportAutoConfiguration
class TestValuationDataConfig extends ValuationDataConfig{

  @Bean
  CommandLineRunner createViews(IpvDataViews views) {
    return { args ->
      views.createViews()
    }
  }
}
