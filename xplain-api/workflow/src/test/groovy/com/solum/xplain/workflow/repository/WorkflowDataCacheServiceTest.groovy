package com.solum.xplain.workflow.repository

import org.springframework.cache.CacheManager
import org.springframework.cache.concurrent.ConcurrentMapCache
import org.springframework.data.mongodb.core.MongoOperations
import spock.lang.Specification

class WorkflowDataCacheServiceTest extends Specification {

  def mongoOperations = Mock(MongoOperations)
  def cacheManager = Mock(CacheManager)
  def workflowDataCacheService = new WorkflowDataCacheService(mongoOperations, cacheManager)

  class Entity {}

  def "should return existing cache if it exists"() {
    given:
    def cacheName = "workflowDataCache"
    def cache = new ConcurrentMapCache(cacheName)

    mongoOperations.getCollectionName(Entity) >> cacheName
    cacheManager.getCache(cacheName) >> cache

    when:
    def result = workflowDataCacheService.getCache(Entity)

    then:
    result == cache
    workflowDataCacheService.cacheCache.size() == 1
  }

  def "should cache the cache and not call mongo or cache manager again"() {
    given:
    def cacheName = "workflowDataCache"
    def cache = new ConcurrentMapCache(cacheName)

    def precachedService = new WorkflowDataCacheService(mongoOperations, cacheManager).tap {
      cacheCache.put(Entity, cache)
    }

    when:
    def result = precachedService.getCache(Entity)

    then:
    result == cache
    0 * mongoOperations._
    0 * cacheManager._
  }
}
