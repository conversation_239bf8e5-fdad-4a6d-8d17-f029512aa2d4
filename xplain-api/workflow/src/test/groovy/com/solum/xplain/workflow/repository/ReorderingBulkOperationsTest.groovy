package com.solum.xplain.workflow.repository


import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query
import static org.springframework.data.mongodb.core.query.Update.update

import com.solum.xplain.workflow.value.WorkflowStatus
import lombok.Data
import org.bson.Document
import org.bson.types.ObjectId
import org.springframework.data.mongodb.core.BulkOperations
import org.springframework.data.mongodb.core.FindAndReplaceOptions
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.aggregation.AggregationUpdate
import org.springframework.data.mongodb.core.convert.MongoConverter
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.util.Pair
import org.springframework.data.util.TypeInformation
import spock.lang.Specification

class ReorderingBulkOperationsTest extends Specification {
  MongoConverter converter = Mock()
  MongoOperations mongoOperations = Mock() {
    getConverter() >> converter
  }

  @Data
  static class EntityClass {
    ObjectId id
    String name = "hello"
    WorkflowStatus status = WorkflowStatus.ACTIVE
  }

  def "should coalesce update with previous insert"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def objectId = ObjectId.getSmallestWithDate(new Date())
    bulkOps.insert(new Document("_id", objectId).append("name", "hello"))
    converter.convertToMongoType("world", _ as TypeInformation) >> "world"

    when:
    bulkOps.updateOne(query(where("id").is(objectId)), update("name", "world"))

    then:
    with (bulkOps.insertQueue.get()) {
      size() == 1
      peek().size() == 2
      peek().getString("name") == "world"
    }
    with (bulkOps.updateQueue.get()) {
      size() == 0
    }
  }

  def "should coalesce pair updates with previous insert"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def objectId = ObjectId.getSmallestWithDate(new Date())
    bulkOps.insert(new Document("_id", objectId).append("name", "hello"))
    converter.convertToMongoType("world", _ as TypeInformation) >> "world"
    converter.convertToMongoType(WorkflowStatus.DONE, _ as TypeInformation) >> "DONE"

    when:
    bulkOps.updateOne([
      Pair.of(query(where("id").is(objectId)), update("name", "world")),
      Pair.of(query(where("id").is(objectId)), update("status", WorkflowStatus.DONE))
    ])

    then:
    with (bulkOps.insertQueue.get()) {
      size() == 1
      peek().size() == 3
      peek().getString("name") == "world"
      peek().getString("status") == "DONE"
    }
    with (bulkOps.updateQueue.get()) {
      size() == 0
    }
  }

  def "should coalesce simple update by ID"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def objectId = ObjectId.getSmallestWithDate(new Date())
    bulkOps.updateOne(query(where("id").is(objectId)), update("name", "world"))

    when:
    bulkOps.updateOne(query(where("id").is(objectId)), update("status", WorkflowStatus.FINALIZING))

    then:
    with (bulkOps.updateQueue.get()) {
      size() == 1
      with (get(objectId).getUpdateObject().get("\$set", Document.class)) {
        assert size() == 2
        assert getString("name") == "world"
        assert get("status") == WorkflowStatus.FINALIZING
      }
    }
  }

  def "should execute non-simple update one immediately and flush queues"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def query = Query.query(criteria)
    def oldInsertQueue = bulkOps.insertQueue.get()
    def oldUpdateQueue = bulkOps.updateQueue.get()
    def oldRemoveQueue = bulkOps.removeQueue.get()

    when:
    bulkOps.updateOne(query, updateDefinition)

    then:
    0 * mongoOperations.insert(_, _)
    0 * mongoOperations.bulkOps(_, _)
    1 * mongoOperations.updateFirst(query, updateDefinition, EntityClass)
    bulkOps.insertQueue.get() !== oldInsertQueue
    bulkOps.updateQueue.get() !== oldUpdateQueue
    bulkOps.removeQueue.get() !== oldRemoveQueue

    where:
    criteria                              | updateDefinition
    where("name").is("boris")             | update("moment", "not now")
    where("id").is("123")                 | new Update().currentDate("now")
  }

  def "should execute non-simple update one pair immediately and flush queues"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def query = Query.query(criteria)
    def oldInsertQueue = bulkOps.insertQueue.get()
    def oldUpdateQueue = bulkOps.updateQueue.get()
    def oldRemoveQueue = bulkOps.removeQueue.get()

    when:
    bulkOps.updateOne([Pair.of(query, updateDefinition)])

    then:
    0 * mongoOperations.insert(_, _)
    0 * mongoOperations.bulkOps(_, _)
    1 * mongoOperations.updateFirst(query, updateDefinition, EntityClass)
    bulkOps.insertQueue.get() !== oldInsertQueue
    bulkOps.updateQueue.get() !== oldUpdateQueue
    bulkOps.removeQueue.get() !== oldRemoveQueue

    where:
    criteria                              | updateDefinition
    where("name").is("boris")             | update("moment", "not now")
    where("id").is("123")                 | new Update().currentDate("now")
  }

  def "should execute update multi immediately and flush queues"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def query = Query.query(criteria)
    def oldInsertQueue = bulkOps.insertQueue.get()
    def oldUpdateQueue = bulkOps.updateQueue.get()
    def oldRemoveQueue = bulkOps.removeQueue.get()

    when:
    bulkOps.updateMulti(query, updateDefinition)

    then:
    0 * mongoOperations.insert(_, _)
    0 * mongoOperations.bulkOps(_, _)
    1 * mongoOperations.updateMulti(query, updateDefinition, EntityClass)
    bulkOps.insertQueue.get() !== oldInsertQueue
    bulkOps.updateQueue.get() !== oldUpdateQueue
    bulkOps.removeQueue.get() !== oldRemoveQueue

    where:
    criteria                              | updateDefinition
    where("id").is("123")                 | update("moment", "not now")
    where("id").is("123")                 | AggregationUpdate.update().set("moment").toValue("not now")
  }

  def "should execute update multi pair immediately using Spring BulkOps and flush queues"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def springBulkOps = Mock(BulkOperations)
    def query = Query.query(criteria)
    def oldInsertQueue = bulkOps.insertQueue.get()
    def oldUpdateQueue = bulkOps.updateQueue.get()
    def oldRemoveQueue = bulkOps.removeQueue.get()

    when:
    bulkOps.updateMulti([Pair.of(query, updateDefinition)])

    then:
    0 * mongoOperations.insert(_, _)
    1 * mongoOperations.bulkOps(BulkOperations.BulkMode.UNORDERED, EntityClass) >> springBulkOps
    1 * springBulkOps.updateMulti(query, updateDefinition)
    bulkOps.insertQueue.get() !== oldInsertQueue
    bulkOps.updateQueue.get() !== oldUpdateQueue
    bulkOps.removeQueue.get() !== oldRemoveQueue

    where:
    criteria                              | updateDefinition
    where("id").is("123")                 | update("moment", "not now")
    where("id").is("123")                 | AggregationUpdate.update().set("moment").toValue("not now")
  }

  def "should refuse to execute poor performing upsert/replace queries"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)

    when:
    bulkOps.upsert(query(where("name").is("boris")), update("moment", "not now"))

    then:
    thrown(UnsupportedOperationException)

    when:
    bulkOps.upsert([Pair.of(query(where("name").is("boris")), update("moment", "not now"))])

    then:
    thrown(UnsupportedOperationException)

    when:
    bulkOps.replaceOne(query(where("name").is("boris")), new EntityClass(), FindAndReplaceOptions.empty())

    then:
    thrown(UnsupportedOperationException)
  }

  def "should execute insert, update and delete"() {
    given:
    def bulkOps = new ReorderingBulkOperations(mongoOperations, EntityClass)
    def springBulkOps = Mock(BulkOperations)
    def objectId = ObjectId.getSmallestWithDate(new Date())
    def queryDef = query(where("id").is(ObjectId.get()))
    def update = update("name", "world")
    def removeQuery = query(where("name").is("boris"))
    def oldInsertQueue = bulkOps.insertQueue.get()
    def oldUpdateQueue = bulkOps.updateQueue.get()
    def oldRemoveQueue = bulkOps.removeQueue.get()

    when:
    bulkOps.insert(new Document("_id", objectId).append("name", "hello"))
    bulkOps.updateOne(queryDef, update)
    bulkOps.remove(removeQuery)

    then:
    oldInsertQueue.size() == 1
    oldUpdateQueue.size() == 1
    oldRemoveQueue.size() == 1

    when:
    bulkOps.execute()

    then:
    1 * mongoOperations.insert(_, EntityClass)
    1 * mongoOperations.bulkOps(BulkOperations.BulkMode.UNORDERED, EntityClass) >> springBulkOps
    1 * springBulkOps.updateOne(_, update)
    1 * springBulkOps.remove(removeQuery)
    1 * springBulkOps.execute()
    // Queues are cleared
    bulkOps.insertQueue.get() !== oldInsertQueue
    bulkOps.updateQueue.get() !== oldUpdateQueue
    bulkOps.removeQueue.get() !== oldRemoveQueue
  }
}
