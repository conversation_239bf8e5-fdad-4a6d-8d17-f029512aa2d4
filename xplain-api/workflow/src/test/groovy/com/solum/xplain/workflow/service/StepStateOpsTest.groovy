package com.solum.xplain.workflow.service

import static com.solum.xplain.workflow.value.HistoricStepInstanceView.fromStepInstance

import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.repository.DataModificationCommand
import com.solum.xplain.workflow.repository.DataModificationCommandQueue
import com.solum.xplain.workflow.service.command.StepUpdateOutcome
import com.solum.xplain.workflow.value.CallActivityDefinitionView
import com.solum.xplain.workflow.value.DataAssociationDirection
import com.solum.xplain.workflow.value.DataAssociationView
import com.solum.xplain.workflow.value.MultiInstance
import com.solum.xplain.workflow.value.ServiceStepDefinitionView
import com.solum.xplain.workflow.value.StepDefinition
import com.solum.xplain.workflow.value.WorkflowStatus
import java.util.concurrent.CompletableFuture
import org.bson.types.ObjectId
import org.springframework.beans.MutablePropertyValues
import org.springframework.beans.PropertyValues
import org.springframework.cache.Cache
import org.springframework.data.mongodb.core.BulkOperations
import org.springframework.expression.EvaluationContext
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import spock.lang.Specification

class StepStateOpsTest extends Specification {
  WorkflowService workflowService = Mock()
  WorkflowQueryService workflowQueryService = Mock()
  DataModificationCommandQueue dataModificationCommandQueue = Mock()
  SubprocessTracker subprocessCompletionTracker = Mock()
  ExpressionParser expressionParser = Mock()
  EvaluationContext evaluationContext = new StandardEvaluationContext()

  static record State(String name) implements Serializable {}

  def "outcome should queue a command"() {
    given:
    def stepInstance = new StepInstance()
    def processExecution = new ProcessExecution()
    def ops = stepStateOps(processExecution, stepInstance)
    def outcome = new MutablePropertyValues(["foo": "bar"])

    when:
    ops.setOutcome(outcome)

    then:
    1 * dataModificationCommandQueue.append(new StepUpdateOutcome(stepInstance, outcome))
  }

  def "deferred outcome should queue a command when completed"() {
    given:
    def stepInstance = new StepInstance()
    def processExecution = new ProcessExecution()
    def ops = stepStateOps(processExecution, stepInstance)
    def outcome = new MutablePropertyValues(["foo": "bar"])
    def futureOutcome = new CompletableFuture<PropertyValues>()

    when:
    def future = ops.setDeferredOutcome(futureOutcome)

    then:
    0 * dataModificationCommandQueue.append(_ as StepUpdateOutcome)

    when:
    futureOutcome.complete(outcome)
    future.join()

    then:
    1 * dataModificationCommandQueue.append(_ as StepUpdateOutcome)
  }

  def "get process id from step instance"() {
    given:
    def stepInstance = new StepInstance(processId: "myProcess")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    def result = ops.getProcessId()

    then:
    result == "myProcess"
  }

  def "get step id from step instance"() {
    given:
    def stepInstance = new StepInstance(stepId: "myStep")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    def result = ops.getStepId()

    then:
    result == "myStep"
  }

  def "get business key from step instance"() {
    given:
    def stepInstance = new StepInstance(businessKey: "myKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    def result = ops.getBusinessKey()

    then:
    result == "myKey"
  }

  def "get context from process execution"() {
    given:
    def stepInstance = new StepInstance(initialState: "Aardvark")
    def processExecution = new ProcessExecution(context: "context")
    def ops = stepStateOps(processExecution, stepInstance)

    when:
    def result = ops.getContext()

    then:
    result == "context"
  }

  def "get initial state from step instance"() {
    given:
    def stepInstance = new StepInstance(initialState: "Aardvark")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    def result = ops.getInitialState()

    then:
    result == "Aardvark"
  }

  def "get business key from root instance"() {
    given:
    def stepInstance = new StepInstance(executionId: new ObjectId())
    def ops = stepStateOps(new ProcessExecution(rootBusinessKey: "myRootKey"), stepInstance)

    when:
    def result = ops.getRootBusinessKey()

    then:
    result == "myRootKey"
  }

  def "get historic steps for this execution"() {
    given:
    def stepInstance = new StepInstance(processId: "myProcess", businessKey: "myKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)
    def stepInstances = [new StepInstance(stepId: "a"), new StepInstance(stepId: "b")]
    def stepViews = stepInstances.collect {
      fromStepInstance(it)
    }
    workflowQueryService.getHistoricStepInstances("myProcess", "myKey") >> stepInstances.stream()

    when:
    def result = ops.getStepInstanceHistory()

    then:
    result == stepViews
  }

  def "run created subprocess"() {
    given:
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKey")
    def subprocess = new ProcessExecution(id: new ObjectId(), businessKey: "key", parentBusinessKey: "parentKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    ops.runSubprocess(subprocess)

    then:
    0 * workflowService.getPropertyValues(_, _, _)
    1 * workflowService.startProcess(subprocess, _ as PropertyValues)
  }

  def "run created subprocess with input variables"() {
    given:
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKey")
    def subprocess = new ProcessExecution(id: new ObjectId(), businessKey: "key", parentBusinessKey: "parentKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance, new CallActivityDefinitionView(
    "callActivityId",
    "calledProcessId",
    MultiInstance.SEQUENTIAL,
    null,
    new DataAssociationView("source", "target", DataAssociationDirection.INPUT)
    ))
    def properties = new MutablePropertyValues(["target": "value"])

    when:
    ops.runSubprocess(subprocess)

    then:
    1 * workflowService.getPropertyValues(ops.stepDefinition as CallActivityDefinitionView, DataAssociationDirection.INPUT, ops.processExecution) >> properties
    1 * workflowService.startProcess(subprocess, properties)
  }

  def "create subprocess"() {
    given:
    def stepInstance = new StepInstance(id: new ObjectId(), executionId: new ObjectId(), businessKey: "parentKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)

    when:
    ops.createSubprocess("childProcessId", "childKey", "gagagoogoo", State, parallel)

    then:
    1 * subprocessCompletionTracker.registerSubprocess(stepInstance) >> sequence
    1 * workflowService.createProcess("childProcessId", "childKey", "gagagoogoo", State, stepInstance.rootExecutionId, stepInstance.rootBusinessKey, stepInstance.executionId, stepInstance.businessKey, stepInstance.id, parallel ? null : sequence, status)

    where:
    parallel | sequence || status
    false    | 1        || WorkflowStatus.ACTIVE
    false    | 2        || WorkflowStatus.INACTIVE
    true     | 1        || WorkflowStatus.ACTIVE
    true     | 2        || WorkflowStatus.ACTIVE
  }

  def "submit data command"() {
    given:
    def stepInstance = new StepInstance(executionId: new ObjectId(), businessKey: "parentKey")
    def ops = stepStateOps(new ProcessExecution(), stepInstance)
    def cmd = new DataModificationCommand() {
      @Override
      Class getEntity() {
        return String
      }

      @Override
      Object apply(BulkOperations bulkOps, Cache cache) {
        return "Bonjour"
      }
    }

    when:
    ops.submitBulkDataModification(cmd)

    then:
    1 * dataModificationCommandQueue.append(cmd)
  }

  def "evaluate an expression against step instance state"() {
    given:
    def stepInstance = new StepInstance<>(executionId: new ObjectId(), initialState: new State("Bob"))
    def processExecution = new ProcessExecution(context: "context")
    def ops = stepStateOps(processExecution, stepInstance)
    def expression = "(state.name + context).toUpperCase()"
    expressionParser.parseExpression(expression) >> new SpelExpressionParser().parseExpression(expression)

    when:
    def result = ops.evaluateWithInitialState(expression, String.class)

    then:
    result == "BOBCONTEXT"
  }

  private StepStateOps stepStateOps(ProcessExecution processExecution, StepInstance stepInstance, StepDefinition stepDefinition = new ServiceStepDefinitionView("stepId", null, false)) {
    new StepStateOps(workflowService, workflowQueryService, dataModificationCommandQueue, subprocessCompletionTracker, expressionParser, evaluationContext, stepDefinition, stepInstance, processExecution)
  }
}
