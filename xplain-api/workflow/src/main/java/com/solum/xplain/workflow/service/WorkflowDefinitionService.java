package com.solum.xplain.workflow.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.solum.xplain.workflow.provider.WorkflowProvider;
import com.solum.xplain.workflow.value.BoundaryEventView;
import com.solum.xplain.workflow.value.ProcessDefinitionView;
import com.solum.xplain.workflow.value.ProcessFlowView;
import com.solum.xplain.workflow.value.StartEventView;
import com.solum.xplain.workflow.value.StepDefinition;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/** Service providing workflow definition lookup capability to the rest of the application. */
@Service
public class WorkflowDefinitionService {
  private final Map<String, ProcessDefinitionView> processDefinitionsById;
  private final Cache<
          ProcessDefinitionView<? extends Serializable, ? extends Serializable>,
          Map<String, List<ProcessFlowView>>>
      cachedFlowsByFromStepId = Caffeine.newBuilder().build();
  private final Cache<
          ProcessDefinitionView<? extends Serializable, ? extends Serializable>,
          Map<String, StepDefinition>>
      cachedStepsById = Caffeine.newBuilder().build();
  private final Cache<
          ProcessDefinitionView<? extends Serializable, ? extends Serializable>,
          Map<String, StartEventView>>
      cachedStartEventsById = Caffeine.newBuilder().build();
  private final Cache<
          ProcessDefinitionView<? extends Serializable, ? extends Serializable>,
          Map<String, List<BoundaryEventView>>>
      cachedBoundaryEventsByFromStepId = Caffeine.newBuilder().build();

  public WorkflowDefinitionService(List<WorkflowProvider> providers) {
    processDefinitionsById =
        providers.stream()
            .flatMap(provider -> provider.provideProcessDefinitions().stream())
            .collect(Collectors.toUnmodifiableMap(ProcessDefinitionView::id, Function.identity()));
  }

  /**
   * Return the process definition for a specific workflow process definition ID.
   *
   * @param processDefId the ID of the process definition as supplied by a {@link WorkflowProvider}.
   * @return the process definition, or null if no such process definition exists
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  public <T extends Serializable, C extends Serializable>
      ProcessDefinitionView<T, C> getProcessDefinition(String processDefId) {
    return processDefinitionsById.get(processDefId);
  }

  /**
   * Map of step ID to step definition. Cached so is cheap to call.
   *
   * @param processDefinition the process definition from which to obtain the steps
   * @return a map of step ID to step definition
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  <T extends Serializable, C extends Serializable> Map<String, StepDefinition<T, C>> stepsByStepId(
      ProcessDefinitionView<T, C> processDefinition) {
    return (Map<String, StepDefinition<T, C>>)
        (Map<String, ?>)
            cachedStepsById.get(
                processDefinition,
                def ->
                    def.steps().stream()
                        .collect(Collectors.toMap(StepDefinition::id, Function.identity())));
  }

  /**
   * Map of event ID to event definition. Cached so is cheap to call.
   *
   * @param processDefinition the process definition from which to obtain the events
   * @return a map of event ID to event definition
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  <T extends Serializable, C extends Serializable> Map<String, StartEventView> startEventsByStartId(
      ProcessDefinitionView<T, C> processDefinition) {
    return cachedStartEventsById.get(
        processDefinition,
        def ->
            def.startEvents().stream()
                .collect(Collectors.toMap(StartEventView::startId, Function.identity())));
  }

  /**
   * Map of step ID to list of boundary event definitions. Cached so is cheap to call.
   *
   * @param processDefinition the process definition from which to obtain the events
   * @return a map of event ID to list of event definitions
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  <T extends Serializable, C extends Serializable>
      Map<String, List<BoundaryEventView>> boundaryEventsByFromStepId(
          ProcessDefinitionView<T, C> processDefinition) {
    return cachedBoundaryEventsByFromStepId.get(
        processDefinition,
        def ->
            def.boundaryEvents().stream()
                .flatMap(
                    boundaryEvent ->
                        boundaryEvent.attachedSteps().stream()
                            .map(stepId -> Map.entry(stepId, boundaryEvent)))
                .collect(
                    Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList()))));
  }

  /**
   * Map of step ID to all possible flows from that step. Cached so is cheap to call.
   *
   * @param processDefinition the process definition from which to obtain the flows
   * @return a map of flow start step ID to list of possible flows
   * @param <T> type representing the attached process state (must be serializable)
   * @param <C> type representing the immutable process context (must be serializable)
   */
  <T extends Serializable, C extends Serializable>
      Map<String, List<ProcessFlowView>> flowsByFromStepId(
          ProcessDefinitionView<T, C> processDefinition) {
    return cachedFlowsByFromStepId.get(
        processDefinition,
        def -> def.flows().stream().collect(Collectors.groupingBy(ProcessFlowView::fromStepId)));
  }
}
