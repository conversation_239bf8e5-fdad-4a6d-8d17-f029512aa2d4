package com.solum.xplain.workflow.service.engine;

import io.netty.util.internal.shaded.org.jctools.queues.atomic.MpmcAtomicArrayQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;

/**
 * A workflow engine implementation that uses JCTools MpmcAtomicArrayQueue for high-performance
 * multi-producer, multi-consumer work item processing.
 *
 * <p>This implementation provides:
 *
 * <ul>
 *   <li>Lock-free MPMC (Multiple Producer Multiple Consumer) queue for minimal contention
 *   <li>Multiple consumer threads for optimal parallel processing
 *   <li>Direct work item execution without coordinator overhead
 *   <li>Efficient batching to reduce polling overhead
 *   <li>Graceful shutdown with configurable timeout
 *   <li>Exception isolation - failed work items don't crash consumers
 * </ul>
 *
 * <p>The MPMC queue allows multiple producers to submit work items concurrently while multiple
 * consumers process them in parallel, achieving optimal throughput for workflow processing
 * scenarios.
 *
 * @since 1.0
 * @see WorkflowEngine
 * @see WorkItem
 */
@Slf4j
public class MpmcAtomicArrayWorkflowEngine implements WorkflowEngine {
  private final MpmcAtomicArrayQueue<WorkItem> workQueue;
  private final ExecutorService consumerExecutor;
  private final AtomicBoolean shutdown = new AtomicBoolean(false);
  private final AtomicInteger activeConsumers = new AtomicInteger(0);
  private volatile CountDownLatch idleLatch = new CountDownLatch(0);

  /**
   * Constructs a new MpmcAtomicArrayWorkflowEngine with default configuration.
   *
   * <p>Configuration:
   *
   * <ul>
   *   <li>Consumer threads: equal to number of available processors
   *   <li>Queue capacity: 1024 * number of available processors
   *   <li>Thread names: "mpmc-consumer-N"
   *   <li>Daemon threads: true
   * </ul>
   *
   * <p>Consumer threads are started immediately and begin polling for work items.
   */
  public MpmcAtomicArrayWorkflowEngine() {
    int queueCapacity = Runtime.getRuntime().availableProcessors() * 1024;
    int consumerCount = Runtime.getRuntime().availableProcessors();

    this.workQueue = new MpmcAtomicArrayQueue<>(queueCapacity);
    final AtomicInteger threadCounter = new AtomicInteger(0);
    this.consumerExecutor =
        Executors.newFixedThreadPool(
            consumerCount,
            r -> {
              Thread t = new Thread(r, "mpmc-consumer-" + threadCounter.incrementAndGet());
              t.setContextClassLoader(Thread.currentThread().getContextClassLoader());
              t.setDaemon(true);
              return t;
            });

    // Start consumer threads
    for (int i = 0; i < consumerCount; i++) {
      consumerExecutor.submit(this::consumerLoop);
    }

    log.info(
        "MpmcAtomicArrayWorkflowEngine started with {} consumers and queue capacity {}",
        consumerCount,
        queueCapacity);
  }

  /**
   * Submits a work item to the workflow engine for asynchronous processing.
   *
   * <p>The work item is added to the lock-free MPMC queue where it will be picked up by the next
   * available consumer thread. If the engine is shutdown or the work item is null, the work item is
   * rejected and a warning is logged.
   *
   * <p>Deadlock Prevention: If called from a consumer thread and the queue is full, the work item
   * is executed directly in the current thread to prevent deadlock scenarios where consumer threads
   * block waiting to add items to a full queue.
   *
   * <p>For external threads, this method is non-blocking. If the queue is full, the work item is
   * rejected to prevent blocking producers.
   *
   * @param workItem the work item to be processed, null items are ignored
   */
  @Override
  public void accept(WorkItem workItem) {
    if (workItem == null) {
      log.warn("Null work item submitted, ignoring");
      return;
    }

    if (shutdown.get()) {
      log.warn("Workflow engine is shutdown, rejecting work item");
      return;
    }

    // Check if current thread is a consumer thread to prevent deadlock
    if (isConsumerThread()) {
      // Consumer thread: if queue is full, execute immediately to prevent deadlock
      if (!workQueue.offer(workItem)) {
        log.debug("Queue full, executing work item directly in consumer thread");
        try {
          workItem.process(this);
        } catch (Exception e) {
          log.error("Error processing work item directly", e);
        }
        return;
      }
    } else {
      // External threads: use non-blocking offer to prevent producer blocking
      if (!workQueue.offer(workItem)) {
        log.warn("Work queue is full, rejecting work item - consider increasing queue capacity");
        return;
      }
    }

    updateIdleLatch();
  }

  /**
   * Initiates a graceful shutdown of the workflow engine.
   *
   * <p>This method:
   *
   * <ol>
   *   <li>Stops accepting new work items
   *   <li>Allows consumer threads to finish processing remaining work items
   *   <li>Waits up to 30 seconds for graceful termination
   *   <li>Forces shutdown if graceful termination fails
   * </ol>
   *
   * <p>This method is idempotent - calling it multiple times has no additional effect. After
   * shutdown, the engine cannot be restarted.
   */
  @Override
  public void shutdown() {
    if (shutdown.compareAndSet(false, true)) {
      log.info("Shutting down MpmcAtomicArrayWorkflowEngine");

      consumerExecutor.shutdown();

      try {
        if (!consumerExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
          log.warn("Consumers did not terminate gracefully, forcing shutdown");
          consumerExecutor.shutdownNow();
        }
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        consumerExecutor.shutdownNow();
      }

      log.info("MpmcAtomicArrayWorkflowEngine shutdown complete");
    }
  }

  /**
   * Waits until the workflow engine becomes idle or the timeout expires.
   *
   * <p>The engine is considered idle when:
   *
   * <ul>
   *   <li>The work queue is empty
   *   <li>No consumers are currently processing work items
   * </ul>
   *
   * <p>If the engine is already shutdown, this method returns immediately with {@code true}.
   *
   * @param timeout the maximum time to wait for the engine to become idle
   * @param unit the time unit of the timeout argument
   * @return {@code true} if the engine became idle before the timeout, {@code false} if the timeout
   *     elapsed
   */
  @Override
  public boolean waitUntilIdle(long timeout, TimeUnit unit) {
    if (shutdown.get()) {
      return true;
    }

    try {
      return getCurrentIdleLatch().await(timeout, unit);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      return false;
    }
  }

  /**
   * Main loop for consumer threads.
   *
   * <p>Each consumer thread runs this loop, continuously polling for work items from the MPMC
   * queue. Multiple consumers can process work items concurrently, providing optimal throughput for
   * parallel workflows.
   *
   * <p>The loop uses efficient batching to reduce polling overhead and includes brief pauses when
   * no work is available to prevent busy-waiting.
   *
   * <p>The loop terminates when the engine is shutdown.
   */
  private void consumerLoop() {
    while (!shutdown.get()) {
      try {
        boolean foundWork = false;

        // Process up to 8 work items in a batch to reduce polling overhead
        for (int i = 0; i < 8 && !shutdown.get(); i++) {
          WorkItem workItem = workQueue.poll();
          if (workItem != null) {
            foundWork = true;
            activeConsumers.incrementAndGet();
            try {
              workItem.process(this);
            } catch (Exception e) {
              log.error("Error processing work item", e);
            } finally {
              activeConsumers.decrementAndGet();
            }
          } else {
            break; // No more work available
          }
        }

        if (foundWork) {
          updateIdleLatch();
        } else {
          // No work available, park thread briefly to avoid busy-waiting
          try {
            Thread.sleep(1); // Sleep for 1ms instead of yielding
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
          }
          updateIdleLatch();
        }
      } catch (Exception e) {
        log.error("Unexpected error in consumer loop", e);
      }
    }
  }

  /**
   * Updates the idle latch based on current engine state.
   *
   * <p>This method is called whenever the engine state changes (work item added/completed). It
   * manages the CountDownLatch used by {@link #waitUntilIdle(long, TimeUnit)} to detect when the
   * engine becomes idle.
   *
   * <p>Thread-safe through atomic operations and volatile fields.
   */
  private void updateIdleLatch() {
    boolean isIdle = workQueue.isEmpty() && activeConsumers.get() == 0;

    if (isIdle) {
      // Engine is idle, release any waiting threads
      idleLatch.countDown();
    } else {
      // Engine is busy, reset latch if it was already released
      synchronized (this) {
        if (idleLatch.getCount() == 0 && (!workQueue.isEmpty() || activeConsumers.get() > 0)) {
          idleLatch = new CountDownLatch(1);
        }
      }
    }
  }

  /**
   * Gets the current idle latch in a thread-safe manner.
   *
   * @return the current CountDownLatch used for idle detection
   */
  private CountDownLatch getCurrentIdleLatch() {
    synchronized (this) {
      return idleLatch;
    }
  }

  /**
   * Checks if the current thread is a consumer thread.
   *
   * @return true if the current thread is an MPMC consumer thread
   */
  private boolean isConsumerThread() {
    return Thread.currentThread().getName().startsWith("mpmc-consumer-");
  }
}
