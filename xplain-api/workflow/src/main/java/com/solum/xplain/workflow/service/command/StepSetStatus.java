package com.solum.xplain.workflow.service.command;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;
import static org.springframework.data.mongodb.core.query.Update.update;

import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.repository.CacheSettingDataModificationCommand;
import com.solum.xplain.workflow.value.WorkflowStatus;
import jakarta.annotation.Nonnull;
import java.time.LocalDateTime;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

/**
 * Command to update the {@link StepInstance#getStatus() status} on a step instance. A {@link
 * BulkOperations#updateOne(Query, Update)} issued to reflect this in the database.
 *
 * @param stepInstance the execution to be updated
 * @param newStatus the new status to apply to the process execution
 */
public record StepSetStatus(StepInstance<?> stepInstance, WorkflowStatus newStatus)
    implements CacheSettingDataModificationCommand<StepInstance<?>> {
  @Override
  public Class<StepInstance> getEntity() {
    return StepInstance.class;
  }

  @Nonnull
  @Override
  public StepInstance<?> apply(BulkOperations bulkOps) {
    stepInstance.setStatus(newStatus);
    stepInstance.setModifiedAt(LocalDateTime.now());
    bulkOps.updateOne(
        query(where(StepInstance.Fields.id).is(stepInstance.getId())),
        update(StepInstance.Fields.status, newStatus)
            .set(StepInstance.Fields.modifiedAt, stepInstance.getModifiedAt()));
    return stepInstance;
  }
}
