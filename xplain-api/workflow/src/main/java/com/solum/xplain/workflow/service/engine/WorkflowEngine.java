package com.solum.xplain.workflow.service.engine;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * WorkflowEngine is the interface for the workflow engine that processes work items.
 *
 * <p>To compare the performance of different workflow engines, add the workflow engine to {@code
 * WorkflowEnginePerformanceIntegrationTest.ENGINES}.
 */
public interface WorkflowEngine extends Consumer<WorkItem> {
  /**
   * Submits a work item to the workflow engine for processing.
   *
   * @param workItem the work item to be processed
   */
  void accept(WorkItem workItem);

  /**
   * Immediately processes a work item synchronously without queuing it.
   *
   * @param workItem the work item to be processed immediately
   */
  default void processImmediately(WorkItem workItem) {
    workItem.process(this);
  }

  /** Shuts down the workflow engine, stopping all processing of work items. */
  void shutdown();

  /**
   * Checks if the workflow engine is tracking idle state. Tests that need to use {@link
   * #waitUntilIdle(long, TimeUnit)} need idle tracking enabled.
   *
   * <p>By default, idle tracking is enabled for all workflow engines but individual engines can
   * choose to support disabling it for optimal production performance.
   *
   * @return true if idle tracking is enabled, false otherwise
   */
  default boolean isIdleTrackingEnabled() {
    return true;
  }

  /**
   * Enables idle tracking for testing purposes.
   *
   * <p>By default, idle tracking is enabled for all workflow engines but individual engines can
   * choose to support disabling it for optimal production performance. Tests that need to use
   * {@link #waitUntilIdle(long, TimeUnit)} should call this method first to ensure idle tracking is
   * enabled.
   *
   * <p><strong>Note:</strong> This should only be called before submitting work items, as enabling
   * it mid-execution may produce inconsistent results.
   *
   * @param enabled true to enable idle tracking, false will throw an {@link
   *     IllegalArgumentException}.
   */
  default void setIdleTrackingEnabled(boolean enabled) {
    if (!enabled) {
      throw new IllegalArgumentException("Idle tracking cannot be disabled for this engine");
    }
  }

  /**
   * Waits until the workflow engine is idle, meaning all submitted work items have been processed.
   * This method will only work correctly if idle tracking is enabled (which is the default).
   *
   * @param timeout the maximum time to wait for the engine to become idle
   * @param unit the time unit of the timeout argument
   * @return true if the engine became idle before the timeout, false otherwise
   */
  boolean waitUntilIdle(long timeout, TimeUnit unit);
}
