package com.solum.xplain.workflow.value;

import static java.util.Collections.unmodifiableList;
import static java.util.Collections.unmodifiableSet;

import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

/**
 * Definition of a workflow process. Has a convenient static {@link #process(String)} builder which
 * provides a fluent interface for creating process definitions without using the constructor
 * directly.
 *
 * @param id ID of the process
 * @param startId ID of the default start event
 * @param steps set of step definitions
 * @param startEvents set of start events that can be fired at the process (apart from the default
 *     start event)
 * @param flows list of flows between steps
 * @param <T> type representing the attached process state (must be serializable)
 * @param <C> type representing the immutable process context (must be serializable)
 */
public record ProcessDefinitionView<T extends Serializable, C extends Serializable>(
    String id,
    String startId,
    Set<? extends StepDefinition<T, C>> steps,
    Set<? extends StartEventView> startEvents,
    Set<? extends BoundaryEventView> boundaryEvents,
    List<ProcessFlowView> flows) {
  /**
   * Fluent interface for creating a new process definition using a builder.
   *
   * @param id ID of the process
   * @return builder to allow the rest of the process definition to be defined
   * @param <T> type representing the attached process state (must be serializable)
   */
  public static <T extends Serializable, C extends Serializable>
      ProcessDefinitionBuilder<T, C> process(String id) {
    return new ProcessDefinitionBuilder<>(id);
  }

  @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
  public static class ProcessDefinitionBuilder<T extends Serializable, C extends Serializable> {
    private final String id;
    private final Set<StepDefinition<T, C>> steps = new HashSet<>();
    private final Set<StartEventView> startEvents = new HashSet<>();
    private final Set<BoundaryEventView> boundaryEvents = new HashSet<>();
    private final List<ProcessFlowView> flows = new LinkedList<>();
    private String startId;

    public ProcessDefinitionBuilder<T, C> startWith(String id) {
      this.startId = id;
      return this;
    }

    public ProcessDefinitionBuilder<T, C> serviceStep(
        String id, ServiceStepExecutor<T, C> executor, boolean reportable) {
      steps.add(new ServiceStepDefinitionView<>(id, executor, reportable));
      return this;
    }

    public <S extends Serializable, D extends Serializable> ProcessDefinitionBuilder<T, C> callStep(
        String id,
        String calledProcessId,
        MultiInstance multiInstance,
        SubprocessContextCreator<T, C, S, D> subprocessContextCreator,
        DataAssociationView... dataAssociations) {
      steps.add(
          new CallActivityDefinitionView<>(
              id, calledProcessId, multiInstance, subprocessContextCreator, dataAssociations));
      return this;
    }

    public ProcessDefinitionBuilder<T, C> exclusiveGateway(String id) {
      steps.add(new ExclusiveGatewayDefinitionView<>(id));
      return this;
    }

    public ProcessDefinitionBuilder<T, C> userTask(
        String id, AssignmentRuleView assignmentRule, FieldDefinitionView... formProperties) {
      steps.add(new UserTaskDefinitionView<>(id, assignmentRule, formProperties));
      return this;
    }

    public ProcessDefinitionBuilder<T, C> startEvents(StartEventView... events) {
      this.startEvents.addAll(Arrays.asList(events));
      return this;
    }

    public ProcessDefinitionBuilder<T, C> boundaryEvents(BoundaryEventView... events) {
      this.boundaryEvents.addAll(Arrays.asList(events));
      return this;
    }

    public ProcessDefinitionBuilder<T, C> flows(ProcessFlowView... flows) {
      this.flows.addAll(Arrays.asList(flows));
      return this;
    }

    public ProcessDefinitionView<T, C> build() {
      return new ProcessDefinitionView<>(
          id,
          startId,
          unmodifiableSet(steps),
          unmodifiableSet(startEvents),
          unmodifiableSet(boundaryEvents),
          unmodifiableList(flows));
    }
  }
}
