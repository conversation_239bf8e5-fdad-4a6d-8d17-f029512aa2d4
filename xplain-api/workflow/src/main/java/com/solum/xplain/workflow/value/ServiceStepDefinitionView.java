package com.solum.xplain.workflow.value;

import com.solum.xplain.workflow.service.StepStateOps;
import java.io.Serializable;

/**
 * Definition of a workflow step to perform an automatic activity. This will usually be created
 * using the {@code ProcessDefinitionView} fluent builder through the {@link
 * ProcessDefinitionView.ProcessDefinitionBuilder#serviceStep(String, ServiceStepExecutor, boolean)}
 * method.
 *
 * @param id ID of the step
 * @param executor implementation of a service step
 * @param reportable whether this step is significant enough to be reportable
 * @param <T> type representing the attached process state (must be serializable)
 * @param <C> type representing the immutable process context (must be serializable)
 */
public record ServiceStepDefinitionView<T extends Serializable, C extends Serializable>(
    String id, ServiceStepExecutor<T, C> executor, boolean reportable)
    implements StepDefinition<T, C> {
  @Override
  public boolean executable() {
    return true;
  }

  @Override
  public void runStep(StepStateOps<T, C> stepStateOps) {
    executor.runStep(stepStateOps);
  }
}
