dependencies {
  compileOnly "com.hazelcast:hazelcast:${hazelcastVersion}"
  compileOnly "org.springframework.data:spring-data-redis"
  compileOnly "io.netty:netty-common"
  implementation project(":xplain-api:core")
  implementation project(":shared:spring-async")
  implementation project(":shared:data-grid")
  implementation "org.jctools:jctools-core:${jctoolsVersion}"
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  implementation 'org.springframework.boot:spring-boot-starter-cache'
  implementation "com.github.ben-manes.caffeine:caffeine"
  runtimeOnly 'javax.cache:cache-api'

  integrationTestImplementation project(":shared:data-grid")
  integrationTestImplementation project(":shared:spring-async")
  integrationTestImplementation "com.hazelcast:hazelcast:${hazelcastVersion}"
  integrationTestImplementation 'org.mongodb:bson'
  integrationTestImplementation 'org.springframework:spring-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation 'org.springframework.boot:spring-boot-test'
  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation "org.jctools:jctools-core:${jctoolsVersion}"
}

tasks.named('integrationTest', Test) {
  systemProperty 'jdk.tracePinnedThreads', 'full'
  systemProperty 'jdk.virtualThreadScheduler.parallelism', '8'
  systemProperty 'jdk.virtualThreadScheduler.maxPoolSize', '8'
  jvmArgs '-XX:StartFlightRecording=duration=120s,filename=pinning.jfr'
}
