package com.solum.xplain.app.config;

import com.solum.xplain.app.license.conditions.GenericProductCondition;
import com.solum.xplain.generic.type.GenericProductConfig;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({GenericProductConfig.class})
@Conditional(GenericProductCondition.class)
public class AppGenericProductConfig {}
