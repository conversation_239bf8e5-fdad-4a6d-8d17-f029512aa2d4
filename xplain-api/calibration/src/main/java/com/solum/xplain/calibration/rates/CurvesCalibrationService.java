package com.solum.xplain.calibration.rates;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static com.solum.xplain.extensions.calendar.ValuationDateReferenceData.wrap;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.data.MarketData;
import com.solum.xplain.calibration.curve.CurveCalibrationResultRepository;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.calibration.discounting.OisConfigurations;
import com.solum.xplain.calibration.rates.set.CalibrationBundle;
import com.solum.xplain.calibration.rates.set.CalibrationBundleResolver;
import com.solum.xplain.calibration.settings.CalibrationSettingsService;
import com.solum.xplain.calibration.value.CalibrateCurveForm;
import com.solum.xplain.calibration.value.CalibrationOptions;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.List;
import java.util.function.Consumer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class CurvesCalibrationService {
  private final CurveGroupCurveRepository curveRepository;
  private final CurveCalibrationResultRepository calibrationResultRepository;
  private final CalibrationSettingsService settingsService;
  private final ReferenceData referenceData;

  public List<CalibrationBundle> resolveCalibrationBundles(
      String groupId,
      BitemporalDate stateDate,
      CalibrateCurveForm form,
      Consumer<List<ErrorItem>> logConsumer) {
    var curves = activeCurves(groupId, stateDate);
    var ccy = form.getCurveDiscountingForm().discountingType();
    var strippingType = form.strippingType();
    var triangulationCcy = form.getCurveDiscountingForm().triangulationCcy();
    var valuationDate = form.getCalibrationOptions().getValuationDate();
    var productSettingsResolver = settingsService.productSettingsResolver(stateDate);

    var groupResolver =
        CalibrationBundleResolver.builder()
            .curves(curves)
            .shifts(form)
            .valuationDate(valuationDate)
            .triangulationCcy(triangulationCcy)
            .warningsConsumer(logConsumer)
            .build();

    var discountingResolver = new DiscountingIndexResolver(OisConfigurations.of(valuationDate));
    var calibrationSubGroupsResolvers =
        new CurveGroupCalibrationsResolver(
                productSettingsResolver, discountingResolver, ccy.getCurrency())
            .splitCurvesIntoGroupResolvers(strippingType, curves);

    var results =
        calibrationSubGroupsResolvers.stream()
            .map(either -> either.flatMap(groupResolver::resolve))
            .toList();

    logConsumer.accept(
        ImmutableList.copyOf(Eithers.filterLeft(results)).stream()
            .map(e -> CALIBRATION_ERROR.entity(e.getDescription()))
            .toList());

    return ImmutableList.copyOf(Eithers.filterRight(results));
  }

  public Either<ErrorItem, CalibrationCombinedResultRates> calibrateCurves(
      CalibrationOptions calibrationOptions,
      MarketData marketData,
      List<CalibrationBundle> calibrationBundles,
      Consumer<List<ErrorItem>> logConsumer) {
    var stateDate = calibrationOptions.getMarketStateKey().getStateDate();
    var curves = activeCurves(calibrationOptions.getCurveGroup().getId(), stateDate);
    var valuationDate = marketData.getValuationDate();

    var valuationRefData = wrap(this.referenceData, marketData.getValuationDate());
    var curvesCalibrationOptions =
        CurvesCalibrationOptions.builder()
            .nodesFilter(calibrationOptions.getNodesFilter())
            .calibrationMeasures(settingsService.calibrationMeasures(stateDate, valuationDate))
            .inflationSeasonalitySettings(settingsService.inflationSeasonalities(stateDate))
            .warningsConsumer(logConsumer)
            .marketData(marketData)
            .referenceData(valuationRefData)
            .build();

    var calibration = new CurvesCalibration(curvesCalibrationOptions);
    // if true, will drop nodes with (expected) missing market data (e.g., for overlay data)
    var allowDropMissingMdNodes =
        calibrationOptions
            .getMarketStateKey()
            .getMarketDataSource()
            .allowCalibrationMissingValues();
    var results =
        calibrationBundles.stream()
            .map(
                bundle ->
                    calibration.calibrate(
                        bundle,
                        calibrationOptions.getCalibrationCurrency().getCurrency(),
                        false,
                        allowDropMissingMdNodes))
            .toList();

    return CurveGroupCalibrationResult.curveGroupCalibrationResult(
            results, marketData, valuationRefData)
        .map(r -> storeCalibrationResults(calibrationOptions, curves, r))
        .map(CurveGroupCalibrationResult::getCombinedResults);
  }

  private List<Curve> activeCurves(String groupId, BitemporalDate stateDate) {
    return curveRepository.getActiveCurves(groupId, stateDate);
  }

  private CurveGroupCalibrationResult storeCalibrationResults(
      CalibrationOptions calibrationOptions,
      List<Curve> curves,
      CurveGroupCalibrationResult results) {
    var stateKey = calibrationOptions.getMarketStateKey();
    for (var curve : curves) {
      var curveName = curve.curveName();
      CurveCalibrationResult result = new CurveCalibrationResult();
      result.setStateDate(stateKey.getStateDate().getActualDate());
      result.setMarketDataGroupId(stateKey.getMarketDataGroupId());
      result.setMarketDataSource(stateKey.getMarketDataSource().name());
      result.setCurveDate(stateKey.getCurveDate());
      result.setCalibrationCurrency(calibrationOptions.getCalibrationCurrency().getLabel());
      result.setCalibrationStrippingType(calibrationOptions.getStrippingType());
      result.setValuationDate(calibrationOptions.getValuationDate());
      result.setPriceRequirements(stateKey.getPriceRequirements());

      result.setDiscountFactorPoints(
          results.calculateDiscountFactors(curveName, this.referenceData));
      result.setNodeValues(results.calculateNodeValues(curveName));
      result.setNodesUsedInCalibration(results.nodesUsedInCalibration(curveName));
      results
          .getResolvedDiscountCurrency(curveName)
          .map(Currency::getCode)
          .ifPresent(result::setDiscountCurrency);

      results
          .calibratedCurve(curveName)
          .ifPresent(
              calibrated -> {
                result.setChartValues(calibrated.getChartPoints());
                result.setInflationSeasonalityAdjustment(
                    calibrated.getInflationSeasonalityAdjustment());
                result.setInflationAdjustmentType(calibrated.getInflationAdjustmentType());
              });

      if (result.isCalibrated()) {
        log.trace("Storing calibration results for curve: {}", curveName);
        result.setCurveId(curve.getEntityId());
        calibrationResultRepository.updateCurveCalibrationResults(result);
      } else {
        log.trace("Skipping storing calibration results for curve: {} - not calibrated", curveName);
      }
    }
    return results;
  }
}
