package com.solum.xplain.calibration.rates;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.solum.xplain.calibration.rates.group.discountable.AbstractDiscountableItemCurrencyResolver;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class CurveDiscountableCurrencyResolver
    extends AbstractDiscountableItemCurrencyResolver<Curve> {
  private final ProductSettingsResolver productSettingsResolver;

  public CurveDiscountableCurrencyResolver(ProductSettingsResolver productSettingsResolver) {
    this.productSettingsResolver = productSettingsResolver;
  }

  @Override
  protected Currency resolveOnshoreCurrency(DiscountableItem<Curve> item) {
    var curve = item.originalItem();
    return switch (curve.getCurveType()) {
      case IR_INDEX, INDEX_BASIS, INFLATION_INDEX ->
          curve.index().map(FloatingRateIndex::getCurrency).orElseThrow();
      case XCCY -> productSettingsResolver.resolveFxCcy(curve.xccyPair().orElseThrow());
    };
  }
}
