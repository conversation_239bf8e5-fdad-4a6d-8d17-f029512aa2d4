package com.solum.xplain.calibration.credit;

import static java.util.Collections.emptyList;

import com.opengamma.strata.market.curve.CurveName;
import com.solum.xplain.core.common.value.ChartPoint;
import java.util.List;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

@Data
public class CreditsCalibrationResult {
  private final CreditRatesDataProvider creditRatesProvider;
  private final List<ShiftedFxCreditRatesProvider> shiftedCreditRatesProviders;

  @Getter(AccessLevel.NONE)
  private final List<CreditCurveCalibrationResult> results;

  public boolean hasResults() {
    return !results.isEmpty();
  }

  public List<ChartPoint> getChart(CurveName curveName) {
    return results.stream()
        .filter(r -> r.curveName().equals(curveName))
        .map(CreditCurveCalibrationResult::calculateChartPoints)
        .findAny()
        .orElse(emptyList());
  }
}
