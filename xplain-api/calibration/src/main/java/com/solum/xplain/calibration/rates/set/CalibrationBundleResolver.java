package com.solum.xplain.calibration.rates.set;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calibration.rates.CalibrationEntry;
import com.solum.xplain.calibration.value.CalculationShifts;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Consumer;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Builder
@Data
public class CalibrationBundleResolver {
  @NonNull private final CalculationShifts shifts;
  @NonNull private final List<Curve> curves;
  @NonNull private final Consumer<List<ErrorItem>> warningsConsumer;
  @NonNull private final Currency triangulationCcy;
  @NonNull private final LocalDate valuationDate;

  public Either<ErrorItem, CalibrationBundle> resolve(CalibrationSubGroupsResolver groupsProvider) {
    var entries =
        curves.stream()
            .map(c -> new CalibrationEntry(c, shifts.curveShifts(c.getEntityId()), null))
            .toList();

    var groupsProviderDescription = groupsProvider.description();
    var groupsProviderCcy = groupsProvider.calibrationCurrency();
    return groupsProvider
        .calibrationSubGroups(entries, triangulationCcy, valuationDate, warningsConsumer)
        .map(r -> new CalibrationBundle(groupsProviderDescription, groupsProviderCcy, r));
  }
}
