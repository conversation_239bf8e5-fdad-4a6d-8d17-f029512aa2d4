package com.solum.xplain.calibration.bond;

import static com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions.findBondCurveByName;
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static com.solum.xplain.core.error.Error.CALIBRATION_WARNING;
import static io.atlassian.fugue.Either.left;
import static java.time.LocalDate.EPOCH;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.DefaultCurveMetadata;
import com.opengamma.strata.market.curve.InterpolatedNodalCurve;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveNode;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import io.atlassian.fugue.Either;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BondCurveCalibration {

  private final BondCurve curve;
  private final CurveConvention curveConvention;

  public static Either<ErrorItem, BondCurveCalibration> of(BondCurve curve) {
    return findBondCurveByName(curve.getName())
        .map(Either::<ErrorItem, CurveConvention>right)
        .orElse(left(CALIBRATION_ERROR.entity("Curve convention not found " + curve.getName())))
        .map(curveConvention -> new BondCurveCalibration(curve, curveConvention));
  }

  public Either<ErrorItem, InterpolatedNodalCurve> calibrate(
      MarketData marketData,
      ValidNodesFilter nodesFilter,
      Consumer<List<ErrorItem>> errorConsumer) {
    var nodes =
        curve.getNodes().stream()
            .map(v -> NodeInstrumentWrapper.of(v, v.instrument(curveConvention)))
            .collect(Collectors.collectingAndThen(toUnmodifiableList(), nodesFilter::filterNodes));

    final List<Double> tenors = new ArrayList<>();
    final List<Double> values = new ArrayList<>();
    nodes.stream()
        .sorted(comparing(BondCurveNode::getMaturityDate))
        .forEach(
            n ->
                marketData
                    .findValue(MarketDataUtils.quoteId(n.mdk()))
                    .ifPresentOrElse(
                        v -> {
                          tenors.add((double) EPOCH.until(n.getMaturityDate(), DAYS));
                          values.add(v);
                        },
                        () -> errorConsumer.accept(mdNotFoundError(n.mdk()))));
    if (values.size() <= 1) {
      return left(CALIBRATION_ERROR.entity("Insufficient nodes for curve " + curve.getName()));
    }
    return Either.right(
        InterpolatedNodalCurve.of(
            DefaultCurveMetadata.of(curve.getName()),
            DoubleArray.copyOf(tenors),
            DoubleArray.copyOf(values),
            CurveInterpolator.of(curve.getInterpolator()),
            CurveExtrapolator.of(curve.getExtrapolatorLeft()),
            CurveExtrapolator.of(curve.getExtrapolatorRight())));
  }

  private List<ErrorItem> mdNotFoundError(String mdk) {
    return List.of(CALIBRATION_WARNING.entity("Market data not found for " + mdk));
  }
}
