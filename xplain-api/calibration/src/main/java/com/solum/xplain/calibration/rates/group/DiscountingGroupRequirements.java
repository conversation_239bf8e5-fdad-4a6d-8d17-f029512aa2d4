package com.solum.xplain.calibration.rates.group;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements;
import java.util.Set;
import org.apache.commons.collections4.SetUtils;
import org.springframework.lang.NonNull;

public record DiscountingGroupRequirements(
    @NonNull Set<Currency> foreignCurrencies,
    @NonNull Set<String> creditNames,
    @NonNull Set<RateIndex> capletIndices,
    @NonNull Set<RateIndex> swaptionIndices,
    @NonNull Set<FloatingRateIndex> requiredIndices) {
  public static DiscountingGroupRequirements fromItemRequirements(
      Currency currency, DiscountableItemRequirements requirements) {
    return new DiscountingGroupRequirements(
        requirements.foreignCurrencies(currency),
        requirements.creditCurves(),
        requirements.capletIndices(),
        requirements.swaptionIndices(),
        requirements.getRequiredIndices());
  }

  public static DiscountingGroupRequirements empty() {
    return new DiscountingGroupRequirements(Set.of(), Set.of(), Set.of(), Set.of(), Set.of());
  }

  public DiscountingGroupRequirements merge(DiscountingGroupRequirements discounting) {
    return new DiscountingGroupRequirements(
        SetUtils.union(foreignCurrencies(), discounting.foreignCurrencies()).toSet(),
        SetUtils.union(creditNames(), discounting.creditNames()).toSet(),
        SetUtils.union(capletIndices(), discounting.capletIndices()).toSet(),
        SetUtils.union(swaptionIndices(), discounting.swaptionIndices()).toSet(),
        SetUtils.union(requiredIndices(), discounting.requiredIndices()).toSet());
  }
}
