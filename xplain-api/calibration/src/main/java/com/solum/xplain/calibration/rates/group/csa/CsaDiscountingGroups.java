package com.solum.xplain.calibration.rates.group.csa;

import com.solum.xplain.calibration.rates.group.CurrencyDiscountingGroupKey;
import com.solum.xplain.calibration.rates.group.DiscountingGroup;
import com.solum.xplain.calibration.rates.group.DiscountingGroups;
import com.solum.xplain.calibration.rates.group.DiscountingGroupsResults;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemCsaResolver;
import com.solum.xplain.calibration.rates.group.ois.OisDiscountingGroup;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class CsaDiscountingGroups<I> implements DiscountingGroups<I> {

  private final DiscountableItemCsaResolver<I> csaResolver;
  @Getter private final Map<CurrencyDiscountingGroupKey, CsaDiscountingGroup> discountingGroups;
  private final DiscountingGroups<I> fallbackDiscountingGroups;

  @Override
  public DiscountingGroupsResults results() {
    var csaDiscounting =
        discountingGroups.values().stream().map(OisDiscountingGroup.class::cast).toList();
    return new CsaResults(csaDiscounting, fallbackDiscountingGroups.results());
  }

  @Override
  public DiscountingGroup itemDiscountingGroup(DiscountableItem<I> item) {
    return csaResolver
        .resolveCsaCurrency(item)
        .map(csaCurrency -> new CurrencyDiscountingGroupKey(csaCurrency, item.requirements()))
        .map(discountingGroups::get)
        .map(DiscountingGroup.class::cast)
        .toOptional()
        .orElseGet(() -> fallbackDiscountingGroups.itemDiscountingGroup(item));
  }
}
