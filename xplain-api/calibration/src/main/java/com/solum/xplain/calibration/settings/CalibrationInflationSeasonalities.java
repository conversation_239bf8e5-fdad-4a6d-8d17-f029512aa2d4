package com.solum.xplain.calibration.settings;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.SeasonalityDefinition;
import com.solum.xplain.calibration.rates.CalibrationEntry;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.entity.CurveSeasonality;
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings;
import com.solum.xplain.core.settings.inflation.CalculatedCurveSeasonality;
import com.solum.xplain.core.settings.inflation.InflationCalculation;
import io.atlassian.fugue.Either;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CalibrationInflationSeasonalities {

  private final Map<String, InflationCalculation> calculationsMap;

  public static CalibrationInflationSeasonalities ofSettings(InflationSeasonalitySettings s) {
    var calculationsMap =
        s.getCurveSeasonalities().stream()
            .collect(
                Collectors.toMap(
                    CurveSeasonality::getPriceIndex, CurveSeasonality::resolveCalculation));
    return new CalibrationInflationSeasonalities(calculationsMap);
  }

  public CalibrationInflationSeasonalitiesResult calcSeasonality(
      MarketData md, Set<CalibrationEntry> entries) {
    var results = ImmutableMap.<CurveName, Either<ErrorItem, SeasonalityDefinition>>builder();
    for (var entry : entries) {
      entry
          .index()
          .map(index -> calculationsMap.get(index.getName()))
          .map(calculation -> calculateSeasonality(calculation, md))
          .ifPresent(calcResults -> results.put(CurveName.of(entry.getCurveName()), calcResults));
    }
    return new CalibrationInflationSeasonalitiesResult(results.build());
  }

  private Either<ErrorItem, SeasonalityDefinition> calculateSeasonality(
      InflationCalculation calculation, MarketData md) {
    return calculation
        .calculate(md)
        .leftMap(e -> Error.CALIBRATION_WARNING.entity(e.getDescription()))
        .map(CalculatedCurveSeasonality::seasonalityDefinition);
  }
}
