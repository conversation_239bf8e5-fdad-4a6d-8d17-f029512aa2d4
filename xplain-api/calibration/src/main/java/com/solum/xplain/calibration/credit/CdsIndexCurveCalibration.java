package com.solum.xplain.calibration.credit;

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.curve.IsdaCreditCurveDefinition;
import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;

@RequiredArgsConstructor(staticName = "newOf")
public class CdsIndexCurveCalibration implements CreditCurveCalibration {
  private static final Logger LOG = getLogger(CdsIndexCurveCalibration.class);
  private final IsdaCreditCurveDefinition definition;
  private final MarketData marketData;
  private final ImmutableCreditRatesProvider ratesProvider;
  private final BigDecimal factor;
  private final ReferenceData referenceData;

  @Override
  public Either<ErrorItem, CreditCurveCalibrationResult> calibrate() {
    try {
      LOG.debug("Starting to calibrate set {}", definition.getName());
      var referenceData =
          ValuationDateReferenceData.wrap(this.referenceData, ratesProvider.getValuationDate());
      var survivalProbabilities =
          CdsIndexCurveCalibrator.standard()
              .calibrate(
                  definition, factor.doubleValue(), marketData, ratesProvider, referenceData);
      return Either.right(
          CreditCurveCalibrationResult.of(definition, survivalProbabilities, referenceData));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(
          new ErrorItem(
              CALIBRATION_ERROR,
              "Error calibrating credit curve  " + definition.getName() + ": " + ex.getMessage()));
    } finally {
      LOG.debug("Finished calibrating Credit Index set {}", definition.getName());
    }
  }
}
