package com.solum.xplain.calibration.rates.group.single;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.Index;
import com.solum.xplain.calibration.rates.group.BaseDiscountingGroup;
import com.solum.xplain.calibration.rates.group.DiscountingGroupRequirements;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.lang.NonNull;
import org.springframework.util.Assert;

@Getter
@ToString
@EqualsAndHashCode
public class SingleDiscountingGroup extends BaseDiscountingGroup {

  @NonNull private final FloatingRateIndex index;
  @NonNull private final ClearingHouse clearingHouse;

  private SingleDiscountingGroup(
      @NonNull FloatingRateIndex index,
      @NonNull ClearingHouse clearingHouse,
      @NonNull DiscountingGroupRequirements discountingGroupRequirements) {
    super(discountingGroupRequirements);
    this.index = index;
    this.clearingHouse = clearingHouse;
  }

  public static SingleDiscountingGroup fromRequirements(
      FloatingRateIndex index, DiscountableItemRequirements requirements) {
    return new SingleDiscountingGroup(
        index,
        requirements.getClearingHouse(),
        DiscountingGroupRequirements.fromItemRequirements(index.getCurrency(), requirements));
  }

  @Override
  public String key() {
    return String.format("%s_%s", index.getName(), clearingHouse);
  }

  @Override
  public Currency getCurrency() {
    return index.getCurrency();
  }

  @Override
  public boolean supportsIndex(@NonNull Index i) {
    return index.getName().equals(i.getName());
  }

  @Override
  public boolean supportsDiscount(@NonNull Currency ccy) {
    return index.getCurrency().equals(ccy);
  }

  public SingleDiscountingGroup merge(SingleDiscountingGroup discounting) {
    Assert.isTrue(
        Objects.equals(index, discounting.getIndex()), "Discounting indices must be equal");
    Assert.isTrue(
        Objects.equals(clearingHouse, discounting.clearingHouse), "Clearing houses must be equal");
    var requirements = getRequirements().merge(discounting.getRequirements());

    return new SingleDiscountingGroup(index, clearingHouse, requirements);
  }
}
