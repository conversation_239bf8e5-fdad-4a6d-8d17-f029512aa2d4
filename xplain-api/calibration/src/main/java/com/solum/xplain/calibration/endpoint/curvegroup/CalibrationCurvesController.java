package com.solum.xplain.calibration.endpoint.curvegroup;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CURVE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.calibration.curve.CalibratedCurveChartOptions;
import com.solum.xplain.calibration.curve.CurveChartsControllerService;
import com.solum.xplain.calibration.curve.charts.ChartDateType;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for accessing calibrated curves from a given group.
 *
 * <p>This controller should only provide GET endpoints. See {@link CurveGroupCalibrationController}
 * for endpoints triggering curves calibration.
 */
@NullMarked
@RequiredArgsConstructor
@RestController
@RequestMapping("/curve-group/{groupId}/curves")
public class CalibrationCurvesController {
  private final CurveChartsControllerService service;

  @Operation(summary = "Gets IR + Inflation curve chart points")
  @GetMapping({"/{curveId}/chart-points"})
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<List<ChartPoint>> getCurveChartPoints(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemResponse(
        service.getCurvePoints(groupId, curveId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets IR + Inflation combined curve points csv")
  @GetMapping("/{curveId}/results/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveCalibrationResultsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getCurvesPointsCsv(groupId, curveId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets all curves IR + Inflation combined curve points csv")
  @GetMapping("/results/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getAllCurvesCalibrationResultsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getAllCurvesPointsCsv(groupId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets IR + Inflation curve points csv")
  @GetMapping("/{curveId}/chart-points/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveChartPointsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getCurveChartPointsCsv(groupId, curveId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets all curves IR + Inflation curve points csv")
  @GetMapping("/all/chart-points/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getAllCurvesChartPointsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getAllCurvesChartPointsCsv(groupId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets IR + Inflation curve discount factor points csv")
  @GetMapping("/{curveId}/discount-factors/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveDiscountPointsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getCurveDiscountPointsCsv(groupId, curveId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets all curves IR + Inflation curve discount factor points csv")
  @GetMapping("/all/discount-factors/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getAllCurvesDiscountPointsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getAllCurvesDiscountPointsCsv(groupId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets Inflation curve market rate points csv")
  @GetMapping("/{curveId}/market-rates/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getCurveMarketRatesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("curveId") String curveId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getCurvesMarketPointsCsv(groupId, curveId, stateForm, calibrationOptions));
  }

  @Operation(summary = "Gets all Inflation curves market rate points csv")
  @GetMapping("/all/market-rates/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE)
  public ResponseEntity<ByteArrayResource> getAllCurvesMarketRatesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate valuationDate,
      @RequestParam(required = false) CalculationDiscountingType discountingType,
      @RequestParam(required = false) CalculationStrippingType calibrationStrippingType,
      @RequestParam(required = false, defaultValue = "ACTUAL_DATE") ChartDateType dateType,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    var calibrationOptions =
        new CalibratedCurveChartOptions(
            valuationDate, discountingType, calibrationStrippingType, dateType);
    return eitherErrorItemFileResponse(
        service.getAllCurvesMarketPointsCsv(groupId, stateForm, calibrationOptions));
  }
}
