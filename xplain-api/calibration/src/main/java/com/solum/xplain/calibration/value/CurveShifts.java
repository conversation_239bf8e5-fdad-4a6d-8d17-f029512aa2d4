package com.solum.xplain.calibration.value;

import static java.util.Collections.emptyList;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.function.ToDoubleFunction;
import lombok.Data;

@Data
public class CurveShifts {

  private static final CurveShifts EMPTY = new CurveShifts(null, emptyList());

  @NotEmpty private final String curveId;

  @Valid @NotNull private final List<NodeShift> nodeToShift;

  public static CurveShifts empty() {
    return EMPTY;
  }

  public ToDoubleFunction<String> additionalSpread() {
    return nodeId ->
        nodeToShift.stream()
            .filter(n -> n.nodeIdIs(nodeId))
            .findAny()
            .map(NodeShift::resolvedAdditionalSpread)
            .orElse(0d);
  }

  public boolean isForCurve(String curveId) {
    return this.curveId.equalsIgnoreCase(curveId);
  }
}
