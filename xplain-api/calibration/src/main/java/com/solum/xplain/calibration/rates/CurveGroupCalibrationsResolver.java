package com.solum.xplain.calibration.rates;

import static com.solum.xplain.core.curvegroup.curve.CurvesUtils.collectIndices;
import static com.solum.xplain.core.curvegroup.curve.CurvesUtils.collectOisDiscountCurrencies;
import static io.atlassian.fugue.Either.left;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calibration.discounting.DiscountingIndexResolver;
import com.solum.xplain.calibration.rates.group.DiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.group.ois.combined.OisLocalCcySingleFallbackDiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.group.ois.localccy.LocalCcyDiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.group.ois.singleccy.SingleCcyDiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.group.single.SingleDiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.set.CalibrationSubGroupsResolver;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.settings.product.ProductSettingsResolver;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.lang.Nullable;

@AllArgsConstructor
@NullMarked
public class CurveGroupCalibrationsResolver {

  private final ProductSettingsResolver productSettingsResolver;
  private final DiscountingIndexResolver discountingIndexResolver;
  @Nullable private final Currency dscCcy;

  public List<Either<ErrorItem, CalibrationSubGroupsResolver>> splitCurvesIntoGroupResolvers(
      CalculationStrippingType strippingType, List<Curve> curves) {
    if (curves.isEmpty()) {
      return List.of();
    }
    var discountingsBuilder = builder(strippingType, curves);
    curves.stream().map(CurveDiscountableItem::new).forEach(discountingsBuilder::withItem);

    var results = ImmutableList.<Either<ErrorItem, CalibrationSubGroupsResolver>>builder();
    var dsc = discountingsBuilder.build(e -> results.add(left(e))).results();
    results.addAll(
        dsc.calibrationSubGroupsResolvers().values().stream()
            .map(Either::<ErrorItem, CalibrationSubGroupsResolver>right)
            .toList());
    return results.build();
  }

  private DiscountingGroupsBuilder<Curve> builder(
      CalculationStrippingType strippingType, List<Curve> curves) {
    var curveDiscountingCurrencyResolver =
        new CurveDiscountableCurrencyResolver(productSettingsResolver);
    var curveDiscountingIndexResolver =
        new CurveDiscountableIndexResolver(productSettingsResolver, discountingIndexResolver);
    var liborBuilder =
        new SingleDiscountingGroupsBuilder<>(curveDiscountingIndexResolver, collectIndices(curves));

    if (strippingType == CalculationStrippingType.LIBOR) {
      return liborBuilder;
    } else if (dscCcy == null) {
      return new OisLocalCcySingleFallbackDiscountingGroupsBuilder<>(
          new LocalCcyDiscountingGroupsBuilder<>(curveDiscountingCurrencyResolver),
          liborBuilder,
          collectOisDiscountCurrencies(curves),
          curveDiscountingCurrencyResolver);
    } else {
      return new SingleCcyDiscountingGroupsBuilder<>(dscCcy);
    }
  }
}
