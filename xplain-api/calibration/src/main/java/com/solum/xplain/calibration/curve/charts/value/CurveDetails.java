package com.solum.xplain.calibration.curve.charts.value;

import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.market.ValueType;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import java.util.Optional;
import org.springframework.lang.Nullable;

public record CurveDetails(
    String name,
    @Nullable String discountingKey,
    ValueType xValueType,
    ValueType yValueType,
    String interpolator,
    String extrapolatorLeft,
    String extrapolatorRight) {

  public Optional<PriceIndex> priceIndex() {
    if (ValueType.PRICE_INDEX.equals(yValueType)) {
      try {
        return ConventionalCurveConfigurations.lookupByName(name, CurveType.INFLATION_INDEX)
            .filter(IndexCurveConvention.class::isInstance)
            .map(IndexCurveConvention.class::cast)
            .map(IndexCurveConvention::getIndex)
            .filter(PriceIndex.class::isInstance)
            .map(PriceIndex.class::cast);

      } catch (IllegalArgumentException ex) {
        return Optional.empty();
      }
    }
    return Optional.empty();
  }
}
