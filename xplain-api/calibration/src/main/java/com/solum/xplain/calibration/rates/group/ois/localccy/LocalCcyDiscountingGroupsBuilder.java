package com.solum.xplain.calibration.rates.group.ois.localccy;

import com.solum.xplain.calibration.rates.group.AbstractDiscountingGroupsBuilder;
import com.solum.xplain.calibration.rates.group.CurrencyDiscountingGroupKey;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemCurrencyResolver;
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements;
import com.solum.xplain.calibration.rates.group.ois.OisDiscountingGroup;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jspecify.annotations.NullMarked;

/**
 * LocalCcyDiscountingGroupBuilder is a concrete implementation of {@code
 * AbstractDiscountingGroupBuilder} designed to collect and aggregate discountable items into groups
 * primarily based on local currency. This collector uses a {@code DiscountableItemCurrencyResolver}
 * to determine the currency associated with each item and manages discounting groups according to
 * the resolved currency, clearing house, and offshore flag.
 *
 * @param <I> the type of the original item associated with a discountable item.
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NullMarked
public class LocalCcyDiscountingGroupsBuilder<I>
    extends AbstractDiscountingGroupsBuilder<OisDiscountingGroup, CurrencyDiscountingGroupKey, I> {
  private final DiscountableItemCurrencyResolver<I> currencyResolver;

  public LocalCcyDiscountingGroupsBuilder(DiscountableItemCurrencyResolver<I> currencyResolver) {
    this.currencyResolver = currencyResolver;
  }

  @Override
  protected LocalCcyDiscountingGroups<I> build(
      Map<CurrencyDiscountingGroupKey, OisDiscountingGroup> discountings) {
    return new LocalCcyDiscountingGroups<>(currencyResolver, discountings);
  }

  @Override
  protected OisDiscountingGroup merge(
      OisDiscountingGroup existing, OisDiscountingGroup newDiscounting) {
    return existing.merge(newDiscounting);
  }

  @Override
  protected Either<ErrorItem, CurrencyDiscountingGroupKey> resolveKey(DiscountableItem<I> item) {
    var ccy = currencyResolver.resolveCurrency(item);
    return Either.right(new CurrencyDiscountingGroupKey(ccy, item.requirements()));
  }

  @Override
  protected OisDiscountingGroup resolveDiscounting(
      CurrencyDiscountingGroupKey key, DiscountableItemRequirements requirements) {
    var currency = key.currency();
    return OisDiscountingGroup.fromRequirements(currency, requirements);
  }
}
