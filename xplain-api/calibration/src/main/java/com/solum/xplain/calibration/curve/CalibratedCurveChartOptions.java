package com.solum.xplain.calibration.curve;

import com.solum.xplain.calibration.curve.charts.ChartDateType;
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;

@Getter
@ToString
@EqualsAndHashCode
public class CalibratedCurveChartOptions extends CalibratedCurvesOptions {

  private final ChartDateType dateType;

  public CalibratedCurveChartOptions(
      @NonNull LocalDate valuationDate,
      CalculationDiscountingType calibrationCurrency,
      CalculationStrippingType calibrationStrippingType,
      ChartDateType dateType) {
    super(valuationDate, calibrationCurrency, calibrationStrippingType);
    this.dateType = dateType;
  }
}
