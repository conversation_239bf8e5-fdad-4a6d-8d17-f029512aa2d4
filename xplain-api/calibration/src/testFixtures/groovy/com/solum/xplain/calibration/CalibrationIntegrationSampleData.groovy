package com.solum.xplain.calibration

import static com.opengamma.strata.market.ValueType.PRICE_INDEX
import static com.opengamma.strata.market.ValueType.ZERO_RATE
import static com.opengamma.strata.product.credit.type.CdsQuoteConvention.QUOTED_SPREAD
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_6M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.USD_FIXED_ZC_US_CPI
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.USD_FIXED_TERM_SOFR_OIS
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.common.versions.State.ACTIVE
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.INFLATION_INDEX
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.IR_INDEX
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.XCCY
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.BOND_YIELD
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CREDIT_INDEX
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_INFLATION_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_OVERNIGHT_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.XCCY_IBOR_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.values
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ASK
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static java.math.BigDecimal.ONE
import static java.util.Arrays.stream
import static java.util.stream.Collectors.toMap

import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.audit.entity.AuditEntryItem
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveCalibrationResult
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveNode
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNode
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveIndexNode
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode
import com.solum.xplain.core.fixings.Fixing
import com.solum.xplain.core.fixings.FixingVersion
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.MarketDataKey
import com.solum.xplain.core.market.MarketDataProviderTicker
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.mdvalue.entity.MarketDataValueVersion
import com.solum.xplain.core.settings.entity.CurveStrippingProductSettings
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.extensions.enums.CreditSeniority
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query

trait CalibrationIntegrationSampleData {
  String CURVE_GROUP_ID = ObjectId.get().toHexString()
  String CURVE_CONFIG_ID = "curveConfigId"
  String MD_GROUP_ID = ObjectId.get().toHexString()
  String PROVIDER_BID_ONLY = "BID"
  String PROVIDER_BOTH = "BOTH"
  String TICKER = "TICKER"

  LocalDate DATE = LocalDate.of(2023, 1, 1)
  LocalDateTime DATE_TIME = DATE.atStartOfDay()
  LocalDate MAX_DATE = LocalDate.of(2199, 1, 1)
  LocalDateTime MAX_DATE_TIME = MAX_DATE.atStartOfDay()

  abstract MongoOperations operations()

  void cleanupCalibrationData() {
    operations().remove(new Query(), AuditEntry)
    operations().remove(new Query(), AuditEntryItem)

    operations().remove(new Query(), MarketDataGroup)
    operations().remove(new Query(), MarketDataValue)
    operations().remove(new Query(), MarketDataKey)
    operations().remove(new Query(), Fixing)

    operations().remove(new Query(), Curve)
    operations().remove(new Query(), CurveGroup)
    operations().remove(new Query(), CurveGroupFxRates)
    operations().remove(new Query(), CurveConfiguration)
    operations().remove(new Query(), CurveCalibrationResult)

    operations().remove(new Query(), CreditCurve)

    operations().remove(new Query(), BondCurve)
    operations().remove(new Query(), BondCurveCalibrationResult)
  }

  void setupCommonCalibrationData() {
    operations().save(new CurveStrippingProductSettings(
    inflationCurvePriorities: ["TENOR_3M"],
    fxCurvePriorities: ["TENOR_3M"],
    validFrom: DATE,
    recordDate: DATE_TIME,
    state: ACTIVE
    ))
    operations().save(new CurveGroup(id: CURVE_GROUP_ID))

    operations().save(new CurveGroupFxRates(
    entityId: CURVE_GROUP_ID,
    nodes: [new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")],
    validFrom: DATE,
    recordDate: DATE_TIME,
    state: ACTIVE))

    operations().save(new CurveConfiguration(
    curveGroupId: CURVE_GROUP_ID,
    entityId: CURVE_CONFIG_ID,
    validFrom: DATE,
    recordDate: DATE_TIME,
    state: ACTIVE,
    instruments: stream(values())
    .collect(toMap(k -> (InstrumentType) k, k -> new MarketDataProviders(primary: PROVIDER_BID_ONLY, secondary: PROVIDER_BOTH)))
    ))

    operations().save(new MarketDataGroup(id: MD_GROUP_ID, allowAllCompanies: true, allowAllTeams: true, name: "MD_NAME"))
    operations().save(new MarketDataValue(MD_GROUP_ID, TICKER, PROVIDER_BID_ONLY, DATE, BID, [new MarketDataValueVersion(value: ONE, recordDate: DATE_TIME)]))
    operations().save(new MarketDataValue(MD_GROUP_ID, TICKER, PROVIDER_BOTH, DATE, BID, [new MarketDataValueVersion(value: ONE, recordDate: DATE_TIME)]))
    operations().save(new Fixing(reference: "US-CPI-U", date: DATE.minusMonths(1), values: [new FixingVersion(value: ONE, recordDate: DATE_TIME)]))
    operations().save(new GlobalValuationSettings(
    validFrom: DATE,
    recordDate: DATE_TIME,
    state: ACTIVE,
    forceIsdaInterpolatorsForCds: true
    ))
  }

  void mockUsdSofr() {
    mockCurve("USD SOFR",
    IR_INDEX,
    [
      curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1D"),
      curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1Y")
    ])

    mockMdk("1D_USD-FIXED-TERM-SOFR-OIS", FIXED_OVERNIGHT_SWAP)
    mockMdk("1Y_USD-FIXED-TERM-SOFR-OIS", FIXED_OVERNIGHT_SWAP)
  }

  void mockUsdSofrIsdaCompliant() {
    mockCurve("USD SOFR",
    IR_INDEX,
    [
      curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1D"),
      curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_SOFR_OIS.name, "1Y")
    ], { c ->
      c.interpolator = CurveInterpolators.PRODUCT_LINEAR.getName()
      c.extrapolatorLeft = CurveExtrapolators.FLAT.getName()
      c.extrapolatorRight = CurveExtrapolators.PRODUCT_LINEAR.getName()
      c
    }
    )

    mockMdk("1D_USD-FIXED-TERM-SOFR-OIS", FIXED_OVERNIGHT_SWAP)
    mockMdk("1Y_USD-FIXED-TERM-SOFR-OIS", FIXED_OVERNIGHT_SWAP)
  }

  void mockUsd3m() {
    mockCurve("USD 3M",
    IR_INDEX,
    [
      curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name, "1D"),
      curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name, "1Y")
    ])

    mockMdk("1D_USD-FIXED-6M-LIBOR-3M", FIXED_IBOR_SWAP)
    mockMdk("1Y_USD-FIXED-6M-LIBOR-3M", FIXED_IBOR_SWAP)
  }

  void mockUsCpi() {
    mockCurve("US CPI U",
    INFLATION_INDEX,
    [
      curveNode(FIXED_INFLATION_SWAP_NODE, USD_FIXED_ZC_US_CPI.name, "1Y"),
      curveNode(FIXED_INFLATION_SWAP_NODE, USD_FIXED_ZC_US_CPI.name, "2Y")
    ])

    mockMdk("1Y_USD-FIXED-ZC-US-CPI", FIXED_INFLATION_SWAP)
    mockMdk("2Y_USD-FIXED-ZC-US-CPI", FIXED_INFLATION_SWAP)
  }

  void mockUsCpiLch() {
    mockCurve("US CPI U LCH",
    INFLATION_INDEX,
    [
      curveNode(FIXED_INFLATION_SWAP_NODE, USD_FIXED_ZC_US_CPI.name, "1Y"),
      curveNode(FIXED_INFLATION_SWAP_NODE, USD_FIXED_ZC_US_CPI.name, "2Y")
    ])

    mockMdk("1Y_USD-FIXED-ZC-US-CPI-LCH", FIXED_INFLATION_SWAP)
    mockMdk("2Y_USD-FIXED-ZC-US-CPI-LCH", FIXED_INFLATION_SWAP)
  }

  void mockEurUsd() {
    mockCurve("EUR/USD", XCCY, [
      curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name, "1D"),
      curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name, "1Y")
    ])

    mockMdk("1D_EUR-EURIBOR-3M-USD-LIBOR-3M", XCCY_IBOR_IBOR_SWAP)
    mockMdk("1Y_EUR-EURIBOR-3M-USD-LIBOR-3M", XCCY_IBOR_IBOR_SWAP)
    mockMdk("EUR/USD", FX_RATE)
  }

  void mockEur3m() {
    mockCurve("EUR 3M", IR_INDEX, [
      curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1D"),
      curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1Y")
    ])

    mockMdk("1D_EUR-FIXED-1Y-EURIBOR-3M", FIXED_IBOR_SWAP)
    mockMdk("1Y_EUR-FIXED-1Y-EURIBOR-3M", FIXED_IBOR_SWAP)
  }

  void mockEur1mWithoutNodes() {
    mockCurve("EUR 1M", IR_INDEX, [])
  }

  void mockCurve(String name, CurveType type, List<CurveNode> nodes, Closure<Curve> c = { a -> a }) {
    operations().save(new Curve(
    curveGroupId: CURVE_GROUP_ID,
    name: name,
    entityId: name,
    curveType: type,
    yInterpolationMethod: type == INFLATION_INDEX ? PRICE_INDEX.getName() : ZERO_RATE.getName(),
    interpolator: CurveInterpolators.LINEAR.getName(),
    extrapolatorLeft: CurveExtrapolators.FLAT.getName(),
    extrapolatorRight: CurveExtrapolators.FLAT.getName(),
    state: ACTIVE,
    validFrom: DATE,
    recordDate: DATE_TIME,
    nodes: nodes).tap(c))
  }

  CurveNode curveNode(String type, String convention, String period) {
    return new CurveNode(type: type, convention: convention, period: period)
  }

  void mockMdk(String key, InstrumentType instrumentType) {
    operations().save(new MarketDataKey(key: key, validFrom: DATE, recordFrom: DATE_TIME, state: ACTIVE,
    assetGroup: instrumentType.getAssetClass().getGroup().name(), instrumentType: instrumentType.name(),
    validities: [new DateRangeVersionValidity(MAX_DATE, DATE_TIME, MAX_DATE_TIME)],
    providerTickers: [
      new MarketDataProviderTicker(code: PROVIDER_BID_ONLY, ticker: TICKER, factor: 1, bidAskType: BID_ONLY),
      new MarketDataProviderTicker(code: PROVIDER_BOTH, ticker: TICKER, factor: 1, bidAskType: BID_ASK),
    ]))
  }

  void mockUsdCreditCurve() {
    mockCreditCurve("SELF_USD_SNRFOR_CR14", "USD", [new CreditCurveCdsNode(tenor: "6M"), new CreditCurveCdsNode(tenor: "1Y")])

    mockMdk("6M_SELF_USD_SNRFOR_CR14_SPREAD", CDS)
    mockMdk("1Y_SELF_USD_SNRFOR_CR14_SPREAD", CDS)
  }

  void mockEurCreditCurve() {
    mockCreditCurve("SELF_EUR_SNRFOR_CR14", "EUR", [new CreditCurveCdsNode(tenor: "6M"), new CreditCurveCdsNode(tenor: "1Y")])

    mockMdk("6M_SELF_EUR_SNRFOR_CR14_SPREAD", CDS)
    mockMdk("1Y_SELF_EUR_SNRFOR_CR14_SPREAD", CDS)
  }

  void mockUsdCreditIndexCurve() {
    mockCreditIndexCurve("2I65BRZB9_USD", "USD", [
      new CreditCurveIndexNode(tenor: "6M", type: CreditCurveNodeType.CREDIT_INDEX),
      new CreditCurveIndexNode(tenor: "1Y", type: CreditCurveNodeType.CREDIT_INDEX)
    ])

    mockMdk("6M_2I65BRZB9_USD_SPREAD", CREDIT_INDEX)
    mockMdk("1Y_2I65BRZB9_USD_SPREAD", CREDIT_INDEX)
  }

  void mockUsdCreditIndexTrancheCurve() {
    mockCreditIndexCurve("CDX.NA.IG.S39.V1.3-7_USD", "USD", [
      new CreditCurveIndexNode(tenor: "6M", type: CreditCurveNodeType.CREDIT_INDEX_TRANCHE),
      new CreditCurveIndexNode(tenor: "1Y", type: CreditCurveNodeType.CREDIT_INDEX_TRANCHE)
    ])

    mockMdk("6M_CDX.NA.IG.S39.V1.3-7_USD_SPREAD", CREDIT_INDEX)
    mockMdk("1Y_CDX.NA.IG.S39.V1.3-7_USD_SPREAD", CREDIT_INDEX)
  }

  void mockCreditCurve(String name, String currency, List<CreditCurveCdsNode> nodes) {
    operations().save(new CreditCurve(
    curveGroupId: CURVE_GROUP_ID,
    name: name,
    entityId: name,
    currency: currency,
    curveType: CreditCurveType.CDS,
    reference: "SELF",
    quoteConvention: QUOTED_SPREAD.name(),
    seniority: CreditSeniority.SNRFOR,
    sector: CreditSector.CONSUMER_SERVICES,
    docClause: CreditDocClause.XR14,
    state: ACTIVE,
    validFrom: DATE,
    recordDate: DATE_TIME,
    cdsNodes: nodes,
    fixedCoupon: 0.05))
  }

  void mockCreditIndexCurve(String name, String currency, List<CreditCurveIndexNode> nodes) {
    operations().save(new CreditCurve(
    curveGroupId: CURVE_GROUP_ID,
    name: name,
    entityId: name,
    currency: currency,
    curveType: CreditCurveType.CREDIT_INDEX,
    reference: "REF",
    quoteConvention: QUOTED_SPREAD.name(),
    seniority: CreditSeniority.SNRFOR,
    sector: CreditSector.CONSUMER_SERVICES,
    docClause: CreditDocClause.XR14,
    state: ACTIVE,
    validFrom: DATE,
    recordDate: DATE_TIME,
    indexNodes: nodes,
    fixedCoupon: 0.05,
    creditIndexStartDate: DATE,
    creditIndexFactor: 0.1
    ))
  }

  void mockBondCurve() {
    mockBondYieldCurve(
    "UKGT",
    [
      new BondCurveNode(maturityDate: DATE, cusip: "CUSIP1"),
      new BondCurveNode(maturityDate: DATE.plusDays(1), cusip: "CUSIP2")
    ]
    )

    mockMdk("01JAN2023_CUSIP1", BOND_YIELD)
    mockMdk("02JAN2023_CUSIP2", BOND_YIELD)
  }

  void mockBondYieldCurve(String name, List<BondCurveNode> nodes) {
    operations().save(new BondCurve(
    curveGroupId: CURVE_GROUP_ID,
    name: name,
    entityId: name,
    state: ACTIVE,
    validFrom: DATE,
    recordDate: DATE_TIME,
    interpolator: CurveInterpolators.LINEAR.getName(),
    extrapolatorLeft: CurveExtrapolators.FLAT.getName(),
    extrapolatorRight: CurveExtrapolators.FLAT.getName(),
    nodes: nodes))
  }
}
