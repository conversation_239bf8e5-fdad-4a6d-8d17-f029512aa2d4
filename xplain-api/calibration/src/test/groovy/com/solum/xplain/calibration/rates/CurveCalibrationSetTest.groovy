package com.solum.xplain.calibration.rates

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.pricer.curve.CalibrationMeasures.PAR_SPREAD
import static com.solum.xplain.calibration.CalibrationSetSample.sampleCurveGroupDefinition
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.curve.CurveGroupName
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.RatesCurveGroupDefinition
import com.opengamma.strata.market.curve.RatesCurveGroupEntry
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.extensions.volsshift.ShiftedFxMarketData
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class CurveCalibrationSetTest extends Specification {
  static REF_DATA = ReferenceData.standard()
  static MARKET_DATA = ogMarketData()
  static WARNINGS = { it -> null }

  def "should calibrate curve group"() {
    setup:
    def calibration = new CurveCalibrationSet(
    sampleCurveGroupDefinition(),
    sampleCurveGroupDefinition(),
    ogMarketData(),
    [],
    REF_DATA
    )

    when:
    def result = calibration.calibrateRates(PAR_SPREAD)

    then:
    result.isRight()
    def calibrationResult = result.getOrNull()
    def ratesProvider = calibrationResult.getRatesResult().getRatesProvider()
    def discountCurves = ratesProvider.getDiscountCurves()
    discountCurves.size() == 2
    discountCurves.get(EUR) != null
    discountCurves.get(USD) != null

    def isdaResult = calibrationResult.getIsdaRatesSupplier().get()
    isdaResult.getOrNull().ratesProvider == ratesProvider // Isda rates provider is the same as rates provider when isda compliant

    def shiftedRates = calibrationResult.getRatesResult().shiftedRatesProviders
    shiftedRates.isEmpty()
  }

  def "should calibrate shifted fx rates"() {
    setup:
    def marketData = ogMarketData()
    def calibration = new CurveCalibrationSet(
    sampleCurveGroupDefinition(),
    sampleCurveGroupDefinition(),
    marketData,
    [CurrencyPair.of(EUR, USD), CurrencyPair.of(GBP, USD)],
    REF_DATA
    )

    when:
    def result = calibration.calibrateRates(PAR_SPREAD)

    then:
    result.isRight()
    def calibrationResult = result.getOrNull()

    def shiftedRates = calibrationResult.getRatesResult().shiftedRatesProviders
    shiftedRates.size() == 2
    with(shiftedRates[0]) {
      currencyPair == CurrencyPair.of(EUR, USD)
      def fxRateProvider = ((ImmutableRatesProvider) ratesProvider).getFxRateProvider()
      def fxProviderMd = ((MarketDataFxRateProvider) fxRateProvider).marketData
      fxProviderMd instanceof ShiftedFxMarketData
      fxProviderMd == ShiftedFxMarketData.shiftedFxMarketData(CurrencyPair.of(EUR, USD), marketData)
    }
    with(shiftedRates[1]) {
      currencyPair == CurrencyPair.of(GBP, USD)
      def fxRateProvider = ((ImmutableRatesProvider) ratesProvider).getFxRateProvider()
      def fxProviderMd = ((MarketDataFxRateProvider) fxRateProvider).marketData
      fxProviderMd instanceof ShiftedFxMarketData
      fxProviderMd == ShiftedFxMarketData.shiftedFxMarketData(CurrencyPair.of(GBP, USD), marketData)
    }
  }

  @Unroll
  def "should return resolved discount currency #result for curve #curveName and #currency"() {
    setup:
    def valuationDate = LocalDate.parse("2016-01-01")
    def curveDefinition = eurOis().curveDefinition({
      0d
    }, valuationDate, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull()
    def definition = RatesCurveGroupDefinition.builder().name(CurveGroupName.of("Name"))
    if (currency != null) {
      definition.addDiscountCurve(curveDefinition, currency)
    } else {
      definition.addForwardCurve(curveDefinition, IborIndices.EUR_EURIBOR_3M)
    }

    def calibration = new CurveCalibrationSet(
    definition.build(),
    definition.build(),
    ogMarketData(),
    [],
    REF_DATA
    )

    expect:
    calibration.definition
    .findEntry(CurveName.of(curveName))
    .map(RatesCurveGroupEntry::getDiscountCurrencies)
    .stream()
    .flatMap(Collection::stream)
    .findFirst() == result

    where:
    curveName     | currency | result
    "EUR EONIA"   | null     | Optional.empty()
    "EUR EONIA"   | EUR      | Optional.of(EUR)
    "Random name" | EUR      | Optional.empty()
  }
}
