package com.solum.xplain.calibration.rates.group.ois

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.calibration.rates.set.OisCalibrationSubGroupsResolver.oisDiscountGroups

import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies
import spock.lang.Specification

class OisResultsTest extends Specification {

  def "should correctly resolve resulting sets"() {
    setup:
    def oisDsc = OisDiscountingGroup.discounting(EUR)
    def results = new OisResults([oisDsc])

    when:
    def res = results.calibrationSubGroupsResolvers()

    then:
    res == [(oisDsc): oisDiscountGroups(IndexBasedDiscountCurrencies.getOf(EUR), Set.of(), Set.of())]
  }
}
