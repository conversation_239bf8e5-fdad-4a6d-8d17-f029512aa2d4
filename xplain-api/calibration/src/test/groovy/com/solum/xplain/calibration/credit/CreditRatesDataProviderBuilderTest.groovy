package com.solum.xplain.calibration.credit

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.calibration.CalibrationSetSample.SELF_ID
import static com.solum.xplain.calibration.CalibrationSetSample.ratesProvider
import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveSample.self
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT

import com.opengamma.strata.collect.tuple.Pair
import com.opengamma.strata.pricer.credit.CreditDiscountFactors
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities
import com.opengamma.strata.pricer.credit.RecoveryRates
import spock.lang.Specification

class CreditRatesDataProviderBuilderTest extends Specification {

  def "should build credit rates provider with correct dfs from rate provider"() {
    setup:
    def builder = new CreditRatesDataProviderBuilder(VAL_DT)
    builder.withCreditDiscountFactors(ratesProvider(VAL_DT))

    when:
    def creditRatesDataProvider = builder.build()

    then:
    creditRatesDataProvider.valuationDate == VAL_DT
    creditRatesDataProvider.creditDiscountFactors.size() == 1
    creditRatesDataProvider.creditDiscountFactors.get(EUR) != null
  }

  def "should build credit rates provider with correct dfs from all dfs"() {
    setup:
    def df = Mock(CreditDiscountFactors)
    def builder = new CreditRatesDataProviderBuilder(VAL_DT)
    builder.withCreditDiscountFactors([(EUR): df])

    when:
    def creditRatesDataProvider = builder.build()

    then:
    creditRatesDataProvider.valuationDate == VAL_DT
    creditRatesDataProvider.creditDiscountFactors.size() == 1
    creditRatesDataProvider.creditDiscountFactors.get(EUR) == df
  }

  def "should build credit rates provider with correct recovery from credit curve"() {
    setup:
    def builder = new CreditRatesDataProviderBuilder(VAL_DT)
    builder.withRecoveryRate(self(VAL_DT))

    when:
    def creditRatesDataProvider = builder.build()

    then:
    creditRatesDataProvider.valuationDate == VAL_DT
    creditRatesDataProvider.recoveryRates.size() == 1
    creditRatesDataProvider.recoveryRates.get(SELF_ID) != null
  }

  def "should build credit rates provider with correct recovery from all recoveries"() {
    setup:
    def recoveryRates = Mock(RecoveryRates)
    def builder = new CreditRatesDataProviderBuilder(VAL_DT)
    builder.withRecoveryRate([(SELF_ID): recoveryRates])

    when:
    def creditRatesDataProvider = builder.build()

    then:
    creditRatesDataProvider.valuationDate == VAL_DT
    creditRatesDataProvider.recoveryRates.size() == 1
    creditRatesDataProvider.recoveryRates.get(SELF_ID) == recoveryRates
  }

  def "should build credit rates provider with correct probabilities from all probabilities"() {
    setup:
    def survivalProbabilities = Mock(CreditDiscountFactors)
    survivalProbabilities.getCurrency() >> EUR
    def probability = LegalEntitySurvivalProbabilities.of(SELF_ID, survivalProbabilities)
    def builder = new CreditRatesDataProviderBuilder(VAL_DT)
    def calibrationResult = Mock(CreditCurveCalibrationResult)
    calibrationResult.getProbabilities() >> probability
    builder.withProbabilities([calibrationResult])

    when:
    def creditRatesDataProvider = builder.build()

    then:
    creditRatesDataProvider.valuationDate == VAL_DT
    creditRatesDataProvider.probabilities.size() == 1
    creditRatesDataProvider.probabilities.get(Pair.of(SELF_ID, EUR)) == probability
  }
}
