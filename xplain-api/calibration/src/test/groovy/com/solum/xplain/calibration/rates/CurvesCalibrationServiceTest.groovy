package com.solum.xplain.calibration.rates

import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.usdOis
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.pricer.curve.CalibrationMeasures
import com.solum.xplain.calibration.curve.CurveCalibrationResultRepository
import com.solum.xplain.calibration.discounting.CalibrationDiscountSubGroup
import com.solum.xplain.calibration.rates.set.CalibrationBundle
import com.solum.xplain.calibration.settings.CalibrationSettingsService
import com.solum.xplain.calibration.value.CalculationShifts
import com.solum.xplain.calibration.value.CalibrationOptions
import com.solum.xplain.calibration.value.CurveShifts
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings
import java.util.function.Consumer
import spock.lang.Specification

class CurvesCalibrationServiceTest extends Specification {
  CurveGroupCurveRepository curveRepository = Mock()
  CalibrationSettingsService settingsService = Mock()
  CurveCalibrationResultRepository resultRepository = Mock()

  def calibrationService = new CurvesCalibrationService(curveRepository, resultRepository, settingsService, ReferenceData.standard())

  def "should calibrate curves with EUR calibration currency"() {
    setup:
    def logConsumer = Mock(Consumer)
    def curveGroup = new CurveGroup(id: "cgId")
    def calibrationOptions = calibrationOptions(curveGroup, DISCOUNT_EUR, OIS)
    def calibrationBundles = [new CalibrationBundle("test", Currency.EUR, [
        CalibrationDiscountSubGroup.newOfBase(Currency.EUR, Set.of(new CalibrationEntry(eurOis(), CurveShifts.empty(), Currency.EUR)))
      ])]

    when:
    def calibratedCurves = calibrationService.calibrateCurves(calibrationOptions, ogMarketData(), calibrationBundles, logConsumer)

    then:
    1 * curveRepository.getActiveCurves("cgId", STATE_DATE) >> [eurOis(), usdOis()]
    1 * settingsService.calibrationMeasures(STATE_DATE, VAL_DT) >> CalibrationMeasures.PAR_SPREAD
    1 * settingsService.inflationSeasonalities(STATE_DATE) >> new InflationSeasonalitySettings()
    4 * logConsumer.accept([])

    1 * resultRepository.updateCurveCalibrationResults(
    _ as CurveCalibrationResult
    ) >> {
      CurveCalibrationResult result -> {
        assert result.discountFactorPoints != null
        assert result.discountFactorPoints[0].date == VAL_DT.plusDays(5)
        assert result.nodeValues != null
        assert result.nodeValues[0].date == VAL_DT.plusDays(5)
        assert result.nodesUsedInCalibration == ["1D", "1Y"]
        assert result.discountCurrency == "EUR"
        assert result.marketDataGroupId == calibrationOptions.marketStateKey.marketDataGroupId
        assert result.marketDataSource == calibrationOptions.marketStateKey.marketDataSource.name()
        assert result.stateDate == STATE_DATE.getActualDate()
        assert result.curveDate == VAL_DT
        assert result.valuationDate == VAL_DT
        return result
      }
    }

    and:
    calibratedCurves.isRight()
    calibratedCurves.getOrNull().getRatesProvider().discountCurves.size() == 1
  }

  def "should calibrate curves with LOCAL calibration currency"() {
    setup:
    def logConsumer = Mock(Consumer)
    def curveGroup = new CurveGroup(id: "cgId")
    def calibrationOptions = calibrationOptions(curveGroup, LOCAL_CURRENCY, OIS)
    def calibrationBundles = [
      new CalibrationBundle("EUR OIS", Currency.EUR, [
        CalibrationDiscountSubGroup.newOfBase(Currency.EUR, Set.of(new CalibrationEntry(eurOis(), CurveShifts.empty(), Currency.EUR)))
      ]),
      new CalibrationBundle("USD OIS", Currency.USD, [
        CalibrationDiscountSubGroup.newOfBase(Currency.USD, Set.of(new CalibrationEntry(usdOis(), CurveShifts.empty(), Currency.USD)))
      ]),
      new CalibrationBundle("EUR OIS", Currency.EUR, [
        CalibrationDiscountSubGroup.newOfBase(Currency.EUR, Set.of(
        new CalibrationEntry(eurOis(), CurveShifts.empty(), Currency.EUR),
        new CalibrationEntry(eur3m(), CurveShifts.empty(), null),
        ))
      ]),
    ]

    when:
    def calibratedCurves = calibrationService.calibrateCurves(calibrationOptions, ogMarketData(), calibrationBundles, logConsumer)

    then:
    1 * curveRepository.getActiveCurves("cgId", STATE_DATE) >> [eurOis(), usdOis(), eur3m()]
    1 * settingsService.calibrationMeasures(STATE_DATE, VAL_DT) >> CalibrationMeasures.PAR_SPREAD
    1 * settingsService.inflationSeasonalities(STATE_DATE) >> new InflationSeasonalitySettings()
    3 * resultRepository.updateCurveCalibrationResults(_ as CurveCalibrationResult)
    13 * logConsumer.accept([])

    and:
    calibratedCurves.isRight()
    calibratedCurves.getOrNull().getRatesProvider().discountCurves.size() == 2
  }

  def "should calibrate curves with LOCAL calibration currency XXX"() {
    setup:
    def logConsumer = Mock(Consumer)
    def curveGroup = new CurveGroup(id: "cgId")
    def calibrationOptions = calibrationOptions(curveGroup, LOCAL_CURRENCY, OIS)
    def calibrationGroups = []

    when:
    def calibratedCurves = calibrationService.calibrateCurves(calibrationOptions, ogMarketData(), calibrationGroups, logConsumer)

    then:
    1 * curveRepository.getActiveCurves("cgId", STATE_DATE) >> []
    1 * settingsService.calibrationMeasures(STATE_DATE, VAL_DT) >> CalibrationMeasures.PAR_SPREAD
    1 * settingsService.inflationSeasonalities(STATE_DATE) >> new InflationSeasonalitySettings()
    0 * resultRepository.updateCurveCalibrationResults(_ as CurveCalibrationResult)
    logConsumer.accept([])

    and:
    calibratedCurves.isLeft()
    calibratedCurves.left().get() == CALIBRATION_ERROR.entity("No curves calibrated successfully!")
  }


  def calibrationOptions(CurveGroup curveGroup, CalculationDiscountingType currency, CalculationStrippingType type) {
    new CalibrationOptions(
    curveGroup,
    EMPTY_FILTER,
    Mock(CalculationShifts) {
      curveShifts(_ as String) >> CurveShifts.empty()
    } as CalculationShifts,
    CurveConfigMarketStateKey.configMarketKey(
    "marketDataGroupId",
    "curveConfigurationId",
    MarketDataSourceType.OVERLAY,
    STATE_DATE,
    VAL_DT,
    InstrumentPriceRequirements.bidRequirements()
    ),
    currency,
    type,
    VAL_DT,
    Currency.EUR)
  }
}
