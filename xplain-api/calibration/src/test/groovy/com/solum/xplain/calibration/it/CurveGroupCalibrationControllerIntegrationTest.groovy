package com.solum.xplain.calibration.it

import static com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus.CALIBRATED
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_SECONDARY
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.MID_PRICE
import static com.solum.xplain.core.error.Error.CALIBRATION_INFO
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_USD
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.LIBOR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static com.solum.xplain.core.teams.TeamBuilder.teamWithId
import static org.hamcrest.Matchers.closeTo
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.opaqueToken
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static spock.lang.Retry.Mode.SETUP_FEATURE_CLEANUP
import static spock.util.matcher.HamcrestSupport.that

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calibration.CalibrationIntegrationSampleData
import com.solum.xplain.calibration.value.CalibrateCurveForm
import com.solum.xplain.calibration.value.CalibrationOptionsForm
import com.solum.xplain.core.audit.entity.AuditEntryItem
import com.solum.xplain.core.authentication.Authorities
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.value.CurveDiscountingForm
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveCalibrationResult
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.mdvalue.entity.MarketDataValueVersion
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.http.MediaType
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Retry
import spock.lang.Unroll

@Retry(mode = SETUP_FEATURE_CLEANUP, count = 3, delay = 100)
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureMockMvc
class CurveGroupCalibrationControllerIntegrationTest extends IntegrationSpecification implements CalibrationIntegrationSampleData {

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper
  @Autowired
  private MongoOperations operations

  private static final String CURVE_GROUP_API = "/curve-group"

  def setup() {
    setupCommonCalibrationData()
  }

  def cleanup() {
    cleanupCalibrationData()
  }

  def "should return error when user has insufficient permission"() {
    setup:
    def authority = new SimpleGrantedAuthority(Authorities.MODIFY_BOND_CURVE)
    XplainPrincipal badUser = new XplainPrincipal("id", "name", [teamWithId().getId()], ["A": "B"], [authority], [], [])

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(opaqueToken().principal(badUser))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 403 }
  }

  def "should calibrate OIS DUAL + USD with curves USD SOFR USD 3M US CPI EUR/USD"() {
    setup:
    mockUsdSofr()
    mockUsd3m()
    mockUsCpi()
    mockEurUsd()
    mockEur3m()
    mockEur1mWithoutNodes()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()
    def response = results.getResponse()
    print(response.getContentAsString())

    then:
    with(response) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 4
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, US CPI U]"
    logs[1].getReason() == CALIBRATION_INFO
    logs[1].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, USD 3M]"
    logs[2].getReason() == CALIBRATION_INFO
    logs[2].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"
    logs[3].getReason() == CALIBRATION_INFO
    logs[3].getDescription() == "Calibration OIS (Dual) USD. Foreign discount group with base discount curve USD SOFR [USD], foreign discount curve EUR/USD [EUR], curves [USD SOFR, EUR/USD, EUR 3M, USD 3M]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 5

    calibrationResults[0].getCurveId() == "EUR 3M"
    calibrationResults[0].getDiscountCurrency() == null
    that calibrationResults[0].getNodeValues()[0].getCalculatedValue(), closeTo(1.0119d, 0.0001d)
    that calibrationResults[0].getNodeValues()[1].getCalculatedValue(), closeTo(0.5840d, 0.0001d)
    calibrationResults[0].getDiscountFactorPoints() == []

    calibrationResults[1].getCurveId() == "EUR/USD"
    calibrationResults[1].getDiscountCurrency() == "EUR"
    that calibrationResults[1].getNodeValues()[0].getCalculatedValue(), closeTo(2.0192d, 0.0001d)
    that calibrationResults[1].getNodeValues()[1].getCalculatedValue(), closeTo(1.1664d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9781d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.3085d, 0.0001d)

    calibrationResults[2].getCurveId() == "US CPI U"
    calibrationResults[2].getDiscountCurrency() == null
    that calibrationResults[2].getNodeValues()[0].getCalculatedValue(), closeTo(2.2558d, 0.0001d)
    that calibrationResults[2].getNodeValues()[1].getCalculatedValue(), closeTo(4.1555d, 0.0001d)
    calibrationResults[2].getDiscountFactorPoints() == []

    calibrationResults[3].getCurveId() == "USD 3M"
    calibrationResults[3].getDiscountCurrency() == null
    that calibrationResults[3].getNodeValues()[0].getCalculatedValue(), closeTo(0.9346d, 0.0001d)
    that calibrationResults[3].getNodeValues()[1].getCalculatedValue(), closeTo(0.8172d, 0.0001d)
    calibrationResults[3].getDiscountFactorPoints() == []

    calibrationResults[4].getCurveId() == "USD SOFR"
    calibrationResults[4].getDiscountCurrency() == "USD"
    that calibrationResults[4].getNodeValues()[0].getCalculatedValue(), closeTo(1.0124d, 0.0001d)
    that calibrationResults[4].getNodeValues()[1].getCalculatedValue(), closeTo(0.7026d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9889d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4924d, 0.0001d)

    def resultCurveGroup = operations.findAll(CurveGroup.class)
    resultCurveGroup[0].getCalibrationCurrency() == DISCOUNT_USD
    resultCurveGroup[0].getCalibrationStrippingType() == OIS
    resultCurveGroup[0].getCalibrationPriceRequirements() == bidRequirements()
    resultCurveGroup[0].getCalibrationStatus() == CALIBRATED
    resultCurveGroup[0].getCalibratedAt() != null
    resultCurveGroup[0].getCalibrationDate() == DATE
  }

  def "should calibrate OIS DUAL + LOCAL with curves USD SOFR USD 3M US CPI EUR/USD"() {
    setup:
    mockUsdSofr()
    mockUsd3m()
    mockUsCpi()
    mockEurUsd()
    mockEur3m()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(LOCAL_CURRENCY.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 5
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, US CPI U]"
    logs[1].getReason() == CALIBRATION_INFO
    logs[1].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, USD 3M]"
    logs[2].getReason() == CALIBRATION_INFO
    logs[2].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"
    logs[3].getReason() == CALIBRATION_INFO
    logs[3].getDescription() == "Calibration Single EUR-EURIBOR-3M. Base discount group with discount curve EUR 3M [EUR], curves [EUR 3M]"
    logs[4].getReason() == CALIBRATION_INFO
    logs[4].getDescription() == "Calibration Single EUR-EURIBOR-3M. Foreign discount group with base discount curve EUR 3M [EUR], foreign discount curve EUR/USD [USD], curves [EUR 3M, EUR/USD, USD 3M]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }

    calibrationResults.size() == 5

    calibrationResults[0].getCurveId() == "EUR 3M"
    calibrationResults[0].getDiscountCurrency() == "EUR"
    that calibrationResults[0].getNodeValues()[0].getCalculatedValue(), closeTo(0.9750d, 0.0001d)
    that calibrationResults[0].getNodeValues()[1].getCalculatedValue(), closeTo(0.6946d, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9920d, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4973d, 0.0001d)

    calibrationResults[1].getCurveId() == "EUR/USD"
    calibrationResults[1].getDiscountCurrency() == "USD"
    that calibrationResults[1].getNodeValues()[0].getCalculatedValue(), closeTo(-0.0271d, 0.0001d)
    that calibrationResults[1].getNodeValues()[1].getCalculatedValue(), closeTo(0.2533d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(1.0002d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.7745d, 0.0001d)

    calibrationResults[2].getCurveId() == "US CPI U"
    calibrationResults[2].getDiscountCurrency() == null
    that calibrationResults[2].getNodeValues()[0].getCalculatedValue(), closeTo(2.2558d, 0.0001d)
    that calibrationResults[2].getNodeValues()[1].getCalculatedValue(), closeTo(4.1555d, 0.0001d)
    calibrationResults[2].getDiscountFactorPoints() == []

    calibrationResults[3].getCurveId() == "USD 3M"
    calibrationResults[3].getDiscountCurrency() == null
    that calibrationResults[3].getNodeValues()[0].getCalculatedValue(), closeTo(0.9346d, 0.0001d)
    that calibrationResults[3].getNodeValues()[1].getCalculatedValue(), closeTo(0.8172d, 0.0001d)
    calibrationResults[3].getDiscountFactorPoints() == []

    calibrationResults[4].getCurveId() == "USD SOFR"
    calibrationResults[4].getDiscountCurrency() == "USD"
    that calibrationResults[4].getNodeValues()[0].getCalculatedValue(), closeTo(1.0124d, 0.0001d)
    that calibrationResults[4].getNodeValues()[1].getCalculatedValue(), closeTo(0.7026d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9889d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4924d, 0.0001d)

    def resultCurveGroup = operations.findAll(CurveGroup.class)
    resultCurveGroup[0].getCalibrationCurrency() == LOCAL_CURRENCY
    resultCurveGroup[0].getCalibrationStrippingType() == OIS
    resultCurveGroup[0].getCalibrationPriceRequirements() == bidRequirements()
    resultCurveGroup[0].getCalibrationStatus() == CALIBRATED
    resultCurveGroup[0].getCalibratedAt() != null
    resultCurveGroup[0].getCalibrationDate() == DATE
  }

  def "should calibrate SINGLE + LOCAL with curves USD SOFR USD 3M US CPI EUR/USD"() {
    setup:
    mockUsdSofr()
    mockUsd3m()
    mockUsCpi()
    mockEurUsd()
    mockEur3m()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(LOCAL_CURRENCY.name(), LIBOR.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 5
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration Single EUR-EURIBOR-3M. Base discount group with discount curve EUR 3M [EUR], curves [EUR 3M]"
    logs[1].getReason() == CALIBRATION_INFO
    logs[1].getDescription() == "Calibration Single EUR-EURIBOR-3M. Foreign discount group with base discount curve EUR 3M [EUR], foreign discount curve EUR/USD [USD], curves [EUR 3M, EUR/USD, USD 3M]"
    logs[2].getReason() == CALIBRATION_INFO
    logs[2].getDescription() == "Calibration Single USD-LIBOR-3M. Base discount group with discount curve USD 3M [USD], curves [USD 3M, US CPI U]"
    logs[3].getReason() == CALIBRATION_INFO
    logs[3].getDescription() == "Calibration Single USD-LIBOR-3M. Base discount group with discount curve USD 3M [USD], curves [USD 3M]"
    logs[4].getReason() == CALIBRATION_INFO
    logs[4].getDescription() == "Calibration Single USD-SOFR. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 5

    calibrationResults[0].getCurveId() == "EUR 3M"
    calibrationResults[0].getDiscountCurrency() == "EUR"
    that calibrationResults[0].getNodeValues()[0].getCalculatedValue(), closeTo(0.9750d, 0.0001d)
    that calibrationResults[0].getNodeValues()[1].getCalculatedValue(), closeTo(0.6946d, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9920d, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4973d, 0.0001d)

    calibrationResults[1].getCurveId() == "EUR/USD"
    calibrationResults[1].getDiscountCurrency() == "USD"
    that calibrationResults[1].getNodeValues()[0].getCalculatedValue(), closeTo(-0.0271d, 0.0001d)
    that calibrationResults[1].getNodeValues()[1].getCalculatedValue(), closeTo(0.2533d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(1.0002d, 0.0001d)
    that calibrationResults[1].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.7745d, 0.0001d)

    calibrationResults[2].getCurveId() == "US CPI U"
    calibrationResults[2].getDiscountCurrency() == null
    that calibrationResults[2].getNodeValues()[0].getCalculatedValue(), closeTo(2.2558d, 0.0001d)
    that calibrationResults[2].getNodeValues()[1].getCalculatedValue(), closeTo(4.1555d, 0.0001d)
    calibrationResults[2].getDiscountFactorPoints() == []

    calibrationResults[3].getCurveId() == "USD 3M"
    calibrationResults[3].getDiscountCurrency() == "USD"
    that calibrationResults[3].getNodeValues()[0].getCalculatedValue(), closeTo(0.9407d, 0.0001d)
    that calibrationResults[3].getNodeValues()[1].getCalculatedValue(), closeTo(0.7991d, 0.0001d)
    that calibrationResults[3].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9897d, 0.0001d)
    that calibrationResults[3].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4467d, 0.0001d)

    calibrationResults[4].getCurveId() == "USD SOFR"
    calibrationResults[4].getDiscountCurrency() == "USD"
    that calibrationResults[4].getNodeValues()[0].getCalculatedValue(), closeTo(1.0124d, 0.0001d)
    that calibrationResults[4].getNodeValues()[1].getCalculatedValue(), closeTo(0.7026d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9889d, 0.0001d)
    that calibrationResults[4].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4924d, 0.0001d)

    def resultCurveGroup = operations.findAll(CurveGroup.class)
    resultCurveGroup[0].getCalibrationCurrency() == LOCAL_CURRENCY
    resultCurveGroup[0].getCalibrationStrippingType() == LIBOR
    resultCurveGroup[0].getCalibrationPriceRequirements() == bidRequirements()
    resultCurveGroup[0].getCalibrationStatus() == CALIBRATED
    resultCurveGroup[0].getCalibratedAt() != null
    resultCurveGroup[0].getCalibrationDate() == DATE
  }

  def "should calibrate LOCAL with US CPI U and US CPI U LCH curves"() {
    setup:
    mockUsdSofr()
    mockUsCpi()
    mockUsCpiLch()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(LOCAL_CURRENCY.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 3
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, US CPI U LCH]"
    logs[1].getReason() == CALIBRATION_INFO
    logs[1].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR, US CPI U]"
    logs[2].getReason() == CALIBRATION_INFO
    logs[2].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 3

    calibrationResults[0].getCurveId() == "US CPI U"
    calibrationResults[0].getDiscountCurrency() == null
    that calibrationResults[0].getNodeValues()[0].getCalculatedValue(), closeTo(2.2558d, 0.0001d)
    that calibrationResults[0].getNodeValues()[1].getCalculatedValue(), closeTo(4.1555d, 0.0001d)
    calibrationResults[0].getDiscountFactorPoints() == []

    calibrationResults[1].getCurveId() == "US CPI U LCH"
    calibrationResults[1].getDiscountCurrency() == null
    that calibrationResults[1].getNodeValues()[0].getCalculatedValue(), closeTo(2.2558d, 0.0001d)
    that calibrationResults[1].getNodeValues()[1].getCalculatedValue(), closeTo(4.1555d, 0.0001d)

    calibrationResults[2].getCurveId() == "USD SOFR"
    calibrationResults[2].getDiscountCurrency() == "USD"
    that calibrationResults[2].getNodeValues()[0].getCalculatedValue(), closeTo(1.0124d, 0.0001d)
    that calibrationResults[2].getNodeValues()[1].getCalculatedValue(), closeTo(0.7026d, 0.0001d)
    that calibrationResults[2].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(0.9889d, 0.0001d)
    that calibrationResults[2].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(0.4924d, 0.0001d)
  }

  def "should calibrate USD credit curve"() {
    setup:
    mockUsdSofrIsdaCompliant()
    mockUsdCreditCurve()
    mockEurCreditCurve()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 1
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"


    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 1

    def creditCurves = operations.findAll(CreditCurve.class).sort { it.getName() }
    creditCurves.size() == 2
    creditCurves[0].name == "SELF_EUR_SNRFOR_CR14"
    creditCurves[0].chartPoints == []

    creditCurves[1].name == "SELF_USD_SNRFOR_CR14"
    creditCurves[1].chartPoints.size() == 14
    that creditCurves[1].chartPoints[0].calculatedValue, closeTo(1.0d, 0d)
    that creditCurves[1].chartPoints[0].maturity, closeTo(0d, 0d)

    that creditCurves[1].chartPoints[6].calculatedValue, closeTo(0.6501d, 0.0001d)
    that creditCurves[1].chartPoints[6].maturity, closeTo(0.4657d, 0.0001d)

    that creditCurves[1].chartPoints[13].calculatedValue, closeTo(0.4079d, 0.0001d)
    that creditCurves[1].chartPoints[13].maturity, closeTo(0.9671d, 0.0001d)
  }

  def "should calibrate credit index curve"() {
    setup:
    mockUsdSofr()
    mockUsdCreditIndexCurve()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 1
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 1

    def creditCurves = operations.findAll(CreditCurve.class).sort { it.getName() }
    creditCurves.size() == 1
    creditCurves[0].name == "2I65BRZB9_USD"
    that creditCurves[0].chartPoints[0].calculatedValue, closeTo(1.0d, 0d)
    that creditCurves[0].chartPoints[0].maturity, closeTo(0d, 0d)

    that creditCurves[0].chartPoints[6].calculatedValue, closeTo(0.6464d, 0.0001d)
    that creditCurves[0].chartPoints[6].maturity, closeTo(0.4657d, 0.0001d)

    that creditCurves[0].chartPoints[13].calculatedValue, closeTo(0.4052d, 0.0001d)
    that creditCurves[0].chartPoints[13].maturity, closeTo(0.9671d, 0.0001d)
  }

  def "should calibrate credit index tranche curve"() {
    setup:
    mockUsdSofr()
    mockUsdCreditIndexTrancheCurve()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 1
    logs[0].getReason() == CALIBRATION_INFO
    logs[0].getDescription() == "Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 1

    def creditCurves = operations.findAll(CreditCurve.class).sort { it.getName() }
    creditCurves.size() == 1
    creditCurves[0].name == "CDX.NA.IG.S39.V1.3-7_USD"
    that creditCurves[0].chartPoints[0].calculatedValue, closeTo(1.0d, 0d)
    that creditCurves[0].chartPoints[0].maturity, closeTo(0d, 0d)

    that creditCurves[0].chartPoints[6].calculatedValue, closeTo(0.6464d, 0.0001d)
    that creditCurves[0].chartPoints[6].maturity, closeTo(0.4657d, 0.0001d)

    that creditCurves[0].chartPoints[13].calculatedValue, closeTo(0.4052d, 0.0001d)
    that creditCurves[0].chartPoints[13].maturity, closeTo(0.9671d, 0.0001d)
  }

  def "should calibrate bond yield curve"() {
    setup:
    mockBondCurve()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 0

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 0

    def bondResults = operations.findAll(BondCurveCalibrationResult.class)
    bondResults.size() == 1
    bondResults[0].chartPoints.size() == 2
    bondResults[0].chartPoints[0].date == DATE
    bondResults[0].chartPoints[0].calculatedValue == 1.0d
    bondResults[0].chartPoints[1].date == DATE.plusDays(1)
    bondResults[0].chartPoints[1].calculatedValue == 1.0d
  }

  @Unroll
  def "should calibrate usd curve with #priceType then node values #nv1 #nv2 and DF #df1 #df2"() {
    setup:
    mockUsdSofr()
    operations().save(new MarketDataValue(MD_GROUP_ID, TICKER, PROVIDER_BOTH, DATE, ASK, [new MarketDataValueVersion(value: 2, recordDate: DATE_TIME)]))

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_SECONDARY.name(), DATE, DATE, DATE, new InstrumentPriceRequirementsForm(dscCurvesPriceType: priceType)),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    expect:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    with(results.getResponse()) { getStatus() == 200 }

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 1
    calibrationResults[0].getCurveId() == "USD SOFR"
    calibrationResults[0].getDiscountCurrency() == "USD"
    that calibrationResults[0].getNodeValues()[0].getCalculatedValue(), closeTo(nv1, 0.0001d)
    that calibrationResults[0].getNodeValues()[1].getCalculatedValue(), closeTo(nv2, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[0].getCalculatedValue(), closeTo(df1, 0.0001d)
    that calibrationResults[0].getDiscountFactorPoints()[1].getCalculatedValue(), closeTo(df2, 0.0001d)

    where:
    priceType | nv1     | nv2     | df1     | df2
    BID_PRICE | 1.0124d | 0.7026d | 0.9889d | 0.4924d
    ASK_PRICE | 2.0221d | 1.1152d | 0.9780d | 0.3248d
    MID_PRICE | 1.5176d | 0.9294d | 0.9835d | 0.3917d
  }

  @Unroll
  def "should fail to calibrate usd curve with #priceType #provider then #expectedLog"() {
    setup:
    mockUsdSofr()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, provider.name(), DATE, DATE, DATE, new InstrumentPriceRequirementsForm(dscCurvesPriceType: priceType)),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    expect:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    with(results.getResponse()) { getStatus() == 422 }
    def logs = operations.findAll(AuditEntryItem.class).collect { it.getReason().name() + " " + it.getDescription() }.sort().join("\n")
    logs == expectedLog
    operations.findAll(CurveCalibrationResult.class).isEmpty()

    where:
    priceType | provider      | expectedLog
    ASK_PRICE | RAW_PRIMARY   | "CALIBRATION_ERROR Curve USD SOFR must have at least 2 nodes with data for the selected valuation date\n" +
      "CALIBRATION_INFO Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]\n" +
      "CALIBRATION_WARNING Market data key: 1D_USD-FIXED-TERM-SOFR-OIS does not have mapping for required price type: Ask\n" +
      "CALIBRATION_WARNING Market data key: 1Y_USD-FIXED-TERM-SOFR-OIS does not have mapping for required price type: Ask"
    MID_PRICE | RAW_PRIMARY   | "CALIBRATION_ERROR Curve USD SOFR must have at least 2 nodes with data for the selected valuation date\n" +
      "CALIBRATION_INFO Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]\n" +
      "CALIBRATION_WARNING Market data key: 1D_USD-FIXED-TERM-SOFR-OIS does not have mapping for required price type: Mid or Bid/Ask\n" +
      "CALIBRATION_WARNING Market data key: 1Y_USD-FIXED-TERM-SOFR-OIS does not have mapping for required price type: Mid or Bid/Ask"
    ASK_PRICE | RAW_SECONDARY | "CALIBRATION_ERROR Market data required by curve USD SOFR not found for identifier: 'QuoteId:XPL~1D_USD-FIXED-TERM-SOFR-OIS/MarketValue' of class 'QuoteId'\n" +
      "CALIBRATION_INFO Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"
    MID_PRICE | RAW_SECONDARY | "CALIBRATION_ERROR Market data required by curve USD SOFR not found for identifier: 'QuoteId:XPL~1D_USD-FIXED-TERM-SOFR-OIS/MarketValue' of class 'QuoteId'\n" +
      "CALIBRATION_INFO Calibration OIS (Dual) USD. Base discount group with discount curve USD SOFR [USD], curves [USD SOFR]"
  }

  def "should calibrate full curve group"() {
    setup:
    mockUsdSofr()
    mockUsd3m()
    mockUsCpi()
    mockEurUsd()
    mockEur3m()
    mockEur1mWithoutNodes()

    mockUsdCreditCurve()
    mockEurCreditCurve()
    mockUsdCreditIndexCurve()

    mockBondCurve()

    def form = new CalibrateCurveForm(
      new CalibrationOptionsForm(MD_GROUP_ID, CURVE_CONFIG_ID, RAW_PRIMARY.name(), DATE, DATE, DATE, null),
      new CurveDiscountingForm(DISCOUNT_USD.name(), OIS.name(), "USD")
      )

    when:
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/{curveGroupId}/calibrate", CURVE_GROUP_ID)
      .with(csrf())
      .with(authentication())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    with(results.getResponse()) { getStatus() == 200 }

    def logs = operations.findAll(AuditEntryItem.class).sort { it.getDescription() }
    logs.size() == 4
    logs.stream().allMatch { v -> v.getReason() == CALIBRATION_INFO }

    def calibrationResults = operations.findAll(CurveCalibrationResult.class).sort { it.getCurveId() }
    calibrationResults.size() == 5

    def bondResults = operations.findAll(BondCurveCalibrationResult.class)
    bondResults.size() == 1

    def creditCurves = operations.findAll(CreditCurve.class)
    creditCurves.size() == 3
    creditCurves.stream().noneMatch { c -> c.getChartPoints().isEmpty() }
  }

  static authentication() {
    def authority = new SimpleGrantedAuthority(Authorities.RUN_CURVE_CONFIG)
    XplainPrincipal USER = new XplainPrincipal("id", "name", [teamWithId().getId()], ["A": "B"], [authority], [], [])
    opaqueToken().principal(USER)
  }

  @Override
  MongoOperations operations() {
    return operations
  }
}
