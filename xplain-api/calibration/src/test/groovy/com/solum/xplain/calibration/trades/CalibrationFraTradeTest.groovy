package com.solum.xplain.calibration.trades

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.solum.xplain.extensions.index.OffshoreIndices.THB_THBFIX_OFFSHORE_6M

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.product.common.BuySell
import com.opengamma.strata.product.fra.type.FraConventions
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.CalculationType
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class CalibrationFraTradeTest extends Specification {
  def "should correctly resolve FRA trade"() {
    setup:
    def fra = FraConventions.of(EUR_EURIBOR_3M)
      .createTrade(LocalDate.of(2018, 1, 1),
      Period.ofMonths(1),
      Period.ofMonths(12),
      BuySell.BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())

    def calibrationTrade = new CalibrationFraTrade(fra, "label")

    when:
    def result = calibrationTrade.toTradeValue()

    then:
    result.isRight()
    result.getOrNull().productType == CoreProductType.FRA

    def tradeDetails = result.getOrNull().tradeDetails
    tradeDetails.startDate == LocalDate.of(2018, 2, 5)
    tradeDetails.endDate == LocalDate.of(2019, 1, 3)
    tradeDetails.info.tradeCurrency == Currency.EUR.code

    def iborLeg = tradeDetails.receiveLeg
    iborLeg.type == CalculationType.IBOR
    iborLeg.notional == 10_000_000d
    iborLeg.currency == "EUR"
    iborLeg.index == EUR_EURIBOR_3M.getName()
    iborLeg.dayCount == EUR_EURIBOR_3M.dayCount.getName()
    !iborLeg.isOffshore

    def fixLeg = tradeDetails.payLeg
    fixLeg.type == CalculationType.FIXED
    fixLeg.notional == 10_000_000d
    fixLeg.currency == "EUR"
    fixLeg.dayCount == EUR_EURIBOR_3M.getDayCount().getName()
    fixLeg.initialValue == 0.025d
    !fixLeg.isOffshore
  }
}
