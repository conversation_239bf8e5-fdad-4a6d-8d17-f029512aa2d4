package com.solum.xplain.calibration.trades

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.product.common.BuySell
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.CalculationType
import java.time.LocalDate
import java.time.Period
import spock.lang.Specification

class CalibrationFixingDepositTradeTest extends Specification {

  def "should correctly resolve Fixing Deposit trade"() {
    setup:
    def fra = IborFixingDepositConvention.of(EUR_EURIBOR_3M)
      .createTrade(LocalDate.of(2018, 1, 1),
      Period.ofMonths(6),
      BuySell.BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())

    def calibrationTrade = new CalibrationFixingDepositTrade(fra, "label")

    when:
    def result = calibrationTrade.toTradeValue()

    then:
    result.isRight()
    result.getOrNull().productType == CoreProductType.IRS
    def tradeDetails = result.getOrNull().tradeDetails
    tradeDetails.startDate == LocalDate.of(2018, 1, 3)
    tradeDetails.endDate == LocalDate.of(2018, 7, 3)
    tradeDetails.businessDayConvention == EUR_EURIBOR_3M.getMaturityDateOffset().getAdjustment().getConvention().getName()
    tradeDetails.info.tradeCurrency == Currency.EUR.code

    def iborLeg = tradeDetails.payLeg
    iborLeg.getType() == CalculationType.IBOR
    iborLeg.notional == 10_000_000d
    iborLeg.currency == "EUR"
    iborLeg.index == EUR_EURIBOR_3M.getName()
    iborLeg.dayCount == EUR_EURIBOR_3M.dayCount.toString()
    !iborLeg.isOffshore

    def fixLeg = tradeDetails.receiveLeg
    fixLeg.getType() == CalculationType.FIXED
    fixLeg.notional == 10_000_000d
    fixLeg.currency == "EUR"
    fixLeg.dayCount == EUR_EURIBOR_3M.getDayCount().getName()
    fixLeg.initialValue == 0.025d
    !fixLeg.isOffshore
  }

  def "should correctly resolve KRW Fixing Deposit trade"() {
    setup:
    def index = IborIndex.of("KRW-CD-13W")
    def fra = IborFixingDepositConvention.of(index)
      .createTrade(LocalDate.of(2018, 1, 1),
      Period.ofMonths(6),
      BuySell.BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())

    def calibrationTrade = new CalibrationFixingDepositTrade(fra, "label")

    when:
    def result = calibrationTrade.toTradeValue()

    then:
    result.isRight()
    result.getOrNull().productType == CoreProductType.IRS
    def tradeDetails = result.getOrNull().tradeDetails
    tradeDetails.startDate == LocalDate.of(2018, 1, 2)
    tradeDetails.endDate == LocalDate.of(2018, 7, 2)
    tradeDetails.businessDayConvention == index.getMaturityDateOffset().getAdjustment().getConvention().getName()
    tradeDetails.info.tradeCurrency == Currency.KRW.code

    def iborLeg = tradeDetails.payLeg
    iborLeg.getType() == CalculationType.IBOR
    iborLeg.notional == 10_000_000d
    iborLeg.currency == "KRW"
    iborLeg.index == index.getName()
    iborLeg.dayCount == index.dayCount.toString()
    iborLeg.accrualFrequency == "3M"
    iborLeg.paymentFrequency == "3M"
    !iborLeg.isOffshore

    def fixLeg = tradeDetails.receiveLeg
    fixLeg.getType() == CalculationType.FIXED
    fixLeg.notional == 10_000_000d
    fixLeg.currency == "KRW"
    fixLeg.dayCount == index.getDayCount().getName()
    fixLeg.initialValue == 0.025d
    fixLeg.accrualFrequency == "3M"
    fixLeg.paymentFrequency == "3M"
    !fixLeg.isOffshore
  }
}
