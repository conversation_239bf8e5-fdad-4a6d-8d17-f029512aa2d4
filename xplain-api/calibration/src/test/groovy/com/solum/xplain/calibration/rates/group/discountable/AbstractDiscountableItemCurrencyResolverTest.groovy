package com.solum.xplain.calibration.rates.group.discountable

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import spock.lang.Specification

class AbstractDiscountableItemCurrencyResolverTest extends Specification {
  def resolver = new MockDiscountableItemCurrencyResolver()

  def "should return correct discount currency"() {
    expect:
    resolver.resolveCurrency(item) == currency

    where:
    item            | currency
    mockItem(false) | Currency.EUR
    mockItem(true)  | Currency.USD
  }

  def mockItem(boolean isOffshore) {
    def item = Mock(DiscountableItem)
    1 * item.requirements() >> new DiscountableItemRequirements(Currency.ZAR, isOffshore, null, null, null, null, null, Set.of())
    return item
  }

  class MockDiscountableItemCurrencyResolver extends AbstractDiscountableItemCurrencyResolver<Curve> {
    @Override
    protected Currency resolveOnshoreCurrency(DiscountableItem<Curve> item) {
      return Currency.EUR
    }
  }
}
