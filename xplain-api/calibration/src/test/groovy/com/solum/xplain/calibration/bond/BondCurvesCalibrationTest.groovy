package com.solum.xplain.calibration.bond

import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveBuilder
import com.solum.xplain.core.curvegroup.bondcurve.entity.BondCurveNodeBuilder
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter
import com.solum.xplain.core.market.MarketDataSample
import java.time.LocalDate
import spock.lang.Specification

class BondCurvesCalibrationTest extends Specification {
  static REF_DATA = ReferenceData.standard()

  def "should calibrate bond curves"() {
    setup:
    def errors = []
    def curve = new BondCurveBuilder()
    .nodes([
      new BondCurveNodeBuilder().maturityDate(LocalDate.of(2023, 1, 1)).build(),
      new BondCurveNodeBuilder().maturityDate(LocalDate.of(2024, 1, 1)).build()])
    .build()
    def calibration = BondCurvesCalibration.newOf(
    [curve],
    MarketDataSample.ogMarketData(),
    ValidNodesFilter.EMPTY_FILTER,
    REF_DATA,
    l -> errors.addAll(l)
    )

    when:
    def result = calibration.calibrate()

    then:
    result != null
    !result.getChart(curve.getName()).isEmpty()
    errors.isEmpty()
  }

  def "should fail to calibrate bond curves"() {
    setup:
    def errors = []
    def curve = new BondCurveBuilder().nodes([]).build()
    def calibration = BondCurvesCalibration.newOf(
    [curve],
    MarketDataSample.ogMarketData(),
    ValidNodesFilter.EMPTY_FILTER,
    REF_DATA,
    l -> errors.addAll(l)
    )

    when:
    def result = calibration.calibrate()

    then:
    result != null
    result == new BondCurveCalibrationResults([:], REF_DATA)

    errors.size() == 1
    errors[0] == CALIBRATION_ERROR.entity("Insufficient nodes for curve UKGT")
  }

  def "should return empty results when no curves"() {
    setup:
    def errors = []
    def calibration = BondCurvesCalibration.newOf(
    [],
    MarketDataSample.ogMarketData(),
    ValidNodesFilter.EMPTY_FILTER,
    REF_DATA,
    l -> errors.addAll(l)
    )

    when:
    def result = calibration.calibrate()

    then:
    result != null
    result == new BondCurveCalibrationResults([:], REF_DATA)
    errors.isEmpty()
  }
}
