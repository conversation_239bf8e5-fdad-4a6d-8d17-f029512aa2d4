package com.solum.xplain.calibration.rates.group.single

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calibration.rates.group.IndexDiscountingGroupKey
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItem
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemIndexResolver
import com.solum.xplain.calibration.rates.group.discountable.DiscountableItemRequirements
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import io.atlassian.fugue.Either
import spock.lang.Specification

class SingleDiscountingGroupsTest extends Specification {

  def "should correctly resolve item discounting when single"() {
    setup:
    def expectedDsc = Mock(SingleDiscountingGroup)
    def item = Mock(DiscountableItem)
    1 * item.requirements() >> new DiscountableItemRequirements(Currency.EUR, false, ClearingHouse.NONE, null, null, null, null, Set.of())
    def indexResolver = Mock(DiscountableItemIndexResolver)
    1 * indexResolver.resolveIndex(item, Set.of()) >> Either.right(EUR_EURIBOR_3M)

    def discountings = new SingleDiscountingGroups(indexResolver, Set.of(), [(new IndexDiscountingGroupKey(EUR_EURIBOR_3M, ClearingHouse.NONE)): (expectedDsc)])

    when:
    def res = discountings.itemDiscountingGroup(item)

    then:
    res == expectedDsc
  }
}
