package com.solum.xplain.calibration.credit


import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.pricer.credit.CreditDiscountFactors
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities
import com.solum.xplain.calibration.CalibrationSetSample
import com.solum.xplain.core.calibration.CurveSample
import com.solum.xplain.core.common.value.ChartPoint
import java.time.LocalDate
import spock.lang.Specification

class CreditsCalibrationResultTest extends Specification {

  def "should return chart and credit rates"() {
    setup:
    def valuationDate = LocalDate.parse("2017-01-01")
    def calibrationResult = Mock(CreditCurveCalibrationResult)
    1 * calibrationResult.curveName() >> CurveName.of("curveId")
    1 * calibrationResult.calculateChartPoints() >> [new ChartPoint(valuationDate, 1, 1.0)]
    1 * calibrationResult.getProbabilities() >> LegalEntitySurvivalProbabilities.of(StandardId
      .of("A", "SELF"), CreditDiscountFactors.of(Currency.EUR, valuationDate, CurveSample.discountCurveIsdaInterpolators()))
    def creditRatesProvider = CalibrationSetSample.creditRatesProviderBuilder(valuationDate).withProbabilities([calibrationResult]).build()
    def calibrationResults = new CreditsCalibrationResult(creditRatesProvider, [], [calibrationResult])

    when:
    def chart = calibrationResults.getChart(CurveName.of("curveId"))

    then:
    chart[0] == new ChartPoint(valuationDate, 1, 1.0)

    when:
    def fullCreditRatesProvider = calibrationResults.getCreditRatesProvider().toImmutableCreditRatesProvider()

    then:
    fullCreditRatesProvider.isRight()
    fullCreditRatesProvider.right().get().survivalProbabilities(StandardId.of("A", "SELF"), Currency.EUR) != null
  }
}
