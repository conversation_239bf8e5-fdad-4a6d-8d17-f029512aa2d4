package com.solum.xplain.xm.tasks.repository

import static com.solum.xplain.core.users.UserBuilder.user

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.teams.Team
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.core.teams.value.TeamNameView
import com.solum.xplain.core.users.events.UserUpdated
import com.solum.xplain.core.users.value.EditUserForm
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter
import com.solum.xplain.xm.excmngmt.rulesipv.value.ProductTypeFilterForm
import com.solum.xplain.xm.tasks.entity.IpvTaskDefinitionOverride
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition
import com.solum.xplain.xm.tasks.entity.IpvTasksTeamsOverride
import com.solum.xplain.xm.tasks.entity.ProductTypeTaskTeams
import com.solum.xplain.xm.tasks.enums.TaskGranularityByContractualTerm
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByTradeType
import com.solum.xplain.xm.tasks.form.IpvTaskDefinitionOverrideForm
import com.solum.xplain.xm.tasks.form.IpvTasksDefinitionForm
import com.solum.xplain.xm.tasks.form.IpvTasksTeamsOverrideForm
import com.solum.xplain.xm.tasks.form.ProductTypeTaskTeamsForm
import jakarta.annotation.Resource
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class IpvTaskDefinitionRepositoryTest extends IntegrationSpecification {

  @Resource
  MongoOperations operations

  @Resource
  IpvTaskDefinitionRepository ipvTaskDefinitionRepository

  def creator = user("creatorId")
  Team team = TeamBuilder.team()

  def setup() {
    operations.insert(team)
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), IpvTasksDefinition)
    operations.remove(new Query(), Team)
  }

  def "should get ipv task definition"() {
    setup:
    taskDefinition()
    def teamNameView = new TeamNameView(id: team.id.toHexString(), name: team.name)
    when:
    def result = ipvTaskDefinitionRepository.view(IpvExceptionManagementPhase.OVERLAY_1)

    then:
    result.type == IpvExceptionManagementPhase.OVERLAY_1
    result.granularityByRate == TaskGranularityByRateType.CURRENCY
    result.granularityByTradeType == TaskGranularityByTradeType.NONE
    result.granularityByFxCcyPairType == TaskGranularityByFxCcyPairType.CCY_PAIR
    result.teams.size() == 1
    result.teams[0].productType == CoreProductType.IRS
    result.teams[0].resolutionTeams == [teamNameView]
    result.teams[0].approvalTeams == [teamNameView]
    result.overrides.size() == 1
    result.overrides[0].overrideTeams[0].filter == new TradeFilter()
    result.overrides[0].overrideTeams[0].resolutionTeams == [teamNameView]
    result.overrides[0].overrideTeams[0].approvalTeams == [teamNameView]
  }

  def "should get all ipv task definitions"() {
    setup:
    taskDefinition()
    taskDefinition(IpvExceptionManagementPhase.OVERLAY_2)

    when:
    def result = ipvTaskDefinitionRepository.getTasksDefinitions(BitemporalDate.newOfNow())

    then:
    result.size() == 2
    result.containsKey(IpvExceptionManagementPhase.OVERLAY_1)
    result.containsKey(IpvExceptionManagementPhase.OVERLAY_2)
    result.get(IpvExceptionManagementPhase.OVERLAY_1).type == IpvExceptionManagementPhase.OVERLAY_1
    result.get(IpvExceptionManagementPhase.OVERLAY_2).type == IpvExceptionManagementPhase.OVERLAY_2
  }

  def "should update ipv task definition"() {
    setup:
    def task = taskDefinition()
    def teamId = ObjectId.get().toHexString()
    when:
    def result = ipvTaskDefinitionRepository.save(
      IpvExceptionManagementPhase.OVERLAY_1,
      new IpvTasksDefinitionForm(
      [
        new ProductTypeTaskTeamsForm(
        productType: CoreProductType.CDS,
        resolutionTeams: [teamId],
        approvalTeams: [teamId]
        )
      ],
      [
        new IpvTaskDefinitionOverrideForm(
        [
          new IpvTasksTeamsOverrideForm(
          filter: new ProductTypeFilterForm(),
          resolutionTeams: [teamId],
          approvalTeams: [teamId]
          )
        ]
        )
      ],
      TaskGranularityByTradeType.NONE,
      TaskGranularityByRateType.NONE,
      TaskGranularityBySectorType.NONE,
      TaskGranularityByFxCcyPairType.NONE,
      TaskGranularityByContractualTerm.NONE
      ))

    then:
    def loaded = operations.findById(result.id, IpvTasksDefinition)
    loaded.granularityByRate == TaskGranularityByRateType.NONE
    loaded.granularityByFxCcyPairType == TaskGranularityByFxCcyPairType.NONE
    loaded.granularityByTradeType == TaskGranularityByTradeType.NONE
    loaded.granularityByContractualTerm == TaskGranularityByContractualTerm.NONE
    loaded.teams.size() == 1
    loaded.teams[0].productType == CoreProductType.CDS
    loaded.teams[0].resolutionTeams == [teamId]
    loaded.teams[0].approvalTeams == [teamId]
    loaded.overrides.size() == 1
    loaded.overrides[0].overrideTeams[0].filter == new TradeFilter()
    loaded.overrides[0].overrideTeams[0].resolutionTeams == [teamId]
    loaded.overrides[0].overrideTeams[0].approvalTeams == [teamId]
    loaded.recordDate != null
    loaded.recordDate.isAfter(task.recordDate)
  }

  def "should update ipv task definition user name"() {
    setup:
    def task = taskDefinition()
    when:
    ipvTaskDefinitionRepository.onEvent(new UserUpdated(creator.id, new EditUserForm(name: "new")))

    then:
    def loaded = operations.findById(task.id, IpvTasksDefinition)
    loaded.modifiedBy.name == "new"
  }

  def "should update ipv task definition with empty teams"() {
    setup:
    taskDefinition()

    when:
    def result = ipvTaskDefinitionRepository.save(
      IpvExceptionManagementPhase.OVERLAY_1,
      new IpvTasksDefinitionForm(
      [
        new ProductTypeTaskTeamsForm(
        productType: CoreProductType.CDS,
        resolutionTeams: [],
        approvalTeams: []
        )
      ],
      [],
      TaskGranularityByTradeType.NONE,
      TaskGranularityByRateType.NONE,
      TaskGranularityBySectorType.NONE,
      TaskGranularityByFxCcyPairType.NONE,
      TaskGranularityByContractualTerm.NONE
      ))

    then:
    result != null
    def loaded = operations.findById(result.id, IpvTasksDefinition)
    loaded.teams.size() == 1
    loaded.teams[0].productType == CoreProductType.CDS
    loaded.teams[0].resolutionTeams.isEmpty()
    loaded.teams[0].approvalTeams.isEmpty()
  }

  def taskDefinition(IpvExceptionManagementPhase phase = IpvExceptionManagementPhase.OVERLAY_1) {
    return operations.insert(new IpvTasksDefinition(
      type: phase,
      teams: [
        new ProductTypeTaskTeams(
        productType: CoreProductType.IRS,
        resolutionTeams: [team.id.toHexString()],
        approvalTeams: [team.id.toHexString()]
        )
      ],
      overrides: [
        new IpvTaskDefinitionOverride(
        overrideTeams: [
          new IpvTasksTeamsOverride(
          filter: new TradeFilter(),
          resolutionTeams: [team.id.toHexString()],
          approvalTeams: [team.id.toHexString()]
          )
        ]
        )
      ],
      granularityByTradeType: TaskGranularityByTradeType.NONE,
      granularityByRate: TaskGranularityByRateType.CURRENCY,
      granularityByFxCcyPairType: TaskGranularityByFxCcyPairType.CCY_PAIR
      ))
  }
}
