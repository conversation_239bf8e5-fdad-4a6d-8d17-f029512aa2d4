package com.solum.xplain.xm.dashboards.repository

import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MARKET_DATA_BATCH_UPLOAD
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MARKET_DATA_UPLOAD
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_BATCH_PRELIMINARY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_BATCH_PRELIMINARY_RUN
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_OVERLAY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_OVERLAY_RUN
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_PRELIMINARY_CLEARING
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_PRELIMINARY_RUN

import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class DashboardEntryRepositoryTest extends IntegrationSpecification {
  private static final LocalDateTime STARTED_AT_TIME = LocalDateTime.of(2021, 1, 1, 0, 0)
  private static final LocalDateTime FINISHED_AT_TIME = LocalDateTime.of(2021, 1, 2, 0, 0)

  private static final String ANOTHER_DASHBOARD_ID = "anotherDashboardId"
  private static final ErrorItem ERROR = Error.UNEXPECTED_ERROR.entity("error")
  private static final Long BREAKS_COUNT = 1L
  private static final String ENTRY_RESULT_ID = "resultID"

  @Resource
  MongoOperations operations

  @Resource
  DashboardEntryRepository repository

  def creator = user("creatorId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), DashboardEntryMdBatch)
    operations.remove(new Query(), DashboardEntryMd)
  }

  def "should create dashboard MD batch entry"() {
    def entry = new DashboardEntryMdBatch()
    entry.dashboardId = MD_BATCH_DASHBOARD_ID
    entry.step = MARKET_DATA_BATCH_UPLOAD
    entry.startedAt = STARTED_AT_TIME
    entry.finishedAt = FINISHED_AT_TIME
    entry.error = ERROR
    entry.breaksCount = BREAKS_COUNT
    entry.resultId = ENTRY_RESULT_ID

    when: "valid entry"
    def result = repository.createEntry(entry)

    then:
    result.isRight()
    result.getOrNull().id != null

    def loaded = operations.findAll(DashboardEntryMdBatch)
    loaded.size() == 1

    loaded[0].id == result.getOrNull().id
    loaded[0].modifiedBy.userId == creator.id
    loaded[0].modifiedBy.username == creator.name
    loaded[0].modifiedAt != null
    loaded[0].previousStatuses == []

    loaded[0].dashboardId == MD_BATCH_DASHBOARD_ID
    loaded[0].step == MARKET_DATA_BATCH_UPLOAD
    loaded[0].startedAt == STARTED_AT_TIME
    loaded[0].finishedAt == FINISHED_AT_TIME
    loaded[0].error == ERROR
    loaded[0].breaksCount == BREAKS_COUNT
    loaded[0].resultId == ENTRY_RESULT_ID

    when: "existing step entry"
    def failureResult = repository.createEntry(entry)

    then:
    failureResult.isLeft()
    def error = failureResult.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Step already exists!"
  }

  def "should create dashboard MD entry"() {
    def entry = new DashboardEntryMd()
    entry.dashboardId = MD_DASHBOARD_ID
    entry.step = MARKET_DATA_UPLOAD
    entry.startedAt = STARTED_AT_TIME
    entry.finishedAt = FINISHED_AT_TIME
    entry.error = ERROR
    entry.breaksCount = BREAKS_COUNT
    entry.resultId = ENTRY_RESULT_ID
    entry.curveConfiguration = CURVE_CONFIGURATION

    when: "valid entry"
    def result = repository.createEntry(entry)

    then:
    result.isRight()
    result.getOrNull().id != null

    def loaded = operations.findAll(DashboardEntryMd)
    loaded.size() == 1

    loaded[0].id == result.getOrNull().id
    loaded[0].modifiedBy.userId == creator.id
    loaded[0].modifiedBy.username == creator.name
    loaded[0].modifiedAt != null
    loaded[0].previousStatuses == []

    loaded[0].dashboardId == MD_DASHBOARD_ID
    loaded[0].step == MARKET_DATA_UPLOAD
    loaded[0].startedAt == STARTED_AT_TIME
    loaded[0].finishedAt == FINISHED_AT_TIME
    loaded[0].error == ERROR
    loaded[0].breaksCount == BREAKS_COUNT
    loaded[0].resultId == ENTRY_RESULT_ID
    loaded[0].curveConfiguration == CURVE_CONFIGURATION

    when: "existing step entry"
    def failureResult = repository.createEntry(entry)

    then:
    failureResult.isLeft()
    def error = failureResult.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Step already exists!"
  }

  def "should get dashboard MD batch entries"() {
    operations.insertAll([
      mdBatchEntry(MD_BATCH_DASHBOARD_ID, MARKET_DATA_BATCH_UPLOAD),
      mdBatchEntry(MD_BATCH_DASHBOARD_ID, MD_BATCH_PRELIMINARY_RUN),
      mdBatchEntry(MD_BATCH_DASHBOARD_ID, MD_BATCH_PRELIMINARY_CLEARING),
    ])
    operations.insertAll([mdBatchEntry(ANOTHER_DASHBOARD_ID, MARKET_DATA_BATCH_UPLOAD)])

    when:
    def result = repository.getMdBatchEntries(MD_BATCH_DASHBOARD_ID)

    then:
    result.size() == 3
  }

  def "should get dashboard MD entries"() {
    operations.insertAll([
      mdEntry(MD_DASHBOARD_ID, MARKET_DATA_UPLOAD),
      mdEntry(MD_DASHBOARD_ID, MD_PRELIMINARY_RUN),
      mdEntry(MD_DASHBOARD_ID, MD_PRELIMINARY_CLEARING),
      mdEntry(MD_DASHBOARD_ID, MD_OVERLAY_RUN),
      mdEntry(MD_DASHBOARD_ID, MD_PRELIMINARY_CLEARING, CURVE_CONFIGURATION)
    ])
    operations.insertAll([mdEntry(ANOTHER_DASHBOARD_ID, MARKET_DATA_UPLOAD)])

    when:
    def result = repository.getMdEntries(MD_DASHBOARD_ID)

    then:
    result.size() == 5
  }

  def "should update dashboard MD batch entry"() {
    def existingEntry = mdBatchEntry(MD_BATCH_DASHBOARD_ID, MD_BATCH_PRELIMINARY_CLEARING)
    operations.insert(existingEntry)

    when: "valid update"
    existingEntry.resultId = ENTRY_RESULT_ID
    def result = repository.updateEntry(existingEntry)

    then:
    result.isRight()
    def loaded = operations.findAll(DashboardEntryMdBatch)
    loaded.size() == 1

    loaded[0].id == existingEntry.id
    loaded[0].isSameEntry(existingEntry)

    loaded[0].resultId == ENTRY_RESULT_ID

    when: "actually non-existing entry"
    def failureResult = repository.updateEntry(mdBatchEntry(MD_BATCH_DASHBOARD_ID, MARKET_DATA_BATCH_UPLOAD))

    then:
    failureResult.isLeft()
    def error = failureResult.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Step does not exist"
  }

  def "should update dashboard MD entry"() {
    def existingEntry = mdEntry(MD_DASHBOARD_ID, MD_OVERLAY_CLEARING, CURVE_CONFIGURATION)
    operations.insert(existingEntry)

    when: "valid update"
    existingEntry.resultId = ENTRY_RESULT_ID
    def result = repository.updateEntry(existingEntry)

    then:
    result.isRight()
    def loaded = operations.findAll(DashboardEntryMd)
    loaded.size() == 1

    loaded[0].id == existingEntry.id
    loaded[0].isSameEntry(existingEntry)

    loaded[0].resultId == ENTRY_RESULT_ID

    when: "actually non-existing entry"
    def failureResult = repository.updateEntry(mdEntry(MD_DASHBOARD_ID, MARKET_DATA_UPLOAD))

    then:
    failureResult.isLeft()
    def error = failureResult.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Step does not exist"
  }


  def "should delete dashboard MD batch entries"() {
    operations.insertAll([
      mdBatchEntry(MD_BATCH_DASHBOARD_ID, MARKET_DATA_BATCH_UPLOAD),
      mdBatchEntry(MD_BATCH_DASHBOARD_ID, MD_BATCH_PRELIMINARY_RUN),
    ])
    operations.insertAll([mdBatchEntry(ANOTHER_DASHBOARD_ID, MD_BATCH_PRELIMINARY_RUN)])

    when:
    repository.deleteMdBatchEntries(MD_BATCH_DASHBOARD_ID, MD_BATCH_PRELIMINARY_RUN)

    then:
    def loaded = operations.findAll(DashboardEntryMdBatch)
    loaded.size() == 2
    loaded[0].dashboardId == MD_BATCH_DASHBOARD_ID
    loaded[0].step == MARKET_DATA_BATCH_UPLOAD
    loaded[1].dashboardId == ANOTHER_DASHBOARD_ID
    loaded[1].step == MD_BATCH_PRELIMINARY_RUN
  }

  def "should delete dashboard MD entries"() {
    operations.insertAll([
      mdEntry(MD_DASHBOARD_ID, MARKET_DATA_UPLOAD),
      mdEntry(MD_DASHBOARD_ID, MD_PRELIMINARY_RUN),
    ])
    operations.insertAll([mdEntry(ANOTHER_DASHBOARD_ID, MARKET_DATA_UPLOAD)])

    when:
    repository.deleteMdEntries(MD_DASHBOARD_ID, MARKET_DATA_UPLOAD)

    then:
    def loaded = operations.findAll(DashboardEntryMd)
    loaded.size() == 2
    loaded[0].dashboardId == MD_DASHBOARD_ID
    loaded[0].step == MD_PRELIMINARY_RUN
    loaded[1].dashboardId == ANOTHER_DASHBOARD_ID
    loaded[1].step == MARKET_DATA_UPLOAD
  }

  static def mdBatchEntry(String dashboardId,
    DashboardStep step) {
    def entry = new DashboardEntryMdBatch()
    entry.dashboardId = dashboardId
    entry.step = step
    entry.status = StepStatus.COMPLETED
    entry.startedAt = LocalDateTime.now()
    entry.finishedAt = LocalDateTime.now()
    entry
  }

  static def mdEntry(String dashboardId,
    DashboardStep step,
    EntityReference curveConfiguration = null) {
    def entry = new DashboardEntryMd()
    entry.dashboardId = dashboardId
    entry.step = step
    entry.status = StepStatus.COMPLETED
    entry.startedAt = LocalDateTime.now()
    entry.finishedAt = LocalDateTime.now()
    entry.curveConfiguration = curveConfiguration
    entry
  }
}
