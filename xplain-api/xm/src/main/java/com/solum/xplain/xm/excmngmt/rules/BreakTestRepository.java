package com.solum.xplain.xm.excmngmt.rules;

import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listMinorVersionedLatest;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.MinorVersionedEntity;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestForm;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rules.value.BreakTestView;
import com.solum.xplain.xm.excmngmt.rules.value.TestScope;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTest;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTestRepository;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter;
import io.atlassian.fugue.Either;
import java.util.Map;
import java.util.function.Function;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
public class BreakTestRepository
    extends BaseBreakTestRepository<
        InstrumentDefinition,
        BreakTestOverride,
        BreakTest,
        BreakTestForm,
        BreakTestOverrideForm,
        BreakTestView,
        BreakTestOverrideView> {

  private final MongoOperations mongoOperations;
  private final BreakTestMapper mapper;

  protected BreakTestRepository(
      MongoOperations mongoOperations,
      ConversionService conversionService,
      AuditingHandler auditingHandler,
      BreakTestMapper mapper) {
    super(
        mongoOperations,
        conversionService,
        auditingHandler,
        mapper,
        BreakTest.class,
        BreakTestView.class);
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
  }

  public Map<String, BreakTest> getAllWithScope(
      TestScope scope, boolean onlyActive, BitemporalDate stateDate) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .addAll(listMinorVersionedLatest(stateDate))
            .add(match(new BreakTestFilter(false).criteria()))
            .add(match(where(BreakTest.Fields.scope).is(scope.name())));
    if (onlyActive) {
      operations.add(match(where(BaseBreakTest.Fields.enabled).is(true)));
    }

    return mongoOperations
        .aggregate(newAggregation(BreakTest.class, operations.build()), BreakTest.class)
        .getMappedResults()
        .stream()
        .collect(toMap(BreakTest::getEntityId, Function.identity()));
  }

  @Override
  protected BreakTest newEntity() {
    return BreakTest.newOf();
  }

  public boolean existsWithNameAndScope(String name, TestScope scope, String id) {
    return existsActiveByCriteria(
        where(BaseBreakTest.Fields.name)
            .is(name)
            .and(BreakTest.Fields.scope)
            .is(scope)
            .and(MinorVersionedEntity.Fields.entityId)
            .ne(id));
  }

  @Override
  protected ProjectionOperation projectToView() {
    return super.projectToView()
        .and(BreakTest.Fields.scope)
        .as(BreakTestView.Fields.scope)
        .and(BreakTest.Fields.assetFilter)
        .as(BreakTestView.Fields.assetFilter)
        .and(BreakTest.Fields.type)
        .as(BreakTestView.Fields.type)
        .and(BreakTest.Fields.measureType)
        .as(BreakTestView.Fields.measureType)
        .and(BreakTest.Fields.zScoreObservationPeriod)
        .as(BreakTestView.Fields.zScoreObservationPeriod);
  }

  @Override
  public Either<ErrorItem, EntityId> update(String id, BreakTestForm f) {
    return super.update(id, f).map(entityId -> updateReferences(f, entityId));
  }

  private EntityId updateReferences(BreakTestForm form, EntityId entityId) {
    Update update =
        Update.update(
            propertyName(BaseBreakTest.Fields.parentTest, BreakTestParent.Fields.name),
            form.getName());
    mongoOperations.updateMulti(
        query(
            where(propertyName(BaseBreakTest.Fields.parentTest, BreakTestParent.Fields.parentId))
                .is(entityId.getId())),
        update,
        BreakTest.class);
    return entityId;
  }

  @Override
  public ScrollableEntry<BreakTestView> getAll(
      TableFilter tableFilter, BreakTestFilter filter, ScrollRequest scrollRequest) {
    return super.getAll(tableFilter, filter, scrollRequest).map(mapper::enhanceView);
  }
}
