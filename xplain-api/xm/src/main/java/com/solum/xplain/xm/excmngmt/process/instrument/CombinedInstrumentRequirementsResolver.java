package com.solum.xplain.xm.excmngmt.process.instrument;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.trs.value.NonMtmInstrumentType;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CombinedInstrumentRequirementsResolver implements InstrumentRequirementsResolver {
  private final CurveConfigurationRequirementsResolver curveConfigurationRequirementsResolver;
  private final TrsInstrumentRequirementsResolver trsInstrumentRequirementsResolver;

  public static CombinedInstrumentRequirementsResolver newOf(
      CurveGroupInstrumentFilter curveGroupInstrumentFilter,
      @Nullable MdExceptionManagementSetup mdSetup,
      @Nullable TrsMdExceptionManagementSetup trsSetup) {
    var mdResolver =
        Optional.ofNullable(mdSetup)
            .map(md -> CurveConfigurationRequirementsResolver.newOf(md, curveGroupInstrumentFilter))
            .orElse(CurveConfigurationRequirementsResolver.empty(curveGroupInstrumentFilter));
    var trsResolver =
        trsSetup == null
            ? TrsInstrumentRequirementsResolver.empty()
            : TrsInstrumentRequirementsResolver.newOf(trsSetup);

    return new CombinedInstrumentRequirementsResolver(mdResolver, trsResolver);
  }

  @Override
  public List<InstrumentRequirements> requirements(InstrumentDefinition definition) {
    if (definition.getInstrument() instanceof NonMtmInstrumentType) {
      return trsInstrumentRequirementsResolver.requirements(definition);
    } else {
      return curveConfigurationRequirementsResolver.requirements(definition);
    }
  }
}
