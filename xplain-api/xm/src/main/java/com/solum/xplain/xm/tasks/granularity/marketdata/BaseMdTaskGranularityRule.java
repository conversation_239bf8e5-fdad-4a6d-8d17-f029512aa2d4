package com.solum.xplain.xm.tasks.granularity.marketdata;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.xm.tasks.TaskMapper;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.granularity.TaskGranularityRule;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;

@EqualsAndHashCode
@RequiredArgsConstructor
public abstract class BaseMdTaskGranularityRule implements TaskGranularityRule<TaskExecution> {
  protected final TaskMapper mapper;
  protected final List<InstrumentType> supportedInstruments;

  protected List<AssetClass> allAssetClasses() {
    return supportedInstruments.stream().map(InstrumentType::getAssetClass).distinct().toList();
  }
}
