package com.solum.xplain.xm.excmngmt.processipv.value;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak.tradeResult;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.process.value.EntryResultCustomResolver;
import com.solum.xplain.xm.excmngmt.process.value.EntryResultResolver;
import com.solum.xplain.xm.excmngmt.processipv.data.OnboardingStatus;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType;
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
class IpvBreakTestCalculation implements Serializable {

  private final IpvBreakTest breakTest;
  private final List<IpvBreakTest> dependentTests;

  public static IpvBreakTestCalculation ofBreakTest(@NonNull IpvBreakTest breakTest) {
    return new IpvBreakTestCalculation(breakTest, new ArrayList<>());
  }

  public void acceptDependant(IpvBreakTest dependantTest) {
    var dependsOnBreakTest =
        Objects.equals(breakTest.getEntityId(), dependantTest.getParentTest().getParentId());
    if (isTrue(dependantTest.getEnabled()) && dependsOnBreakTest) {
      dependentTests.add(dependantTest);
    }
  }

  public Optional<Integer> staleValueDuration() {
    return breakTest.maxStaleThreshold();
  }

  /**
   * Return true if this calculation can be applied to a trade. If this method returns false, then
   * {@link #resolveBreak(TradeBreakCalculator, LocalDate)} will always return {@code false} for the
   * trade
   *
   * <p>A break test calculation can be applied if:
   *
   * <ul>
   *   <li>The break test conditions match the trade
   *   <li>The break test is {@linkplain #isRelevant() relevant}.
   * </ul>
   *
   * @param trade the trade to check the break test calculation against
   * @return true if the calculation can be applied to the trade, or false if it will never return
   *     any breaks
   */
  public boolean isApplicable(Trade trade) {
    return isRelevant() && breakTest.matches(trade);
  }

  /**
   * Return true if this calculation is relevant. If this method returns false, then {@link
   * #resolveBreak(TradeBreakCalculator, LocalDate)} will always return {@code false} for
   * <em>any</em> trade.
   *
   * <p>A break test calculation is relevant if:
   *
   * <p>Either:
   *
   * <ul>
   *   <li>The break test is enabled; or
   *   <li>The break test has other enabled tests which depend on it
   * </ul>
   *
   * @return true if the calculation is relevant, or false if it will never return any breaks.
   */
  public boolean isRelevant() {
    return CollectionUtils.isNotEmpty(dependentTests)
        || Boolean.TRUE.equals(breakTest.getEnabled());
  }

  public List<TradeResultBreak> resolveBreak(TradeBreakCalculator data, LocalDate stateDate) {
    if (!breakTest.matches(data.getTrade())) {
      return List.of();
    }

    var breakTestResults = processTest(data, stateDate);
    var dependentTestsResults = processDependantTests(breakTestResults, data);
    return join(breakTestResults, dependentTestsResults);
  }

  private List<TradeResultBreak> processDependantTests(
      List<TradeResultBreak> parentResults, TradeBreakCalculator data) {
    if (dependentTests.isEmpty()) {
      return List.of();
    }

    var providerValues =
        parentResults.stream()
            .filter(v -> Objects.nonNull(v.getProviderValue().getValue()))
            .collect(
                Collectors.toMap(
                    TradeResultBreak::getProviderType, v -> v.getProviderValue().getValue()));
    var calculator = data.toChildBreakTestCalculator(providerValues);
    return dependentTests.stream().map(calculator::calculateChildTest).toList();
  }

  private List<TradeResultBreak> processTest(TradeBreakCalculator data, LocalDate stateDate) {
    if (CollectionUtils.isEmpty(dependentTests) && Boolean.FALSE.equals(breakTest.getEnabled())) {
      return List.of();
    }

    return switch (breakTest.getType()) {
      case NULL_VALUE -> processNullTest(data);
      case ZERO_VALUE -> processZeroValueTest(data);
      case STALE_VALUE -> processStaleTest(data);
      case DAY_TO_DAY -> processDayToDayTest(data);
      case DAY_TO_DAY_SIGN -> processDayToDaySignTest(data);
      case VALUE -> valueTest(data);
      case PRIMARY_VS_SECONDARY -> processPrimaryVsProviderTest(data, IpvProvidersType.P2);
      case PRIMARY_VS_TERTIARY -> processPrimaryVsProviderTest(data, IpvProvidersType.P3);
      case PRIMARY_VS_QUATERNARY -> processPrimaryVsProviderTest(data, IpvProvidersType.P4);
      case PRIMARY_VS_ACCOUNTING_COST ->
          processPrimaryVsAccountingCostTest(data, IpvProvidersType.ACCOUNTING_COST, stateDate);
    };
  }

  private List<TradeResultBreak> processNullTest(TradeBreakCalculator data) {
    return providersResults(type -> data.nullProvider(type, customTestResolver(type)), data);
  }

  private List<TradeResultBreak> processZeroValueTest(TradeBreakCalculator data) {
    return providersResults(type -> data.zeroValue(type, customTestResolver(type)), data);
  }

  private List<TradeResultBreak> processStaleTest(TradeBreakCalculator data) {
    return ofNullable(breakTest.resolveFirstThreshold(data.getTrade()))
        .map(BigDecimal::intValue)
        .map(t -> providersResults(type -> data.stale(type, t, customTestResolver(type)), data))
        .orElse(List.of());
  }

  private List<TradeResultBreak> processDayToDayTest(TradeBreakCalculator data) {
    var measure = breakTest.getMeasureType();
    return providersResults(
        type -> data.dayOnDay(type, measure, testResolver(type, data.getTrade())), data);
  }

  private List<TradeResultBreak> processDayToDaySignTest(TradeBreakCalculator data) {
    return providersResults(type -> data.dayToDaySign(type, customTestResolver(type)), data);
  }

  private List<TradeResultBreak> valueTest(TradeBreakCalculator data) {
    var measure = breakTest.getMeasureType();
    return providersResults(
        type -> data.value(type, measure, testResolver(type, data.getTrade())), data);
  }

  private List<TradeResultBreak> processPrimaryVsProviderTest(
      TradeBreakCalculator data, IpvProvidersType provider) {
    var providerDiff = providerDiffBreakSupplier(breakTest, provider, data).get();
    return List.of(toTradeResultBreak(IpvBreakProviderType.PRIMARY, providerDiff, data));
  }

  private List<TradeResultBreak> processPrimaryVsAccountingCostTest(
      TradeBreakCalculator data, IpvProvidersType provider, LocalDate stateDate) {
    Trade trade = data.getTrade();

    // First check if the onboarding test is required for this trade
    if (trade.getVendorOnboardingDate() == null
        || trade.getOnboardingPeriodEndDate() == null
        || stateDate.isBefore(trade.getVendorOnboardingDate())
        || stateDate.isAfter(trade.getOnboardingPeriodEndDate())
        || !Boolean.TRUE.equals(trade.getVendorCheck())) {
      return List.of(
          toTradeResultBreak(
              IpvBreakProviderType.PRIMARY, null, data, OnboardingStatus.NOT_REQUIRED));
    }

    // Only calculate break if onboarding test is required
    var providerDiff = providerDiffBreakSupplier(breakTest, provider, data).get();

    // Onboarding status is success if trade has accounting cost & doesn't break, failed otherwise
    var onboardingStatus =
        (trade.getAccountingCost() != null && !providerDiff.isTriggered())
            ? OnboardingStatus.SUCCESS
            : OnboardingStatus.FAILED;

    return List.of(
        toTradeResultBreak(IpvBreakProviderType.PRIMARY, providerDiff, data, onboardingStatus));
  }

  private Supplier<EntryResultBreakByProvider> providerDiffBreakSupplier(
      IpvBreakTest test, IpvProvidersType provider, TradeBreakCalculator data) {
    return () ->
        data.providerDiff(provider, test.getMeasureType(), test.resultResolver(data.getTrade()));
  }

  private EntryResultResolver testResolver(IpvProvidersType providerType, Trade trade) {
    if (providerType == IpvProvidersType.P1 && Boolean.TRUE.equals(breakTest.getEnabled())) {
      return breakTest.resultResolver(trade);
    }
    return EntryResultResolver.calculationOnly();
  }

  private EntryResultCustomResolver customTestResolver(IpvProvidersType providerType) {
    return providerType == IpvProvidersType.P1
        ? EntryResultBreakByProvider::ofBreakWithoutLevel
        : EntryResultCustomResolver.calculationOnly();
  }

  private List<TradeResultBreak> providersResults(
      Function<IpvProvidersType, EntryResultBreakByProvider> testFn, TradeBreakCalculator data) {
    return Arrays.stream(IpvBreakProviderType.values())
        .filter(p -> breakTest.isForProvider(p.getProvidersType()))
        .filter(data::hasMappedProviderType)
        .map(p -> toTradeResultBreak(p, testFn.apply(p.getProvidersType()), data))
        .toList();
  }

  private TradeResultBreak toTradeResultBreak(
      IpvBreakProviderType type, EntryResultBreakByProvider result, TradeBreakCalculator data) {
    return tradeResult(
        breakTest,
        type,
        result,
        data.getTrade(),
        data.daysBreaking(breakTest, result.isTriggered()),
        null);
  }

  // Overloaded method for use with onboarding tests only
  private TradeResultBreak toTradeResultBreak(
      IpvBreakProviderType type,
      EntryResultBreakByProvider result,
      TradeBreakCalculator data,
      OnboardingStatus onboardingStatus) {
    return tradeResult(
        breakTest,
        type,
        onboardingStatus == OnboardingStatus.NOT_REQUIRED
            ? EntryResultBreakByProvider.ofEmpty()
            : result,
        data.getTrade(),
        data.daysBreaking(
            breakTest, onboardingStatus != OnboardingStatus.NOT_REQUIRED && result.isTriggered()),
        onboardingStatus);
  }
}
