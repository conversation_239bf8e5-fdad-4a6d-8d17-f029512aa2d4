package com.solum.xplain.xm.tasks;

import com.solum.xplain.core.teams.TeamNameMapper;
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition;
import com.solum.xplain.xm.tasks.form.IpvTasksDefinitionForm;
import com.solum.xplain.xm.tasks.view.IpvTasksDefinitionView;
import com.solum.xplain.xm.tasks.view.summary.TradeFilterSummary;
import java.util.List;
import org.bson.types.ObjectId;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    uses = {TeamNameMapper.class},
    imports = {ObjectId.class, List.class, TradeFilterSummary.class})
public interface IpvTaskMapper {

  IpvTasksDefinitionView fromEntity(IpvTasksDefinition definition);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "type", ignore = true)
  IpvTasksDefinition from(
      IpvTasksDefinitionForm form, @MappingTarget IpvTasksDefinition definition);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  IpvTasksDefinition copy(IpvTasksDefinition definition);
}
