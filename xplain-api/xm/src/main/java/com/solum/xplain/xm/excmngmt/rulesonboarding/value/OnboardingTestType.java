package com.solum.xplain.xm.excmngmt.rulesonboarding.value;

import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.ABSOLUTE_DIFF;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01_VEGA;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_VEGA;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.NAV;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.NOTIONAL;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.RELATIVE_DIFF;
import static java.util.List.of;

import com.solum.xplain.xm.excmngmt.rulesbase.BaseTestType;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OnboardingTestType implements BaseTestType {
  DC_XPLAIN(
      "Xplain Conformity (vs. Deal Cost)",
      of(ABSOLUTE_DIFF, RELATIVE_DIFF, GREEKS_DV01, GREEKS_DV01_VEGA, GREEKS_VEGA, NAV, NOTIONAL)),
  MARKET_CONF(
      "Market Conformity (vs. Deal Cost)",
      of(ABSOLUTE_DIFF, RELATIVE_DIFF, GREEKS_DV01, GREEKS_DV01_VEGA, GREEKS_VEGA, NAV, NOTIONAL)),
  AC_P1(
      "Vendor Conformity (vs. Accounting Cost)",
      of(ABSOLUTE_DIFF, RELATIVE_DIFF, GREEKS_DV01, GREEKS_DV01_VEGA, GREEKS_VEGA, NAV, NOTIONAL));

  private final String name;
  private final List<IpvMeasureType> allowedMeasures;

  @Override
  public boolean isInternal() {
    return false;
  }
}
