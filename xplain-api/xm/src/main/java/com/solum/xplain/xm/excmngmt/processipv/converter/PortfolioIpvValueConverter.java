package com.solum.xplain.xm.excmngmt.processipv.converter;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData;
import java.util.Optional;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public interface PortfolioIpvValueConverter {
  TradeBreakScalingData convertScalingData(
      @Nullable TradeBreakScalingData scalingData, @NonNull Currency tradeCurrency);

  Optional<Currency> xmCurrency(@NonNull Currency tradeCurrency);
}
