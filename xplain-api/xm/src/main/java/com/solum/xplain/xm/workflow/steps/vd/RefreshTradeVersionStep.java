package com.solum.xplain.xm.workflow.steps.vd;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RefreshTradeVersionStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {

  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    LocalDateTime tradeRecordDate = ops.getInitialState().getTradeRecordDate();
    LocalDateTime contextTradeRecordDate = ops.getContext().tradeRecordDate();

    boolean hasUpdatedTrade = !tradeRecordDate.isEqual(contextTradeRecordDate);

    MutablePropertyValues outcome =
        new MutablePropertyValues().add(VdPhaseState.Fields.hasUpdatedTrade, hasUpdatedTrade);

    if (hasUpdatedTrade) {
      outcome.add(VdPhaseState.Fields.tradeRecordDate, tradeRecordDate);
    }

    ops.setOutcome(outcome);
  }
}
