package com.solum.xplain.xm.workflow.steps.md;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VerifyOverlayResolutionStep
    implements ServiceStepExecutor<MdOverlayState, MdOverlayContext> {
  @Override
  public void runStep(StepStateOps<MdOverlayState, MdOverlayContext> ops) {
    AttributedValue pendingValue = ops.getInitialState().getValuePendingApproval();
    ops.setOutcome(
        new MutablePropertyValues()
            .add(MdOverlayState.Fields.valuePendingApproval, null)
            .add(MdOverlayState.Fields.baseValue, pendingValue.value())
            .add(MdOverlayState.Fields.providerName, pendingValue.providerName()));
    // Don't need to set VERIFIED as that is done in SaveOverlayStep anyway
  }
}
