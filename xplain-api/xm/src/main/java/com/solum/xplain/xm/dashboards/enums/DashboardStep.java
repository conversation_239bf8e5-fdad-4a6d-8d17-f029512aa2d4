package com.solum.xplain.xm.dashboards.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
@Schema(enumAsRef = true)
public enum DashboardStep {
  // VD Steps
  IPV_OVERLAY_CLEARING_2("Overlay II Break Clearing", 10, false),
  IPV_OVERLAY_CLEARING("Overlay I Break Clearing", 7, false),
  OPV_VALUATIONS("Valuation Calculations", 3, false),
  TRADE_DATA_UPLOAD("Trade Data Upload", 1, false),

  // MD steps
  MD_OVERLAY_CLEARING("Market Data Exception Clearing Overlay Phase", 4, false),
  MD_OVERLAY_RUN("Market Data Exception Report Overlay Phase", 3, false),
  TRS_MD_OVERLAY_RUN("TRS Market Data Exception Report Overlay Phase", 3, false),
  MD_BATCH_PRELIMINARY_CLEARING("Market Data Batch Exception Clearing Preliminary Phase", 2, false),
  MD_PRELIMINARY_CLEARING("Market Data Exception Clearing Preliminary Phase", 2, false),
  MD_BATCH_PRELIMINARY_RUN("Market Data Batch Exception Report Preliminary Phase", 1, false),
  TRS_MD_BATCH_PRELIMINARY_RUN(
      "TRS Market Data Batch Exception Report Preliminary Phase", 1, false),
  MD_PRELIMINARY_RUN("Market Data Exception Report Preliminary Phase", 1, false),
  TRS_MD_PRELIMINARY_RUN("TRS Market Data Exception Report Preliminary Phase", 1, false),
  MARKET_DATA_BATCH_UPLOAD("Market Data Upload", 0, false),
  MARKET_DATA_UPLOAD("Market Data Upload", 0, false),

  // Hidden steps
  FINALIZE("Complete dashboard", -1, false),
  RERUN("Rerun dashboard", -1, false);

  private final String label;
  private final int order;
  private final boolean allowDuplicates;

  public boolean isAfter(DashboardStep dashboardStep) {
    return this.order > dashboardStep.order;
  }
}
