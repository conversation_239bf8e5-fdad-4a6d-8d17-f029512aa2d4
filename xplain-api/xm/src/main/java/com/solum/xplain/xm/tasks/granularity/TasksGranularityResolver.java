package com.solum.xplain.xm.tasks.granularity;

import com.solum.xplain.xm.tasks.ExceptionManagementTaskExecution;
import java.util.Collection;
import java.util.List;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class TasksGranularityResolver<T extends ExceptionManagementTaskExecution> {
  private final List<TaskGranularityRule<T>> rules;

  public List<T> splitByGranularity(T execution) {
    var splitTasks = List.of(execution);
    for (TaskGranularityRule<T> rule : rules) {
      splitTasks = splitTasks.stream().map(rule::split).flatMap(Collection::stream).toList();
    }
    return splitTasks;
  }
}
