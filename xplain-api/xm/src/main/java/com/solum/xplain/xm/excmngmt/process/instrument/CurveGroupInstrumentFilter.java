package com.solum.xplain.xm.excmngmt.process.instrument;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@AllArgsConstructor
@EqualsAndHashCode
public class CurveGroupInstrumentFilter {
  private final Map<String, Set<String>> curveGroupInstruments;

  public static CurveGroupInstrumentFilter newOf(
      Map<String, List<InstrumentDefinition>> instrumentsMap) {
    var curveGroupInstruments =
        instrumentsMap.entrySet().stream()
            .collect(
                Collectors.toMap(
                    Entry::getKey,
                    v ->
                        v.getValue().stream()
                            .map(InstrumentDefinition::getKey)
                            .collect(Collectors.toSet())));

    return new CurveGroupInstrumentFilter(curveGroupInstruments);
  }

  public boolean hasInstrument(String curveGroupId, String instrumentKey) {
    return Optional.ofNullable(curveGroupInstruments.get(curveGroupId))
        .map(instruments -> instruments.contains(instrumentKey))
        .orElse(false);
  }
}
