package com.solum.xplain.xm.tasks.view;

import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.enums.TaskGranularityByAssetClassType;
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType;
import com.solum.xplain.xm.tasks.enums.TaskGranularityByInstrumentType;
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType;
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType;
import java.util.List;

public record TasksDefinitionView(
    TaskExceptionManagementType type,
    List<AssetClassTaskTeamsView> teams,
    List<TasksDefinitionOverrideView> overrides,
    TaskGranularityByAssetClassType granularityByAssetClassType,
    TaskGranularityByRateType granularityByRate,
    TaskGranularityBySectorType granularityBySector,
    TaskGranularityByInstrumentType granularityByInstrument,
    TaskGranularityByFxCcyPairType granularityByFxCcyPair) {}
