package com.solum.xplain.xm.workflow.steps.vd;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AutoRejectEmptyValueStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {
  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    ops.setOutcome(
        new MutablePropertyValues()
            .add(VdPhaseState.Fields.approval, VerificationStatus.REJECTED)
            .add(VdPhaseState.Fields.approvalComment, "NULL automatically rejected by the system.")
            .add(VdPhaseState.Fields.approvalEvidence, new ExceptionManagementEvidence()));
  }
}
