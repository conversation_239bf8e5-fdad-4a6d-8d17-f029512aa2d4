package com.solum.xplain.xm.excmngmt.rulesipv;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_CONFIGURE_VD_BREAK_TEST;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VD_BREAK_TEST;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VD_BREAK_TEST;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestOverrideKey.of;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.value.EnableForm;
import com.solum.xplain.core.common.value.ResequenceForm;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestView;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BreakTestFilter;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/exception-management/ipv-break-tests")
@AllArgsConstructor
public class IpvBreakTestController {

  private final IpvBreakTestControllerService service;

  @Operation(summary = "Get all IPV break tests")
  @GetMapping
  @CommonErrors
  @ScrolledFiltered
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public ScrollableEntry<IpvBreakTestView> getAll(
      TableFilter tableFilter,
      BreakTestFilter filter,
      @SortDefault(sort = {BaseBreakTestView.Fields.sequence}) ScrollRequest scrollRequest) {
    return service.getAll(scrollRequest, tableFilter, filter);
  }

  @Operation(summary = "Get one IPV break test")
  @GetMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public ResponseEntity<IpvBreakTestView> getOne(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.getOne(id));
  }

  @Operation(summary = "Create new IPV break test")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> create(@Valid @RequestBody IpvBreakTestForm form) {
    return eitherErrorItemResponse(service.create(form));
  }

  @Operation(summary = "Update IPV break test")
  @PutMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> update(
      @PathVariable("id") String id, @Valid @RequestBody IpvBreakTestForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Set enable flag")
  @PutMapping("/{id}/enable")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_VD_BREAK_TEST)
  public ResponseEntity<EntityId> updateEnabled(
      @PathVariable("id") String id, @Valid @RequestBody EnableForm form) {
    return eitherErrorItemResponse(service.enableDisable(id, form));
  }

  @Operation(summary = "Reorder sequence of break test to be before another")
  @PutMapping("/{id}/sequence")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_VD_BREAK_TEST)
  public ResponseEntity<EntityId> updateSequence(
      @PathVariable("id") String id, @Valid @RequestBody ResequenceForm form) {
    return eitherErrorItemResponse(service.resequence(id, form));
  }

  @Operation(summary = "Archive IPV break test")
  @PutMapping("/{id}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> archive(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.archive(id));
  }

  @Operation(summary = "Get IPV break test versions")
  @GetMapping("/{id}/versions")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public ResponseEntity<List<IpvBreakTestView>> getVersions(@PathVariable("id") String id) {
    return eitherErrorItemResponse(service.getVersions(id));
  }

  // Overrides

  @Operation(summary = "Get all IPV break overrides")
  @GetMapping("/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public List<IpvBreakTestOverrideView> getAll() {
    return service.getAllOverrides();
  }

  @Operation(summary = "Get IPV break test overrides")
  @GetMapping("/{id}/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public List<IpvBreakTestOverrideView> getAll(@PathVariable("id") String id) {
    return service.getOverrides(of(id, null));
  }

  @Operation(summary = "Get IPV break test override by id")
  @GetMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VD_BREAK_TEST)
  public ResponseEntity<IpvBreakTestOverrideView> getOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.getOverride(of(id, overrideId)));
  }

  @Operation(summary = "Create new IPV break test override")
  @PostMapping("/{id}/overrides")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> createOverride(
      @PathVariable("id") String id, @Valid @RequestBody IpvBreakTestOverrideForm form) {
    return eitherErrorItemResponse(service.createOverride(of(id, null), form));
  }

  @Operation(summary = "Update IPV break test override")
  @PutMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> updateOverride(
      @PathVariable("id") String id,
      @PathVariable("overrideId") String overrideId,
      @Valid @RequestBody IpvBreakTestOverrideForm form) {
    return eitherErrorItemResponse(service.updateOverride(of(id, overrideId), form));
  }

  @Operation(summary = "Delete IPV break test override")
  @DeleteMapping("/{id}/overrides/{overrideId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> deleteOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.deleteOverride(of(id, overrideId)));
  }

  @Operation(summary = "Enable/disable IPV break test override")
  @PutMapping("/{id}/overrides/{overrideId}/enable")
  @CommonErrors
  @PreAuthorize(AUTHORITY_CONFIGURE_VD_BREAK_TEST)
  public ResponseEntity<EntityId> enableDisableOverride(
      @PathVariable("id") String id,
      @PathVariable("overrideId") String overrideId,
      @Valid @RequestBody EnableForm f) {
    return eitherErrorItemResponse(service.enableDisableOverride(of(id, overrideId), f));
  }

  @Operation(summary = "Clone IPV break test override")
  @PutMapping("/{id}/overrides/{overrideId}/clone")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VD_BREAK_TEST)
  public ResponseEntity<EntityId> cloneOverride(
      @PathVariable("id") String id, @PathVariable("overrideId") String overrideId) {
    return eitherErrorItemResponse(service.cloneOverride(of(id, overrideId)));
  }
}
