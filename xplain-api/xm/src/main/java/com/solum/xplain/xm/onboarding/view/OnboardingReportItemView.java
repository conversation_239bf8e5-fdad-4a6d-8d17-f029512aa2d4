package com.solum.xplain.xm.onboarding.view;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import com.solum.xplain.core.portfolio.value.OnboardingValuationMetrics;
import com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus;
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItemSubmissionStatus;
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak;
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
public record OnboardingReportItemView(
    @Schema(description = "Item ID", requiredMode = REQUIRED) String id,
    @Schema(description = "Onboarding trade", requiredMode = REQUIRED) OnboardingTradeView trade,
    @Schema(description = "NAV on trade date") BigDecimal navOnTradeDate,
    @Schema(description = "NAV on effective date") BigDecimal navOnVendorOnboardingDate,
    @Schema(description = "Xplain check status", requiredMode = REQUIRED)
        ConformityCheckStatus xplainCheckStatus,
    @Schema(description = "Xplain check message", requiredMode = REQUIRED)
        String xplainCheckMessage,
    @Schema(description = "Market check status", requiredMode = REQUIRED)
        ConformityCheckStatus marketCheckStatus,
    @Schema(description = "Market check message", requiredMode = REQUIRED)
        String marketCheckMessage,
    @Schema(description = "Vendor check status", requiredMode = REQUIRED)
        ConformityCheckStatus vendorCheckStatus,
    @Schema(description = "Vendor check message", requiredMode = REQUIRED)
        String vendorCheckMessage,
    @Schema(description = "Submission status", requiredMode = REQUIRED)
        OnboardingReportItemSubmissionStatus submissionStatus,
    @Schema(description = "Used break tests", requiredMode = REQUIRED)
        List<OnboardingTradeResultBreak> breakTests,
    @Schema(description = "Valuation metrics", requiredMode = REQUIRED)
        OnboardingValuationMetrics valuationMetrics,
    @Schema(description = "Primary provider data") OnboardingVendorMetrics vendorPrimaryMetrics,
    @Schema(description = "Secondary provider data") OnboardingVendorMetrics vendorSecondaryMetrics,
    @Schema(description = "Tertiary provider data") OnboardingVendorMetrics vendorTertiaryMetrics,
    @Schema(description = "Quaternary provider data")
        OnboardingVendorMetrics vendorQuaternaryMetrics) {}
