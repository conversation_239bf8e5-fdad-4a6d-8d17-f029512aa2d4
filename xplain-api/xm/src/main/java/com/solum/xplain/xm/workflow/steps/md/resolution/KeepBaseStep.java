package com.solum.xplain.xm.workflow.steps.md.resolution;

import static com.solum.xplain.xm.workflow.steps.md.resolution.Errors.resolvedValueIsEmpty;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import com.solum.xplain.xm.workflow.state.MdPhaseContext;
import com.solum.xplain.xm.workflow.state.MdPhaseState;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component("mdKeepBaseStep")
@RequiredArgsConstructor
public class KeepBaseStep<T extends MdPhaseState, C extends MdPhaseContext>
    implements ServiceStepExecutor<T, C> {

  @Override
  public void runStep(StepStateOps<T, C> ops) {
    if (ops.getInitialState().getBaseValue() == null) {
      ops.throwError(resolvedValueIsEmpty(ops.getContext()));
    }

    ops.setOutcome(
        new MutablePropertyValues()
            .add(
                ops.getInitialState() instanceof MdPreliminaryState
                    ? MdPreliminaryState.Fields.valuePendingApproval
                    : MdOverlayState.Fields.valuePendingApproval,
                new AttributedValue(
                    ops.getInitialState().getBaseValue(),
                    null,
                    ops.getInitialState().getProviderName()))
            .add(
                ops.getInitialState() instanceof MdPreliminaryState
                    ? MdPreliminaryState.Fields.entryStatus
                    : MdOverlayState.Fields.entryStatus,
                EntryResultStatus.WAITING_APPROVAL));
  }
}
