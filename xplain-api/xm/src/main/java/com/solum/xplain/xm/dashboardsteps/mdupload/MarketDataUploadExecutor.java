package com.solum.xplain.xm.dashboardsteps.mdupload;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import io.atlassian.fugue.Either;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class MarketDataUploadExecutor {

  private final DashboardStepProcessor processor;

  public MarketDataUploadExecutor(DashboardStepProcessor processor) {
    this.processor = processor;
  }

  public Either<List<ErrorItem>, EntityId> execute(Dashboard dashboard) {
    return processor.createMdSteps(steps(dashboard)).map(steps -> dashboard.entityId());
  }

  private List<DashboardEntryMd> steps(Dashboard dashboard) {
    DashboardEntryMd step =
        DashboardEntryMd.newOfMarketDataUpload(dashboard.getId()).started().completed();
    return List.of(step);
  }
}
