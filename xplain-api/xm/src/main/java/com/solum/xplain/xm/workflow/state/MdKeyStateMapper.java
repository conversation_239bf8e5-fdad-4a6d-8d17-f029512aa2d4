package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = {ExceptionManagementResultMapper.class})
public abstract class MdKeyStateMapper {
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "marketDataGroupId", source = "context.marketDataGroupId")
  @Mapping(
      target = "dashboardId",
      expression =
          "java(com.solum.xplain.xm.workflow.XmWorkflowService.dashboardIdFromBusinessKey(ops.getRootBusinessKey()))")
  @Mapping(target = "exceptionManagementResultId", ignore = true)
  @Mapping(target = "taskId", ignore = true)
  @Mapping(target = "valuationDate", source = "context.stateDate.actualDate")
  @Mapping(target = "status", constant = "VERIFIED")
  @Mapping(target = "allProvidersData", source = "context.allProviderData")
  @Mapping(target = "providerData", source = "preliminaryBreakResults.providerData")
  @Mapping(target = "breakTests", source = "preliminaryBreakResults.breakTestResults")
  @Mapping(
      target = "appliedTestsCount",
      expression =
          "java(com.solum.xplain.xm.excmngmt.HasEntryResult.countAppliedTests(preliminaryBreakResults.breakTestResults()))")
  @Mapping(target = "hasBreaks", expression = "java(preliminaryBreakResults.hasBreak())")
  @Mapping(
      target = "maxTriggeredThresholdLevel",
      expression =
          "java(com.solum.xplain.xm.excmngmt.HasEntryResult.maxTriggeredThresholdLevel(preliminaryBreakResults.breakTestResults()))")
  @Mapping(
      target = "resolution",
      expression =
          "java(com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution.newOf())")
  @Mapping(target = "previousStatuses", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  public abstract InstrumentResultPreliminary toInstrumentResultPreliminary(
      MdKeyContext context,
      PreliminaryBreakResults preliminaryBreakResults,
      @Context StepStateOps<MdKeyState, MdKeyContext> ops);
}
