package com.solum.xplain.xm.excmngmt.rules.value;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Size;
import java.util.List;
import org.hibernate.validator.group.GroupSequenceProvider;

@GroupSequenceProvider(TenorFilterFormGroupProvider.class)
public record TenorFilterForm(
    @NotNull Boolean allowAllTenors,
    @Valid @Null(groups = AllTenorsGroup.class) @Size(min = 1, groups = TenorBucketsGroup.class)
        List<TenorBucketForm> tenorBuckets) {}
