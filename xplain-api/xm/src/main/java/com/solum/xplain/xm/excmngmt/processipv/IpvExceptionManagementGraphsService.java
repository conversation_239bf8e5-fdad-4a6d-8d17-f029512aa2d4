package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01_VEGA;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_VEGA;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.math.BigDecimal.valueOf;
import static java.math.MathContext.DECIMAL64;
import static java.math.RoundingMode.FLOOR;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeResultTasksBreaksFilterForm;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvXmDistributionGraphView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvXmDistributionGraphView.Bin;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IpvExceptionManagementGraphsService {

  private static final int NUMBER_OF_BINS = 20;
  private static final int MAX_BIN_WIDTH_01 = 2;
  private static final int MAX_BIN_WIDTH_NON_01 = 10;
  private final IpvExceptionManagementCalculationRepository repository;

  public List<IpvXmDistributionGraphView> breaksDistributionGraphs(
      TradeResultTasksBreaksFilterForm filter) {
    var results = ImmutableList.<IpvXmDistributionGraphView>builder();
    var distribution = repository.resultValuesDistribution(filter.getTaskIds(), filter.getFilter());
    for (var bt : distribution) {
      var thresholds = bt.getThresholds().stream().sorted(BigDecimal::compareTo).toList();
      var values = bt.getValues().stream().sorted(BigDecimal::compareTo).toList();

      var stats = new DescriptiveStatistics();
      values.stream().map(BigDecimal::doubleValue).forEach(stats::addValue);

      var binOffset = thresholds.getFirst();

      var maxValue = values.getLast();
      var binWidth = resolveBinWidth(bt.getMeasureType(), thresholds, values);

      int[] histogram = calculateHistogram(bt.getValues(), binWidth, binOffset);

      var bins = new ArrayList<Bin>();
      for (int i = 0; i < NUMBER_OF_BINS; i++) {
        var startValue = binOffset.add(binWidth.multiply(valueOf(i)));
        var endValue =
            i + 1 == NUMBER_OF_BINS
                ? maxValue.max(binOffset.add(binWidth.multiply(valueOf(NUMBER_OF_BINS))))
                : binOffset.add(binWidth.multiply(valueOf(i + 1L)));
        bins.add(new Bin(startValue, endValue, histogram[i]));
      }

      results.add(
          new IpvXmDistributionGraphView(
              bt.getBreakTestName(),
              thresholds,
              stats.getMean(),
              stats.getStandardDeviation(),
              binWidth,
              bins));
    }
    return results.build();
  }

  private BigDecimal resolveBinWidth(
      IpvMeasureType measureType, List<BigDecimal> thresholds, List<BigDecimal> values) {
    var averageBinWidth = values.getLast().divide(valueOf(NUMBER_OF_BINS), 2, FLOOR);
    if (averageBinWidth.compareTo(ZERO) == 0) {
      averageBinWidth = ONE;
    }
    if (thresholds.size() >= 2) {
      var threshold12Delta = thresholds.get(1).subtract(thresholds.get(0)).abs();
      return averageBinWidth.min(threshold12Delta);
    } else if (is01Measure(measureType)) {
      return averageBinWidth.min(valueOf(MAX_BIN_WIDTH_01));
    } else {
      return averageBinWidth.min(valueOf(MAX_BIN_WIDTH_NON_01));
    }
  }

  private boolean is01Measure(IpvMeasureType measureType) {
    return measureType == GREEKS_DV01
        || measureType == GREEKS_VEGA
        || measureType == GREEKS_DV01_VEGA
        || measureType == GREEKS;
  }

  private int[] calculateHistogram(
      List<BigDecimal> data, BigDecimal binWidth, BigDecimal binOffset) {
    int[] histogram = new int[NUMBER_OF_BINS];

    for (var value : data) {
      int binIndex =
          (int) Math.floor(value.subtract(binOffset).divide(binWidth, DECIMAL64).doubleValue());
      if (binIndex >= NUMBER_OF_BINS) {
        binIndex = NUMBER_OF_BINS - 1;
      }
      histogram[binIndex]++;
    }

    return histogram;
  }
}
