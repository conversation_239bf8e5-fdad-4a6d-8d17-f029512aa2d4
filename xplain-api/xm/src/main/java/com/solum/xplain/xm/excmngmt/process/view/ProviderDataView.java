package com.solum.xplain.xm.excmngmt.process.view;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class ProviderDataView {

  @ConfigurableViewField(InstrumentResultViewFieldName.PROVIDER_SUFFIX)
  private String provider;

  @ConfigurableViewField(InstrumentResultViewFieldName.ASK_VALUE_SUFFIX)
  @ConfigurableViewField(
      value = InstrumentResultViewFieldName.ASK_DELTA_SUFFIX,
      deriveAs = "askPreviousValue - askValue")
  private BigDecimal askValue;

  @ConfigurableViewField(InstrumentResultViewFieldName.ASK_PREVIOUS_VALUE_SUFFIX)
  private BigDecimal askPreviousValue;

  @ConfigurableViewField(InstrumentResultViewFieldName.MID_VALUE_SUFFIX)
  @ConfigurableViewField(
      value = InstrumentResultViewFieldName.MID_DELTA_SUFFIX,
      deriveAs = "midPreviousValue - midValue")
  private BigDecimal midValue;

  @ConfigurableViewField(InstrumentResultViewFieldName.MID_PREVIOUS_VALUE_SUFFIX)
  private BigDecimal midPreviousValue;

  @ConfigurableViewField(InstrumentResultViewFieldName.BID_VALUE_SUFFIX)
  @ConfigurableViewField(
      value = InstrumentResultViewFieldName.BID_DELTA_SUFFIX,
      deriveAs = "bidPreviousValue - bidValue")
  private BigDecimal bidValue;

  @ConfigurableViewField(InstrumentResultViewFieldName.BID_PREVIOUS_VALUE_SUFFIX)
  private BigDecimal bidPreviousValue;
}
