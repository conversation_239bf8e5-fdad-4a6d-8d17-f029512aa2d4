package com.solum.xplain.xm.dashboards.views.auditablebreaktest.mappers;

import com.solum.xplain.xm.dashboards.views.auditablebreaktest.DashboardIpvProviderView;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface DashboardIpvProviderViewMapper {
  @Mapping(source = "impliedVol", target = "impliedVolatility")
  @Mapping(source = "atmImpliedVol", target = "atmImpliedVolatility")
  @Mapping(source = "realisedVol", target = "realisedVolatility")
  @Mapping(source = "fairVol", target = "fairVolatility")
  DashboardIpvProviderView from(ProviderDataWithGreeks providerDataWithGreeks);
}
