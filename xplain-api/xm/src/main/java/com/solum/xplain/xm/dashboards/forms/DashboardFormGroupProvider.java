package com.solum.xplain.xm.dashboards.forms;

import com.google.common.collect.ImmutableList;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class DashboardFormGroupProvider implements DefaultGroupSequenceProvider<DashboardForm> {

  @Override
  public List<Class<?>> getValidationGroups(DashboardForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(DashboardForm.class);

    if (form == null || form.getType() == null) {
      return builder.build();
    }

    switch (form.getType()) {
      case MARKET_DATA_BATCH, TRS_MARKET_DATA_BATCH -> builder.add(MarketDataBatch.class);
      case MARKET_DATA, TRS_MARKET_DATA -> builder.add(MarketData.class);
      case VALUATION_DATA -> builder.add(ValuationData.class);
    }
    return builder.build();
  }
}
