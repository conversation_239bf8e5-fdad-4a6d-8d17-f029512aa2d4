package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardStepCleanup;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MdBatchPreliminaryClearingCleanup implements DashboardStepCleanup {

  private final TaskExecutionRepository repository;
  private final DashboardEntryRepository entryRepository;

  @Override
  public void execute(String dashboardId) {
    repository.preliminaryBatchDeleted(dashboardId);
    entryRepository.deleteMdBatchEntries(dashboardId, DashboardStep.MD_BATCH_PRELIMINARY_CLEARING);
  }
}
