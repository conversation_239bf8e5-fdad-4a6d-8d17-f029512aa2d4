package com.solum.xplain.xm.excmngmt.rulesbase.value;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
public class BaseBreakTestOverrideForm {

  @NotEmpty private final List<BigDecimal> threshold;

  @NotNull private final Boolean enabled;
}
