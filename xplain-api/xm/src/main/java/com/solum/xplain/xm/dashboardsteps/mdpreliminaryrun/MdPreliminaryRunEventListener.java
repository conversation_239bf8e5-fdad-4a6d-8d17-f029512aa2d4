package com.solum.xplain.xm.dashboardsteps.mdpreliminaryrun;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing.MdPreliminaryClearingEvent;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdPreliminaryRunEventListener {

  private final DashboardRepository dashboardRepository;
  private final ApplicationEventPublisher publisher;
  private final MdPreliminaryRunExecutor runExecutor;
  private final MdPreliminaryRunCleanup runEraser;

  public MdPreliminaryRunEventListener(
      DashboardRepository dashboardRepository,
      ApplicationEventPublisher publisher,
      MdPreliminaryRunExecutor runExecutor,
      MdPreliminaryRunCleanup runEraser) {
    this.dashboardRepository = dashboardRepository;
    this.publisher = publisher;
    this.runExecutor = runExecutor;
    this.runEraser = runEraser;
  }

  @Async
  @EventListener
  public void onEvent(MdPreliminaryRunEvent event) {
    log.debug("Received MdPreliminaryRunEvent: {}", event);
    if (DashboardEvent.Type.REQUESTED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::executeRun);
    } else if (DashboardEvent.Type.COMPLETED == event.getType()) {
      dashboard(event.getDashboardId())
          .ifPresentOrElse(this::finalizeRun, () -> runEraser.execute(event.getDashboardId()));
    }
  }

  private void executeRun(Dashboard dashboard) {
    runExecutor
        .execute(dashboard)
        .toOptional()
        .ifPresent(exceptionManagementResultId -> onRunCompleted(dashboard));
  }

  private void onRunCompleted(Dashboard dashboard) {
    publisher.publishEvent(
        MdPreliminaryRunEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.COMPLETED)
            .build());
  }

  private void finalizeRun(Dashboard dashboard) {
    publisher.publishEvent(
        MdPreliminaryClearingEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.REQUESTED)
            .build());
  }

  private Optional<Dashboard> dashboard(String dashboardId) {
    return dashboardRepository.dashboard(dashboardId).toOptional();
  }
}
