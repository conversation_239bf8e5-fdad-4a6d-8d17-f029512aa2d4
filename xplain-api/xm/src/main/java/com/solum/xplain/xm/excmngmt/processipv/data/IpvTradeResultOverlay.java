package com.solum.xplain.xm.excmngmt.processipv.data;

import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.MANUAL;
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P1;
import static java.util.Optional.empty;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.versions.WithPreviousStatuses;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.excmngmt.HasEntryResult;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence;
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistory;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
@FieldNameConstants
public class IpvTradeResultOverlay
    implements HasEntryResult<IpvTradeResultResolution>,
        WithPreviousStatuses<EntryResultStatusHistory> {

  public static final String IPV_TRADE_RESULT_OVERLAY_COLLECTION = "ipvTradeResultOverlay";

  @Id private String id;
  private IpvExceptionManagementPhase phase;
  private String dashboardId;
  private LocalDate valuationDate;
  private String taskId;

  private PricingSlot pricingSlot;
  private SlaDeadline slaDeadline;
  private String ipvDataGroupId;
  private Trade trade;

  // XM calculation currency
  private String calculationCurrency;

  private EntryResultStatus status;

  // Final trade PV. Provider1 PV if no breaks, else mirrors resolution value
  private BigDecimal resolvedValue;

  private ProviderDataWithGreeks primaryProviderData;
  private ProviderDataWithGreeks secondaryProviderData;
  private ProviderDataWithGreeks tertiaryProviderData;
  private ProviderDataWithGreeks quaternaryProviderData;

  private List<TradeResultBreak> breakTests;
  private long appliedTestsCount;
  private boolean hasBreaks;
  private Integer maxTriggeredThresholdLevel;

  private IpvTradeResultResolution resolution;

  private List<EntryResultStatusHistory> previousStatuses;

  @LastModifiedBy private AuditUser modifiedBy;
  @LastModifiedDate private LocalDateTime modifiedAt;

  public Optional<ErrorItem> overrideResult(
      BigDecimal newValue,
      String comment,
      ExceptionManagementEvidence evidence,
      AuditUser currentUser,
      LocalDateTime overrideTs) {
    saveStatus();

    // Add the new resolution
    status = WAITING_APPROVAL;
    resolution = new IpvTradeResultResolution();
    resolution.setResolution(TradeResultResolutionType.OVERRIDE_USER);
    resolution.setResolutionComment(comment);
    resolution.setResolutionEvidence(evidence);
    resolution.resolveProvider(null, MANUAL);
    var err = resolution.resolveValue(trade, () -> Optional.ofNullable(newValue));
    if (err.isPresent()) {
      return err;
    }
    resolvedValue = resolution.getValue();
    modifiedBy = currentUser;
    modifiedAt = overrideTs;
    saveStatus();

    // Add an automatic approval
    status = VERIFIED;
    resolution.setApprovalComment("Automatically validated by the system.");
    resolution.setApprovalEvidence(new ExceptionManagementEvidence());
    return empty();
  }

  @Override
  public HasEntryResult<IpvTradeResultResolution> undoResolution() {
    this.saveStatus();
    this.setStatus(EntryResultStatus.WAITING_RESOLUTION);
    this.setResolution(null);
    this.setResolvedValue(null);
    return this;
  }

  public IpvTradeResultOverlay withBreakTests(List<TradeResultBreak> breakTests) {
    var breaksTriggered = HasEntryResult.anyHasBreak(breakTests);
    this.setHasBreaks(breaksTriggered);
    this.setBreakTests(breakTests);
    this.setStatus(
        breaksTriggered ? EntryResultStatus.WAITING_RESOLUTION : EntryResultStatus.VERIFIED);
    this.setAppliedTestsCount(HasEntryResult.countAppliedTests(breakTests));
    this.setMaxTriggeredThresholdLevel(HasEntryResult.maxTriggeredThresholdLevel(breakTests));
    if (!breaksTriggered) {
      this.setResolution(IpvTradeResultResolution.newOf());
      this.setResolvedValue(
          primaryProviderData.getPv() == null ? null : primaryProviderData.getPv().getValue());
      resolution.resolveProvider(primaryProviderData.getProvider(), P1);
    }
    return this;
  }

  @EqualsAndHashCode.Include(replaces = "resolvedValue")
  private BigDecimal normalisedResolvedValue() {
    return resolvedValue == null ? null : resolvedValue.stripTrailingZeros();
  }
}
