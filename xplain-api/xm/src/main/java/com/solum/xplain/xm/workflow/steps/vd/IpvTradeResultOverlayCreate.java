package com.solum.xplain.xm.workflow.steps.vd;

import com.solum.xplain.workflow.repository.DataModificationCommand;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import java.time.LocalDateTime;
import org.springframework.cache.Cache;
import org.springframework.data.mongodb.core.BulkOperations;

public record IpvTradeResultOverlayCreate(IpvTradeResultOverlay data)
    implements DataModificationCommand<IpvTradeResultOverlay> {
  @Override
  public Class<? super IpvTradeResultOverlay> getEntity() {
    return IpvTradeResultOverlay.class;
  }

  @Override
  public IpvTradeResultOverlay apply(BulkOperations bulkOps, Cache cache) {
    data.setModifiedAt(LocalDateTime.now());
    bulkOps.insert(data);
    // Cache ignored
    return data;
  }
}
