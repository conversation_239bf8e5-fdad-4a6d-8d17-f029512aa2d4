package com.solum.xplain.xm.excmngmt.process.data;

import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.xm.excmngmt.HasProviderData;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@FieldNameConstants
public class ProviderData implements HasProviderData, Serializable {

  @ConfigurableViewQuery(sortable = true)
  private String provider;

  @ConfigurableViewIgnore private BigDecimal value;
  @ConfigurableViewIgnore private BigDecimal previousValue;
  @ConfigurableViewIgnore private ValueBidAskType bidAskType;

  public ProviderData copyWithNewValue(BigDecimal newValue) {
    return ProviderData.of(
        this.getProvider(), newValue, this.getPreviousValue(), this.getBidAskType());
  }

  @EqualsAndHashCode.Include(replaces = "value")
  private BigDecimal normalisedValue() {
    return value == null ? null : value.stripTrailingZeros();
  }

  @EqualsAndHashCode.Include(replaces = "previousValue")
  private BigDecimal normalisedPreviousValue() {
    return previousValue == null ? null : previousValue.stripTrailingZeros();
  }
}
