package com.solum.xplain.xm.tasks.form;

import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.settings.entity.TaskDefaultTeams;
import com.solum.xplain.core.teams.TeamUtils;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.xm.tasks.ExceptionManagementTaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import org.springframework.data.mongodb.core.query.Criteria;

public record TaskExecutionFilter(boolean onlyTeam) {
  public static TaskExecutionFilter userFilter() {
    return new TaskExecutionFilter(true);
  }

  public static TaskExecutionFilter adminFilter() {
    return new TaskExecutionFilter(false);
  }

  public Criteria criteria(XplainPrincipal user, Supplier<TaskDefaultTeams> teamsSupplier) {
    Criteria criteria = new Criteria();
    if (onlyTeam) {
      var userTeams = TeamUtils.collectUserTeamIds(user);

      criteria.orOperator(
          inProgressCriteria(
              ExceptionManagementTaskExecution.Fields.approvedBy,
              user.getId(),
              TaskExecutionStatus.IN_APPROVAL),
          inProgressCriteria(
              ExceptionManagementTaskExecution.Fields.performedBy,
              user.getId(),
              TaskExecutionStatus.IN_RESOLUTION),
          inProgressCriteria(
              ExceptionManagementTaskExecution.Fields.performedBy,
              user.getId(),
              TaskExecutionStatus.PAUSED),
          teamCriteria(
              ExceptionManagementTaskExecution.Fields.resolutionTeams,
              userTeams,
              teamsSupplier.get().getDefaultResolutionTeam(),
              TaskExecutionStatus.NOT_STARTED,
              TaskExecutionStatus.REJECTED),
          teamCriteria(
              ExceptionManagementTaskExecution.Fields.approvalTeams,
              userTeams,
              teamsSupplier.get().getDefaultApprovalTeam(),
              TaskExecutionStatus.PENDING_APPROVAL));
    }
    return criteria;
  }

  private Criteria teamCriteria(
      String teamPath,
      List<String> userTeamIds,
      TeamNameView defaultTeam,
      TaskExecutionStatus... statuses) {
    var criteria = where(ExceptionManagementTaskExecution.Fields.status).in(List.of(statuses));
    if (!userBelongsToDefaultTeam(userTeamIds, defaultTeam)) {
      criteria.andOperator(
          where(teamPath).elemMatch(where(TeamNameView.Fields.id).in(userTeamIds)));
    }
    return criteria;
  }

  private boolean userBelongsToDefaultTeam(List<String> userTeamIds, TeamNameView view) {
    return Optional.ofNullable(view)
        .map(TeamNameView::getId)
        .filter(userTeamIds::contains)
        .isPresent();
  }

  private Criteria inProgressCriteria(String userPath, String userId, TaskExecutionStatus status) {
    return where(ExceptionManagementTaskExecution.Fields.status)
        .is(status)
        .and(propertyName(userPath, AuditUser.Fields.userId))
        .is(userId);
  }
}
