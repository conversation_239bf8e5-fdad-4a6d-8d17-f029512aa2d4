package com.solum.xplain.xm.dashboards.views;

import com.solum.xplain.xm.dashboards.enums.DashboardStatus;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.DashboardType;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @param mdExceptionManagementSetup For MARKET_DATA
 * @param trsMdExceptionManagementSetup For MARKET_DATA
 * @param vdExceptionManagementSetup For VALUATION_DATA
 * @param dateRange Curve / Valuation date
 */
public record DashboardView(
    String id,
    DashboardType type,
    MdExceptionManagementSetupView mdExceptionManagementSetup,
    TrsMdExceptionManagementSetupView trsMdExceptionManagementSetup,
    VdExceptionManagementSetupView vdExceptionManagementSetup,
    DateRangeView dateRange,
    List<DashboardEntryMdBatchView> mdBatchEntries,
    List<DashboardEntryMdView> mdEntries,
    List<DashboardEntryVdView> vdEntries,
    LocalDateTime startedAt,
    LocalDateTime finishedAt,
    LocalDate stateDate,
    LocalDateTime recordDate)
    implements HasDashboardStatus {
  public boolean getShowMdResults() {
    return mdEntries.stream()
        .filter(c -> c.getStep() == DashboardStep.MD_OVERLAY_CLEARING)
        .anyMatch(c -> c.getStatus() != StepStatus.NOT_STARTED);
  }

  public boolean getShowVdResults() {
    if (getStatus() == DashboardStatus.COMPLETED) {
      return true;
    }
    final DashboardStep resultsStep = getVdResultsStep();
    // Show VD results if any of the final-phase clearing steps have been started
    return vdEntries.stream()
        .filter(c1 -> c1.getStep() == resultsStep)
        .anyMatch(c -> c.getStatus() != StepStatus.NOT_STARTED);
  }

  private DashboardStep getVdResultsStep() {
    if (vdEntries.stream().anyMatch(c -> c.getStep() == DashboardStep.IPV_OVERLAY_CLEARING_2)) {
      return DashboardStep.IPV_OVERLAY_CLEARING_2;
    } else {
      return DashboardStep.IPV_OVERLAY_CLEARING;
    }
  }

  public IpvExceptionManagementPhase getVdResultsPhase() {
    return switch (getVdResultsStep()) {
      case IPV_OVERLAY_CLEARING_2 -> IpvExceptionManagementPhase.OVERLAY_2;
      case IPV_OVERLAY_CLEARING -> IpvExceptionManagementPhase.OVERLAY_1;
      default ->
          throw new IllegalStateException("Unexpected VD results step: " + getVdResultsStep());
    };
  }
}
