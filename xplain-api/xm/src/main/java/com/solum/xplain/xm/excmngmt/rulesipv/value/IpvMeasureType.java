package com.solum.xplain.xm.excmngmt.rulesipv.value;

import static java.math.BigDecimal.ZERO;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.Set;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public enum IpvMeasureType {
  RELATIVE_DIFF("Relative", null),
  ABSOLUTE_DIFF("Absolute", null),
  VALUE("Value", null),
  GREEKS_DV01("Greeks - 01", null),
  GREEKS_VEGA(
      "Greeks - Vega",
      Set.of(
          CoreProductType.SWAPTION,
          CoreProductType.FXOPT,
          CoreProductType.FXCOLLAR,
          CoreProductType.CAP_FLOOR)),
  GRE<PERSON>KS_DV01_VEGA("Greeks - 01 + Vega", null),
  GRE<PERSON><PERSON>("Greeks - Day-on-Day", null),
  NAV("NAV", null),
  NOTIONAL("Notional", null);

  private final String name;
  private final Set<ProductType> onlyProductsAllowed;

  private static final BigDecimal ONE_BASIS_POINT = BigDecimal.valueOf(1e-4);
  private static final BigDecimal TWO_DECIMAL_POINTS = BigDecimal.valueOf(1e-2);
  private static final BigDecimal NAV_SCALE = BigDecimal.valueOf(10_000);

  IpvMeasureType(String name, Set<ProductType> onlyProductsAllowed) {
    this.name = name;
    this.onlyProductsAllowed = onlyProductsAllowed;
  }

  public String getName() {
    return name;
  }

  public boolean supportsProductType(ProductType type) {
    return onlyProductsAllowed == null || onlyProductsAllowed.contains(type);
  }

  public Optional<BigDecimal> performMeasure(@Nullable BigDecimal v1, @Nullable BigDecimal v2) {
    return performMeasure(v1, v2, null, null, null, null, null, null);
  }

  public Optional<BigDecimal> performMeasure(
      @Nullable BigDecimal v1,
      @Nullable BigDecimal v2,
      @Nullable BigDecimal d,
      @Nullable BigDecimal v,
      @Nullable TradeBreakScalingData scalingData) {
    return performMeasure(v1, v2, d, v, null, null, null, scalingData);
  }

  public Optional<BigDecimal> performMeasure(
      @Nullable BigDecimal presentValue,
      @Nullable BigDecimal previousValue,
      @Nullable BigDecimal deltaDayAverage,
      @Nullable BigDecimal vegaDayAverage,
      @Nullable BigDecimal parRateDayDiff,
      @Nullable BigDecimal impliedVolDayDiff,
      @Nullable ProductType productType,
      @Nullable TradeBreakScalingData scalingData) {

    if (this == VALUE) {
      return Optional.ofNullable(presentValue).map(BigDecimal::abs);
    }

    if (presentValue == null || previousValue == null) {
      return Optional.empty();
    }

    var valueDiff = presentValue.subtract(previousValue);
    return switch (this) {
      case RELATIVE_DIFF -> safeDiv(valueDiff, previousValue);
      case ABSOLUTE_DIFF -> Optional.of(valueDiff.abs());
      case GREEKS_DV01 ->
          ofNullable(deltaDayAverage).flatMap(r -> safeDiv(valueDiff.abs(), r.abs()));
      case GREEKS_VEGA ->
          ofNullable(vegaDayAverage).flatMap(r -> safeDiv(valueDiff.abs(), r.abs()));
      case GREEKS_DV01_VEGA ->
          ofNullable(deltaDayAverage)
              .flatMap(
                  r ->
                      safeDiv(
                          valueDiff.abs(), ofNullable(vegaDayAverage).orElse(ZERO).add(r).abs()));
      case GREEKS ->
          performMeasureGreeks(
              presentValue,
              previousValue,
              deltaDayAverage,
              vegaDayAverage,
              parRateDayDiff,
              impliedVolDayDiff,
              productType);
      case NAV ->
          ofNullable(scalingData)
              .map(TradeBreakScalingData::nav)
              .flatMap(v -> safeDiv(valueDiff.multiply(NAV_SCALE), v));
      case NOTIONAL ->
          ofNullable(scalingData)
              .map(TradeBreakScalingData::notional)
              .flatMap(v -> safeDiv(valueDiff.multiply(NAV_SCALE), v));
      case VALUE -> throw new IllegalStateException("Unexpected value: " + this);
    };
  }

  private Optional<BigDecimal> performMeasureGreeks(
      @NonNull BigDecimal presentValue,
      @NonNull BigDecimal previousValue,
      @Nullable BigDecimal deltaDayAverage,
      @Nullable BigDecimal vegaDayAverage,
      @Nullable BigDecimal parRateDayDiff,
      @Nullable BigDecimal impliedVolDayDiff,
      @Nullable ProductType productType) {
    if (deltaDayAverage == null || parRateDayDiff == null) {
      return Optional.empty();
    }

    deltaDayAverage = deltaDayAverage.divide(ONE_BASIS_POINT, RoundingMode.FLOOR);

    if (productType != null
        && (productType.equals(CoreProductType.FXOPT)
            || productType.equals(CoreProductType.FXCOLLAR))) {
      vegaDayAverage =
          ofNullable(vegaDayAverage).orElse(ZERO).divide(TWO_DECIMAL_POINTS, RoundingMode.FLOOR);
    } else {
      vegaDayAverage =
          ofNullable(vegaDayAverage).orElse(ZERO).divide(ONE_BASIS_POINT, RoundingMode.FLOOR);
    }

    var deltaV = presentValue.subtract(previousValue);
    var dp = deltaDayAverage.multiply(parRateDayDiff);
    var vi = vegaDayAverage.multiply(ofNullable(impliedVolDayDiff).orElse(ZERO));

    return safeDiv(deltaV, dp.add(vi));
  }

  private Optional<BigDecimal> safeDiv(@NonNull BigDecimal v1, @NonNull BigDecimal v2) {
    if (v2.compareTo(ZERO) == 0) {
      return Optional.empty();
    }

    return Optional.of(v1.divide(v2, 6, RoundingMode.FLOOR).abs());
  }
}
