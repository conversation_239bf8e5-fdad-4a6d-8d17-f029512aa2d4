package com.solum.xplain.xm.excmngmt.rulesbase.value;

import com.solum.xplain.xm.excmngmt.rules.value.Operator;
import com.solum.xplain.xm.excmngmt.rulesbase.validation.ValidThresholds;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

@Data
@SuperBuilder
@ValidThresholds
public abstract class BaseBreakTestForm {

  @Schema(description = "Name of the break test")
  @NotEmpty
  private final String name;

  @Schema(
      description = "User-specified ordering of the break test, used mainly for display purposes")
  @Nullable
  private final Integer sequence;

  @Schema(description = "Whether the break test is enabled. Disabled break tests won't be run.")
  @NotNull
  private final Boolean enabled;

  @Schema(description = "Version description")
  private final String comment;

  public abstract String getParentBreakTestId();

  public abstract Operator getOperator();

  public abstract List<BigDecimal> getThreshold();
}
