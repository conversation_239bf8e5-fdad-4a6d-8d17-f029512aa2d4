package com.solum.xplain.xm.workflow.repository.fragment;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition;
import com.solum.xplain.xm.tasks.entity.TasksDefinition;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import java.util.List;
import java.util.Map;

public interface XmStepInstanceQueries {
  /**
   * Find the user task step instances which are reportable and match the given task groups and
   * status. They must be actionable by the given user.
   *
   * <p>A step is actionable by a user if it is either assigned to the user, or is unassigned but
   * has candidate criteria which the user meets (i.e. member of the candidate team and not
   * excluded).
   *
   * @param taskGroups the groups of tasks to match
   * @param statuses the statuses the step instances must be in
   * @param user the user who must be able to action the step instances
   * @param tasksDefinitions the definition of task granularity to use to interpret the group
   *     specifications
   * @return list of all step instances matching the task groups and status, with initial state and
   *     outcome unset for performance
   */
  List<StepInstance> findStepInstancesForVdUserTasks(
      List<String> taskGroups,
      List<WorkflowStatus> statuses,
      XplainPrincipal user,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions);

  /**
   * Find the user task step instances which are reportable and match the given task groups and
   * status. They must be actionable by the given user.
   *
   * <p>A step is actionable by a user if it is either assigned to the user, or is unassigned but
   * has candidate criteria which the user meets (i.e. member of the candidate team and not
   * excluded).
   *
   * @param taskGroups the groups of tasks to match
   * @param statuses the statuses the step instances must be in
   * @param user the user who must be able to action the step instances
   * @param tasksDefinitions the definition of task granularity to use to interpret the group
   *     specifications
   * @return list of all step instances matching the task groups and status, with initial state and
   *     outcome unset for performance
   */
  List<StepInstance> findStepInstancesForMdUserTasks(
      List<String> taskGroups,
      List<WorkflowStatus> statuses,
      XplainPrincipal user,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions);
}
