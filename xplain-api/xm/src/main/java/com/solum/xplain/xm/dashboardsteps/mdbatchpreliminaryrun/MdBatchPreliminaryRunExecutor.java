package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationService;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Supplier;
import org.springframework.stereotype.Component;

@Component
public class MdBatchPreliminaryRunExecutor {

  private final DashboardStepProcessor processor;
  private final ExceptionManagementCalculationService exceptionManagementService;

  public MdBatchPreliminaryRunExecutor(
      DashboardStepProcessor processor,
      ExceptionManagementCalculationService exceptionManagementService) {
    this.processor = processor;
    this.exceptionManagementService = exceptionManagementService;
  }

  public Either<List<ErrorItem>, EntityId> execute(Dashboard dashboard) {
    var stateDate = processor.getStateDate();
    return processor.performMdBatchStep(
        stateDate, step(dashboard), resultProvider(dashboard, stateDate));
  }

  private DashboardEntryMdBatch step(Dashboard dashboard) {
    return DashboardEntryMdBatch.newOfPreliminaryRun(dashboard.getId());
  }

  private Supplier<Either<ErrorItem, EntityId>> resultProvider(
      Dashboard dashboard, BitemporalDate stateDate) {
    return () ->
        exceptionManagementService
            .performPreliminaryBatch(dashboard, stateDate)
            .map(resultIds -> dashboard.entityId());
  }
}
