package com.solum.xplain.xm.workflow.repository.fragment;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.xm.workflow.repository.fragment.XmStepInstanceQueriesImpl.PROCESS_FIELD;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.portfolio.CoreProductTypeGroup;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.xm.excmngmt.process.data.Instrument;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.workflow.MdXmWorkflowProvider;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import java.time.Instant;
import org.bson.BsonDateTime;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.data.mongodb.core.query.Criteria;

/** Mapper for creating task specification criteria for workflow tasks. */
@NullMarked
public class WorkflowTaskCriteriaMapper implements TaskCriteriaMapper, IpvTaskCriteriaMapper {

  private final String contextField;
  private final String businessKeyField;
  private final String processIdField;
  @Nullable private final String stepIdField;
  private final boolean filterByPhase;

  private WorkflowTaskCriteriaMapper(
      String contextField,
      String businessKeyField,
      String processIdField,
      @Nullable String stepIdField,
      boolean filterByPhase) {
    this.contextField = contextField;
    this.businessKeyField = businessKeyField;
    this.processIdField = processIdField;
    this.stepIdField = stepIdField;
    this.filterByPhase = filterByPhase;
  }

  public static TaskCriteriaMapper forProcessExecution() {
    return new WorkflowTaskCriteriaMapper(
        ProcessExecution.Fields.context,
        ProcessExecution.Fields.rootBusinessKey,
        ProcessExecution.Fields.processId,
        null,
        true);
  }

  public static IpvTaskCriteriaMapper forProcessExecutionIpv() {
    return new WorkflowTaskCriteriaMapper(
        ProcessExecution.Fields.context,
        ProcessExecution.Fields.rootBusinessKey,
        ProcessExecution.Fields.processId,
        null,
        true);
  }

  public static TaskCriteriaMapper forStepInstance() {
    return new WorkflowTaskCriteriaMapper(
        joinPaths(PROCESS_FIELD, ProcessExecution.Fields.context),
        joinPaths(PROCESS_FIELD, ProcessExecution.Fields.rootBusinessKey),
        ProcessExecution.Fields.processId,
        StepInstance.Fields.stepId,
        true);
  }

  public static IpvTaskCriteriaMapper forStepInstanceIpv() {
    return new WorkflowTaskCriteriaMapper(
        joinPaths(PROCESS_FIELD, ProcessExecution.Fields.context),
        joinPaths(PROCESS_FIELD, ProcessExecution.Fields.rootBusinessKey),
        ProcessExecution.Fields.processId,
        StepInstance.Fields.stepId,
        true);
  }

  @Override
  public @Nullable Criteria marketDataGroupIdCriteria(String marketDataGroupId) {
    return where(joinPaths(contextField, MdOverlayContext.Fields.marketDataGroupId))
        .is(marketDataGroupId);
  }

  @Override
  public @Nullable Criteria businessKeyCriteria(String businessKey) {
    return where(businessKeyField).is(businessKey);
  }

  @Override
  public @Nullable Criteria stepIdCriteria(String stepId) {
    return stepIdField == null
        ? null
        : new Criteria().orOperator(where(stepIdField).is(stepId), where(stepIdField).isNull());
  }

  @Override
  public @Nullable Criteria valuationDateCriteria(String valuationDate) {
    return where(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.stateDate,
                BitemporalDate.Fields.actualDate))
        .is(new BsonDateTime(Instant.parse(valuationDate).toEpochMilli()));
  }

  @Override
  public @Nullable Criteria taskExceptionManagementTypeCriteria(String value) {
    return where(processIdField)
        .is(
            switch (TaskExceptionManagementType.valueOf(value)) {
              case PRELIMINARY -> MdXmWorkflowProvider.MD_XM_PRELIMINARY_PROCESS_ID;
              case OVERLAY -> MdXmWorkflowProvider.MD_XM_OVERLAY_PROCESS_ID;
              case PRELIMINARY_BATCH ->
                  throw new IllegalArgumentException("Batch tasks are not supported");
            });
  }

  @Override
  public @Nullable Criteria curveConfigurationIdCriteria(String value) {
    return where(joinPaths(contextField, MdOverlayContext.Fields.curveConfigId)).is(value);
  }

  @Override
  public @Nullable Criteria assetClassCriteria(String assetClass) {
    return where(
            joinPaths(
                contextField, MdPreliminaryContext.Fields.instrument, Instrument.Fields.assetClass))
        .is(assetClass);
  }

  @Override
  public @Nullable Criteria assetClassGroupCriteria(String assetClassGroup) {
    return where(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.assetClassGroup))
        .is(assetClassGroup);
  }

  @Override
  public @Nullable Criteria instrumentTypeIsInstrumentCriteria(String instrument) {
    return where(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.instrumentType))
        .is(instrument);
  }

  @Override
  public @Nullable Criteria instrumentTypeIsNotInstrumentCriteria(String instrument) {
    return where(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.instrumentType))
        .ne(instrument);
  }

  @Override
  public @Nullable Criteria granularityByRateCriteria(String rate) {
    return where(
            joinPaths(
                contextField, MdPreliminaryContext.Fields.instrument, Instrument.Fields.currency))
        .is(rate)
        .and(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.assetClassGroup))
        .is(CoreAssetGroup.RATES);
  }

  @Override
  public @Nullable Criteria granularityBySectorCriteria(String sector) {
    return where(
            joinPaths(
                contextField, MdPreliminaryContext.Fields.instrument, Instrument.Fields.sector))
        .is(sector)
        .and(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.assetClassGroup))
        .is(CoreAssetGroup.CREDIT);
  }

  @Override
  public @Nullable Criteria granularityByFxCcyPairCriteria(String fxCcyPairType) {
    return where(
            joinPaths(
                contextField, MdPreliminaryContext.Fields.instrument, Instrument.Fields.fxPair))
        .regex("^" + fxCcyPairType)
        .and(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.assetClassGroup))
        .is(CoreAssetGroup.FX);
  }

  // Implicit filters
  @Override
  public Criteria assetClassGroupIsNot(CoreAssetGroup assetClassGroup) {
    return where(
            joinPaths(
                contextField,
                MdPreliminaryContext.Fields.instrument,
                Instrument.Fields.assetClassGroup))
        .ne(assetClassGroup);
  }

  @Override
  public Criteria assetClassIsNot(CoreAssetClass assetClass) {
    return where(
            joinPaths(
                contextField, MdPreliminaryContext.Fields.instrument, Instrument.Fields.assetClass))
        .ne(assetClass);
  }

  // IPV Filters
  @Override
  public Criteria ipvDataGroupIdCriteria(String ipvDataGroupId) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.vdg, IpvDataGroupVo.Fields.entityId))
        .is(ipvDataGroupId);
  }

  @Override
  public @Nullable Criteria ipvPhaseCriteria(String phase) {
    return filterByPhase
        ? where(joinPaths(contextField, VdPhaseContext.Fields.phase)).is(phase)
        : null;
  }

  @Override
  public Criteria ipvGranularityByContractualTermCriteria(String contractualTerm) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.slaDeadline)).is(contractualTerm);
  }

  @Override
  public @Nullable Criteria ipvProductTypeCriteria(String productType) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productType))
        .is(productType);
  }

  @Override
  public @Nullable Criteria ipvProductGroupCriteria(String productGroup) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productGroup))
        .is(productGroup);
  }

  @Override
  public @Nullable Criteria ipvGranularityByRateCriteria(String rate) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.currency))
        .is(rate)
        .and(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productGroup))
        .is(CoreProductTypeGroup.RATES);
  }

  @Override
  public @Nullable Criteria ipvGranularityBySectorCriteria(String sector) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.creditSector))
        .is(sector)
        .and(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productGroup))
        .is(CoreProductTypeGroup.CREDIT);
  }

  @Override
  public @Nullable Criteria ipvGranularityByFxCcyPairCriteria(String fxCcyPairType) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.currencyPair))
        .regex("^" + fxCcyPairType)
        .and(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productGroup))
        .is(CoreProductTypeGroup.FX);
  }

  // IPV implicit filters
  @Override
  public Criteria ipvProductGroupIsNot(CoreProductTypeGroup productGroup) {
    return where(joinPaths(contextField, VdPhaseContext.Fields.trade, Trade.Fields.productGroup))
        .ne(productGroup);
  }
}
