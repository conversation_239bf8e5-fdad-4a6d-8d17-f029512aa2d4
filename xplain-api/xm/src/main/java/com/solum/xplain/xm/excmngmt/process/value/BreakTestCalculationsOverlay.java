package com.solum.xplain.xm.excmngmt.process.value;

import static com.solum.xplain.xm.excmngmt.process.value.BreakTestCalculationOverlay.ofBreakTest;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak;
import com.solum.xplain.xm.excmngmt.rules.BreakTest;
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.lang.NonNull;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BreakTestCalculationsOverlay implements Serializable {

  private final List<BreakTestCalculationOverlay> calculations;

  public static BreakTestCalculationsOverlay ofBreakTests(
      @NonNull Map<String, BreakTest> breakTests) {
    var rootTests =
        breakTests.entrySet().stream()
            .filter(r -> r.getValue().getParentTest() == null)
            .map(r -> ofBreakTest(r.getKey(), r.getValue()))
            .toList();

    breakTests.entrySet().stream()
        .filter(r -> r.getValue().getParentTest() != null)
        .forEach(r -> rootTests.forEach(rt -> rt.acceptDependant(r.getValue())));

    return new BreakTestCalculationsOverlay(rootTests);
  }

  public BreakTestCalculationsOverlay filteredFor(InstrumentDefinition instrument) {
    return new BreakTestCalculationsOverlay(
        calculations.stream().filter(b -> b.isApplicable(instrument)).toList());
  }

  public List<InstrumentResultBreak> processCalc(
      InstrumentMarketDataBreakCalculatorOverlay breakCalculator) {
    return calculations.stream().flatMap(b -> b.resolveBreak(breakCalculator).stream()).toList();
  }

  public Set<Tenor> getObservationPeriods(MeasureType measureType) {
    return calculations.stream()
        .filter(b -> b.isApplicable(measureType))
        .map(BreakTestCalculationOverlay::getObservationPeriod)
        .collect(Collectors.toUnmodifiableSet());
  }
}
