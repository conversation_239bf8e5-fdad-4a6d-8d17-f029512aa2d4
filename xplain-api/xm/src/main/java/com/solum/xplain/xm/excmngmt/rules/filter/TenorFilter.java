package com.solum.xplain.xm.excmngmt.rules.filter;

import com.solum.xplain.xm.excmngmt.rules.TenorBucket;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;

@Data
@FieldNameConstants
public class TenorFilter implements Serializable {
  private boolean allowAllTenors;
  private List<TenorBucket> tenorBuckets;

  public boolean matchesTenor(String tenor) {
    if (StringUtils.isEmpty(tenor)) {
      return true;
    }
    return allowAllTenors
        || Stream.ofNullable(tenorBuckets)
            .flatMap(Collection::stream)
            .anyMatch(tb -> tb.matchesBucket(tenor));
  }
}
