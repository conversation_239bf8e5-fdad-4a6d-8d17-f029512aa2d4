package com.solum.xplain.xm.dashboards.views.auditablebreaktest;

import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import java.time.LocalDate;

public record DashboardBreakTestTradeView(
    String key,
    String currency,
    String currencyPair,
    String underlying,
    ProductType productType,
    ProductGroup productGroup,
    String externalTradeId,
    Double notional,
    Double dealCost,
    Double accountingCost,
    LocalDate vendorOnboardingDate,
    String portfolioId,
    String portfolioExternalId,
    String externalCompanyId,
    String externalEntityId,
    String creditSector) {}
