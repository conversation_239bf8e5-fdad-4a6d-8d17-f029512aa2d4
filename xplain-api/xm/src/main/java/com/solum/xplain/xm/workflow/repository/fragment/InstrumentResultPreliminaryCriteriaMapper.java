package com.solum.xplain.xm.workflow.repository.fragment;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.xm.workflow.XmWorkflowService.dashboardIdFromBusinessKey;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.xm.excmngmt.process.data.Instrument;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.data.NonRequiredProviderData;
import java.time.Instant;
import org.bson.BsonDateTime;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.data.mongodb.core.query.Criteria;

/** Mapper for creating task specification criteria for instrument result preliminary queries. */
@NullMarked
public class InstrumentResultPreliminaryCriteriaMapper implements TaskCriteriaMapper {

  private final String valuationDateField;

  private InstrumentResultPreliminaryCriteriaMapper(String valuationDateField) {
    this.valuationDateField = valuationDateField;
  }

  public static TaskCriteriaMapper forInstrumentResultPreliminary() {
    return new InstrumentResultPreliminaryCriteriaMapper(
        InstrumentResultPreliminary.Fields.valuationDate);
  }

  public static TaskCriteriaMapper forNonRequiredProviderData() {
    return new InstrumentResultPreliminaryCriteriaMapper(
        joinPaths(NonRequiredProviderData.Fields.stateDate, "actualDate"));
  }

  @Override
  public @Nullable Criteria marketDataGroupIdCriteria(String marketDataGroupId) {
    return where(InstrumentResultPreliminary.Fields.marketDataGroupId).is(marketDataGroupId);
  }

  @Override
  public @Nullable Criteria businessKeyCriteria(String businessKey) {
    return where(InstrumentResultPreliminary.Fields.dashboardId)
        .is(dashboardIdFromBusinessKey(businessKey));
  }

  @Override
  public @Nullable Criteria valuationDateCriteria(String valuationDate) {
    return where(valuationDateField)
        .is(new BsonDateTime(Instant.parse(valuationDate).toEpochMilli()));
  }

  @Override
  public @Nullable Criteria assetClassCriteria(String assetClass) {
    return where(
            joinPaths(InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClass))
        .is(assetClass);
  }

  @Override
  public @Nullable Criteria assetClassGroupCriteria(String assetClassGroup) {
    return where(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClassGroup))
        .is(assetClassGroup);
  }

  @Override
  public @Nullable Criteria granularityByRateCriteria(String rate) {
    return where(
            joinPaths(InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.currency))
        .is(rate);
  }

  @Override
  public @Nullable Criteria granularityBySectorCriteria(String sector) {
    return where(joinPaths(InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.sector))
        .is(sector)
        .and(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClassGroup))
        .is(CoreAssetGroup.CREDIT);
  }

  @Override
  public @Nullable Criteria granularityByFxCcyPairCriteria(String fxCcyPairType) {
    return where(joinPaths(InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.fxPair))
        .regex("^" + fxCcyPairType)
        .and(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClassGroup))
        .is(CoreAssetGroup.FX);
  }

  @Override
  public @Nullable Criteria instrumentTypeIsInstrumentCriteria(String instrument) {
    return where(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.instrumentType))
        .is(instrument);
  }

  @Override
  public @Nullable Criteria instrumentTypeIsNotInstrumentCriteria(String instrument) {
    return where(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.instrumentType))
        .ne(instrument);
  }

  // Implicit Filters
  @Override
  public Criteria assetClassGroupIsNot(CoreAssetGroup assetClassGroup) {
    return where(
            joinPaths(
                InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClassGroup))
        .ne(assetClassGroup);
  }

  @Override
  public Criteria assetClassIsNot(CoreAssetClass assetClass) {
    return where(
            joinPaths(InstrumentResultPreliminary.Fields.instrument, Instrument.Fields.assetClass))
        .ne(assetClass);
  }

  // Not needed for instrument result preliminary
  @Override
  public @Nullable Criteria taskExceptionManagementTypeCriteria(String value) {
    return null;
  }

  @Override
  public @Nullable Criteria curveConfigurationIdCriteria(String value) {
    return null;
  }

  @Override
  public @Nullable Criteria stepIdCriteria(String stepId) {
    return null;
  }
}
