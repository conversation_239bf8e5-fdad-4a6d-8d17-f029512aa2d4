package com.solum.xplain.xm.excmngmt.process.view.viewconfig;

import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.provider.StandardViewConfigurationProvider;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import org.springframework.stereotype.Component;

/**
 * Define standard view configurations for {@link InstrumentPreliminaryResultView} and {@link
 * InstrumentOverlayResultView}.
 */
@Component
public class InstrumentOverlayResultStandardViewConfigurationProvider
    extends StandardViewConfigurationProvider {

  public InstrumentOverlayResultStandardViewConfigurationProvider(PaletteService paletteService) {
    super(
        InstrumentOverlayResultView.class,
        paletteService,
        "662907383d0c5109c3f62503",
        () -> new DefaultColumnsBuilder(false),
        "662907503d0c5109c3f62504",
        () -> new MinimalColumnsBuilder(false));
  }
}
