package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.workflow.value.SubprocessContextCreator;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/** Creates one context for each broken preliminary data present in the parent context. */
@Component
@RequiredArgsConstructor
public class MdPreliminaryContextCreator
    implements SubprocessContextCreator<
        MdKeyState, MdKeyContext, MdPreliminaryState, MdPreliminaryContext> {
  private final ExceptionManagementResultMapper exceptionManagementResultMapper;

  @Override
  public Stream<MdPreliminaryContext> subprocessContext(
      MdKeyState parentState, MdKeyContext parentContext) {

    return parentContext.brokenPreliminaryData().stream()
        .map(provider -> toMdPreliminaryContext(parentContext, provider));
  }

  private MdPreliminaryContext toMdPreliminaryContext(
      MdKeyContext parentContext, PreliminaryBreakResults preliminaryBreakResults) {

    return new MdPreliminaryContext(
        parentContext.stateDate(),
        parentContext.previousDate(),
        parentContext.marketDataGroupId(),
        parentContext.marketDataGroupName(),
        exceptionManagementResultMapper.fromDefinition(parentContext.instrument()),
        preliminaryBreakResults.providerData().getBidAskType(),
        preliminaryBreakResults.providerData().getProvider(),
        parentContext.allProviderData(),
        preliminaryBreakResults.breakTestResults(),
        preliminaryBreakResults.hasBreak());
  }

  @Override
  public Class<MdPreliminaryState> subprocessStateType() {
    return MdPreliminaryState.class;
  }

  @Override
  public String subprocessBusinessKey(String parentBusinessKey, MdPreliminaryContext context) {
    return parentBusinessKey + "-" + context.pricePoint().name() + "-" + context.provider();
  }
}
