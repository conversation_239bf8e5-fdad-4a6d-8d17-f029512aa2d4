package com.solum.xplain.xm.excmngmt.processipv.view;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView;
import com.solum.xplain.xm.excmngmt.processipv.data.OnboardingStatus;
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class IpvPortfolioItemResultView extends PortfolioItemFlatView {

  private String id;
  private String key;
  private PricingSlot pricingSlot;
  private SlaDeadline slaDeadline;
  private String calculationCurrency;
  private TradeResultResolutionType resolution;
  private BigDecimal resolvedValue;
  private IpvProvidersType resolutionProviderType;
  private String resolutionProviderName;
  private LocalDateTime verifiedAt;
  private String portfolioExternalId;
  private String externalCompanyId;
  private String externalEntityId;
  private OnboardingStatus onboardingStatus;
  private LocalDate navEffectiveDate;
  private LocalDate onboardingEndDate;
}
