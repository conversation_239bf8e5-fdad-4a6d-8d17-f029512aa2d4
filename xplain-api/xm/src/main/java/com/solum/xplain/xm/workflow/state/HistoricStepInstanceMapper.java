package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.HistoricStepInstanceView;
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistory;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public abstract class HistoricStepInstanceMapper {
  List<EntryResultStatusHistory> toEntryResultStatusHistories(
      StepStateOps<? extends ResolutionHistory, ?> ops) {
    var stepInstanceHistory = ops.getStepInstanceHistory();
    List<EntryResultStatusHistory> mappedHistory = new ArrayList<>(stepInstanceHistory.size());
    HistoricStepInstanceView<? extends ResolutionHistory> previousStep = null;
    for (HistoricStepInstanceView<? extends ResolutionHistory> step : stepInstanceHistory) {
      mappedHistory.add(toEntryResultStatusHistory(step, previousStep));
      previousStep = step;
    }
    return mappedHistory;
  }

  @Mapping(target = "status", source = "initialState.entryStatus")
  @Mapping(target = "resolutionComment", source = "initialState.resolutionComment")
  @Mapping(target = "approvalComment", source = "initialState.approvalComment")
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", source = "startedAt")
  abstract EntryResultStatusHistory toEntryResultStatusHistory(
      HistoricStepInstanceView<? extends ResolutionHistory> step,
      @Context HistoricStepInstanceView<? extends ResolutionHistory> previousStep);

  @AfterMapping
  void updatedModifiedFromPreviousStep(
      @MappingTarget EntryResultStatusHistory history,
      @Context HistoricStepInstanceView<? extends ResolutionHistory> previousStep) {
    if (history.getModifiedBy() == null && previousStep != null) {
      history.setModifiedBy(previousStep.assignee());
    }
  }
}
