package com.solum.xplain.xm.dashboards.resolver;

import static java.lang.Boolean.TRUE;

import com.solum.xplain.core.common.daterange.DateRange;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.forms.DashboardForm;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DashboardResolver {

  private final MdExceptionManagementSetupResolver mdSetupResolver;
  private final VdExceptionManagementSetupResolver vdSetupResolver;
  private final VdExceptionManagementPortfolioFilterBuilder portfoliosFilterBuilder;

  public Either<List<ErrorItem>, Dashboard> resolve(BitemporalDate stateDate, DashboardForm form) {
    return switch (form.getType()) {
      case MARKET_DATA_BATCH, TRS_MARKET_DATA_BATCH -> marketDataBatchDashboard(stateDate, form);
      case MARKET_DATA, TRS_MARKET_DATA -> marketDataDashboard(stateDate, form);
      case VALUATION_DATA -> valuationDataDashboard(stateDate, form);
    };
  }

  private Either<List<ErrorItem>, Dashboard> marketDataBatchDashboard(
      BitemporalDate stateDate, DashboardForm form) {
    if (form.getTrsMarketDataGroupId() != null) {
      return marketTrsDataBatchDashboard(stateDate, form);
    }

    var dateRange = DateRange.newOf(form.getDateRange().startDate(), form.getDateRange().endDate());

    return Steps.begin(
            mdSetupResolver.resolveBatch(stateDate, dateRange, form.getMarketDataGroupId()))
        .yield(md -> Dashboard.newOfMarketDataBatch(stateDate, dateRange, md.orElse(null), null));
  }

  private Either<List<ErrorItem>, Dashboard> marketTrsDataBatchDashboard(
      BitemporalDate stateDate, DashboardForm form) {

    var dateRange = DateRange.newOf(form.getDateRange().startDate(), form.getDateRange().endDate());

    return Steps.begin(
            mdSetupResolver.resolveBatchTrs(stateDate, dateRange, form.getTrsMarketDataGroupId()))
        .yield(trs -> Dashboard.newOfMarketDataBatch(stateDate, dateRange, null, trs.orElse(null)));
  }

  private Either<List<ErrorItem>, Dashboard> marketDataDashboard(
      BitemporalDate stateDate, DashboardForm form) {

    if (form.getTrsMarketDataGroupId() != null) {
      return marketTrsDataDashboard(stateDate, form);
    }

    var relevantOnly = TRUE.equals(form.getRelevantOnly());
    var date = form.getDateRange().singleDate();
    var dateRange = DateRange.newOf(date);

    return mdSetupResolver
        .resolve(stateDate, date, form.getMarketDataGroupId(), relevantOnly)
        .map(
            mdSetup -> Dashboard.newOfMarketData(stateDate, dateRange, mdSetup.orElse(null), null));
  }

  private Either<List<ErrorItem>, Dashboard> marketTrsDataDashboard(
      BitemporalDate stateDate, DashboardForm form) {

    var date = form.getDateRange().singleDate();
    var dateRange = DateRange.newOf(date);

    return mdSetupResolver
        .resolveTrs(stateDate, date, form.getTrsMarketDataGroupId())
        .map(
            trsSetup ->
                Dashboard.newOfMarketData(stateDate, dateRange, null, trsSetup.orElse(null)));
  }

  private Either<List<ErrorItem>, Dashboard> valuationDataDashboard(
      BitemporalDate stateDate, DashboardForm form) {
    var dateRange = DateRange.newOf(form.getDateRange().singleDate());
    return Steps.begin(
            vdSetupResolver.resolve(
                stateDate,
                dateRange.singleDate(),
                portfoliosFilterBuilder.build(form.getPortfolios())))
        .yield(vSetup -> Dashboard.newOfValuationData(stateDate, dateRange, vSetup));
  }
}
