package com.solum.xplain.xm.excmngmt.rulesipv;

import static java.util.stream.Stream.concat;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTest;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.NonNull;

@Document
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class IpvBreakTest extends BaseBreakTest<Trade, IpvBreakTestOverride, IpvBreakTest> {

  public static final String IPV_BREAK_TEST_COLLECTION = "ipvBreakTest";

  private IpvExceptionManagementPhase scope;
  private IpvTestType type;
  private IpvMeasureType measureType;
  private List<IpvProvidersType> providersTypes;
  private TradeFilter tradeFilter;
  private Boolean onboardingTest;

  public static IpvBreakTest newOf() {
    var t = new IpvBreakTest();
    t.setEntityId(ObjectId.get().toHexString());
    t.setRecordDate(LocalDateTime.now());
    return t;
  }

  @Override
  protected IpvBreakTest self() {
    return this;
  }

  @Override
  public boolean matches(@NonNull Trade trade) {
    var measureMatch =
        measureType == null || measureType.supportsProductType(trade.getProductType());
    return measureMatch && tradeFilter.matches(trade);
  }

  public Optional<Integer> maxStaleThreshold() {
    if (type != IpvTestType.STALE_VALUE) {
      return Optional.empty();
    }
    return concat(
            Stream.of(getThreshold()),
            overridesStream()
                .filter(o -> isTrue(o.getEnabled()))
                .map(IpvBreakTestOverride::getThreshold))
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .max(BigDecimal::compareTo)
        .map(BigDecimal::intValue);
  }

  public boolean isForProvider(IpvProvidersType providersType) {
    return CollectionUtils.isEmpty(providersTypes) || providersTypes.contains(providersType);
  }

  @Override
  public boolean valueEquals(IpvBreakTest entity) {
    return super.valueEquals(entity)
        && Objects.equals(this.scope, entity.scope)
        && Objects.equals(this.measureType, entity.measureType)
        && Objects.equals(this.tradeFilter, entity.tradeFilter)
        && Objects.equals(this.type, entity.type)
        && CollectionUtils.isEqualCollection(
            emptyIfNull(this.providersTypes), emptyIfNull(entity.providersTypes));
  }
}
