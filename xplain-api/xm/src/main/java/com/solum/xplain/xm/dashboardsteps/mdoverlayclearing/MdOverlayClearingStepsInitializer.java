package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository;
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component
public class MdOverlayClearingStepsInitializer {

  private final DashboardStepProcessor processor;
  private final MdTaskExecutionService taskExecutionService;
  private final ExceptionManagementCalculationRepository exceptionManagementRepository;

  public MdOverlayClearingStepsInitializer(
      DashboardStepProcessor processor,
      MdTaskExecutionService taskExecutionService,
      ExceptionManagementCalculationRepository exceptionManagementRepository) {
    this.processor = processor;
    this.taskExecutionService = taskExecutionService;
    this.exceptionManagementRepository = exceptionManagementRepository;
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> execute(Dashboard dashboard) {
    var stateDate = processor.getStateDate();
    return exceptionManagementRepository
        .entityByDashboard(dashboard.getId())
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(r -> process(dashboard, r, stateDate));
  }

  private Either<List<ErrorItem>, List<DashboardEntryMd>> process(
      Dashboard dashboard, ExceptionManagementResult result, BitemporalDate stateDate) {
    var taskExecutions = taskExecutionService.createTaskExecutions(dashboard, result, stateDate);
    return tasksByDashboardTaskGroup(taskExecutions).entrySet().stream()
        .map(ccTasks -> step(stateDate, result, ccTasks))
        .collect(Collectors.collectingAndThen(toList(), processor::createMdSteps));
  }

  private Map<DashboardTaskGroup, List<TaskExecution>> tasksByDashboardTaskGroup(
      List<TaskExecution> taskExecutions) {
    return CollectionUtils.toGroupMap(taskExecutions, DashboardTaskGroup::newOf);
  }

  private DashboardEntryMd step(
      BitemporalDate stateDate,
      ExceptionManagementResult result,
      Entry<DashboardTaskGroup, List<TaskExecution>> ccTasks) {
    var curveConfig = ccTasks.getKey();
    var breaksCount = breaksCount(ccTasks);
    var step =
        DashboardEntryMd.newOfOverlayClearing(
                result.getDashboardId(), result.getId(), curveConfig, breaksCount)
            .started(stateDate);
    if (isStepCompleted(ccTasks)) {
      step.completed();
    }
    return (DashboardEntryMd) step;
  }

  private Long breaksCount(Entry<DashboardTaskGroup, List<TaskExecution>> ccTasks) {
    return ccTasks.getValue().stream()
        .map(t -> ObjectUtils.defaultIfNull(t.getBreaksCount(), 0L))
        .reduce(0L, Long::sum);
  }

  private boolean isStepCompleted(Entry<DashboardTaskGroup, List<TaskExecution>> ccTasks) {
    return ccTasks.getValue().stream().allMatch(e -> e.getStatus() == TaskExecutionStatus.APPROVED);
  }
}
