package com.solum.xplain.xm.workflow.state.minifier;

import static lombok.AccessLevel.PRIVATE;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.core.company.value.ProvidersVo;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.xm.workflow.state.PricingSlotTradeWithProviders;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.InflaterInputStream;
import lombok.NoArgsConstructor;
import org.jspecify.annotations.NullMarked;

/**
 * This class is mainly here for mitigation of an issue with large workflows where the initial steps
 * needs to be stored a massive collection in a single document in the database. Alas, Mongo has (a
 * justified) a hard limit of 16MB for a single document.
 *
 * <p>This class is used to deflate and inflate the trade collection to/from a byte array which is
 * the most offending part of the VdDashboardState object.
 *
 * <p>We are using Kryo, and it must not change as it would break the compatibility of the already
 * stored workflow objects once released.
 */
@NoArgsConstructor(access = PRIVATE)
@NullMarked
public final class VdDashboardStateMinifier {

  /** Returns an inflated list of pricing slot trades from the given byte array. */
  public static List<PricingSlotTradeWithProviders> inflateFromKryoSerializedTrades(
      byte[] kryoTradeCollection) {
    Kryo kryo = getKryo();
    try (Input input =
        new Input(new InflaterInputStream(new ByteArrayInputStream(kryoTradeCollection)))) {
      PricingSlotTradeWithProviders[] tradesArr =
          kryo.readObject(input, PricingSlotTradeWithProviders[].class);
      return Arrays.asList(tradesArr);
    }
  }

  /** Returns a deflated byte array of the given pricing slot trades. */
  public static byte[] deflateToKryoSerializedTrades(
      List<PricingSlotTradeWithProviders> pricingSlotTrades) {
    Kryo kryo = getKryo();
    var objectToSerialize =
        pricingSlotTrades.toArray(new PricingSlotTradeWithProviders[pricingSlotTrades.size()]);
    try (var baos = new ByteArrayOutputStream()) {
      var deflater = new DeflaterOutputStream(baos);
      var output = new Output(deflater);
      // arrays are strongly typed, not like collections.
      kryo.writeObject(output, objectToSerialize);
      output.close();
      deflater.finish();
      return baos.toByteArray();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private static Kryo getKryo() {
    // could be optimized, but this is a rare path overall.
    // we only come once or twice per workflow run.
    Kryo kryo = new Kryo();
    kryo.register(PricingSlotTradeWithProviders.class);
    kryo.register(PricingSlot.class);
    kryo.register(CoreProductType.class);
    kryo.register(PricingSlotTradeWithProviders[].class);
    kryo.register(ProvidersVo.class);
    kryo.register(IpvDataGroupVo.class);
    return kryo;
  }
}
