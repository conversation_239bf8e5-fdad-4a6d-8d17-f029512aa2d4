package com.solum.xplain.xm.excmngmt.stat.value;

import com.solum.xplain.xm.excmngmt.stat.data.StatisticalZScoreData;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.linear.RealMatrix;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Setter(AccessLevel.NONE)
public class CalculationStatisticalZScoreData implements Serializable {

  private ZScoreData primaryZ;
  private ZScoreData secondaryZ;

  private ConditionalZScoreData primaryCondZ;
  private ConditionalZScoreData secondaryCondZ;

  public void withZ(StatisticalZScoreData data) {
    if (data.isPrimary()) {
      this.primaryZ = new ZScoreDataImpl(data.getMean(), data.getStdDev());
    } else {
      this.secondaryZ = new ZScoreDataImpl(data.getMean(), data.getStdDev());
    }
  }

  public void withCondZ(StatisticalZScoreData data, List<Double> v) {
    var related = new double[v.size()][1];
    int i = 0;
    for (var val : v) {
      related[i++][0] = val;
    }

    if (data.isPrimary()) {
      this.primaryCondZ =
          new ConditionalZScoreData(
              data.getMean(),
              data.getCondVariation(),
              data.getCovariance(),
              data.getMeanVector(),
              related);
    } else {
      this.secondaryCondZ =
          new ConditionalZScoreData(
              data.getMean(),
              data.getCondVariation(),
              data.getCovariance(),
              data.getMeanVector(),
              related);
    }
  }

  @AllArgsConstructor(access = AccessLevel.PRIVATE)
  @ToString
  @Data
  public static class ZScoreDataImpl implements ZScoreData {
    private final double mean;
    private final double stdDev;
  }

  @AllArgsConstructor(access = AccessLevel.PRIVATE)
  @ToString
  @EqualsAndHashCode
  public static class ConditionalZScoreData implements Serializable {
    private final double mean;
    private final double condVariation;
    private final RealMatrix covariance;
    private final RealMatrix meanVector;
    private final double[][] relatedInstrumentValues;

    public BigDecimal performConditionalZScore(BigDecimal v) {
      if (Double.compare(condVariation, 0d) == 0) {
        throw new IllegalArgumentException(
            "Unable to calculate conditional Z-Score. Conditional variation is" + " 0");
      }
      if (relatedInstrumentValues.length < 1) {
        throw new IllegalArgumentException(
            "Unable to calculate conditional Z-Score. Interconnected instrument data "
                + "not found");
      }
      var instrMatrix = new Array2DRowRealMatrix(relatedInstrumentValues);
      if (instrMatrix.getRowDimension() != covariance.getColumnDimension()) {
        throw new IllegalArgumentException(
            "Unable to calculate conditional Z-Score, matrix dimensions do not match!"
                + " "
                + instrMatrix.getRowDimension()
                + " <> "
                + covariance.getColumnDimension());
      }
      var ii = covariance.multiply(instrMatrix.subtract(meanVector));
      return v.subtract(BigDecimal.valueOf(mean + ii.getData()[0][0]))
          .divide(BigDecimal.valueOf(condVariation), 6, RoundingMode.FLOOR)
          .abs();
    }
  }
}
