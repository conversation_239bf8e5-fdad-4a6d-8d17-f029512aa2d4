package com.solum.xplain.xm.workflow.steps.md.resolution;

import static com.solum.xplain.xm.workflow.steps.md.resolution.Errors.resolvedValueIsNotEmpty;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class IgnoreNullStep
    implements ServiceStepExecutor<MdPreliminaryState, MdPreliminaryContext> {
  @Override
  public void runStep(StepStateOps<MdPreliminaryState, MdPreliminaryContext> ops) {
    if (ops.getInitialState().getBaseValue() != null) {
      ops.throwError(resolvedValueIsNotEmpty(ops.getContext()));
    }

    ops.setOutcome(
        new MutablePropertyValues()
            .add(
                MdPreliminaryState.Fields.valuePendingApproval,
                new AttributedValue(
                    ops.getInitialState().getBaseValue(),
                    null,
                    ops.getInitialState().getProviderName()))
            .add(MdPreliminaryState.Fields.entryStatus, EntryResultStatus.WAITING_APPROVAL));
  }
}
