package com.solum.xplain.xm.workflow.repository.fragment;

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.data.mongodb.core.query.Criteria;

/** Interface for creating criteria for task specifications. */
@NullMarked
public interface TaskCriteriaMapper {

  // Explicit filters
  public @Nullable Criteria marketDataGroupIdCriteria(String marketDataGroupId);

  public @Nullable Criteria businessKeyCriteria(String businessKey);

  public @Nullable Criteria stepIdCriteria(String stepId);

  public @Nullable Criteria valuationDateCriteria(String valuationDate);

  public @Nullable Criteria taskExceptionManagementTypeCriteria(String taskExceptionManagementType);

  public @Nullable Criteria curveConfigurationIdCriteria(String curveConfigurationId);

  public @Nullable Criteria assetClassCriteria(String assetClass);

  public @Nullable Criteria assetClassGroupCriteria(String assetClassGroup);

  public @Nullable Criteria granularityByRateCriteria(String rate);

  public @Nullable Criteria granularityBySectorCriteria(String sector);

  public @Nullable Criteria granularityByFxCcyPairCriteria(String fxCcyPairType);

  public @Nullable Criteria instrumentTypeIsInstrumentCriteria(String instrument);

  public @Nullable Criteria instrumentTypeIsNotInstrumentCriteria(String instrument);

  // Implicit filters
  public Criteria assetClassGroupIsNot(CoreAssetGroup assetClassGroup);

  public Criteria assetClassIsNot(CoreAssetClass assetClass);
}
