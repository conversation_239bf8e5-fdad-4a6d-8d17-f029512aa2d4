package com.solum.xplain.xm.dashboards.entity

import static DashboardBuilder.CURVE_CONFIGURATION
import static DashboardBuilder.EXCEPTION_MANAGEMENT_ID
import static DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static DashboardBuilder.PORTFOLIO
import static DashboardBuilder.PORTFOLIO_NO_XPLAIN_PROVIDER
import static DashboardBuilder.TRS_MARKET_DATA_DASHBOARD_ID
import static DashboardBuilder.VALUATION_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import java.time.LocalDateTime
import org.bson.types.ObjectId

class DashboardEntryBuilder {

  static dashboardStepMarketDataBatchUpload() {
    return new DashboardEntryMdBatch(
      id: new ObjectId().toHexString(),
      dashboardId: MD_BATCH_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MARKET_DATA_BATCH_UPLOAD,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: null,
      resultId: null,
      )
  }

  static dashboardStepMdBatchPreliminaryRun() {
    return new DashboardEntryMdBatch(
      id: new ObjectId().toHexString(),
      dashboardId: MD_BATCH_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_BATCH_PRELIMINARY_RUN,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: null,
      resultId: null
      )
  }

  static dashboardStepMdBatchPreliminaryClearing() {
    return new DashboardEntryMdBatch(
      id: new ObjectId().toHexString(),
      dashboardId: MD_BATCH_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_BATCH_PRELIMINARY_CLEARING,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: 1L,
      resultId: null
      )
  }

  static dashboardStepMarketDataUpload() {
    return new DashboardEntryMd(
      id: new ObjectId().toHexString(),
      dashboardId: MD_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MARKET_DATA_UPLOAD,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: null,
      resultId: null,
      curveConfiguration: null,
      )
  }

  static dashboardStepMdPreliminaryRun() {
    return new DashboardEntryMd(
      id: new ObjectId().toHexString(),
      dashboardId: MD_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_PRELIMINARY_RUN,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: null,
      resultId: EXCEPTION_MANAGEMENT_ID,
      curveConfiguration: null,
      )
  }

  static dashboardStepMdPreliminaryClearing() {
    return new DashboardEntryMd(
      id: new ObjectId().toHexString(),
      dashboardId: MD_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_PRELIMINARY_CLEARING,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: 1L,
      resultId: EXCEPTION_MANAGEMENT_ID,
      curveConfiguration: null,
      )
  }

  static dashboardStepMdOverlayRun() {
    return new DashboardEntryMd(
      id: new ObjectId().toHexString(),
      dashboardId: MD_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_OVERLAY_RUN,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: null,
      resultId: EXCEPTION_MANAGEMENT_ID,
      curveConfiguration: null,
      )
  }

  def static dashboardStepMdOverlayClearing() {
    return new DashboardEntryMd(
      id: new ObjectId().toHexString(),
      dashboardId: MD_DASHBOARD_ID,
      status: StepStatus.COMPLETED,
      previousStatuses: [], // irrelevant,
      step: DashboardStep.MD_OVERLAY_CLEARING,
      startedAt: LocalDateTime.now(),
      finishedAt: LocalDateTime.now(),
      error: null,
      breaksCount: 2L,
      resultId: EXCEPTION_MANAGEMENT_ID,
      curveConfiguration: CURVE_CONFIGURATION
      )
  }

  static marketDataBatchDashboardSteps(String dashboardId = MD_BATCH_DASHBOARD_ID) {
    return [
      dashboardStepMarketDataBatchUpload(),
      dashboardStepMdBatchPreliminaryRun(),
      dashboardStepMdBatchPreliminaryClearing()
    ].each {
      it.tap { setDashboardId(dashboardId) }
    }
  }

  static trsMarketDataBatchDashboardSteps(String dashboardId = TRS_MARKET_DATA_DASHBOARD_ID) {
    return [
      dashboardStepMarketDataBatchUpload(),
      dashboardStepMdBatchPreliminaryRun(),
      dashboardStepMdBatchPreliminaryClearing()
    ].each {
      it.tap { setDashboardId(dashboardId) }
    }
  }

  static marketDataDashboardSteps(String dashboardId = MD_DASHBOARD_ID) {
    return [
      dashboardStepMarketDataUpload(),
      dashboardStepMdPreliminaryRun(),
      dashboardStepMdPreliminaryClearing(),
      dashboardStepMdOverlayRun(),
      dashboardStepMdOverlayClearing()
    ].each {
      it.tap { setDashboardId(dashboardId) }
    }
  }
}
