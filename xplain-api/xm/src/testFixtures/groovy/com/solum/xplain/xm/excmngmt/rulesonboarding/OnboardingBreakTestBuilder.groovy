package com.solum.xplain.xm.excmngmt.rulesonboarding

import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.RELATIVE_DIFF

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter
import com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType
import groovy.transform.builder.Builder
import groovy.transform.builder.ExternalStrategy
import java.time.LocalDateTime
import org.bson.types.ObjectId

@Builder(builderStrategy = ExternalStrategy, forClass = OnboardingBreakTest, includeSuperProperties = true)
class OnboardingBreakTestBuilder {

  OnboardingBreakTestBuilder() {
    name("Test1")
    sequence(0)
    entityId(ObjectId.get().toString())
    type(OnboardingTestType.DC_XPLAIN)
    measureType(RELATIVE_DIFF)
    tradeFilter(new TradeFilter(productTypes: [CoreProductType.IRS], rateCcys: ["EUR"], fxPairs: ["EUR/USD"]))
    operator(Operator.GT)
    threshold([BigDecimal.ONE, BigDecimal.ZERO])
    recordDate(LocalDateTime.now())
    comment("comment")
    enabled(true)
  }
}
