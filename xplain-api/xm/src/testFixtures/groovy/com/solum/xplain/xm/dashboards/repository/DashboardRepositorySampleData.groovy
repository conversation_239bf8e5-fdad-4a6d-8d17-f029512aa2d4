package com.solum.xplain.xm.dashboards.repository

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID

import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.workflow.MdXmWorkflowProvider
import java.time.LocalDateTime
import org.bson.types.ObjectId

trait DashboardRepositorySampleData {
  static ObjectId PROCESS_EXECUTION_ID = ObjectId.get()
  static ObjectId OVERLAY_PROCESS_EXECUTION_ID = ObjectId.get()

  StepInstance findInstrumentsStepInstance() {
    new StepInstance(
      executionId: PROCESS_EXECUTION_ID,
      processId: MdXmWorkflowProvider.MD_XM_PROCESS_ID,
      businessKey: 'urn:dashboard:' + MD_DASHBOARD_ID,
      rootBusinessKey: 'urn:dashboard:' + MD_DASHBOARD_ID,
      status: WorkflowStatus.ACTIVE,
      stepId: MdXmWorkflowProvider.Steps.FIND_INSTRUMENT_DEFINITIONS,
      reportable: true,
      startedAt: LocalDateTime.now()
      )
  }

  ProcessExecution mdXmProcessExecution() {
    new ProcessExecution(
      id: PROCESS_EXECUTION_ID,
      processId: MdXmWorkflowProvider.MD_XM_PROCESS_ID,
      businessKey: 'urn:dashboard:' + MD_DASHBOARD_ID,
      rootBusinessKey: 'urn:dashboard:' + MD_DASHBOARD_ID,
      status: WorkflowStatus.ACTIVE
      )
  }

  ProcessExecution mdXmOverlayProcessExecution() {
    new ProcessExecution(
      id: OVERLAY_PROCESS_EXECUTION_ID,
      processId: MdXmWorkflowProvider.MD_XM_OVERLAY_PROCESS_ID,
      businessKey: 'urn:dashboard:' + MD_DASHBOARD_ID + "_overlay",
      rootBusinessKey: 'urn:dashboard:' + MD_DASHBOARD_ID,
      status: WorkflowStatus.ACTIVE
      )
  }
}
