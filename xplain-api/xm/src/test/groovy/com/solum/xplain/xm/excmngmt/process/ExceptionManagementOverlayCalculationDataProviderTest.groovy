package com.solum.xplain.xm.excmngmt.process

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PREVIOUS_DASHBOARD_DATE

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup
import com.solum.xplain.xm.excmngmt.market.value.ResolvedOverlayInstrument
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.instrument.CurveGroupInstrumentFilter
import spock.lang.Specification

class ExceptionManagementOverlayCalculationDataProviderTest extends Specification {

  def calculationRepository = Mock(ExceptionManagementCalculationRepository)
  def marketDataExtractionService = Mock(MarketDataExtractionService)
  def curveGroupRepository = Mock(CurveGroupRepository)

  def provider = new ExceptionManagementOverlayCalculationDataProvider(
  calculationRepository,
  marketDataExtractionService,
  curveGroupRepository
  )

  def "should provider curve group instrument filter"() {
    setup:
    def stateDate = Mock(BitemporalDate)
    def resolver1 = new CurveConfigurationInstrumentResolver(
      id: CURVE_CONFIGURATION.entityId,
      name: CURVE_CONFIGURATION.name,
      curveGroupId: 'curveGroupId',
      )


    def resolver2 = new CurveConfigurationInstrumentResolver(
      id: CURVE_CONFIGURATION_ANOTHER.entityId,
      name: CURVE_CONFIGURATION_ANOTHER.name,
      curveGroupId: 'curveGroupId',
      )


    def resolver3 = new CurveConfigurationInstrumentResolver(
      id: "third",
      name: "thirdName",
      curveGroupId: 'curveGroupId2',
      )


    def dashboard = DashboardBuilder.marketDataDashboard()
    dashboard.setMdExceptionManagementSetup(
      MdExceptionManagementSetup.newOf(MARKET_DATA_GROUP, [resolver1, resolver2, resolver3], PREVIOUS_DASHBOARD_DATE, false),
      )

    when:
    def filter = provider.instrumentFilter(dashboard, stateDate)

    then:
    1 * curveGroupRepository.allInstruments('curveGroupId', stateDate) >> [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT]
    1 * curveGroupRepository.allInstruments('curveGroupId2', stateDate) >> [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT]

    and:
    filter == new CurveGroupInstrumentFilter([
      'curveGroupId' : [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key] as Set<String>,
      'curveGroupId2': [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key] as Set<String>
    ])
  }

  def "should return empty filter when no MD setup"() {
    setup:
    def stateDate = Mock(BitemporalDate)
    def dashboard = DashboardBuilder.trsMarketDataDashboard()

    when:
    def filter = provider.instrumentFilter(dashboard, stateDate)

    then:
    filter == new CurveGroupInstrumentFilter([:])
  }

  def "should provider overlay market data"() {
    setup:
    def dashboard = DashboardBuilder.MARKET_DATA_DASHBOARD
    def providerData = Mock(ProviderData)
    def preliminaryResult = Mock(InstrumentResultPreliminary) {
      instrument >> Mock(Instrument) {
        toDefinition() >> InstrumentDefinitionBuilder.DUMMY_INSTRUMENT
      }
      resolvedValue() >> providerData
    }
    1 * calculationRepository.allVerifiedPreliminaryItemsByDashboardId(dashboard.id) >> [preliminaryResult]

    def curveConfigurationData = Mock(ResolvedOverlayInstrument)
    1 * marketDataExtractionService.overlayData(
      MARKET_DATA_GROUP.entityId,
      PREVIOUS_DASHBOARD_DATE
      ) >> Map.of(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key, [curveConfigurationData])

    when:
    def data = provider.provide(dashboard)

    then:
    data.preliminaryMarketDataByInstrument == Map.of(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [preliminaryResult])
    data.overlayMarketDataByInstrument == Map.of(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [curveConfigurationData])
  }
}
