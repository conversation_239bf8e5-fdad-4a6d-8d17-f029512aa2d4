package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_BATCH_PRELIMINARY_RUN

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationService
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdBatchPreliminaryRunExecutorTest extends Specification {

  private static final BitemporalDate STEP_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  private static final String RESULT_ID = "xmResultId"

  DashboardStepProcessor processor = mockDashboardStepProcessor()
  ExceptionManagementCalculationService exceptionManagementService = Mock()

  MdBatchPreliminaryRunExecutor executor = new MdBatchPreliminaryRunExecutor(
  processor,
  exceptionManagementService
  )

  def setup() {
    processor.getStateDate() >> STEP_STATE_DATE
  }

  def "should run preliminary"() {
    setup:
    1 * exceptionManagementService.performPreliminaryBatch(
      MD_BATCH_DASHBOARD,
      STEP_STATE_DATE
      ) >> Either.right(List.of(RESULT_ID))

    when:
    def result = executor.execute(MD_BATCH_DASHBOARD)

    then:
    1 * processor.performMdBatchStep(
      STEP_STATE_DATE,
      { s -> s.dashboardId == MD_BATCH_DASHBOARD_ID && s.step == MD_BATCH_PRELIMINARY_RUN },
      _
      )

    result.isRight()
    result.getOrNull().id == MD_BATCH_DASHBOARD_ID
  }

  def mockDashboardStepProcessor() {
    DashboardEntryRepository entryRepository = Mock()
    Spy(DashboardStepProcessor, constructorArgs: [entryRepository, Mock(AuditEntryService)]) {
      entryRepository.createEntry(_ as DashboardEntryMdBatch) >> { DashboardEntryMdBatch e -> Either.right(e) }
      entryRepository.updateEntry(_ as DashboardEntryMdBatch) >> { DashboardEntryMdBatch e -> Either.right(e) }
    } as DashboardStepProcessor
  }
}
