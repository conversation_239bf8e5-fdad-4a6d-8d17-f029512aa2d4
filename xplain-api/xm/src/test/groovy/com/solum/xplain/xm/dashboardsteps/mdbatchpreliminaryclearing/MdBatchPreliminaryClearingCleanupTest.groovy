package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import spock.lang.Specification

class MdBatchPreliminaryClearingCleanupTest extends Specification {
  TaskExecutionRepository repository = Mock()
  DashboardEntryRepository entryRepository = Mock()
  MdBatchPreliminaryClearingCleanup stepCleanup = new MdBatchPreliminaryClearingCleanup(repository, entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_BATCH_DASHBOARD_ID)

    then:
    1 * repository.preliminaryBatchDeleted(MD_BATCH_DASHBOARD_ID)
    1 * entryRepository.deleteMdBatchEntries(MD_BATCH_DASHBOARD_ID, DashboardStep.MD_BATCH_PRELIMINARY_CLEARING)
  }
}
