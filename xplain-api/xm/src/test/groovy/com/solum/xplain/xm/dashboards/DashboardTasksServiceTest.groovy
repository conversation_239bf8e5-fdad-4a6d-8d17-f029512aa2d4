package com.solum.xplain.xm.dashboards

import com.solum.xplain.core.settings.entity.IpvTaskDefaultTeams
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams
import com.solum.xplain.core.settings.repository.TaskDefaultTeamsSettingsRepository
import com.solum.xplain.core.sockets.events.EventType
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.repository.IpvTaskExecutionRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import com.solum.xplain.xm.tasks.service.TaskNotificationService
import spock.lang.Specification

class DashboardTasksServiceTest extends Specification {

  private static final String DASHBOARD_ID = "id"

  TaskExecutionRepository taskExecutionRepository = Mock()
  IpvTaskExecutionRepository ipvTaskExecutionRepository = Mock()
  TaskNotificationService taskNotificationService = Mock()
  TaskDefaultTeamsSettingsRepository taskDefaultTeamsSettingsRepository = Mock()

  DashboardTasksService service = new DashboardTasksService(
  taskExecutionRepository,
  ipvTaskExecutionRepository,
  taskNotificationService,
  taskDefaultTeamsSettingsRepository
  )

  def "should return task counts"() {
    setup:
    1 * taskExecutionRepository.countsView(DASHBOARD_ID) >> []
    expect:
    service.taskCounts(DASHBOARD_ID) == []
  }

  def "should get tasks"() {
    setup:
    def mdTaskPreliminary = Mock(TaskExecution)
    def mdTaskOverlay = Mock(TaskExecution)
    1 * taskExecutionRepository.executions(DASHBOARD_ID) >> [mdTaskPreliminary, mdTaskOverlay]

    when:
    def result = service.getTasks(DASHBOARD_ID)

    then:
    result.size() == 2
    result[0] == mdTaskPreliminary
    result[1] == mdTaskOverlay
  }

  def "should notify tasks changed"() {
    setup:
    def mdTaskPreliminary = Mock(TaskExecution)
    mdTaskPreliminary.taskExceptionManagementType >> TaskExceptionManagementType.PRELIMINARY
    def mdTaskOverlay = Mock(TaskExecution)
    mdTaskOverlay.taskExceptionManagementType >> TaskExceptionManagementType.OVERLAY
    def tasks = [mdTaskPreliminary, mdTaskOverlay]

    1 * taskDefaultTeamsSettingsRepository.getTaskDefaultTeams() >> MdTaskDefaultTeams.empty()

    when:
    service.notifyTasksChanged(tasks)

    then:
    1 * taskNotificationService.notifyUsers(EventType.MD_PRELIMINARY_UPDATED, [mdTaskPreliminary], MdTaskDefaultTeams.empty())
    1 * taskNotificationService.notifyUsers(EventType.MD_OVERLAY_UPDATED, [mdTaskOverlay], MdTaskDefaultTeams.empty())
  }

  def "should notify batch task changed"() {
    setup:
    def mdBatchTaskPreliminary = Mock(TaskExecution)
    mdBatchTaskPreliminary.taskExceptionManagementType >> TaskExceptionManagementType.PRELIMINARY_BATCH
    def tasks = [mdBatchTaskPreliminary]

    1 * taskDefaultTeamsSettingsRepository.getTaskDefaultTeams() >> MdTaskDefaultTeams.empty()
    0 * taskDefaultTeamsSettingsRepository.getIpvTaskDefaultTeams()

    when:
    service.notifyTasksChanged(tasks)

    then:
    1 * taskNotificationService.notifyUsers(EventType.MD_PRELIMINARY_BATCH_UPDATED, [mdBatchTaskPreliminary], MdTaskDefaultTeams.empty())
  }
}
