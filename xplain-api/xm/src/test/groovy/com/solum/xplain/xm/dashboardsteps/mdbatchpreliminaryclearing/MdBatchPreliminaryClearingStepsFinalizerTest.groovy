package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMarketDataBatchUpload
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryRun

import com.solum.xplain.xm.dashboards.entity.DashboardEntryMdBatch
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdBatchPreliminaryClearingStepsFinalizerTest extends Specification {

  DashboardStepProcessor processor = Mock()
  ExceptionManagementCalculationRepository exceptionManagementRepository = Mock()

  MdBatchPreliminaryClearingStepsFinalizer finalizer = new MdBatchPreliminaryClearingStepsFinalizer(
  processor,
  exceptionManagementRepository
  )

  def "should update preliminary clearing step"() {
    1 * processor.getMdBatchSteps(MD_BATCH_DASHBOARD) >> [
      dashboardStepMarketDataBatchUpload(),
      dashboardStepMdBatchPreliminaryRun(),
      dashboardStepMdBatchPreliminaryClearing().tap {
        status = StepStatus.IN_PROGRESS
      }
    ]

    def xmResult = Mock(ExceptionManagementResult)
    xmResult.status >> CalculationTestStatus.PRELIMINARY_BATCH_APPROVED
    1 * exceptionManagementRepository.entitiesByDashboard(MD_BATCH_DASHBOARD_ID) >> Either.right([xmResult])

    1 * processor.updateMdBatchSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMdBatch>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MD_BATCH_PRELIMINARY_CLEARING
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = finalizer.execute(MD_BATCH_DASHBOARD)

    then:
    result.isRight()
  }
}
