package com.solum.xplain.xm.onboarding

import static com.solum.xplain.xm.onboarding.view.SubmissionResult.SUBMITTED
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.onboarding.form.CreateOnboardingReportForm
import java.time.LocalDate
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@WebMvcTest(controllers = [OnboardingController])
@MockMvcConfiguration
class OnboardingControllerTest extends Specification {

  @SpringBean
  OnboardingControllerService service = Mock()

  @SpringBean
  OnboardingReportCreationService creationService = Mock()

  @SpringBean
  OnboardingReportSubmissionService submissionService = Mock()

  @Autowired
  MockMvc mockMvc

  @Autowired
  ObjectMapper mapper


  @WithMockUser
  def "should run onboarding report"() {
    setup:
    def expectedForm = new CreateOnboardingReportForm([], true, false, true)
    creationService.createReport(expectedForm, { it.getActualDate() == LocalDate.now() }) >> right(EntityId.entityId("id"))

    def results = mockMvc.perform(post("/onboarding")
      .with(csrf())
      .content(mapper.writeValueAsString(expectedForm))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("id") >= 0
    }
  }

  @WithMockUser
  def "should get all reports"() {
    setup:
    service.allReports(_, _) >> ScrollableEntry.empty()
    when:
    def results = mockMvc.perform(get("/onboarding")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should submit onboarding report"() {
    setup:
    def reportId = ObjectId.get()
    submissionService.submitReport(reportId) >> right(SUBMITTED)

    def results = mockMvc.perform(post("/onboarding/" + reportId.toHexString() + "/submit")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf(SUBMITTED.name()) >= 0
    }
  }

  @WithMockUser
  def "should delete onboarding report"() {
    setup:
    def reportId = ObjectId.get()
    service.deleteReport(reportId) >> right(EntityId.entityId(reportId))

    def results = mockMvc.perform(delete("/onboarding/" + reportId.toHexString())
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf(reportId.toHexString()) >= 0
    }
  }
}
