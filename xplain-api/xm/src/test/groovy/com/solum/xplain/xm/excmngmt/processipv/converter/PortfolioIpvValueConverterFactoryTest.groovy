package com.solum.xplain.xm.excmngmt.processipv.converter

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.CurrencyPair.of
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.MID_PRICE
import static com.solum.xplain.xm.settings.IpvValueCurrencyType.REPORTING_CCY
import static com.solum.xplain.xm.settings.IpvValueCurrencyType.TRADE_CCY

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.FxMatrix
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.value.VersionedList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationView
import com.solum.xplain.core.curvegroup.ratefx.CurveGroupFxRatesRepository
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.CompanyEntityRecordData
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverterFactory.PortfolioIpvValueConverterParameters
import com.solum.xplain.xm.excmngmt.processipv.value.PortfolioExceptionManagementData
import com.solum.xplain.xm.settings.ExceptionManagementSettings
import com.solum.xplain.xm.settings.ExceptionManagementSettingsRepository
import com.solum.xplain.xm.settings.IpvValueCurrencyType
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class PortfolioIpvValueConverterFactoryTest extends Specification {
  CompanyLegalEntityValuationSettingsRepository valuationSettingsRepository = Mock()
  DashboardMarketDataSourceResolver sourceResolver = Mock()
  CurveConfigurationRepository curveConfigurationRepository = Mock()
  CurveGroupFxRatesRepository curveGroupFxRatesRepository = Mock()
  MarketDataQuotesSupport marketDataQuotesSupport = Mock()
  ExceptionManagementSettingsRepository settingsRepository = Mock()
  AuditEntryService auditEntryService = Mock()

  PortfolioIpvValueConverterFactory factory = new PortfolioIpvValueConverterFactory(
  valuationSettingsRepository,
  sourceResolver,
  curveConfigurationRepository,
  curveGroupFxRatesRepository,
  marketDataQuotesSupport,
  settingsRepository,
  auditEntryService
  )

  def "should return converter with null fx rates reporting currency converter"() {
    setup:
    List<ErrorItem> errors = []
    def stateDate = BitemporalDate.newOfNow()
    def portfolioData = new PortfolioExceptionManagementData(
    "portfolioId",
    "externalPortfolioId",
    "companyId",
    "externalCompanyId",
    "legalEntityId",
    "externalLegalEntityId",
    null,
    null,
    null,
    true,
    "marketDataGroupId",
    stateDate,
    [:]
    )

    when:
    def converter = factory.converter(LocalDate.now(), REPORTING_CCY, CompanyEntityRecordData.of(portfolioData), portfolioData.externalPortfolioId, stateDate, errors::add)

    then:
    1 * valuationSettingsRepository.getCompanyEntitySettingsView("companyId", "legalEntityId", stateDate) >> new CompanyLegalEntityValuationSettingsView()

    and:
    converter == new ReportingIpvValueCurrencyConverter(null, null)
    errors.description == ["Cannot build NAV/NOTIONAL currency converter for portfolio externalPortfolioId: Reporting currency missing. NAV/NOTIONAL tests might be skipped"]
  }

  @Unroll
  def "should build FX currency converter when #currencyType then #expectedResult"() {
    setup:
    def errorLog = []
    def date = LocalDate.now()
    def stateDate = BitemporalDate.newOfNow()
    def portfolioData = new PortfolioExceptionManagementData(
    "portfolioId",
    "externalPortfolioId",
    "companyId",
    "externalCompanyId",
    "legalEntityId",
    "externalLegalEntityId",
    null,
    null,
    null,
    true,
    "marketDataGroupId",
    stateDate,
    [:]
    )

    def curveConfigStateForm = new CurveConfigMarketStateForm(
    "MD",
    "CC",
    MarketDataSourceType.RAW_PRIMARY,
    stateDate.getActualDate(),
    LocalDate.now(),
    new InstrumentPriceRequirementsForm(fxRatesPriceType: MID_PRICE))

    1 * valuationSettingsRepository.getCompanyEntitySettingsView("companyId", "legalEntityId", stateDate) >> new CompanyLegalEntityValuationSettingsView(
    marketDataGroupId: "MD",
    curveConfigurationId: "CC",
    reportingCurrency: "EUR",
    priceRequirements: new InstrumentPriceRequirements(BID_PRICE, BID_PRICE, MID_PRICE, BID_PRICE, BID_PRICE)

    )
    1 * sourceResolver.resolve("MD", LocalDate.now()) >> MarketDataSourceType.RAW_PRIMARY
    1 * curveConfigurationRepository.getView("CC", stateDate) >> Either.right(new CurveConfigurationView(curveGroupId: "CG"))
    1 * marketDataQuotesSupport.getFullQuotes(curveConfigStateForm) >> [:]
    1 * curveGroupFxRatesRepository.getRatesNodesValuesViews("CG", stateDate.getActualDate(), [:]) >> VersionedList.atEpochStart([new CurveGroupFxRatesNodeValueView(domesticCurrency: "EUR", foreignCurrency: "USD", value: 1.5)])

    expect:
    def converter = factory.converter(
    date,
    currencyType,
    CompanyEntityRecordData.of(portfolioData),
    portfolioData.externalPortfolioId,
    stateDate,
    errorLog::add)

    converter == expectedResult
    errorLog == []

    where:
    currencyType  | expectedResult
    TRADE_CCY     | new TradeCurrencyIpvValueConverter(EUR, FxMatrix.of(of(EUR, Currency.USD), 1.5))
    REPORTING_CCY | new ReportingIpvValueCurrencyConverter(EUR, FxMatrix.of(of(EUR, Currency.USD), 1.5))
  }

  @Unroll
  def "should build FX currency converter when #currencyType then #expectedResult using alternative constructor"() {
    setup:
    def errorLog = []
    def date = LocalDate.now()
    def stateDate = BitemporalDate.newOfNow()
    def portfolioData = new PortfolioExceptionManagementData(
    "portfolioId",
    "externalPortfolioId",
    "companyId",
    "externalCompanyId",
    "legalEntityId",
    "externalLegalEntityId",
    null,
    null,
    null,
    true,
    "marketDataGroupId",
    stateDate,
    [:]
    )

    def curveConfigStateForm = new CurveConfigMarketStateForm(
    "MD",
    "CC",
    MarketDataSourceType.RAW_PRIMARY,
    stateDate.getActualDate(),
    LocalDate.now(),
    new InstrumentPriceRequirementsForm(fxRatesPriceType: MID_PRICE))

    1 * valuationSettingsRepository.getCompanyEntitySettingsView("companyId", "legalEntityId", stateDate) >> new CompanyLegalEntityValuationSettingsView(
    marketDataGroupId: "MD",
    curveConfigurationId: "CC",
    reportingCurrency: "EUR",
    priceRequirements: new InstrumentPriceRequirements(BID_PRICE, BID_PRICE, MID_PRICE, BID_PRICE, BID_PRICE)

    )
    1 * sourceResolver.resolve("MD", LocalDate.now()) >> MarketDataSourceType.RAW_PRIMARY
    1 * curveConfigurationRepository.getView("CC", stateDate) >> Either.right(new CurveConfigurationView(curveGroupId: "CG"))
    1 * marketDataQuotesSupport.getFullQuotes(curveConfigStateForm) >> [:]
    1 * curveGroupFxRatesRepository.getRatesNodesValuesViews("CG", stateDate.getActualDate(), [:]) >> VersionedList.atEpochStart([new CurveGroupFxRatesNodeValueView(domesticCurrency: "EUR", foreignCurrency: "USD", value: 1.5)])
    1 * settingsRepository.exceptionManagementSettings(stateDate) >> new ExceptionManagementSettings(currencyType: currencyType)

    expect:
    def converter = factory.converter(
    date,
    CompanyEntityRecordData.of(portfolioData),
    portfolioData.externalPortfolioId,
    stateDate)

    converter == expectedResult
    errorLog == []

    where:
    currencyType  | expectedResult
    TRADE_CCY     | new TradeCurrencyIpvValueConverter(EUR, FxMatrix.of(of(EUR, Currency.USD), 1.5))
    REPORTING_CCY | new ReportingIpvValueCurrencyConverter(EUR, FxMatrix.of(of(EUR, Currency.USD), 1.5))
  }

  @Unroll
  def "should build FxMatrix correctly with USD triangulation"() {
    def fx = PortfolioIpvValueConverterFactory.buildFxMatrix(Currency.GBP, VersionedList.atEpochStart(
    [
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "CAD", foreignCurrency: "GBP", value: 2.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "AUD", foreignCurrency: "USD", value: 8.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "USD", foreignCurrency: "NZD", value: 0.25d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "GBP", foreignCurrency: "USD", value: 3.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "EUR", foreignCurrency: "USD", value: 2.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "GBP", foreignCurrency: "BRL", value: 7.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "NOK", foreignCurrency: "SEK", value: 1.0d)
    ]))

    when:
    def result = fx.fxRate(ccy1, ccy2)
    then:
    result == expectedResult
    where:
    ccy1         | ccy2         | expectedResult
    Currency.GBP | Currency.AUD | 0.375d
    Currency.GBP | Currency.NZD | 0.75d
    Currency.GBP | EUR          | 1.5d
    Currency.GBP | Currency.USD | 3.0d
    Currency.GBP | Currency.BRL | 7.0d
    Currency.GBP | Currency.CAD | 0.5d
    Currency.CAD | Currency.GBP | 2.0d
  }

  @Unroll
  def "should fail FxMatrix when asking for unrelated / uncalculated rates"() {
    def fx = PortfolioIpvValueConverterFactory.buildFxMatrix(Currency.GBP, VersionedList.atEpochStart(
    [
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "GBP", foreignCurrency: "USD", value: 3.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "EUR", foreignCurrency: "USD", value: 2.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "GBP", foreignCurrency: "AUD", value: 1.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "AUD", foreignCurrency: "NZD", value: 1.0d),
      new CurveGroupFxRatesNodeValueView(domesticCurrency: "NOK", foreignCurrency: "SEK", value: 1.0d)
    ])
    )

    when:
    fx.fxRate(ccy1, ccy2)
    then:
    def e = thrown(IllegalArgumentException)
    e.message == error
    where:
    ccy1         | ccy2         | error
    Currency.GBP | Currency.JPY | "No FX rate found for GBP/JPY, matrix only contains rates for [USD, EUR, GBP, AUD]"
    Currency.NOK | Currency.SEK | "No FX rate found for NOK/SEK, matrix only contains rates for [USD, EUR, GBP, AUD]"
    Currency.USD | Currency.CZK | "No FX rate found for USD/CZK, matrix only contains rates for [USD, EUR, GBP, AUD]"
    Currency.GBP | Currency.NZD | "No FX rate found for GBP/NZD, matrix only contains rates for [USD, EUR, GBP, AUD]"
  }

  def "should return converter with missing FX rates with #error"() {
    setup:
    ArrayList<ErrorItem> errorLog = []
    def reportingCcy = settings.getReportingCurrency() != null ? Currency.of(settings.getReportingCurrency()) : null
    def date = LocalDate.now()
    def stateDate = BitemporalDate.newOfNow()
    def portfolioData = new PortfolioExceptionManagementData(
    "portfolioId",
    "externalPortfolioId",
    "companyId",
    "externalCompanyId",
    "legalEntityId",
    "externalLegalEntityId",
    null,
    null,
    null,
    true,
    "marketDataGroupId",
    stateDate,
    [:]
    )

    when:
    def converter = factory.converter(
    date,
    TRADE_CCY,
    CompanyEntityRecordData.of(portfolioData),
    portfolioData.externalPortfolioId,
    stateDate,
    errorLog::add)

    then:
    1 * valuationSettingsRepository.getCompanyEntitySettingsView("companyId", "legalEntityId", stateDate) >> settings

    and:
    converter == new TradeCurrencyIpvValueConverter(reportingCcy, null)
    errorLog.description == [error]

    where:
    settings                    | error
    settings(null, "CC", "EUR") | "Cannot build NAV/NOTIONAL currency converter for portfolio externalPortfolioId: Market data group id missing. NAV/NOTIONAL tests might be skipped"
    settings("MD", null, "EUR") | "Cannot build NAV/NOTIONAL currency converter for portfolio externalPortfolioId: Curve configuration id missing. NAV/NOTIONAL tests might be skipped"
    settings("MD", "CC", null)  | "Cannot build NAV/NOTIONAL currency converter for portfolio externalPortfolioId: Reporting currency missing. NAV/NOTIONAL tests might be skipped"
  }

  def "should return EntryStream with converters for valid parameters"() {
    given:
    def stateDate = BitemporalDate.newOfNow()
    def param1 = new PortfolioIpvValueConverterParameters(
    new CompanyEntityRecordData("comp1", "le1"), "portfolio-1")
    def param2 = new PortfolioIpvValueConverterParameters(
    new CompanyEntityRecordData("comp2", "le2"), "portfolio-2")
    def parameters = [param1, param2] as Set

    def settings1 = settings("MD1", "CC1", "EUR").tap {
      it.priceRequirements = new InstrumentPriceRequirements(BID_PRICE, BID_PRICE, MID_PRICE, BID_PRICE, BID_PRICE)
    }
    def settings2 = settings("MD2", "CC2", "USD").tap {
      it.priceRequirements = new InstrumentPriceRequirements(BID_PRICE, BID_PRICE, MID_PRICE, BID_PRICE, BID_PRICE)
    }

    def ccView1 = Either.right(new CurveConfigurationView(curveGroupId: "CG1"))
    def ccView2 = Either.right(new CurveConfigurationView(curveGroupId: "CG2"))

    def xmSettings = new ExceptionManagementSettings(currencyType: IpvValueCurrencyType.REPORTING_CCY)

    def marketDataSourceTypes = ["MD1": null, "MD2": null]
    def marketDataQuotes = [:]
    def fxRates1 = new CurveGroupFxRatesNode(domesticCurrency: "EUR", foreignCurrency: "USD")
    def fxRates2 = new CurveGroupFxRatesNode(domesticCurrency: "USD", foreignCurrency: "EUR")

    def fxRates1List = VersionedList.atEpochStart([new CurveGroupFxRatesNodeValueView(domesticCurrency: "EUR", foreignCurrency: "USD", value: 1.5)])
    def fxRates2List = VersionedList.atEpochStart([new CurveGroupFxRatesNodeValueView(domesticCurrency: "USD", foreignCurrency: "EUR", value: 0.9)])

    when:
    // Mock settings repository
    settingsRepository.exceptionManagementSettings(stateDate) >> xmSettings

    // Mock valuation settings
    valuationSettingsRepository.getCompanyLegalEntitySettings(["le1", "le2"], stateDate) >> ["le1": settings1, "le2": settings2]

    // Mock curve configuration views
    curveConfigurationRepository.getViews(["CC1", "CC2"] as Set, stateDate) >> ["CC1": ccView1, "CC2": ccView2]

    // Mock market data source resolver
    sourceResolver.resolve(["MD1", "MD2"] as Set, stateDate.getActualDate()) >> marketDataSourceTypes

    // Mock market data quotes
    marketDataQuotesSupport.getFullQuotes(_ as CurveConfigMarketStateForm) >> marketDataQuotes

    // Mock FX rates
    curveGroupFxRatesRepository.findActiveRates(["CG1", "CG2"] as Set, stateDate) >> [
      "CG1": Either.right(new CurveGroupFxRates().tap {
        it.entityId = "CG1"
        it.nodes = [fxRates1] as Set
      }),
      "CG2": Either.right(new CurveGroupFxRates().tap {
        it.entityId = "CG2"
        it.nodes = [fxRates2] as Set
      })
    ]

    // Mock mergeCurveGroupFxRates
    curveGroupFxRatesRepository.mergeCurveGroupFxRates(_ as Map, { it.entityId == "CG1" }) >> fxRates1List
    curveGroupFxRatesRepository.mergeCurveGroupFxRates(_ as Map, { it.entityId == "CG2" }) >> fxRates2List

    then:
    def result = factory.allConverters(parameters, stateDate).toMap()
    result.size() == 2
    result[param1] instanceof ReportingIpvValueCurrencyConverter
    result[param2] instanceof ReportingIpvValueCurrencyConverter
  }

  private CompanyLegalEntityValuationSettingsView settings(String mdId, String curveConfigId, String reportingCurrency) {
    new CompanyLegalEntityValuationSettingsView(marketDataGroupId: mdId, curveConfigurationId: curveConfigId, reportingCurrency: reportingCurrency)
  }
}
