package com.solum.xplain.xm.excmngmt.process.value


import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.instrument.ExceptionManagementDataProvider
import com.solum.xplain.xm.excmngmt.process.instrument.InstrumentRequirements
import com.solum.xplain.xm.excmngmt.process.instrument.InstrumentRequirementsResolver
import com.solum.xplain.xm.excmngmt.process.value.InstrumentMarketDataBreakCalculatorPreliminary.InstrumentMarketDataBreakCalculatorPreliminarySupplier
import com.solum.xplain.xm.excmngmt.stat.data.StatisticalZScoreData
import java.time.LocalDate
import java.util.function.Function
import java.util.function.IntFunction
import spock.lang.Specification

class PreliminaryCalculationTest extends Specification {

  private static final LocalDate VALUATION_DATE = LocalDate.of(2018, 1, 2)
  private static final BitemporalDate STATE_DATE = BitemporalDate.newOf(LocalDate.of(2018, 1, 3))

  def "should correctly construct preliminary calculation"() {
    setup:
    def mdkBidAskMappings = [:]
    def btCalcs = Mock(BreakTestCalculationsPreliminary)
    btCalcs.longestStaleDuration() >> Optional.of(5)

    def overlay = Mock(InstrumentResultOverlay)
    overlay.resolvedValue() >> TEN
    def instr = Mock(Instrument)
    def instrDef = Mock(InstrumentDefinition)
    instr.toDefinition() >> instrDef
    overlay.getInstrument() >> instr

    def hd = Mock(HistoricalMarketData)
    def hist = Mock(IntFunction)
    hist.apply(5) >> hd

    def stat = Mock(StatisticalZScoreData)
    stat.getInstrumentKey() >> "KEY"

    when:
    def calc = PreliminaryCalculation.ofDate(VALUATION_DATE, mdkBidAskMappings, btCalcs, null, hist)

    then:
    calc.valuationDate == VALUATION_DATE
    calc.breakTestCalculations == btCalcs
    calc.allMarketData == hd
    calc.mdkBidAskMappings == mdkBidAskMappings
  }

  def "should correctly perform preliminary calculation"() {
    setup:
    def prevDate = LocalDate.of(2018, 1, 1)

    def instrBreak = Mock(InstrumentResultBreak)
    def btCalcs = Mock(BreakTestCalculationsPreliminary)
    btCalcs.longestStaleDuration() >> Optional.of(5)
    def mdkBidAskMappings = ["KEY": ["P": BID_ASK, "S": BID_ASK]]

    def allData = Mock(Function)
    allData.apply(VALUATION_DATE) >> ["KEY": ["P": [(BID): ONE]]]
    allData.apply(prevDate) >> ["KEY": ["P": [(BID): TEN]]]

    def primary = new ProviderData(provider: "P", value: ONE, previousValue: TEN, bidAskType: BID)

    def btCalc = Mock(InstrumentMarketDataBreakCalculatorPreliminary)
    def btcSupplier = Mock(InstrumentMarketDataBreakCalculatorPreliminarySupplier)
    1 * btcSupplier.supply(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, _, primary) >> btCalc

    1 * btCalcs.processCalc(btCalc) >> [instrBreak]

    def hd = Mock(HistoricalMarketData)
    hd.historicalData(5) >> ["KEY": ["P": [(BID): [ZERO]]]]

    def calc = new PreliminaryCalculation(VALUATION_DATE, mdkBidAskMappings, btCalcs, allData, hd, btcSupplier)
    def requirementsResolver = Mock(InstrumentRequirementsResolver)
    requirementsResolver.requirements(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT) >> [
      new InstrumentRequirements("marketDataId", new MarketDataProviders(primary: "P", secondary: "S"), "cc1", null, null),
      new InstrumentRequirements("marketDataId", new MarketDataProviders(primary: "P", secondary: "S"), null, "companyId", "entity")
    ]
    def dataProvider = new ExceptionManagementDataProvider("marketData", prevDate, [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT] as Set, requirementsResolver, Arrays.asList(CoreAssetGroup.values()))

    when:
    def result = calc.perform(dataProvider)

    then:
    result.size() == 4
    def resultPrelim = result.find { it.getProviderData().getProvider() == "P" && it.getProviderData().getBidAskType() == BID }
    resultPrelim.providerData == primary
    resultPrelim.allProvidersData.size() == 4
    resultPrelim.breakTests.size() == 1
    resultPrelim.breakTests[0] == instrBreak

    def resultPrelimNoData = result.find { it.getProviderData().getProvider() == "S" && it.getProviderData().getBidAskType() == ASK }
    resultPrelimNoData.providerData == new ProviderData(provider: "S", value: null, previousValue: null, bidAskType: ASK)
  }

  def "should correctly perform preliminary calculation when no mdk mappings and no data"() {
    setup:
    def mdkBidAskMappings = [:]

    def allData = Mock(Function)
    allData.apply(_) >> [:]

    def c1 = Mock(CurveConfigurationInstrumentResolver)
    c1.getCurveGroupId() >> "cgId"
    c1.resolveProvider(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT) >> Optional.of(new MarketDataProviders(primary: "P"))

    def requirementsResolver = Mock(InstrumentRequirementsResolver)
    requirementsResolver.requirements(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT) >> [
      new InstrumentRequirements("marketDataId", new MarketDataProviders(primary: "P", secondary: "S"), "cc1", null, null),
    ]
    def dataProvider = new ExceptionManagementDataProvider("marketData", null, [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT] as Set, requirementsResolver, Arrays.asList(CoreAssetGroup.values()))
    def calc = new PreliminaryCalculation(VALUATION_DATE, mdkBidAskMappings, Mock(BreakTestCalculationsPreliminary), allData, Mock(HistoricalMarketData), Mock(InstrumentMarketDataBreakCalculatorPreliminarySupplier))

    when:
    def result = calc.perform(dataProvider)

    then:
    result.isEmpty()
  }
}
