package com.solum.xplain.xm.dashboards.entity

import static DashboardBuilder.CURVE_CONFIGURATION_ANOTHER
import static DashboardEntryBuilder.dashboardStepMdOverlayClearing
import static DashboardEntryBuilder.dashboardStepMdOverlayRun
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryClearing

import spock.lang.Specification

class DashboardEntryMdTest extends Specification {

  def "should determine same entry"() {
    setup:
    def entry = dashboardStepMdOverlayClearing()

    expect: "not same entry when different class"
    !entry.isSameEntry(dashboardStepMdBatchPreliminaryClearing())

    and: "not same entry when step does not match"
    !entry.isSameEntry(dashboardStepMdOverlayRun())

    and: "not same entry when curve configuration does not match"
    !entry.isSameEntry(dashboardStepMdOverlayClearing().tap {
      curveConfiguration = CURVE_CONFIGURATION_ANOTHER
    })

    and: "same entry"
    entry.isSameEntry(dashboardStepMdOverlayClearing())
  }
}
