package com.solum.xplain.xm.dashboards

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.valuationDataDashboard
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.calculationapi.CalculationExportProvider
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.daterange.DateRangeForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.xm.dashboards.enums.DashboardType
import com.solum.xplain.xm.dashboards.forms.DashboardForm
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboards.resolver.DashboardResolver
import com.solum.xplain.xm.tasks.entity.TaskExecution
import java.time.LocalDate
import org.springframework.core.io.ByteArrayResource
import spock.lang.Specification

class DashboardServiceTest extends Specification {

  private static final BitemporalDate BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  private static final String DASHBOARD_ID = "dashboardId"

  DashboardRepository repository = Mock()
  DashboardResolver dashboardResolver = Mock()
  DashboardTasksService dashboardTasksService = Mock()
  CalculationExportProvider calculationService = Mock()
  AuditEntryService auditEntryService = Mock()

  DashboardService service = new DashboardService(
  repository,
  dashboardResolver,
  dashboardTasksService,
  calculationService,
  auditEntryService
  )

  def "should create dashboard"() {
    setup:
    def dashboardForm = DashboardForm
      .builder()
      .type(DashboardType.MARKET_DATA)
      .dateRange(new DateRangeForm(DASHBOARD_DATE, DASHBOARD_DATE))
      .stateDate(STATE_DATE)
      .marketDataGroupId(MARKET_DATA_GROUP.entityId)
      .build()

    def dashboard = marketDataDashboard()
    1 * dashboardResolver.resolve(BITEMPORAL_STATE_DATE, dashboardForm) >> right(dashboard)
    1 * repository.createDashboard(dashboard) >> right(EntityId.entityId(DASHBOARD_ID))

    when:
    def result = service.createDashboard(BITEMPORAL_STATE_DATE, dashboardForm)

    then:
    result.isRight()
    result.getOrNull().id == DASHBOARD_ID
  }


  def "should create dashboard (portfolio criteria feature)"() {
    setup:
    def dashboardForm = DashboardForm
      .builder()
      .type(DashboardType.MARKET_DATA)
      .dateRange(new DateRangeForm(DASHBOARD_DATE, DASHBOARD_DATE))
      .stateDate(STATE_DATE)
      .marketDataGroupId(MARKET_DATA_GROUP.entityId)
      .relevantOnly(true)
      .build()

    def dashboard = marketDataDashboard()
    1 * dashboardResolver.resolve(BITEMPORAL_STATE_DATE, dashboardForm) >> right(dashboard)
    1 * repository.createDashboard(dashboard) >> right(EntityId.entityId(DASHBOARD_ID))

    when:
    def result = service.createDashboard(BITEMPORAL_STATE_DATE, dashboardForm)

    then:
    result.isRight()
    result.getOrNull().id == DASHBOARD_ID
  }

  def "should get all dashboards"() {
    setup:
    def dashboard = valuationDataDashboard()
    def dashboard2 = marketDataDashboard()
    1 * repository.getDashboards(_, _, _, _) >> ScrollableEntry.of(
      [dashboard, dashboard2],
      ScrollRequest.unconstrained()
      )

    when:
    def result = service.getDashboards(
      TableFilter.emptyTableFilter(),
      ScrollRequest.unconstrained(),
      STATE_DATE, true
      )

    then:
    result.content.size() == 2
    result.content[0].id == dashboard.id
    result.content[0].type == dashboard.type
    result.content[1].id == dashboard2.id
    result.content[1].type == dashboard2.type
  }

  def "should set fromDate to null when showLastWeekOnly is false"() {
    given:
    def dashboard = valuationDataDashboard()
    def dashboard2 = marketDataDashboard()

    and:
    1 * repository.getDashboards(_, _, null, _) >> ScrollableEntry.of(
      [dashboard, dashboard2],
      ScrollRequest.unconstrained()
      )

    when:
    def result = service.getDashboards(
      TableFilter.emptyTableFilter(),
      ScrollRequest.unconstrained(),
      STATE_DATE,
      false  // showLastWeekOnly = false
      )

    then:
    result.content.size() == 2
    result.content[0].id == dashboard.id
    result.content[1].id == dashboard2.id
  }

  def "should set fromDate to 7 days before stateDate when showLastWeekOnly is true"() {
    given:
    def expectedFromDate = STATE_DATE.minusDays(7).atStartOfDay()
    def dashboard = valuationDataDashboard()
    def dashboard2 = marketDataDashboard()

    and:
    1 * repository.getDashboards(_, _, expectedFromDate, _) >> ScrollableEntry.of(
      [dashboard, dashboard2],
      ScrollRequest.unconstrained()
      )

    when:
    def result = service.getDashboards(
      TableFilter.emptyTableFilter(),
      ScrollRequest.unconstrained(),
      STATE_DATE,
      true  // showLastWeekOnly = true
      )

    then:
    result.content.size() == 2
    result.content[0].id == dashboard.id
    result.content[1].id == dashboard2.id
  }

  def "should get dashboard view"() {
    setup:
    def dashboard = marketDataDashboard()
    1 * repository.getDashboard(dashboard.id, _) >> right(dashboard)

    when:
    def result = service.getDashboard(dashboard.id)

    then:
    result.isRight()
  }

  def "should delete dashboard"() {
    setup:
    def tasks = [Mock(TaskExecution)]
    1 * dashboardTasksService.getTasks(DASHBOARD_ID) >> tasks

    1 * repository.deleteDashboard(DASHBOARD_ID) >> right(EntityId.entityId(DASHBOARD_ID))

    def entry = AuditEntry.of("dashboard", "Dashboard deleted", DASHBOARD_ID)
    1 * auditEntryService.newEntry(entry) >> right(entry)

    when:
    def result = service.deleteDashboard(DASHBOARD_ID)

    then:
    1 * dashboardTasksService.notifyTasksChanged(tasks)

    result.isRight()
    result.getOrNull().id == DASHBOARD_ID
  }

  def "should export all PV calculations portfolio items"() {
    setup:
    def header = "Company ID,Entity ID,Portfolio ID,Trade ID,Trade Type,Counterparty,Counterparty Type,Trade Date,Underlying,Buy/Sell,Start Date,End Date,Client PV,Valuation Status,Valuation Error,PV (Trade Ccy),Clean PV (Trade Ccy),PV Pay Leg (Leg Ccy),PV Rec Leg (Leg Ccy),PV01 (Trade Ccy),Total DV01 (Trade Ccy),INF01 / CS01 / BR01 (Trade Ccy),Gamma (Trade Ccy),Theta (Trade Ccy),Vega (Trade Ccy),Pay T0 CFS (Leg Ccy),Rec T0 CFS (Leg Ccy),T0 Net CFS (Trade Ccy),Delta Forward (Trade Ccy),Delta Spot (Trade Ccy),PV (Reporting Ccy),Clean PV (Reporting Ccy),PV01 (Reporting Ccy),Total DV01 (Reporting Ccy),INF01 / CS01 / BR01 (Reporting Ccy),Gamma (Reporting Ccy),Theta (Reporting Ccy),Vega (Reporting Ccy),T0 Net CFS (Reporting Ccy),Delta Forward (Reporting Ccy),Delta Spot (Reporting Ccy),Par Rate,Implied Vol,Pay Ccy,Pay Notional,Pay Rate/Margin,Pay Index,Pay Freq,Pay Daycount,Rec Ccy,Rec Notional,Rec Rate/Margin,Rec Index,Rec Freq,Rec Daycount\n"

    1 * repository.dashboard(VD_DASHBOARD_ID) >> right(VD_DASHBOARD)

    1 * repository.getCalculationResultIds(VD_DASHBOARD_ID, TableFilter.emptyTableFilter()) >> ["id", "id2"]

    1 * calculationService.exportAllPVCalculationResults(
      ["id", "id2"],
      DASHBOARD_DATE,
      LocalDate.now()) >> FileResponseEntity.csvFile(new ByteArrayResource(header.bytes), "name")

    when:
    def result = service.getAllDashboardPVCalculationResults(VD_DASHBOARD_ID, LocalDate.now(), TableFilter.emptyTableFilter())

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == header
  }
}
