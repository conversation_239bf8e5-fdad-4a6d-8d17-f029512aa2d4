package com.solum.xplain.xm.excmngmt.processipv.value

import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.ABSOLUTE_DIFF
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_DV01_VEGA
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.GREEKS_VEGA
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.NAV
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.RELATIVE_DIFF
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.ACCOUNTING_COST
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P1
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P2
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P3
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P4
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO

import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.process.value.EntryResultCustomResolver
import com.solum.xplain.xm.excmngmt.process.value.EntryResultResolver
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest
import com.solum.xplain.xm.excmngmt.value.BreakTestHistory
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory
import spock.lang.Specification
import spock.lang.Unroll

class TradeBreakCalculatorTest extends Specification {

  private static final TradeBreakScalingData SCALING_DATA = new TradeBreakScalingData(ONE, TEN)
  private static final NotionalData NOTIONAL_DATA = new NotionalData(ONE, ONE)

  private static final BigDecimal P1_VALUE = BigDecimal.valueOf(0.5)
  private static final BigDecimal P1_PREVIOUS_VALUE = BigDecimal.valueOf(10)
  private static final BigDecimal P1_DELTA_VALUE = BigDecimal.valueOf(0.01)
  private static final BigDecimal P1_DELTA_PREVIOUS_VALUE = BigDecimal.valueOf(0.011)
  private static final BigDecimal P1_VEGA_VALUE = BigDecimal.valueOf(0.001)
  private static final BigDecimal P1_VEGA_PREVIOUS_VALUE = BigDecimal.valueOf(0.0011)

  private static final BigDecimal P2_VALUE = BigDecimal.valueOf(2)
  private static final BigDecimal P2_PREVIOUS_VALUE = BigDecimal.valueOf(20)
  private static final BigDecimal P2_DELTA_VALUE = BigDecimal.valueOf(0.02)
  private static final BigDecimal P2_DELTA_PREVIOUS_VALUE = BigDecimal.valueOf(0.022)
  private static final BigDecimal P2_VEGA_VALUE = BigDecimal.valueOf(0.002)
  private static final BigDecimal P2_VEGA_PREVIOUS_VALUE = BigDecimal.valueOf(0.0022)

  private static final BigDecimal P3_VALUE = BigDecimal.valueOf(4)
  private static final BigDecimal P3_PREVIOUS_VALUE = BigDecimal.valueOf(40)
  private static final BigDecimal P3_DELTA_VALUE = BigDecimal.valueOf(0.04)
  private static final BigDecimal P3_DELTA_PREVIOUS_VALUE = BigDecimal.valueOf(0.044)
  private static final BigDecimal P3_VEGA_VALUE = BigDecimal.valueOf(0.004)
  private static final BigDecimal P3_VEGA_PREVIOUS_VALUE = BigDecimal.valueOf(0.0044)

  private static final BigDecimal P4_VALUE = BigDecimal.valueOf(8)
  private static final BigDecimal P4_PREVIOUS_VALUE = BigDecimal.valueOf(80)
  private static final BigDecimal P4_DELTA_VALUE = BigDecimal.valueOf(0.08)
  private static final BigDecimal P4_DELTA_PREVIOUS_VALUE = BigDecimal.valueOf(0.088)
  private static final BigDecimal P4_VEGA_VALUE = BigDecimal.valueOf(0.008)
  private static final BigDecimal P4_VEGA_PREVIOUS_VALUE = BigDecimal.valueOf(0.0088)

  private static final BundledProviderData PROVIDER_DATA = new BundledProviderData(
  new ProviderDataWithGreeks(
  provider: "P",
  pv: new ProviderDataValue(P1_VALUE, P1_PREVIOUS_VALUE),
  delta: new ProviderDataValue(P1_DELTA_VALUE, P1_DELTA_PREVIOUS_VALUE),
  vega: new ProviderDataValue(P1_VEGA_VALUE, P1_VEGA_PREVIOUS_VALUE),
  ),
  new ProviderDataWithGreeks(
  provider: "S",
  pv: new ProviderDataValue(P2_VALUE, P2_PREVIOUS_VALUE),
  delta: new ProviderDataValue(P2_DELTA_VALUE, P2_DELTA_PREVIOUS_VALUE),
  vega: new ProviderDataValue(P2_VEGA_VALUE, P2_VEGA_PREVIOUS_VALUE),
  ),
  new ProviderDataWithGreeks(
  provider: "T",
  pv: new ProviderDataValue(P3_VALUE, P3_PREVIOUS_VALUE),
  delta: new ProviderDataValue(P3_DELTA_VALUE, P3_DELTA_PREVIOUS_VALUE),
  vega: new ProviderDataValue(P3_VEGA_VALUE, P3_VEGA_PREVIOUS_VALUE),
  ),
  new ProviderDataWithGreeks(
  provider: "Q",
  pv: new ProviderDataValue(P4_VALUE, P4_PREVIOUS_VALUE),
  delta: new ProviderDataValue(P4_DELTA_VALUE, P4_DELTA_PREVIOUS_VALUE),
  vega: new ProviderDataValue(P4_VEGA_VALUE, P4_VEGA_PREVIOUS_VALUE),
  )
  )

  private static final HistoricalTradeValuationData EMPTY_HISTORY = new HistoricalTradeValuationData([], [:])

  private static final EntryResultResolver BREAKING_RESOLVER = Operator.GT.resolver([ONE.negate()])

  private static final EntryResultResolver CALCULATION_ONLY_RESOLVER =
  EntryResultResolver.calculationOnly()

  private static final EntryResultCustomResolver BREAKING_CUSTOM_RESOLVER = { t, v ->
    EntryResultBreakByProvider.ofBreakWithoutLevel(t, v)
  } as EntryResultCustomResolver

  private static final EntryResultCustomResolver CALCULATION_ONLY_CUSTOM_RESOLVER =
  EntryResultCustomResolver.calculationOnly()


  private Trade TRADE = Mock(Trade)
  private EntryBreakHistory ENTRY_BREAK_HISTORY = new EntryBreakHistory("id", [])

  def setup() {
    TRADE.productType >> CoreProductType.CAP_FLOOR
  }

  def "should correctly P1 null test with triggered result"() {
    setup:
    def data = Mock(BundledProviderData)
    data.pv(P1) >> null
    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, data, SCALING_DATA, NOTIONAL_DATA)

    when:
    def result = calculator.nullProvider(P1, BREAKING_CUSTOM_RESOLVER)

    then:
    result.triggered
    result.value == null
  }

  def "should correctly P1 zero test with triggered result"() {
    setup:
    def data = Mock(BundledProviderData)
    data.pv(P1) >> new BigDecimal("0.00")
    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, data, SCALING_DATA, NOTIONAL_DATA)

    when:
    def result = calculator.zeroValue(P1, BREAKING_CUSTOM_RESOLVER)

    then:
    result.triggered
    result.value == 0.0
  }

  def "should correctly perform day on day for #p #measure = #value (#triggered/#calcOnly)"() {
    setup:
    def notionalData = new NotionalData(currDayNotional, ONE)
    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, PROVIDER_DATA, SCALING_DATA, notionalData)

    when:
    def result = calculator.dayOnDay(p, measure, resolver)

    then:
    result.triggered == triggered
    result.value == value
    result.calculationOnly == calcOnly

    where:
    p  | measure       | resolver                  | triggered | value        | calcOnly  | currDayNotional
    // P1
    // RELATIVE_DIFF
    P1 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.950000     | false     | ONE
    P1 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.950000     | true      | ONE
    P1 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.995000     | true      | TEN
    P1 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.995000     | false     | TEN
    // ABSOLUTE_DIFF
    P1 | ABSOLUTE_DIFF | BREAKING_RESOLVER         | true      | 9.5          | false     | ONE
    P1 | ABSOLUTE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 9.5          | true      | ONE
    P1 | ABSOLUTE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 99.5         | true      | TEN
    P1 | ABSOLUTE_DIFF | BREAKING_RESOLVER         | true      | 99.5         | false     | TEN
    // P2
    // RELATIVE_DIFF
    P2 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.9          | false     | ONE
    P2 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.9          | true      | ONE
    P2 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.990000     | true      | TEN
    P2 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.990000     | false     | TEN
    // ABSOLUTE_DIFF
    P2 | ABSOLUTE_DIFF | BREAKING_RESOLVER         | true      | 18           | false     | ONE
    P2 | ABSOLUTE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 18           | true      | ONE
    // P3
    // RELATIVE_DIFF
    P3 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.9          | false     | ONE
    P3 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.9          | true      | ONE
    // ABSOLUTE_DIFF
    P3 | ABSOLUTE_DIFF | BREAKING_RESOLVER         | true      | 36           | false     | ONE
    P3 | ABSOLUTE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 36           | true      | ONE
    // P4
    // RELATIVE_DIFF
    P4 | RELATIVE_DIFF | BREAKING_RESOLVER         | true      | 0.9          | false     | ONE
    P4 | RELATIVE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 0.9          | true      | ONE
    // ABSOLUTE_DIFF
    P4 | ABSOLUTE_DIFF | BREAKING_RESOLVER         | true      | 72           | false     | ONE
    P4 | ABSOLUTE_DIFF | CALCULATION_ONLY_RESOLVER | false     | 72           | true      | ONE
    // P1
    // NAV
    P1 | NAV           | BREAKING_RESOLVER         | true      | 95000        | false     | ONE
    P1 | NAV           | CALCULATION_ONLY_RESOLVER | false     | 95000        | true      | ONE
    P1 | NAV           | CALCULATION_ONLY_RESOLVER | false     | 99500        | true      | TEN
    P1 | NAV           | BREAKING_RESOLVER         | true      | 99500        | false     | TEN
    // GREEKS_DV01
    P1 | GREEKS_DV01   | BREAKING_RESOLVER         | true      | 904.761904   | false     | ONE
    P1 | GREEKS_DV01   | CALCULATION_ONLY_RESOLVER | false     | 904.761904   | true      | ONE
    P1 | GREEKS_DV01   | CALCULATION_ONLY_RESOLVER | false     | 1658.333333  | true      | TEN
    P1 | GREEKS_DV01   | BREAKING_RESOLVER         | true      | 1658.333333  | false     | TEN
    // GREEKS_VEGA
    P1 | GREEKS_VEGA   | BREAKING_RESOLVER         | true      | 9047.619047   | false     | ONE
    P1 | GREEKS_VEGA   | CALCULATION_ONLY_RESOLVER | false     | 9047.619047   | true      | ONE
    P1 | GREEKS_VEGA   | CALCULATION_ONLY_RESOLVER | false     | 16583.333333  | true      | TEN
    P1 | GREEKS_VEGA   | BREAKING_RESOLVER         | true      | 16583.333333  | false     | TEN
  }

  def "should correctly calculate diff measure value for #productType"() {
    setup:
    def trade = Mock(Trade)
    trade.productType >> productType

    def calculator = TradeBreakCalculator.forTrade(trade, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, PROVIDER_DATA, SCALING_DATA, NOTIONAL_DATA)

    expect:
    calculator.providerDiff(P2, RELATIVE_DIFF, CALCULATION_ONLY_RESOLVER).value == relativeDiff
    calculator.providerDiff(P2, ABSOLUTE_DIFF, CALCULATION_ONLY_RESOLVER).value == absoluteDiff
    calculator.providerDiff(P2, GREEKS_DV01, CALCULATION_ONLY_RESOLVER).value == greeksDv01
    calculator.providerDiff(P2, GREEKS_DV01_VEGA, CALCULATION_ONLY_RESOLVER).value == greeksDv01Vega
    calculator.providerDiff(P2, GREEKS_VEGA, CALCULATION_ONLY_RESOLVER).value == greeksVega


    where:
    productType               | relativeDiff | absoluteDiff | greeksDv01 | greeksDv01Vega | greeksVega
    CoreProductType.IRS       | 3.000000     | 1.5          | 150.000000 | 150.000000     | null
    CoreProductType.INFLATION | 3.000000     | 1.5          | 150.000000 | 150.000000     | null
    CoreProductType.SWAPTION  | 3.000000     | 1.5          | 150.000000 | 136.363636     | 1500.000000
    CoreProductType.CAP_FLOOR | 3.000000     | 1.5          | 150.000000 | 136.363636     | 1500.000000
    CoreProductType.XCCY      | 3.000000     | 1.5          | 150.000000 | 150.000000     | null
    CoreProductType.FXFWD     | 3.000000     | 1.5          | 150.000000 | 150.000000     | null
    CoreProductType.FXOPT     | 3.000000     | 1.5          | 150.000000 | 136.363636     | 1500.000000
    CoreProductType.CDS       | 3.000000     | 1.5          | 150.000000 | 150.000000     | null
  }

  def "should correctly calculate diff measure value for ACCOUNTING_COST provider"() {
    setup:
    def trade = Mock(Trade)
    trade.getAccountingCost() >> 400
    trade.getProductType() >> CoreProductType.IRS

    def calculator = TradeBreakCalculator.forTrade(trade, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, PROVIDER_DATA, SCALING_DATA, NOTIONAL_DATA)

    expect:
    calculator.providerDiff(ACCOUNTING_COST, RELATIVE_DIFF, CALCULATION_ONLY_RESOLVER).value == 799.000000
    calculator.providerDiff(ACCOUNTING_COST, ABSOLUTE_DIFF, CALCULATION_ONLY_RESOLVER).value == 399.5
    calculator.providerDiff(ACCOUNTING_COST, GREEKS_DV01, CALCULATION_ONLY_RESOLVER).value == 39950.000000
    calculator.providerDiff(ACCOUNTING_COST, GREEKS_DV01_VEGA, CALCULATION_ONLY_RESOLVER).value == 39950.000000
    calculator.providerDiff(ACCOUNTING_COST, GREEKS_VEGA, CALCULATION_ONLY_RESOLVER).value == null
  }

  @Unroll
  def "should correctly perform day to day sign for t=#value, t-1=#previousValue"() {
    setup:
    def providerData = new BundledProviderData(
      new ProviderDataWithGreeks(
      provider: "P",
      pv: new ProviderDataValue(value, previousValue),
      // other fields not relevant
      ),
      null,
      null,
      null
      )

    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, providerData, SCALING_DATA, NOTIONAL_DATA)


    when:
    def result = calculator.dayToDaySign(P1, BREAKING_CUSTOM_RESOLVER)

    then:
    with(result) {
      triggered == isTriggered
      value == value
      !calculationOnly
    }

    where:
    value        | previousValue | isTriggered
    null         | null          | false
    ONE          | null          | false
    null         | ONE           | false

    ZERO         | ONE           | false
    ZERO         | ONE.negate()  | false
    ONE.negate() | ZERO          | false
    ZERO         | ZERO.negate() | false

    ONE          | ONE           | false
    ONE          | ONE.negate()  | true
    ONE.negate() | ONE           | true
    ONE          | TEN.negate()  | true
    TEN.negate() | ONE           | true
  }

  def "should correctly perform diff for #p2/P1 #measure = #value (#triggered/#calcOnly)"() {
    setup:
    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, EMPTY_HISTORY, PROVIDER_DATA, SCALING_DATA, NOTIONAL_DATA)

    when:
    def result = calculator.providerDiff(p2, measure, resolver)

    then:
    result.triggered == triggered
    result.value == value
    result.calculationOnly == calcOnly

    where:
    p2 | measure          | resolver                  | triggered | value       | calcOnly
    // P1 vs P2
    // RELATIVE_DIFF
    P2 | RELATIVE_DIFF    | BREAKING_RESOLVER         | true      | 3.000000    | false
    P2 | RELATIVE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 3.000000    | true
    // ABSOLUTE_DIFF
    P2 | ABSOLUTE_DIFF    | BREAKING_RESOLVER         | true      | 1.5         | false
    P2 | ABSOLUTE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 1.5         | true
    // GREEKS_DV01
    P2 | GREEKS_DV01      | BREAKING_RESOLVER         | true      | 150.000000  | false
    P2 | GREEKS_DV01      | CALCULATION_ONLY_RESOLVER | false     | 150.000000  | true
    // GREEKS_VEGA
    P2 | GREEKS_VEGA      | BREAKING_RESOLVER         | true      | 1500.000000 | false
    P2 | GREEKS_VEGA      | CALCULATION_ONLY_RESOLVER | false     | 1500.000000 | true
    // GREEKS_DV01_VEGA
    P2 | GREEKS_DV01_VEGA | BREAKING_RESOLVER         | true      | 136.363636  | false
    P2 | GREEKS_DV01_VEGA | CALCULATION_ONLY_RESOLVER | false     | 136.363636  | true
    // P1 vs P3
    // RELATIVE_DIFF
    P3 | RELATIVE_DIFF    | BREAKING_RESOLVER         | true      | 7.000000    | false
    P3 | RELATIVE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 7.000000    | true
    // ABSOLUTE_DIFF
    P3 | ABSOLUTE_DIFF    | BREAKING_RESOLVER         | true      | 3.5         | false
    P3 | ABSOLUTE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 3.5         | true
    // GREEKS_DV01
    P3 | GREEKS_DV01      | BREAKING_RESOLVER         | true      | 350.000000  | false
    P3 | GREEKS_DV01      | CALCULATION_ONLY_RESOLVER | false     | 350.000000  | true
    // GREEKS_VEGA
    P3 | GREEKS_VEGA      | BREAKING_RESOLVER         | true      | 3500.000000 | false
    P3 | GREEKS_VEGA      | CALCULATION_ONLY_RESOLVER | false     | 3500.000000 | true
    // GREEKS_DV01_VEGA
    P3 | GREEKS_DV01_VEGA | BREAKING_RESOLVER         | true      | 318.181818  | false
    P3 | GREEKS_DV01_VEGA | CALCULATION_ONLY_RESOLVER | false     | 318.181818  | true
    // P1 vs P4
    // RELATIVE_DIFF
    P4 | RELATIVE_DIFF    | BREAKING_RESOLVER         | true      | 15.000000   | false
    P4 | RELATIVE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 15.000000   | true
    // ABSOLUTE_DIFF
    P4 | ABSOLUTE_DIFF    | BREAKING_RESOLVER         | true      | 7.5         | false
    P4 | ABSOLUTE_DIFF    | CALCULATION_ONLY_RESOLVER | false     | 7.5         | true
    // GREEKS_DV01
    P4 | GREEKS_DV01      | BREAKING_RESOLVER         | true      | 750.000000  | false
    P4 | GREEKS_DV01      | CALCULATION_ONLY_RESOLVER | false     | 750.000000  | true
    // GREEKS_VEGA
    P4 | GREEKS_VEGA      | BREAKING_RESOLVER         | true      | 7500.000000 | false
    P4 | GREEKS_VEGA      | CALCULATION_ONLY_RESOLVER | false     | 7500.000000 | true
    // GREEKS_DV01_VEGA
    P4 | GREEKS_DV01_VEGA | BREAKING_RESOLVER         | true      | 681.818181  | false
    P4 | GREEKS_DV01_VEGA | CALCULATION_ONLY_RESOLVER | false     | 681.818181  | true
  }

  def "should correctly perform stale for #provider and triggered #triggered"() {
    setup:
    def historicalData = Mock(HistoricalTradeValuationData)
    def providerData = PROVIDER_DATA.get(provider).orElseThrow()
    1 * historicalData.staleValueDays(providerData.getPv().getValue(), providerData.getProvider()) >> staleDays

    def calculator = TradeBreakCalculator.forTrade(TRADE, ENTRY_BREAK_HISTORY, historicalData, PROVIDER_DATA, SCALING_DATA, NOTIONAL_DATA)

    when:
    def result = calculator.stale(provider, 1, BREAKING_CUSTOM_RESOLVER)

    then:
    result.triggered == triggered
    result.value == staleDays
    !result.calculationOnly

    where:
    provider | staleDays | triggered
    P1       | 0         | false
    P2       | 0         | false
    P3       | 0         | false
    P4       | 0         | false

    P1       | 1         | true
    P2       | 1         | true
    P3       | 1         | true
    P4       | 1         | true
  }

  def "should correctly calculate breaking days for test"() {
    setup:
    def test = Mock(IpvBreakTest)
    test.getId() >> "id"
    def breakHistory = new EntryBreakHistory("VDK", historicBreaks)
    def calculator = TradeBreakCalculator.forTrade(TRADE, breakHistory, EMPTY_HISTORY, PROVIDER_DATA, SCALING_DATA, NOTIONAL_DATA)

    expect:
    calculator.daysBreaking(test, triggered) == expectedDays

    where:
    triggered | expectedDays | historicBreaks
    true      | 2            | [new BreakTestHistory("id", 1)]
    true      | 1            | []
    false     | 0            | [new BreakTestHistory("id", 2)]
  }
}
