package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import spock.lang.Specification

class MdBatchPreliminaryRunCleanupTest extends Specification {
  ExceptionManagementCalculationRepository repository = Mock()
  DashboardEntryRepository entryRepository = Mock()
  MdBatchPreliminaryRunCleanup stepCleanup = new MdBatchPreliminaryRunCleanup(repository, entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_BATCH_DASHBOARD_ID)

    then:
    1 * repository.deletePreliminary(MD_BATCH_DASHBOARD_ID)
    1 * entryRepository.deleteMdBatchEntries(MD_BATCH_DASHBOARD_ID, DashboardStep.MD_BATCH_PRELIMINARY_RUN)
  }
}
