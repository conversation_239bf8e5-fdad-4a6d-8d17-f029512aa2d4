package com.solum.xplain.xm.workflow.steps.md

import static com.solum.xplain.core.market.value.MdkProviderBidAskType.MID_ONLY

import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuilder
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataBuildersHolder
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupDataService
import com.solum.xplain.calculation.trades.CalculationTradesFactory
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository
import com.solum.xplain.core.company.value.CompanyLegalEntityNamesView
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.value.MdkProviderBidAskType
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.xm.dashboards.entity.MdUniqueValuationSettings
import com.solum.xplain.xm.dashboards.entity.MdValuationSetting
import com.solum.xplain.xm.excmngmt.process.InstrumentDefinitionsCacheService
import com.solum.xplain.xm.excmngmt.process.MarketDataDashboardDataService
import com.solum.xplain.xm.workflow.state.InstrumentCurveConfigurations
import com.solum.xplain.xm.workflow.state.InstrumentKeyCurveConfigurations
import com.solum.xplain.xm.workflow.state.MdDashboardContext
import com.solum.xplain.xm.workflow.state.MdDashboardContextSample
import com.solum.xplain.xm.workflow.state.MdDashboardState
import io.atlassian.fugue.Either
import org.springframework.beans.MutablePropertyValues
import spock.lang.Specification

class FindInstrumentDefinitionsStepTest extends Specification implements MdDashboardContextSample {

  MarketDataDashboardDataService marketDataDashboardDataService = Mock()
  CalculationCurveGroupDataService calculationCurveGroupDataService = Mock()
  StepStateOps stepStateOps = Mock({
    getInitialState() >> new MdDashboardState()
  })
  MarketDataKeyRepository marketDataKeyRepository = Mock()

  CompanyLegalEntityRepository companyLegalEntityRepository = Mock()
  CalculationTradesFactory calculationTradesFactory = Mock()
  InstrumentDefinitionsCacheService instrumentDefinitionsCacheService = Mock()

  def "should return instruments mapped to curve configuration (classic behavior)"() {
    given:
    def context = mdDashboardContext()
    def curveConfigurations = context.curveConfigurations()
    def stateDate = context.stateDate()
    def step = new FindInstrumentDefinitionsStep(
      marketDataDashboardDataService,
      marketDataKeyRepository,
      calculationCurveGroupDataService,
      companyLegalEntityRepository,
      calculationTradesFactory,
      instrumentDefinitionsCacheService)
    stepStateOps.getContext() >> context
    Map<String, Map<String, MdkProviderBidAskType>> keyProviderPricePointMap = [
      (InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key): [
        (PROVIDER_1): MdkProviderBidAskType.BID_ASK,
        (PROVIDER_2): MdkProviderBidAskType.BID_ONLY,
      ],
      (InstrumentDefinitionBuilder.CDS_1Y.key)          : [
        (PROVIDER_1): MID_ONLY,
        (PROVIDER_2): MID_ONLY,
      ],
    ]
    marketDataKeyRepository.marketDataKeysGroupedBidAsks(STATE_DATE) >> keyProviderPricePointMap

    and: "curve group 1 has one instrument and curve group 2 has two instruments"
    Map<String, List<InstrumentDefinition>> curveGroupInstruments = [
      (CURVE_GROUP_ID_1): [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, InstrumentDefinitionBuilder.DUMMY_INSTRUMENT],
      (CURVE_GROUP_ID_2): [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, InstrumentDefinitionBuilder.CDS_1Y]
    ]
    marketDataDashboardDataService.curveGroupInstruments(curveConfigurations, stateDate) >> curveGroupInstruments

    when:
    step.runStep(stepStateOps)

    then:
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition(new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_2].toSet()))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition(new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3].toSet()))
    1 * stepStateOps.setOutcome(_) >> { MutablePropertyValues outcome ->
      List<InstrumentKeyCurveConfigurations> instrumentKeys = outcome.get(MdDashboardState.Fields.instrumentDefinitions) as List<InstrumentKeyCurveConfigurations>
      assert instrumentKeys.sort(false) { it.instrumentKey() } == [
        new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y.key, [CURVE_CONFIG_ID_2].toSet()),
        new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3].toSet()),
      ]
    }
  }

  def "should return instruments mapped to curve configuration (classic behavior with flag, but not data)"() {
    given:
    def context = mdDashboardContext()
    def curveConfigurations = context.curveConfigurations()
    def stateDate = context.stateDate()
    def step = new FindInstrumentDefinitionsStep(
      marketDataDashboardDataService,
      marketDataKeyRepository,
      calculationCurveGroupDataService,
      companyLegalEntityRepository,
      calculationTradesFactory,
      instrumentDefinitionsCacheService)
    stepStateOps.getContext() >> context
    Map<String, Map<String, MdkProviderBidAskType>> keyProviderPricePointMap = [
      (InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key): [
        (PROVIDER_1): MdkProviderBidAskType.BID_ASK,
        (PROVIDER_2): MdkProviderBidAskType.BID_ONLY,
      ],
      (InstrumentDefinitionBuilder.CDS_1Y.key)          : [
        (PROVIDER_1): MID_ONLY,
        (PROVIDER_2): MID_ONLY,
      ],
    ]
    marketDataKeyRepository.marketDataKeysGroupedBidAsks(STATE_DATE) >> keyProviderPricePointMap

    and: "curve group 1 has one instrument and curve group 2 has two instruments"
    Map<String, List<InstrumentDefinition>> curveGroupInstruments = [
      (CURVE_GROUP_ID_1): [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, InstrumentDefinitionBuilder.DUMMY_INSTRUMENT],
      (CURVE_GROUP_ID_2): [InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, InstrumentDefinitionBuilder.CDS_1Y]
    ]
    marketDataDashboardDataService.curveGroupInstruments(curveConfigurations, stateDate) >> curveGroupInstruments

    when:
    step.runStep(stepStateOps)

    then:
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition(new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_2].toSet()))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition(new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3].toSet()))
    1 * stepStateOps.setOutcome(_) >> { MutablePropertyValues outcome ->
      List<InstrumentKeyCurveConfigurations> instrumentKeys = outcome.get(MdDashboardState.Fields.instrumentDefinitions) as List<InstrumentKeyCurveConfigurations>
      assert instrumentKeys.sort(false) { it.instrumentKey() } == [
        new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y.key, [CURVE_CONFIG_ID_2].toSet()),
        new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3].toSet()),
      ]
    }
  }

  def "should return instruments mapped to curve configuration with active portfolio items"() {
    given:
    // Mdk Setups
    List<InstrumentDefinition> validInstruments = [
      InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M,
      InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS,
      InstrumentDefinitionBuilder.SWAPTION_ATM_EUR_6M_VOLS,
      InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS,
      InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD,
      InstrumentDefinitionBuilder.CDS_1Y,
    ]

    marketDataKeyRepository.marketDataKeysGroupedBidAsks(STATE_DATE) >>
    validInstruments.collectEntries {
      [it.key, [PROVIDER_1: MID_ONLY, PROVIDER_2: MID_ONLY]]
    }

    // mock the names of the legal entities
    companyLegalEntityRepository.companyLegalEntityNameViews(Set.of("legalEntity1", "legalEntity2", "legalEntity3", "legalEntity4")) >>
    [
      new CompanyLegalEntityNamesView(entityId: "legalEntity1", externalId: "extLegalEntity1", companyExternalId: "extCompId1"),
      new CompanyLegalEntityNamesView(entityId: "legalEntity2", externalId: "extLegalEntity2", companyExternalId: "extCompId1"),
      new CompanyLegalEntityNamesView(entityId: "legalEntity3", externalId: "extLegalEntity3", companyExternalId: "extCompId2"),
      new CompanyLegalEntityNamesView(entityId: "legalEntity4", externalId: "extLegalEntity4", companyExternalId: "extCompId2")
    ]

    CalculationCurveGroupDataBuilder builder1 = Mock()
    builder1.currentCurveConfigurationId() >> CURVE_CONFIG_ID_1
    CalculationCurveGroupDataBuildersHolder calculationCurveGroupDataBuildersHolder1 = Mock()
    calculationCurveGroupDataBuildersHolder1.builders() >> [builder1]
    builder1.requiredInstrumentDefinitions(_ as Set) >> {
      () -> [
        InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS,
        InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD,
        InstrumentDefinitionBuilder.CDS_1Y,]
    }

    CalculationCurveGroupDataBuilder builder2 = Mock()
    builder2.currentCurveConfigurationId() >> CURVE_CONFIG_ID_2
    CalculationCurveGroupDataBuildersHolder calculationCurveGroupDataBuildersHolder2 = Mock()
    calculationCurveGroupDataBuildersHolder2.builders() >> [builder2]
    builder2.requiredInstrumentDefinitions(_ as Set) >> {
      () -> [
        InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M,
        InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS,
        InstrumentDefinitionBuilder.SWAPTION_ATM_EUR_6M_VOLS, InstrumentDefinitionBuilder.CDS_1Y,]
    }

    calculationCurveGroupDataService.resolveCalculationData(_, _, _, _,) >>> [
      Either.right(calculationCurveGroupDataBuildersHolder1),
      Either.right(calculationCurveGroupDataBuildersHolder2)
    ]

    stepStateOps.getContext() >> mdDashboardContextWithPrefetchedEntities()
    def step = new FindInstrumentDefinitionsStep(
    marketDataDashboardDataService,
    marketDataKeyRepository,
    calculationCurveGroupDataService,
    companyLegalEntityRepository,
    calculationTradesFactory,
    instrumentDefinitionsCacheService)


    when:
    step.runStep(stepStateOps)

    then:
    //    2 curves configuration for this instrument, since we got it twice by mock
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2] as Set))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS, [CURVE_CONFIG_ID_1] as Set))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD, [CURVE_CONFIG_ID_1] as Set))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M, [CURVE_CONFIG_ID_2] as Set))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS, [CURVE_CONFIG_ID_2] as Set))
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( new InstrumentCurveConfigurations(InstrumentDefinitionBuilder.SWAPTION_ATM_EUR_6M_VOLS, [CURVE_CONFIG_ID_2] as Set))

    1 * stepStateOps.setOutcome(_) >> {
      MutablePropertyValues outcome ->
      List<InstrumentKeyCurveConfigurations> instrumentKeys = outcome.get(MdDashboardState.Fields.instrumentDefinitions) as List<InstrumentKeyCurveConfigurations>
      assert instrumentKeys.size() == 6
      // 2 curves configuration for this instrument, since we got it twice by mock
      assert instrumentKeys.contains(
      new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CDS_1Y.key, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2] as Set))
      assert instrumentKeys.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS.key, [CURVE_CONFIG_ID_1] as Set))
      assert instrumentKeys.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD.key, [CURVE_CONFIG_ID_1] as Set))
      assert instrumentKeys.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M.key, [CURVE_CONFIG_ID_2] as Set))
      assert instrumentKeys.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS.key, [CURVE_CONFIG_ID_2] as Set))
      assert instrumentKeys.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.SWAPTION_ATM_EUR_6M_VOLS.key, [CURVE_CONFIG_ID_2] as Set))
    }
  }

  def "should return relevant/non relevant instruments mapped to curve configuration with active portfolio items"() {
    given:
    def curveConfigurations = mdDashboardContextWithPrefetchedEntitiesAndCurveConfigs().curveConfigurations()
    def stateDate = mdDashboardContextWithPrefetchedEntitiesAndCurveConfigs().stateDate()
    // Mdk Setups
    List<InstrumentDefinition> validInstruments = [
      InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M,
      InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS,
      InstrumentDefinitionBuilder.SWAPTION_ATM_EUR_6M_VOLS,
      InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS,
      InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD,
      InstrumentDefinitionBuilder.CDS_1Y,
    ]

    Map<String, Map<String, MdkProviderBidAskType>> keyProviderPricePointMap = [
      (InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key): [
        (PROVIDER_1): MdkProviderBidAskType.BID_ASK,
        (PROVIDER_2): MdkProviderBidAskType.BID_ONLY,
      ],
      *: validInstruments.collectEntries {
        [it.key, [(PROVIDER_1): MID_ONLY, (PROVIDER_2): MID_ONLY]]
      }
    ]
    marketDataKeyRepository.marketDataKeysGroupedBidAsks(STATE_DATE) >> keyProviderPricePointMap

    Map<String, List<InstrumentDefinition>> curveGroupInstruments = [
      (CURVE_GROUP_ID_1) : [
        InstrumentDefinitionBuilder.DUMMY_INSTRUMENT,
        InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS,
        InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD,
        InstrumentDefinitionBuilder.CDS_1Y,
        InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS
      ],
      (CURVE_GROUP_ID_2): [
        InstrumentDefinitionBuilder.DUMMY_INSTRUMENT,
        InstrumentDefinitionBuilder.CDS_1Y,
        InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M,
        InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS,
        InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS]
    ]
    marketDataDashboardDataService.curveGroupInstruments(curveConfigurations, stateDate) >> curveGroupInstruments

    // mock the names of the legal entities
    companyLegalEntityRepository.companyLegalEntityNameViews(Set.of("legalEntity1", "legalEntity2")) >>
    [
      new CompanyLegalEntityNamesView(entityId: "legalEntity1", externalId: "extLegalEntity1", companyExternalId: "extCompId1"),
      new CompanyLegalEntityNamesView(entityId: "legalEntity2", externalId: "extLegalEntity2", companyExternalId: "extCompId1"),
    ]

    CalculationCurveGroupDataBuilder builder1 = Mock()
    builder1.currentCurveConfigurationId() >> CURVE_CONFIG_ID_1
    CalculationCurveGroupDataBuildersHolder calculationCurveGroupDataBuildersHolder1 = Mock()
    calculationCurveGroupDataBuildersHolder1.builders() >> [builder1]
    builder1.requiredInstrumentDefinitions(_ as Set) >> {
      () -> [
        InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS,
        InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD,
        InstrumentDefinitionBuilder.CDS_1Y,
        InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS]
    }

    CalculationCurveGroupDataBuilder builder2 = Mock()
    builder2.currentCurveConfigurationId() >> CURVE_CONFIG_ID_2
    CalculationCurveGroupDataBuildersHolder calculationCurveGroupDataBuildersHolder2 = Mock()
    calculationCurveGroupDataBuildersHolder2.builders() >> [builder2]
    builder2.requiredInstrumentDefinitions(_ as Set) >> {
      () -> [
        InstrumentDefinitionBuilder.CDS_1Y]
    }

    calculationCurveGroupDataService.resolveCalculationData(_, _, _, _,) >>> [
      Either.right(calculationCurveGroupDataBuildersHolder1),
      Either.right(calculationCurveGroupDataBuildersHolder2)
    ]

    stepStateOps.getContext() >> mdDashboardContextWithPrefetchedEntitiesAndCurveConfigs()

    def step = new FindInstrumentDefinitionsStep(
    marketDataDashboardDataService,
    marketDataKeyRepository,
    calculationCurveGroupDataService,
    companyLegalEntityRepository,
    calculationTradesFactory,
    instrumentDefinitionsCacheService)


    when:
    step.runStep(stepStateOps)

    then:

    //calls from relevantInstrumentDefinitions
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_1])
    })

    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS, [CURVE_CONFIG_ID_1])
    })
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD, [CURVE_CONFIG_ID_1])
    })
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS, [CURVE_CONFIG_ID_1])
    })

    //calls from nonRequiredInstrumentDefinitions
    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition( {
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M, [CURVE_CONFIG_ID_2])
    })

    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3])
    })

    1 * instrumentDefinitionsCacheService.storeInstrumentDefinition({
      InstrumentCurveConfigurations c ->
      curveConfigMatches(c, InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3])
    })


    1 * stepStateOps.setOutcome(_) >> {
      MutablePropertyValues outcome ->
      List<InstrumentKeyCurveConfigurations> instrumentDefinitions = outcome.get(MdDashboardState.Fields.instrumentDefinitions) as List<InstrumentCurveConfigurations>
      List<InstrumentKeyCurveConfigurations> nonRequiredInstrumentDefinitions = outcome.get(MdDashboardState.Fields.nonRequiredInstrumentDefinitions) as List<InstrumentCurveConfigurations>

      assert instrumentDefinitions.size() == 4
      assert containsEntry(instrumentDefinitions, InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_1])
      assert containsEntry(instrumentDefinitions, InstrumentDefinitionBuilder.SWAPTION_SKEW_USD_3M_VOLS, [CURVE_CONFIG_ID_1])
      assert containsEntry(instrumentDefinitions, InstrumentDefinitionBuilder.FX_VOL_SKEW_EUR_USD, [CURVE_CONFIG_ID_1])

      //Instruments found in relevant & all, should contain only the curve configs not found in the relevant set
      assert nonRequiredInstrumentDefinitions.size() == 3
      assert containsEntry(nonRequiredInstrumentDefinitions, InstrumentDefinitionBuilder.CDS_1Y, [CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3])

      //Instruments only found in all, therefore non relevant
      assert containsEntry(nonRequiredInstrumentDefinitions, InstrumentDefinitionBuilder.FIXED_IBOR_SWAP_EUR_6M, [CURVE_CONFIG_ID_2])
      assert containsEntry(nonRequiredInstrumentDefinitions, InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, [CURVE_CONFIG_ID_1, CURVE_CONFIG_ID_2, CURVE_CONFIG_ID_3])

      //If instrument found in both relevant & all, with the same curve configs it should not be present in non-relevant set but present in relevant
      assert instrumentDefinitions.contains(new InstrumentKeyCurveConfigurations(InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS.key, [CURVE_CONFIG_ID_1] as Set))
      assert !containsEntry(nonRequiredInstrumentDefinitions, InstrumentDefinitionBuilder.CAP_FLOOR_VOL_USD_3M_VOLS, [CURVE_CONFIG_ID_1])
    }
  }

  MdDashboardContext mdDashboardContextWithPrefetchedEntities() {
    var firstSetting = new MdValuationSetting(
    "SINGLE",
    CURVE_CONFIG_ID_1,
    null,
    "OIS",
    "LOCAL_CURRENCY",
    "USD",
    "USD",
    false,
    InstrumentPriceRequirements.bidRequirements()
    )
    var secondSetting = new MdValuationSetting(
    "SINGLE",
    CURVE_CONFIG_ID_2,
    null,
    "OIS",
    "LOCAL_CURRENCY",
    "EUR",
    "EUR",
    false,
    InstrumentPriceRequirements.bidRequirements()
    )
    new MdDashboardContext(
    MARKET_DATA_GROUP_ID,
    MARKET_DATA_GROUP_NAME,
    [],
    STATE_DATE,
    PREVIOUS_DATE,
    [
      new MdUniqueValuationSettings(firstSetting, List.of("legalEntity1", "legalEntity2")),
      new MdUniqueValuationSettings(secondSetting, List.of("legalEntity3", "legalEntity4")),
    ],
    UserBuilder.user()
    )
  }

  MdDashboardContext mdDashboardContextWithPrefetchedEntitiesAndCurveConfigs() {
    var firstSetting = new MdValuationSetting(
    "SINGLE",
    CURVE_CONFIG_ID_1,
    null,
    "OIS",
    "LOCAL_CURRENCY",
    "USD",
    "USD",
    false,
    InstrumentPriceRequirements.bidRequirements()
    )
    new MdDashboardContext(
    MARKET_DATA_GROUP_ID,
    MARKET_DATA_GROUP_NAME,
    curveConfigurationInstrumentResolvers(),
    STATE_DATE,
    PREVIOUS_DATE,
    [new MdUniqueValuationSettings(firstSetting, List.of("legalEntity1", "legalEntity2")),],
    UserBuilder.user()
    )
  }

  private static boolean containsEntry(List<InstrumentKeyCurveConfigurations> instrumentKeyCurveConfigurations, InstrumentDefinition instrumentDefinition, List<String> strings) {
    return instrumentKeyCurveConfigurations.stream().anyMatch(i -> keyCurveConfigMatches(i, instrumentDefinition, strings))
  }

  private static boolean curveConfigMatches(InstrumentCurveConfigurations config, InstrumentDefinition expectedInstrumentDefinition, List<String> expectedConfigIds) {
    config.instrumentDefinition().key == expectedInstrumentDefinition.getKey() && config.curveConfigIds().containsAll(expectedConfigIds) && config.curveConfigIds().size() == expectedConfigIds.size()
  }

  private static boolean keyCurveConfigMatches(InstrumentKeyCurveConfigurations config, InstrumentDefinition expectedInstrumentDefinition, List<String> expectedConfigIds) {
    config.instrumentKey() == expectedInstrumentDefinition.getKey() && config.curveConfigIds().containsAll(expectedConfigIds) && config.curveConfigIds().size() == expectedConfigIds.size()
  }
}
