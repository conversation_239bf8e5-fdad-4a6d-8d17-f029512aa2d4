package com.solum.xplain.xm.tasks.granularity.marketdata.asset

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.instrument.InstrumentType
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.tasks.TaskMapper
import com.solum.xplain.xm.tasks.entity.TaskExecution
import org.mapstruct.factory.Mappers
import spock.lang.Specification

class GranularityByAssetClassTest extends Specification {
  def taskMapper = Mappers.getMapper(TaskMapper.class)
  static List<InstrumentType> INSTRUMENTS = Arrays.asList(CoreInstrumentType.values())

  def "should split by all asset classes"() {
    setup:
    def rule = new GranularityByAssetClass(taskMapper, INSTRUMENTS)

    when:
    def executions = rule.split(new TaskExecution(assetFilter: new AssetFilter()))

    then:
    executions.size() == CoreAssetClass.values().size()
    verifyAll {
      for (int i = 0; i < CoreAssetClass.values().size(); i++) {
        executions.stream().anyMatch { e -> e.assetFilter.assetClasses == [CoreAssetClass.values()[i]] }
      }
    }
  }

  def "should split by asset filter asset classes"() {
    setup:
    def rule = new GranularityByAssetClass(taskMapper, INSTRUMENTS)

    when:
    def executions = rule.split(new TaskExecution(assetFilter: new AssetFilter(assetClasses: [CoreAssetClass.CDS, CoreAssetClass.CREDIT_INDEX_TRANCHE])))

    then:
    executions.size() == 2
    executions[0].assetFilter.assetClasses == [CoreAssetClass.CDS]
    executions[1].assetFilter.assetClasses == [CoreAssetClass.CREDIT_INDEX_TRANCHE]
  }
}
