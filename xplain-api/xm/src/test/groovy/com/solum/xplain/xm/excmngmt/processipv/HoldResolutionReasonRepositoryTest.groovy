package com.solum.xplain.xm.excmngmt.processipv

import com.solum.xplain.xm.excmngmt.processipv.resolution.data.TradeResultResolutionSubTypeReference
import com.solum.xplain.xm.excmngmt.processipv.resolution.holdresolution.HoldResolutionReasonRepository
import spock.lang.Specification

class HoldResolutionReasonRepositoryTest extends Specification {
  def onHoldResolutionTypeRepository = new HoldResolutionReasonRepository()

  def "should return all on hold resolution types"() {
    when:
    def onHoldResolutionTypes = onHoldResolutionTypeRepository.holdResolutionReasons().toList()

    then:
    onHoldResolutionTypes*.reasonId() == ["QUERY_VENDOR", "SOURCE_VENDOR_PRICE", "RERUN_UPDATED", "RELOAD", "OTHER"]
  }

  def "should return resolution reason from subtype reference and round trip"() {
    when:
    def reference = TradeResultResolutionSubTypeReference.of("QUERY_VENDOR")
    def resolutionReason = onHoldResolutionTypeRepository.fromResolutionSubTypeReference(reference)

    then:
    resolutionReason.isPresent()
    resolutionReason.get().reasonId() == "QUERY_VENDOR"
    resolutionReason.get().toResolutionSubTypeReference() == reference
  }
}
