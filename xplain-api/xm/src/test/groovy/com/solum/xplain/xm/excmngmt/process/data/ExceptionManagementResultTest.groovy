package com.solum.xplain.xm.excmngmt.process.data

import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.APPROVED
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.BATCH_PROCESSING
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_OVERLAY
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_PRELIMINARY
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_APPROVED
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_BATCH_APPROVED

import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup
import java.time.LocalDate
import spock.lang.Specification

class ExceptionManagementResultTest extends Specification {

  def "should correctly create preliminary result"() {
    setup:

    when:
    def result = ExceptionManagementResult.preliminary(
      "id",
      LocalDate.of(2018, 1, 2),
      MdExceptionManagementSetup.newOf(
      EntityReference.newOf("marketDataGroupId", "name"),
      [],
      LocalDate.of(2018, 1, 1),
      false,
      ),
      TrsMdExceptionManagementSetup.newOf(
      EntityReference.newOf("marketDataGroupId2", "name2"),
      [],
      LocalDate.of(2018, 1, 1),
      )
      )

    then:
    result.status == IN_PRELIMINARY
    result.curveDate == LocalDate.of(2018, 1, 2)
    result.marketDataGroupId == "marketDataGroupId"
    result.marketDataGroupName == "name"
    result.trsMarketDataGroupId == "marketDataGroupId2"
    result.trsMarketDataGroupName == "name2"
  }

  def "should correctly approve result"() {
    setup:
    def res = new ExceptionManagementResult(status: IN_PRELIMINARY)
    def audit = Mock(AuditContext)
    def auditUser = Mock(AuditUser)
    audit.now() >> LocalDate.of(2010, 1, 1).atStartOfDay()
    audit.user() >> auditUser

    when:
    res.withApproved()

    then:
    res.status == PRELIMINARY_APPROVED

    when:
    res.setStatus(IN_OVERLAY)
    res.withApproved()

    then:
    res.status == APPROVED

    when:
    res.setStatus(BATCH_PROCESSING)
    res.withApproved()

    then:
    res.status == PRELIMINARY_BATCH_APPROVED
  }
}
