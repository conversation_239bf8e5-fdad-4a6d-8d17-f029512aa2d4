package com.solum.xplain.xm.excmngmt.stat.value

import static com.solum.xplain.core.error.Error.CALCULATION_ERROR

import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.xm.excmngmt.market.value.ResolvedPreliminaryInstrument
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import java.time.LocalDate
import spock.lang.Specification

class DiffCalculatorTest extends Specification {

  def "should correctly construct DiffCalculator for primary"() {
    setup:
    def d1 = LocalDate.of(2020, 1, 1)
    def d2 = LocalDate.of(2020, 1, 2)
    def data = [
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d1, providerData: ProviderData.of("P1", 1.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d1, providerData: ProviderData.of("S1", 2.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d2, providerData: ProviderData.of("P1", 3.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d2, providerData: ProviderData.of("S1", 30.0, null, ValueBidAskType.BID))
    ]
    def providers = ["KEY1": new MarketDataProviders(primary: "P1", secondary: "S1"),
      "KEY2": new MarketDataProviders(primary: "P2", secondary: "S2")]

    when:
    def res = DiffCalculator.of(data, providers, true)

    then:
    res.availableDates == [d2]
    res.instrumentDiffs == ["KEY1": [(d2): 2d]]
  }

  def "should correctly construct DiffCalculator for secondary"() {
    setup:
    def d1 = LocalDate.of(2020, 1, 1)
    def d2 = LocalDate.of(2020, 1, 2)
    def data = [
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d1, providerData: ProviderData.of("P1", 1.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d1, providerData: ProviderData.of("S1", 2.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d2, providerData: ProviderData.of("P1", 3.0, null, ValueBidAskType.BID)),
      new ResolvedPreliminaryInstrument(instrumentKey: "KEY1", date: d2, providerData: ProviderData.of("S1", 30.0, null, ValueBidAskType.BID))
    ]
    def providers = ["KEY1": new MarketDataProviders(primary: "P1", secondary: "S1"),
      "KEY2": new MarketDataProviders(primary: "P2", secondary: "S2")]

    when:
    def res = DiffCalculator.of(data, providers, false)

    then:
    res.availableDates == [d2]
    res.instrumentDiffs == ["KEY1": [(d2): 28d]]
  }

  def "should correctly validate DiffCalculator"() {
    setup:
    def d1 = LocalDate.of(2020, 1, 1)
    def d2 = LocalDate.of(2020, 1, 2)
    def diff = new DiffCalculator([d1, d2],
    ["KEY1": [(d1): 1d, (d2): 1d],
      "KEY2": [(d1): 1d],
      "KEY3": null])

    when:
    def res = diff.validatedData()

    then:
    res["KEY1"].isRight()
    res["KEY2"].isLeft()
    res["KEY2"].left().get()[0] == CALCULATION_ERROR.entity("Data not available for KEY2 for 2020-01-02")
    res["KEY3"].isLeft()
    res["KEY3"].left().get()[0] == CALCULATION_ERROR.entity("Data not available for KEY3 for 2020-01-01")
  }

  def "should correctly validate DiffCalculator with fallback"() {
    setup:
    def d1 = LocalDate.of(2020, 1, 1)
    def d2 = LocalDate.of(2020, 1, 2)
    def diff = new DiffCalculator([d1, d2],
    ["KEY1": [(d1): 1d, (d2): 1d],
      "KEY2": [(d1): 1d],
      "KEY3": null])

    def fallback = new DiffCalculator([d1, d2],
    ["KEY2": [(d1): 1d, (d2): 1d]])

    when:
    def res = diff.validatedData(fallback)

    then:
    res["KEY1"].isRight()
    res["KEY2"].isRight()
    res["KEY2"].getOrNull().get(d2) == 1d
    res["KEY3"].isLeft()
    res["KEY3"].left().get()[0] == CALCULATION_ERROR.entity("Data not available for KEY3 for 2020-01-01")
  }
}
