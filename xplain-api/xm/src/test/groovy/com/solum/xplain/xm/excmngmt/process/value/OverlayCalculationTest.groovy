package com.solum.xplain.xm.excmngmt.process.value

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID

import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.market.value.ResolvedOverlayInstrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.instrument.CurveConfigurationRequirementsResolver
import com.solum.xplain.xm.excmngmt.process.instrument.CurveGroupInstrumentFilter
import com.solum.xplain.xm.excmngmt.process.value.InstrumentMarketDataBreakCalculatorOverlay.InstrumentMarketDataBreakCalculatorSupplier
import java.time.LocalDate
import spock.lang.Specification

class OverlayCalculationTest extends Specification {

  private static final BigDecimal P1_VALUE_ASK = 0
  private static final BigDecimal P1_VALUE_BID = 100
  private static final BigDecimal P1_PREVIOUS_VALUE_ASK = 10
  private static final BigDecimal P1_PREVIOUS_VALUE_BID = 110
  private static final BigDecimal P2_VALUE_ASK = 1
  private static final BigDecimal P2_VALUE_BID = 101
  private static final BigDecimal P2_PREVIOUS_VALUE_ASK = 11
  private static final BigDecimal P2_PREVIOUS_VALUE_BID = 111
  private static final BigDecimal PREVIOUS_OVERLAY_VALUE = 33
  public static final LocalDate VALUATION_DATE = LocalDate.of(2018, 1, 2)

  def "should correctly construct overlay calculation"() {
    setup:
    def btCalcs = Mock(BreakTestCalculationsOverlay)
    def overlayZScoreCalculation = Mock(OverlayZScoreCalculation)

    when:
    def calc = OverlayCalculation.ofResult(
      LocalDate.now(),
      null,
      null,
      btCalcs,
      overlayZScoreCalculation
      )

    then:
    calc.valuationDate == LocalDate.now()
    calc.breakTestCalculations == btCalcs
    calc.overlayZScoreCalculation == overlayZScoreCalculation
  }

  def "should correctly perform overlay calculation"() {
    setup:
    def curveConfResolver = Mock(CurveConfigurationInstrumentResolver)
    curveConfResolver.resolveProvider(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT) >> Optional.of(new MarketDataProviders(primary: "P", secondary: "S"))
    curveConfResolver.getCurveGroupId() >> "cgId"
    curveConfResolver.getId() >> "ccId"

    def primaryAsk = new ProviderData(provider: "P", value: P1_VALUE_ASK, previousValue: P1_PREVIOUS_VALUE_ASK, bidAskType: ASK)
    def secondaryAsk = new ProviderData(provider: "S", value: P2_VALUE_ASK, previousValue: P2_PREVIOUS_VALUE_ASK, bidAskType: ASK)
    def primaryBid = new ProviderData(provider: "P", value: P1_VALUE_BID, previousValue: P1_PREVIOUS_VALUE_BID, bidAskType: BID)
    def secondaryBid = new ProviderData(provider: "S", value: P2_VALUE_BID, previousValue: P2_PREVIOUS_VALUE_BID, bidAskType: BID)

    def primaryAskResolved = primaryAsk.copyWithNewValue(7.0)
    def secondaryAskResolved = secondaryAsk.copyWithNewValue(8.0)

    def btCalc = Mock(InstrumentMarketDataBreakCalculatorOverlay)
    def btcSupplier = Mock(InstrumentMarketDataBreakCalculatorSupplier)
    btcSupplier.supply(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, _, null, primaryAskResolved, secondaryAskResolved) >> btCalc
    btcSupplier.supply(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, _, null, primaryAsk, secondaryAsk) >> btCalc
    btcSupplier.supply(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT, _, null, primaryBid, secondaryBid) >> btCalc
    def instrBreak = Mock(InstrumentResultBreak)
    def btCalcs = Mock(BreakTestCalculationsOverlay)
    btCalcs.processCalc(btCalc) >> [instrBreak]

    def instrumentFilter = Mock(CurveGroupInstrumentFilter)
    instrumentFilter.hasInstrument("cgId", InstrumentDefinitionBuilder.DUMMY_INSTRUMENT.key) >> true

    def result
    def instrumentRequirementResolver = CurveConfigurationRequirementsResolver.newOf(MdExceptionManagementSetup.newOf(DashboardBuilder.MARKET_DATA_GROUP, [curveConfResolver], null, false), instrumentFilter)

    when:
    result = new OverlayCalculation(
      VALUATION_DATE,
      [(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT): [
          preliminaryResult(primaryAsk, 7.0),
          preliminaryResult(secondaryAsk, 8.0),
          preliminaryResult(primaryBid),
          preliminaryResult(secondaryBid)
        ]],
      [(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT): [
          new ResolvedOverlayInstrument(curveConfigurationId: "ccId", providerData: ProviderData.of("BBG", PREVIOUS_OVERLAY_VALUE, null, BID))
        ]],
      btCalcs,
      Mock(OverlayZScoreCalculation),
      btcSupplier
      ).perform(instrumentRequirementResolver)


    then:
    result.size() == 2

    result[0].valuationDate == VALUATION_DATE
    result[0].primaryProviderData == primaryAskResolved
    result[0].secondaryProviderData == secondaryAskResolved
    result[0].allProvidersData == [primaryAskResolved, primaryBid, secondaryAskResolved, secondaryBid]
    result[0].previousOverlayValue == P1_PREVIOUS_VALUE_ASK
    result[0].breakTests.size() == 1
    result[0].breakTests[0] == instrBreak
    result[0].breakTests[0] == instrBreak
    result[0].preliminaryBreaks.size() == 1
    result[0].preliminaryBreaks[0].breakTestName == "TEST1"

    result[1].valuationDate == VALUATION_DATE
    result[1].primaryProviderData == primaryBid
    result[1].secondaryProviderData == secondaryBid
    result[1].allProvidersData == [primaryAskResolved, primaryBid, secondaryAskResolved, secondaryBid]
    result[1].previousOverlayValue == PREVIOUS_OVERLAY_VALUE
    result[1].breakTests.size() == 1
    result[1].breakTests[0] == instrBreak
    result[1].preliminaryBreaks.size() == 1
    result[1].preliminaryBreaks[0].breakTestName == "TEST1"

    when:
    result = new OverlayCalculation(
      VALUATION_DATE,
      [(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT): [
          preliminaryResult(primaryAsk),
          preliminaryResult(secondaryAsk),
          preliminaryResult(primaryBid),
          preliminaryResult(secondaryBid)
        ]],
      [(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT): [
          new ResolvedOverlayInstrument(curveConfigurationId: "ccId", providerData: ProviderData.of("BBG", PREVIOUS_OVERLAY_VALUE, null, ASK))
        ]],
      btCalcs,
      Mock(OverlayZScoreCalculation),
      btcSupplier
      ).perform(instrumentRequirementResolver)

    then:
    result.size() == 2

    result[0].primaryProviderData == primaryAsk
    result[0].secondaryProviderData == secondaryAsk
    result[0].previousOverlayValue == PREVIOUS_OVERLAY_VALUE
    result[0].allProvidersData == [primaryAsk, primaryBid, secondaryAsk, secondaryBid]
    result[0].breakTests.size() == 1
    result[0].breakTests[0] == instrBreak
    result[0].preliminaryBreaks.size() == 1
    result[0].preliminaryBreaks[0].breakTestName == "TEST1"

    result[1].primaryProviderData == primaryBid
    result[1].secondaryProviderData == secondaryBid
    result[1].allProvidersData == [primaryAsk, primaryBid, secondaryAsk, secondaryBid]
    result[1].previousOverlayValue == P1_PREVIOUS_VALUE_BID
    result[1].breakTests.size() == 1
    result[1].breakTests[0] == instrBreak
    result[1].preliminaryBreaks.size() == 1
    result[1].preliminaryBreaks[0].breakTestName == "TEST1"
  }

  static InstrumentResultPreliminary preliminaryResult(ProviderData data, BigDecimal overrideValue = null) {
    return new InstrumentResultPreliminary(
      providerData: data,
      breakTests: [
        new InstrumentResultBreak(breakTestName: "TEST1", providerValue: new EntryResultBreakByProvider(triggered: true)),
        new InstrumentResultBreak(breakTestName: "TEST2", providerValue: new EntryResultBreakByProvider(triggered: false))
      ],
      resolution: new InstrumentResultResolution(value: overrideValue),
      )
  }
}
