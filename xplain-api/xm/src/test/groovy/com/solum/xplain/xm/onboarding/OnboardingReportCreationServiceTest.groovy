package com.solum.xplain.xm.onboarding

import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_SETTINGS_MIX_PROVIDERS
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.NOT_REQUIRED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.IpvSettingsRuleService
import com.solum.xplain.core.company.mapper.IpvRuleFacts
import com.solum.xplain.core.company.value.IpvDataGroupVo
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.trade.OnboardingDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.rules.RulesService
import com.solum.xplain.core.rules.jeasy.SafeRules
import com.solum.xplain.data.valuation.ipv.IpvDataRepository
import com.solum.xplain.data.valuation.ipv.value.IpvDataProviderValueView
import com.solum.xplain.xm.dashboards.entity.filter.PortfoliosFilter
import com.solum.xplain.xm.dashboards.resolver.PortfoliosFilterBuilder
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.onboarding.breaks.ValuationConformityCalculationInvocation
import com.solum.xplain.xm.onboarding.breaks.VendorConformityBreaksService
import com.solum.xplain.xm.onboarding.entity.OnboardingReport
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import com.solum.xplain.xm.onboarding.form.CreateOnboardingReportForm
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettingsResolver
import io.atlassian.fugue.Either
import jakarta.inject.Provider
import java.time.LocalDate
import org.bson.types.ObjectId
import spock.lang.Specification

class OnboardingReportCreationServiceTest extends Specification {

  private static def TRADE_DATE = LocalDate.of(2024, 1, 1)
  private static def ONBOARDING_DATE = LocalDate.of(2024, 2, 1)
  private static def TRADE_KEY = "KEY"

  def portfolioItemRepository = Mock(PortfolioItemRepository)
  def valuationConformityCalculationInvocation = Mock(ValuationConformityCalculationInvocation)
  def onboardingReportRepository = Mock(OnboardingReportRepository)
  def portfoliosFilterBuilder = Mock(PortfoliosFilterBuilder)
  def resultMapper = Mock(IpvExceptionManagementResultMapper)
  def vendorConformityBreaksService = Mock(VendorConformityBreaksService)
  def ipvDataRepository = Mock( Provider<IpvDataRepository>)
  def auditEntryService = Mock(AuditEntryService)
  def ipvSettingsRuleService = Mock(IpvSettingsRuleService)
  def ruleService = Mock(RulesService)

  IpvDataRepository mockIpvDataRepository = Mock()

  def service = new OnboardingReportCreationService(
  portfolioItemRepository,
  valuationConformityCalculationInvocation,
  onboardingReportRepository,
  portfoliosFilterBuilder,
  resultMapper,
  vendorConformityBreaksService,
  ipvDataRepository,
  auditEntryService,
  ipvSettingsRuleService,
  ruleService
  )

  def "should correctly create report with both valuations and vendor checks"() {
    setup:
    def reportId = ObjectId.get()
    def stateDate = BitemporalDate.newOfNow()
    def portfolioId = ObjectId.get().toHexString()
    def form = new CreateOnboardingReportForm([portfolioId], true, false, true)

    def portfolioFilter = Mock(PortfoliosFilter)
    1 * portfoliosFilterBuilder.build([portfolioId]) >> portfolioFilter

    def trade = new Trade(
    portfolioId: portfolioId,
    productType: IRS,
    tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: TRADE_DATE))
    )
    def itemXplainConf = new PortfolioItem(onboardingDetails: new OnboardingDetails(xplainCostCheck: true))
    1 * resultMapper.fromPortfolioItem(itemXplainConf) >> trade
    def itemMarketConf = new PortfolioItem(onboardingDetails: new OnboardingDetails(marketConfCheck: true))
    1 * resultMapper.fromPortfolioItem(itemMarketConf) >> trade
    def itemVendorConf = new PortfolioItem(onboardingDetails: new OnboardingDetails(vendorCheck: true))
    1 * resultMapper.fromPortfolioItem(itemVendorConf) >> trade

    1 * portfolioItemRepository.conformityRequiredTrades(portfolioId, stateDate, true, false, true) >> [itemXplainConf, itemMarketConf, itemVendorConf]

    def expectedReport = new OnboardingReport(
    portfoliosFilter: portfolioFilter,
    stateDate: stateDate.getActualDate(),
    recordDate: stateDate.getRecordDate(),
    xplainConformity: true,
    marketConformity: false,
    vendorConformity: true,
    tradesCount: 3,
    )
    1 * onboardingReportRepository.saveReport(expectedReport) >> {
      OnboardingReport report -> report.id = reportId
    }

    1 * onboardingReportRepository.allReportItemsProcessed(reportId) >> false

    def dataGroupRules = Mock(SafeRules)
    def providersRules = Mock(SafeRules)
    1 * ipvSettingsRuleService.getAllIpvDataGroupRules(stateDate) >> dataGroupRules
    1 * ipvSettingsRuleService.getAllIpvProvidersRules(stateDate) >> providersRules
    3 * ruleService.execute(dataGroupRules, _ as IpvRuleFacts, IpvDataGroupVo) >> Optional.empty()

    0 * ipvDataRepository.getValueAtDate

    def expectedXplainItem = new OnboardingReportItem(reportId: reportId, xplainCheckStatus: REQUESTED, trade: trade)
    def expectedMarketItem = new OnboardingReportItem(reportId: reportId, marketCheckStatus: NOT_REQUIRED, trade: trade)
    def expectedVendorItem = new OnboardingReportItem(reportId: reportId, vendorCheckStatus: REQUESTED, trade: trade)

    1 * vendorConformityBreaksService.processBreaks([expectedXplainItem, expectedMarketItem, expectedVendorItem], _ as TradeCompanySettingsResolver, stateDate) >> []
    1 * valuationConformityCalculationInvocation.invokeValuations([expectedXplainItem, expectedMarketItem, expectedVendorItem], _) >> []

    when:
    def result = service.createReport(form, stateDate)

    then:
    result.isRight()
    result.getOrNull().id == reportId.toHexString()
    1 * onboardingReportRepository.saveReportItems([expectedXplainItem, expectedMarketItem, expectedVendorItem])
    0 * onboardingReportRepository.updateReportCompleted(_)
  }

  def "should correctly create report with ONLY vendor checks"() {
    setup:
    def reportId = ObjectId.get()
    def stateDate = BitemporalDate.newOfNow()
    def portfolioId = PORTFOLIO_SETTINGS_MIX_PROVIDERS.view.id
    def form = new CreateOnboardingReportForm([portfolioId], true, false, true)
    def ipvDataGroupId = PORTFOLIO_SETTINGS_MIX_PROVIDERS.settings.ipvSettings.products.get(IRS).ipvDataGroupView.entityId
    def ipvDataGroupName = PORTFOLIO_SETTINGS_MIX_PROVIDERS.settings.ipvSettings.products.get(IRS).ipvDataGroupView.name
    def primaryProvider = PORTFOLIO_SETTINGS_MIX_PROVIDERS.settings.ipvSettings.products.get(IRS).primary

    def portfolioFilter = Mock(PortfoliosFilter)
    1 * portfoliosFilterBuilder.build([portfolioId]) >> portfolioFilter


    def trade = new Trade(
    portfolioId: portfolioId,
    key: TRADE_KEY,
    productType: IRS,
    tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: TRADE_DATE))
    )
    def itemVendorConf = new PortfolioItem(onboardingDetails: new OnboardingDetails(vendorCheck: true, vendorOnboardingDate: ONBOARDING_DATE))
    1 * resultMapper.fromPortfolioItem(itemVendorConf) >> trade
    1 * portfolioItemRepository.conformityRequiredTrades(portfolioId, stateDate, true, false, true) >> [itemVendorConf]

    def expectedReport = new OnboardingReport(
    portfoliosFilter: portfolioFilter,
    stateDate: stateDate.getActualDate(),
    recordDate: stateDate.getRecordDate(),
    xplainConformity: true,
    marketConformity: false,
    vendorConformity: true,
    tradesCount: 1,
    )
    1 * onboardingReportRepository.saveReport(expectedReport) >> {
      OnboardingReport report -> report.id = reportId
    }

    1 * onboardingReportRepository.allReportItemsProcessed(reportId) >> true

    def dataGroupRules = Mock(SafeRules)
    def providersRules = Mock(SafeRules)
    1 * ipvSettingsRuleService.getAllIpvDataGroupRules(stateDate) >> dataGroupRules
    1 * ipvSettingsRuleService.getAllIpvProvidersRules(stateDate) >> providersRules
    2 * ruleService.execute(dataGroupRules, _ as IpvRuleFacts, IpvDataGroupVo) >> Optional.of(new IpvDataGroupVo(ipvDataGroupId, ipvDataGroupName))
    2 * ruleService.execute(providersRules, _ as IpvRuleFacts, ProvidersVo) >> Optional.of(new ProvidersVo(primaryProvider, null, null, null))

    2 * ipvDataRepository.get() >> mockIpvDataRepository
    1 * mockIpvDataRepository.getValueAtDate(ipvDataGroupId, TRADE_KEY, NAV_PROVIDER_CODE, TRADE_DATE) >> Either.right(
    new IpvDataProviderValueView(value: BigDecimal.ZERO)
    )
    1 * mockIpvDataRepository.getValueAtDate(ipvDataGroupId, TRADE_KEY, NAV_PROVIDER_CODE, ONBOARDING_DATE) >> Either.right(
    new IpvDataProviderValueView(value: BigDecimal.ONE)
    )

    def expectedVendorItem = new OnboardingReportItem(
    reportId: reportId,
    vendorCheckStatus: REQUESTED,
    trade: trade,
    navOnTradeDate: BigDecimal.ZERO,
    navOnVendorOnboardingDate: BigDecimal.ONE,
    )
    1 * valuationConformityCalculationInvocation.invokeValuations([expectedVendorItem], _) >> []
    1 * vendorConformityBreaksService.processBreaks([expectedVendorItem], _ as TradeCompanySettingsResolver, stateDate) >> []

    when:
    def result = service.createReport(form, stateDate)

    then:
    result.isRight()
    result.getOrNull().id == reportId.toHexString()

    1 * onboardingReportRepository.saveReportItems([expectedVendorItem])
    1 * onboardingReportRepository.updateReportCompleted(reportId)
  }

  def "should return error when no trades to process"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def portfolioId = ObjectId.get().toHexString()
    def form = new CreateOnboardingReportForm([portfolioId], true, false, true)

    def portfolioFilter = Mock(PortfoliosFilter)
    1 * portfoliosFilterBuilder.build([portfolioId]) >> portfolioFilter

    1 * portfolioItemRepository.conformityRequiredTrades(portfolioId, stateDate, true, false, true) >> []
    when:
    def result = service.createReport(form, stateDate)

    then:
    result.isLeft()
    def error = (ErrorItem) result.left().get()
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Selected portfolios have no trades to run onboarding report for."

    and:
    0 * onboardingReportRepository.saveReport(_)
    0 * valuationConformityCalculationInvocation.invokeValuations(_, _)
    0 * vendorConformityBreaksService._
    0 * onboardingReportRepository.saveReportItems(_)
    0 * onboardingReportRepository.updateReportCompleted(_)
  }
}
