package com.solum.xplain.xm.dashboards.events


import com.solum.xplain.xm.dashboardsteps.DashboardStepCleanup
import spock.lang.Specification

class DashboardDeletedEventListenerTest extends Specification {
  DashboardStepCleanup cleanup = Mock()

  DashboardDeletedEventListener eventListener = new DashboardDeletedEventListener([cleanup])

  def "should execute all step cleanups on dashboard deletion"() {
    when:
    eventListener.onDashboardDeleted(DashboardDeletedEvent.newOf("ID"))

    then:
    1 * cleanup.execute("ID")
  }
}
