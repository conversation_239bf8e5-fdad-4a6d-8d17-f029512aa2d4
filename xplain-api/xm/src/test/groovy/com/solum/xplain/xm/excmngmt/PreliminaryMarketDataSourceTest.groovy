package com.solum.xplain.xm.excmngmt

import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.PRELIMINARY_PRIMARY
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationMarketValueResolver
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.value.MarketDataKeyView
import com.solum.xplain.core.market.value.MarketDataProviderTickerView
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.xm.excmngmt.market.CleanMarketDataRepository
import com.solum.xplain.xm.excmngmt.market.PreliminaryMarketDataSource
import com.solum.xplain.xm.excmngmt.market.value.ResolvedInstrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.view.PreliminaryMarketDataView
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class PreliminaryMarketDataSourceTest extends Specification {
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 01))
  private static def CURVE_DATE = LocalDate.of(2022, 01, 02)

  CurveConfigurationMarketValueResolver resolver = Mock()
  MarketDataKeyRepository keyRepository = Mock()
  CleanMarketDataRepository cleanMdRepository = Mock()

  PreliminaryMarketDataSource preliminaryMarketDataSource = new PreliminaryMarketDataSource(
  cleanMdRepository,
  resolver,
  keyRepository
  )

  def "should resolve calculation market data with preliminary values"() {
    setup:
    def mdParams = Mock(MarketDataExtractionParams)
    mdParams.getStateDate() >> STATE_DATE
    mdParams.getCurveDate() >> CURVE_DATE
    mdParams.getMarketDataGroupId() >> "md"
    mdParams.getConfigurationId() >> "config"
    1 * cleanMdRepository.cleanPreliminaryData("md", CURVE_DATE) >>
      [
        new ResolvedInstrument(
        instrumentKey: "key",
        providerData: ProviderData.of("BBG", BigDecimal.ONE, BigDecimal.TEN, BID),
        resolution: new InstrumentResultResolution(value: BigDecimal.valueOf(100d))),
        new ResolvedInstrument(
        instrumentKey: "key",
        providerData: ProviderData.of("BBG", BigDecimal.TEN, BigDecimal.ZERO, ASK),
        )
      ]


    1 * keyRepository.marketDataKeyViews(STATE_DATE) >> [
      new MarketDataKeyView(key: "key",
      providerTickers: [new MarketDataProviderTickerView(code: "BBG", ticker: "ticker", factor: 100)])
    ]

    1 * resolver.curveConfigurationMarketData(
      mdParams,
      ["key": new PreliminaryMarketDataView(
        key: "key",
        values: [
          new MarketDataValueFlatView(provider: "BBG", ticker: "ticker", value: BigDecimal.valueOf(100d), bidAsk: BID),
          new MarketDataValueFlatView(provider: "BBG", ticker: "ticker", value: BigDecimal.TEN, bidAsk: ASK),
        ])]) >> Either.right(CalculationMarketData.builder()
      .values([new CalculationMarketValueView(key: "key", value: BigDecimal.ONE)])
      .build())

    when:
    def result = preliminaryMarketDataSource.provide(mdParams)

    then:
    result.isRight()
    result.getOrNull().values.size() == 1
    result.getOrNull().values[0].value == BigDecimal.ONE
  }

  def "should resolve values for given curve group"() {
    setup:
    def priceTypeResolver = Mock(InstrumentPriceTypeResolver)
    def mdParams = Mock(MarketDataExtractionParams)
    mdParams.getStateDate() >> STATE_DATE
    mdParams.getCurveDate() >> CURVE_DATE
    mdParams.getMarketDataGroupId() >> "md"
    mdParams.getConfigurationId() >> "config"
    mdParams.getPriceTypeResolver() >> priceTypeResolver
    1 * cleanMdRepository.cleanPreliminaryData("md", CURVE_DATE) >>
      [
        new ResolvedInstrument(instrumentKey: "key", providerData: ProviderData.of("BBG", BigDecimal.ONE, BigDecimal.TEN, BID))
      ]
    keyRepository.marketDataKeyViews(STATE_DATE) >> [
      new MarketDataKeyView(key: "key",
      providerTickers: [new MarketDataProviderTickerView(code: "BBG", ticker: "ticker")])
    ]

    resolver.resolveMarketData(
      STATE_DATE,
      "config",
      priceTypeResolver,
      ["key": new PreliminaryMarketDataView(
        key: "key",
        values: [
          new MarketDataValueFlatView(provider: "BBG", ticker: "ticker", value: BigDecimal.ONE, bidAsk: BID)
        ])],
      PRELIMINARY_PRIMARY.toFullView) >> [new CalculationMarketValueView(key: "key", value: BigDecimal.TEN)]
    when:
    def result = preliminaryMarketDataSource.provideMarketData(mdParams,
      PRELIMINARY_PRIMARY.toFullView
      )

    then:
    result != null
  }
}
