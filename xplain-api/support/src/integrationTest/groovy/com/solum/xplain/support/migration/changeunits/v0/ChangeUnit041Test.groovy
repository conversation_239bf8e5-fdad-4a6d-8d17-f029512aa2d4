package com.solum.xplain.support.migration.changeunits.v0


import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit041Test extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(ChangeUnit041.CALCULATION_RESULT_COLLECTION)
  }

  def "should create index"() {
    setup:
    def changeUnit = new ChangeUnit041(mongoTemplate)

    when:
    changeUnit.beforeExecution()

    then:
    mongoTemplate.getCollection(ChangeUnit041.CALCULATION_RESULT_COLLECTION).listIndexes().find {
      it.get("name") == "${ChangeUnit041.CALCULATION_TYPE}_${ChangeUnit041.CREATED_AT}_${ChangeUnit041.CALCULATION_RESULT_STATUS}"
    } != null
  }
}
