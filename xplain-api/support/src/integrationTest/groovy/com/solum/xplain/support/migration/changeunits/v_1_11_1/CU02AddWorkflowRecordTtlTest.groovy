package com.solum.xplain.support.migration.changeunits.v_1_11_1

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.support.retention.value.RetentionProperties
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CU02AddWorkflowRecordTtlTest extends IntegrationSpecification {

  @Resource
  MongoTemplate mongoTemplate
  @Resource
  RetentionProperties retentionProperties

  def setup() {
    mongoTemplate.dropCollection("wfStepInstance")
    mongoTemplate.dropCollection("wfProcessExecution")
  }

  def cleanup() {
    mongoTemplate.dropCollection("wfStepInstance")
    mongoTemplate.dropCollection("wfProcessExecution")
  }

  def "should create indices"() {
    setup:
    def twoWeeksTtl = 60 * 60 * 24 * 14
    def changeUnit = new CU02AddWorkflowRecordTtl(mongoTemplate, retentionProperties)

    when:
    changeUnit.beforeExecution()

    then:
    def wfStepInstanceIndex = mongoTemplate.indexOps("wfStepInstance").getIndexInfo()
    wfStepInstanceIndex.size() == 2
    wfStepInstanceIndex[1].getName() == "TTL_modifiedAt"
    wfStepInstanceIndex[1].getExpireAfter().get().getSeconds() == twoWeeksTtl

    def wfProcessExecutionIndex = mongoTemplate.indexOps("wfProcessExecution").getIndexInfo()
    wfProcessExecutionIndex.size() == 2
    wfProcessExecutionIndex[1].getName() == "TTL_modifiedAt"
    wfProcessExecutionIndex[1].getExpireAfter().get().getSeconds() == twoWeeksTtl
  }

  def "should run empty rollback"() {
    setup:
    def changeUnit = new CU02AddWorkflowRecordTtl(mongoTemplate, retentionProperties)

    expect:
    changeUnit.rollback()
  }
}
