package com.solum.xplain.support.migration.changeunits.v_2_03_0

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CU02AddTradesCountByPortfolioTest extends IntegrationSpecification {

  @Resource
  MongoOperations mongoOperations

  def setup() {
    mongoOperations.dropCollection("wfProcessExecution")
  }

  def "should execute on empty db"() {
    when:
    def changeUnit = new CU02AddTradesCountByPortfolio(mongoOperations)
    changeUnit.beforeExecution()

    then:
    mongoOperations.find(new Query(), Document.class, "wfProcessExecution").isEmpty()
  }

  def "should execute" () {
    given:
    // Load JSON document from resources
    def jsonStream = getClass().getResourceAsStream("solum-xplain.wfProcessExecution.json")
    def json = new groovy.json.JsonSlurper().parse(jsonStream)
    def document1 = Document.parse(groovy.json.JsonOutput.toJson(json[0]))
    def document2 = Document.parse(groovy.json.JsonOutput.toJson(json[1]))
    mongoOperations.save(document1, "wfProcessExecution")
    mongoOperations.save(document2, "wfProcessExecution")

    and:
    def migration = new CU02AddTradesCountByPortfolio(mongoOperations)

    when:
    migration.beforeExecution()

    then:
    def updated1 = mongoOperations.findOne(
      Query.query(Criteria.where("_id").is(document1.get("_id"))),
      Document.class,
      "wfProcessExecution"
      )
    updated1.get("currentState").get("tradesCountByPortfolio").equals(["p1":3, "p10":1, "p2":1, "p4":1])


    def updated2 = mongoOperations.findOne(
      Query.query(Criteria.where("_id").is(document2.get("_id"))),
      Document.class,
      "wfProcessExecution"
      )
    updated2.get("currentState").get("tradesCountByPortfolio").equals(["p1":2, "p5":4])
  }
}
