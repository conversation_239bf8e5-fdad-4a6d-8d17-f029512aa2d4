package com.solum.xplain.support.migration.changeunits.v0


import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.company.CompanySettingsType
import com.solum.xplain.core.company.entity.CompanyIpvSettings
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit033Test extends IntegrationSpecification {
  @Resource
  MongoOperations mongoOperations

  def cleanup() {
    mongoOperations.dropCollection(CompanyIpvSettings)
    mongoOperations.dropCollection(CompanyLegalEntityIpvSettings)
  }

  def "should execute on empty db"() {
    when:
    new ChangeUnit033(mongoOperations).execution()

    then:
    mongoOperations.findAll(IpvDataGroup).isEmpty()
  }

  def "should execute"() {
    setup:
    def compSettings1 = mongoOperations.insert(new CompanyIpvSettings())
    def compSettings2 = mongoOperations.insert(new CompanyIpvSettings(slaDeadline: SlaDeadline.LDN_1200))

    def entitySettings1 = mongoOperations.insert(new CompanyLegalEntityIpvSettings(settingsType: CompanySettingsType.DEFAULT))
    def entitySettings2 = mongoOperations.insert(new CompanyLegalEntityIpvSettings(settingsType: CompanySettingsType.BESPOKE))
    def entitySettings3 = mongoOperations.insert(new CompanyLegalEntityIpvSettings(settingsType: CompanySettingsType.BESPOKE, slaDeadline: SlaDeadline.LDN_1200))

    when:
    new ChangeUnit033(mongoOperations).execution()

    then:
    var companiesSettings = mongoOperations.findAll(CompanyIpvSettings).sort { it.id }
    companiesSettings[0].id == compSettings1.id
    companiesSettings[0].slaDeadline == SlaDeadline.OTHER

    companiesSettings[1].id == compSettings2.id
    companiesSettings[1].slaDeadline == SlaDeadline.LDN_1200

    and:
    var entitiesSettings = mongoOperations.findAll(CompanyLegalEntityIpvSettings).sort { it.id }
    entitiesSettings[0].id == entitySettings1.id
    entitiesSettings[0].settingsType == CompanySettingsType.DEFAULT
    entitiesSettings[0].slaDeadline == null

    entitiesSettings[1].id == entitySettings2.id
    entitiesSettings[1].settingsType == CompanySettingsType.BESPOKE
    entitiesSettings[1].slaDeadline == SlaDeadline.OTHER

    entitiesSettings[2].id == entitySettings3.id
    entitiesSettings[2].settingsType == CompanySettingsType.BESPOKE
    entitiesSettings[2].slaDeadline == SlaDeadline.LDN_1200
  }
}
