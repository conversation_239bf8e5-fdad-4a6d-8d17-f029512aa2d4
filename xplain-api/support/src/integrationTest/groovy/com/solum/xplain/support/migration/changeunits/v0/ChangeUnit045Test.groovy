package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.CalculationResultCurves
import com.solum.xplain.core.common.value.ChartPoint
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.support.migration.changeunits.v0.ChangeUnit045.CalculationResultsCharts
import com.solum.xplain.support.migration.changeunits.v0.ChangeUnit045.CurveChartData
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit045Test extends IntegrationSpecification {

  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(CurveCalibrationResult)
  }

  def "should correctly run on empty database"() {
    setup:
    def changeUnit = new ChangeUnit045(mongoTemplate)
    when:
    changeUnit.beforeExecute()

    then:
    mongoTemplate.query(CalculationResultCurves).all().isEmpty()
  }

  def "should migrate calculation curves"() {
    setup:
    def chart1 = new CurveChartData(indexName: "EUR 3M", valueType: "ZeroRate", chartPoints: [new ChartPoint(
      LocalDate.now(), 1, 2)])
    def chart2 = new CurveChartData(indexName: "GB RPI", valueType: "PriceIndex", chartPoints: [new ChartPoint(LocalDate.now(), 2, 1)])

    CalculationResultsCharts charts1 = new CalculationResultsCharts(
      id: ObjectId.get(),
      curveConfigurationChartData: [chart1],
      fxCurveConfigurationChartData: [chart2]
      )
    mongoTemplate.save(charts1, ChangeUnit045.OLD_COLLECTION_NAME)
    mongoTemplate.save(new CalculationResult(id: charts1.id, valuationDate: LocalDate.now()))

    //Charts without fx data
    CalculationResultsCharts charts2 = new CalculationResultsCharts(
      id: ObjectId.get(),
      curveConfigurationChartData: [chart1]
      )
    mongoTemplate.save(charts2, ChangeUnit045.OLD_COLLECTION_NAME)
    mongoTemplate.save(new CalculationResult(id: charts2.id, valuationDate: LocalDate.now()))


    //Charts without result entity
    CalculationResultsCharts charts3 = new CalculationResultsCharts(
      id: ObjectId.get(),
      curveConfigurationChartData: [chart1]
      )
    mongoTemplate.save(charts3, ChangeUnit045.OLD_COLLECTION_NAME)

    def changeUnit = new ChangeUnit045(mongoTemplate)

    when:
    changeUnit.beforeExecute()

    then:
    def results = mongoTemplate.query(CalculationResultCurves).all()
    results.size() == 2
    results[0].id == charts1.id
    results[0].curveConfigurationCurves.size() == 1
    results[0].curveConfigurationCurves[0].name == "EUR 3M"
    results[0].curveConfigurationCurves[0].YValueType == "ZeroRate"
    results[0].curveConfigurationCurves[0].chartPoints == [new ChartPoint(LocalDate.now(), 1, 2)]
    results[0].fxCurveConfigurationCurves[0].name == "GB RPI"
    results[0].fxCurveConfigurationCurves[0].YValueType == "PriceIndex"
    results[0].fxCurveConfigurationCurves[0].chartPoints == [new ChartPoint(LocalDate.now(), 2, 1)]

    results[1].id == charts2.id
    results[1].fxCurveConfigurationCurves == null
    results[1].curveConfigurationCurves.size() == 1
  }

  def "should drop old collection"() {
    setup:
    CalculationResultsCharts charts1 = new CalculationResultsCharts(id: ObjectId.get(),)
    mongoTemplate.save(charts1, ChangeUnit045.OLD_COLLECTION_NAME)

    def changeUnit = new ChangeUnit045(mongoTemplate)

    when:
    changeUnit.beforeExecute()

    then:
    !mongoTemplate.collectionExists(ChangeUnit045.OLD_COLLECTION_NAME)
  }
}
