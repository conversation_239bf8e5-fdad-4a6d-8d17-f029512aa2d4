package com.solum.xplain.support.retention.explicit.mdk

import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataKey
import com.solum.xplain.core.market.MarketDataKeyEntity
import com.solum.xplain.core.market.MarketDataKeyValue
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class MarketDataKeySupportRepositoryTest extends IntegrationSpecification {

  @Resource
  MarketDataKeySupportRepository supportRepository
  @Resource
  MongoOperations operations

  def cleanup() {
    operations.remove(new Query(), MarketDataKeyEntity)
    operations.remove(new Query(), MarketDataKey)
  }

  def "should return market data key entities stream"() {
    setup:
    def mdk = item()
    operations.insert(mdk)

    when:
    def result = supportRepository.entitiesStream().toList()

    then:
    result.size() == 1
    result[0].versions.size() == 2
  }

  def "should update version and generate views"() {
    setup:
    def mdk = item()
    operations.insert(mdk)
    def version1 = new EmbeddedVersion<MarketDataKeyValue>(LocalDate.now(), LocalDateTime.now(), State.ACTIVE, null, null, new MarketDataKeyValue())

    when:
    supportRepository.updateEntity(mdk, [version1])

    then:
    def loaded = operations.findAll(MarketDataKeyEntity)
    loaded.size() == 1
    loaded[0].versions.size() == 1
    loaded[0].versions.validFrom == [LocalDate.now()]
    def views = operations.findAll(MarketDataKey)
    views.size() == 1
  }

  def "should delete entity and view"() {
    setup:
    def mdk1 = item()
    def mdk2 = item()
    operations.insertAll([mdk1, mdk2])

    def view1 = new MarketDataKey(entityId: mdk1.id)
    def view2 = new MarketDataKey(entityId: mdk2.id)
    operations.insertAll([view1, view2])

    when:
    supportRepository.deleteEntity(mdk1)

    then:
    def loaded = operations.findAll(MarketDataKeyEntity)
    loaded.size() == 1
    loaded.id == [mdk2.id]
    def views = operations.findAll(MarketDataKey)
    views.size() == 1
    views.entityId == [mdk2.id]
  }

  MarketDataKeyEntity item() {
    new MarketDataKeyEntity(
      semanticId: "semantic",
      versions: [
        new EmbeddedVersion<MarketDataKeyValue>(LocalDate.ofEpochDay(0), LocalDateTime.now(), State.ACTIVE, null, null, null),
        new EmbeddedVersion<MarketDataKeyValue>(LocalDate.ofEpochDay(10), LocalDateTime.now(), State.ACTIVE, null, null, null)
      ]
      )
  }
}
