package com.solum.xplain.support.setup.reset

import static org.springframework.test.web.client.match.MockRestRequestMatchers.content
import static org.springframework.test.web.client.match.MockRestRequestMatchers.method
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo
import static org.springframework.test.web.client.response.MockRestResponseCreators.withSuccess

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.support.setup.SetupProperties
import com.solum.xplain.support.setup.reset.dto.DataSetupRequest
import com.solum.xplain.support.setup.reset.dto.DataSetupResponse
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.web.client.MockRestServiceServer
import org.springframework.web.client.RestTemplate
import software.amazon.awssdk.regions.Region
import spock.lang.Specification

class LocalLambdaInvokerTest extends Specification {
  ObjectMapper objectMapper = new ObjectMapper()
  SetupProperties setupProperties = new SetupProperties("test", new SetupProperties.ResetProperties(true, Region.AP_EAST_1, "fish:fowl:aardvark:dinosaur", URI.create("http://local.container/function/invoke"), [], [] as Set))
  RestTemplate restTemplate = new RestTemplate()
  MockRestServiceServer mockServer = MockRestServiceServer.createServer(restTemplate)
  LocalLambdaInvoker localLambdaInvoker = new LocalLambdaInvoker(setupProperties, restTemplate)

  def "should invoke lambda RIE proxy with a null environment name, and the dataset and access token provided"() {
    given:
    def requestPayload = new DataSetupRequest("EVERYTHING EVERYWHERE ALL AT ONCE", setupProperties.getEnvironmentName(),"of my affection", "active active")
    def responsePayload = new DataSetupResponse("in a bottle")
    mockServer.expect(requestTo("http://local.container/function/invoke"))
      .andExpect(method(HttpMethod.POST))
      .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
      .andExpect(content().json('{"xpl_env_name":null,"environment_name":"EVERYTHING EVERYWHERE ALL AT ONCE","access_token":"of my affection", "activity_session" : "active active"}'))
      .andRespond(withSuccess(objectMapper.writeValueAsString(responsePayload), MediaType.APPLICATION_JSON))

    when:
    def result = localLambdaInvoker.invokeLambda(requestPayload)

    then:
    result.toOptional().get() == responsePayload

    and:
    mockServer.verify()
  }
}
