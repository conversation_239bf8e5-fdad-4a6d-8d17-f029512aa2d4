package com.solum.xplain.support.retention.daterange;

import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import java.util.List;
import java.util.stream.Stream;

public interface DateRangeEntitySupportRepository<V, T extends EmbeddedVersionEntity<V>> {
  Stream<T> entitiesStream();

  void updateEntity(T entity, List<EmbeddedVersion<V>> versions);

  void deleteEntity(T entity);
}
