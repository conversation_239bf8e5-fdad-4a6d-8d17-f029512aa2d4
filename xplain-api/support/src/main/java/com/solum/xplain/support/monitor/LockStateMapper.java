package com.solum.xplain.support.monitor;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.lock.CachedLockHistory;
import com.solum.xplain.core.lock.CachedLockState;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.annotation.Nullable;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = AuditUserMapper.class)
public abstract class LockStateMapper {
  LockStateView toView(CachedLockState state) {
    boolean locked = state.threadId() != null;
    Optional<CachedLockHistory> last =
        state.history().isEmpty() ? Optional.empty() : Optional.of(state.history().getLast());

    Optional<AuditUser> owner =
        Optional.ofNullable(state.owner()).or(() -> last.map(CachedLockHistory::owner));
    Optional<String> requestPath =
        Optional.ofNullable(state.requestPath()).or(() -> last.map(CachedLockHistory::requestPath));
    Optional<Long> obtained =
        Optional.ofNullable(state.obtained()).or(() -> last.map(CachedLockHistory::obtained));
    Optional<Long> released = locked ? Optional.empty() : last.map(CachedLockHistory::released);
    Optional<Duration> duration =
        Steps.begin(obtained.map(Instant::ofEpochMilli))
            .then(__ -> released.map(Instant::ofEpochMilli))
            .yield(Duration::between);

    return new LockStateView(
        state.lock().getId(),
        locked,
        owner.map(AuditUser::getName).orElse(null),
        requestPath.orElse(null),
        obtained.map(this::toLocalDateTime).orElse(null),
        released.map(this::toLocalDateTime).orElse(null),
        duration.orElse(null));
  }

  @Mapping(target = "name", source = "state.lock.id")
  @Mapping(target = "locked", constant = "true")
  @Mapping(target = "user", source = "history.owner")
  @Mapping(target = "requestPath", source = "history.requestPath")
  @Mapping(target = "obtained", source = "history.obtained")
  @Mapping(
      target = "duration",
      expression = "java(toDuration(history.obtained(), history.released()))")
  abstract LockStateView toHistoryView(CachedLockState state, CachedLockHistory history);

  LocalDateTime toLocalDateTime(Long value) {
    return value == null
        ? null
        : LocalDateTime.ofInstant(Instant.ofEpochMilli(value), ZoneId.systemDefault());
  }

  @SuppressWarnings("unused") // Used by toHistoryView's mapping.
  @Nullable
  Duration toDuration(@Nullable Long start, @Nullable Long end) {
    if (start == null || end == null) {
      return null;
    }
    return Duration.ofMillis(end - start);
  }
}
