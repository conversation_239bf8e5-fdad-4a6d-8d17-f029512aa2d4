package com.solum.xplain.support.migration.changeunits.v_1_11_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;

import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@AllArgsConstructor
@ChangeUnit(order = "v01.11.00_03", id = "v1.11.0_03", author = "xplain")
public class CU03AddXmResultsIndexes {

  private static final String INSTRUMENT_RESULT_OVERLAY_COLLECTION = "instrumentResultOverlay";
  private static final String INSTRUMENT_RESULT_PRELIMINARY_COLLECTION =
      "instrumentResultPreliminary";
  private static final String STATUS = "status";
  private static final String DEFAULT_SORT = "DEFAULT_SORT";
  private static final String DASHBOARD_ID = "dashboardId";
  private static final String ASSET_NAME = "instrument.assetName";
  private static final String INSTRUMENT_TYPE_SORT = "instrument.instrumentTypeSort";
  private static final String PARSED_TENOR = "instrument.parsedTenor";
  private static final String NODE_INSTRUMENT = "instrument.nodeInstrument";
  private static final String UNDERLYING = "instrument.underlying";
  private static final String ID = "id";
  private static final String TASK_ID = "taskId";
  private static final String HAS_BREAKS = "hasBreaks";
  private static final String CURVE_CONFIGURATION_ID = "curveConfigurationId";

  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    mongoTemplate
        .indexOps(INSTRUMENT_RESULT_OVERLAY_COLLECTION)
        .ensureIndex(
            new Index()
                .named(indexName(STATUS, DASHBOARD_ID, DEFAULT_SORT))
                .on(STATUS, Direction.ASC)
                .on(DASHBOARD_ID, Direction.ASC)
                .on(CURVE_CONFIGURATION_ID, Direction.ASC)
                .on(ASSET_NAME, Direction.ASC)
                .on(INSTRUMENT_TYPE_SORT, Direction.ASC)
                .on(PARSED_TENOR, Direction.ASC)
                .on(NODE_INSTRUMENT, Direction.ASC)
                .on(UNDERLYING, Direction.ASC)
                .on(ID, Direction.ASC));

    mongoTemplate
        .indexOps(INSTRUMENT_RESULT_OVERLAY_COLLECTION)
        .ensureIndex(
            new Index()
                .named(indexName(TASK_ID, STATUS, HAS_BREAKS, DEFAULT_SORT))
                .on(TASK_ID, Direction.ASC)
                .on(STATUS, Direction.ASC)
                .on(HAS_BREAKS, Direction.ASC)
                .on(INSTRUMENT_TYPE_SORT, Direction.ASC)
                .on(PARSED_TENOR, Direction.ASC));

    // same as overlay
    mongoTemplate
        .indexOps(INSTRUMENT_RESULT_PRELIMINARY_COLLECTION)
        .ensureIndex(
            new Index()
                .named(indexName(TASK_ID, STATUS, HAS_BREAKS, DEFAULT_SORT))
                .on(TASK_ID, Direction.ASC)
                .on(STATUS, Direction.ASC)
                .on(HAS_BREAKS, Direction.ASC)
                .on(INSTRUMENT_TYPE_SORT, Direction.ASC)
                .on(PARSED_TENOR, Direction.ASC));
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
