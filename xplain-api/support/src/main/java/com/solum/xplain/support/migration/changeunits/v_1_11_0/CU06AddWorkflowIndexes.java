package com.solum.xplain.support.migration.changeunits.v_1_11_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.utils.PathUtils;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.PartialIndexFilter;

@RequiredArgsConstructor
@ChangeUnit(order = "v01.11.00_06", id = "v1.11.0_06", author = "xplain")
public class CU06AddWorkflowIndexes {
  public static final String PROCESS_EXECUTION = "wfProcessExecution";
  public static final String STEP_INSTANCE = "wfStepInstance";
  public static final String BUSINESS_KEY = "businessKey";
  public static final String PROCESS_ID = "processId";
  public static final String PARENT_STEP_INSTANCE_ID = "parentStepInstanceId";
  public static final String CURRENT_STATE_BREAK_TEST_RESULTS_PROVIDER_VALUE =
      "currentState.breakTestResults.providerValue";
  public static final String TRIGGERED = "triggered";
  public static final String PARENT_BUSINESS_KEY = "parentBusinessKey";
  public static final String CURRENT_STATE_VDG_ENTITY_ID = "currentState.vdg.entityId";
  public static final String CURRENT_STATE_TRADE_PRODUCT_GROUP = "currentState.trade.productGroup";
  public static final String STATUS = "status";
  public static final String REPORTABLE = "reportable";
  public static final String STEP_ID = "stepId";
  public static final String EXECUTION_ID = "executionId";
  public static final String IPV_TRADE_RESULT_OVERLAY = "ipvTradeResultOverlay";
  public static final String TRADE_KEY = "trade.key";
  public static final String PHASE = "phase";
  public static final String VALUATION_DATE = "valuationDate";
  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    // Workflow
    addProcessExecutionIndexes();
    addStepInstanceIndexes();
    addIpvTradeResultOverlayIndexes();
  }

  private void addProcessExecutionIndexes() {
    mongoTemplate
        .indexOps(PROCESS_EXECUTION)
        .ensureIndex(
            new Index()
                .named(indexName(PROCESS_ID, BUSINESS_KEY))
                .on(PROCESS_ID, Direction.ASC)
                .on(BUSINESS_KEY, Direction.ASC));
    mongoTemplate
        .indexOps(PROCESS_EXECUTION)
        .ensureIndex(
            new Index()
                .named(indexName(PARENT_STEP_INSTANCE_ID, TRIGGERED))
                .on(PARENT_STEP_INSTANCE_ID, Direction.ASC)
                .on(
                    PathUtils.joinPaths(CURRENT_STATE_BREAK_TEST_RESULTS_PROVIDER_VALUE, TRIGGERED),
                    Direction.ASC));
    mongoTemplate
        .indexOps(PROCESS_EXECUTION)
        .ensureIndex(
            new Index()
                .named(
                    indexName(
                        PROCESS_ID,
                        PARENT_BUSINESS_KEY,
                        CURRENT_STATE_VDG_ENTITY_ID,
                        CURRENT_STATE_TRADE_PRODUCT_GROUP))
                .on(PROCESS_ID, Direction.ASC)
                .on(PARENT_BUSINESS_KEY, Direction.ASC)
                .on(CURRENT_STATE_VDG_ENTITY_ID, Direction.ASC)
                .on(CURRENT_STATE_TRADE_PRODUCT_GROUP, Direction.ASC));
  }

  private void addStepInstanceIndexes() {
    mongoTemplate
        .indexOps(STEP_INSTANCE)
        .ensureIndex(
            new Index()
                .named(indexName(PROCESS_ID, STATUS, REPORTABLE, STEP_ID))
                .on(PROCESS_ID, Direction.ASC)
                .on(STATUS, Direction.ASC)
                .on(REPORTABLE, Direction.ASC)
                .on(STEP_ID, Direction.ASC)
                .partial(PartialIndexFilter.of(where(REPORTABLE).is(true))));
    mongoTemplate
        .indexOps(STEP_INSTANCE)
        .ensureIndex(
            new Index()
                .named(indexName(PROCESS_ID, BUSINESS_KEY, REPORTABLE))
                .on(PROCESS_ID, Direction.ASC)
                .on(BUSINESS_KEY, Direction.ASC)
                .on(REPORTABLE, Direction.ASC)
                .partial(PartialIndexFilter.of(where(REPORTABLE).is(true))));
    mongoTemplate
        .indexOps(STEP_INSTANCE)
        .ensureIndex(
            new Index()
                .named(indexName(EXECUTION_ID, REPORTABLE))
                .on(EXECUTION_ID, Direction.ASC)
                .on(REPORTABLE, Direction.DESC));
  }

  private void addIpvTradeResultOverlayIndexes() {
    mongoTemplate
        .indexOps(IPV_TRADE_RESULT_OVERLAY)
        .ensureIndex(
            new Index()
                .named(indexName(VALUATION_DATE, PHASE, TRADE_KEY))
                .on(VALUATION_DATE, Direction.DESC)
                .on(PHASE, Direction.DESC)
                .on(TRADE_KEY, Direction.ASC));
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
