package com.solum.xplain.support.retention.daterange;

import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.support.retention.CollectionRetentionManager;
import com.solum.xplain.support.retention.utils.MajorVersion;
import com.solum.xplain.support.retention.utils.MinorVersion;
import com.solum.xplain.support.retention.utils.VersionValiditiesFactory;
import com.solum.xplain.support.retention.value.DocumentValidities;
import com.solum.xplain.support.retention.value.RemovalSummary;
import java.util.Collection;
import java.util.List;

public class DateRangeValuesRetentionManager<V, T extends EmbeddedVersionEntity<V>>
    implements CollectionRetentionManager {
  private final DateRangeEntitySupportRepository<V, T> supportRepository;
  private final String collectionName;
  private final boolean deleteMajorVersions;

  public DateRangeValuesRetentionManager(
      DateRangeEntitySupportRepository<V, T> supportRepository, String collectionName) {
    this(supportRepository, collectionName, true);
  }

  public DateRangeValuesRetentionManager(
      DateRangeEntitySupportRepository<V, T> supportRepository,
      String collectionName,
      boolean deleteMajorVersions) {
    this.supportRepository = supportRepository;
    this.collectionName = collectionName;
    this.deleteMajorVersions = deleteMajorVersions;
  }

  @Override
  public RemovalSummary clean(DocumentValidities validities) {
    return supportRepository
        .entitiesStream()
        .map(i -> processEntity(i, validities))
        .reduce(RemovalSummary::reduce)
        .orElse(new RemovalSummary(0, collectionName));
  }

  private RemovalSummary processEntity(T entity, DocumentValidities validities) {
    var versions = VersionValiditiesFactory.forEntity(entity);
    if (versions.stream().allMatch(v -> v.isNotActiveAndOld(validities))) {
      // All versions are old and inactive so whole object can be removed
      supportRepository.deleteEntity(entity);
      return new RemovalSummary(entity.getVersions().size(), collectionName);
    }

    var filteredVersions =
        versions.stream()
            .map(group -> filterVersions(group, validities))
            .flatMap(Collection::stream)
            .toList();
    var newVersions = entity.getVersions().stream().filter(filteredVersions::contains).toList();

    if (newVersions.size() != entity.getVersions().size()) {
      var diff = entity.getVersions().size() - newVersions.size();
      supportRepository.updateEntity(entity, newVersions);
      return new RemovalSummary(diff, collectionName);
    }
    return new RemovalSummary(0, collectionName);
  }

  private List<EmbeddedVersion<V>> filterVersions(
      MajorVersion<EmbeddedVersion<V>> majorVersion, DocumentValidities validities) {
    if (deleteMajorVersions && majorVersion.isNotRootAndOld(validities)) {
      // Major version is old, remove
      return List.of();
    }

    return majorVersion.minors().stream()
        .filter(i -> !i.recordTo().isBefore(validities.getValidRecordToDate()))
        .map(MinorVersion::key)
        .toList();
  }
}
