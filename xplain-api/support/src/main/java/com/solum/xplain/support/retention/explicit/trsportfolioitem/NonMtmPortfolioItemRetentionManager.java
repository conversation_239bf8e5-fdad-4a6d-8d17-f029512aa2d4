package com.solum.xplain.support.retention.explicit.trsportfolioitem;

import static com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity.NON_MTM_PORTFOLIO_ITEM_WRITE_COLLECTION;

import com.solum.xplain.support.retention.daterange.DateRangeValuesRetentionManager;
import com.solum.xplain.support.retention.explicit.ExplicitCollectionRetentionManager;
import com.solum.xplain.support.retention.value.DocumentValidities;
import com.solum.xplain.support.retention.value.RemovalSummary;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity;
import com.solum.xplain.trs.portfolio.trade.NonMtmPortfolioItemEntityToViewConverterProvider;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnBean(NonMtmPortfolioItemEntityToViewConverterProvider.class)
public class NonMtmPortfolioItemRetentionManager implements ExplicitCollectionRetentionManager {

  private final DateRangeValuesRetentionManager<TrsTradeValue, NonMtmPortfolioItemEntity>
      retentionManager;

  public NonMtmPortfolioItemRetentionManager(
      NonMtmPortfolioItemSupportRepository supportRepository) {
    this.retentionManager =
        new DateRangeValuesRetentionManager<>(
            supportRepository, NON_MTM_PORTFOLIO_ITEM_WRITE_COLLECTION);
  }

  @Override
  public RemovalSummary clean(DocumentValidities validities) {
    return retentionManager.clean(validities);
  }

  @Override
  public boolean matchesCollection(Class<?> collectionClass) {
    return NonMtmPortfolioItemEntity.class.equals(collectionClass);
  }

  @Override
  public boolean matchesCollectionToIgnore(Class<?> collectionClass) {
    return NonMtmPortfolioItem.class.equals(collectionClass);
  }
}
