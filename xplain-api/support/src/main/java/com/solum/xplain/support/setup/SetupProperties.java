package com.solum.xplain.support.setup;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import java.net.URI;
import java.util.List;
import java.util.Set;
import lombok.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import software.amazon.awssdk.regions.Region;

@Value
@Validated
@ConfigurationProperties(prefix = "app.setup")
public class SetupProperties {
  /** The logical name of this environment e.g. qa, demo. */
  @NonNull private final String environmentName;

  @Valid @NonNull private final ResetProperties reset;

  @Value
  public static class ResetProperties {
    /** Enable the reset functionality. */
    @NonNull private final boolean enabled;

    /** The AWS region to use when invoking the setup lambda function. */
    @NonNull private final Region awsRegion;

    /** The name of the AWS lambda function to invoke to setup the database. */
    @NonNull private final String awsLambdaFunctionName;

    /**
     * If this is set, then instead of using the AWS SDK to invoke the lambda function, make an HTTP
     * request to this URL. Used to <a
     * href="https://docs.aws.amazon.com/lambda/latest/dg/images-test.html">invoke the lambda
     * function in a local container</a> using the bundled <a
     * href="https://github.com/aws/aws-lambda-runtime-interface-emulator">Lambda runtime interface
     * emulator</a>.
     */
    @Nullable private final URI awsEmulatorProxyUrl;

    /**
     * List of valid dataset names which can be used to setup the database. These datasets will be
     * made available as a {@value
     * com.solum.xplain.support.SupportClassifiersProvider#RESET_DATASETS} classifier and validated
     * when calling the {@link
     * com.solum.xplain.support.setup.reset.ResetController#resetDatabase(String, String)} endpoint.
     */
    @NonNull
    @Size(min = 1)
    private final List<String> datasets;

    /** Set of collections which should not be removed when resetting the database. */
    @NonNull
    @Size(min = 1)
    private final Set<String> protectedCollections;
  }
}
