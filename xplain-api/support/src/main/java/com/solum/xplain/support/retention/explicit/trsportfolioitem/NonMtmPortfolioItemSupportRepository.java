package com.solum.xplain.support.retention.explicit.trsportfolioitem;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityMongoOperations;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateOperations;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.support.retention.daterange.DateRangeEntitySupportRepository;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity;
import com.solum.xplain.trs.portfolio.trade.NonMtmPortfolioItemEntityToViewConverterProvider;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
@ConditionalOnBean(NonMtmPortfolioItemEntityToViewConverterProvider.class)
public class NonMtmPortfolioItemSupportRepository
    implements DateRangeEntitySupportRepository<TrsTradeValue, NonMtmPortfolioItemEntity> {

  private final EmbeddedVersionEntityMongoOperations<
          TrsTradeValue, NonMtmPortfolioItemEntity, NonMtmPortfolioItem>
      operations;
  private final MongoOperations mongoOperations;

  public NonMtmPortfolioItemSupportRepository(
      MongoOperations mongoOperations,
      AuditorAware<AuditUser> auditUserAuditorAware,
      NonMtmPortfolioItemEntityToViewConverterProvider converterProvider) {
    this.mongoOperations = mongoOperations;
    this.operations =
        new EmbeddedVersionEntityMongoOperations<>(
            NonMtmPortfolioItemEntity.class,
            NonMtmPortfolioItem.class,
            mongoOperations,
            converterProvider,
            new EntityUpdateOperations<>(auditUserAuditorAware),
            trades -> {});
  }

  @Override
  public Stream<NonMtmPortfolioItemEntity> entitiesStream() {
    return mongoOperations.stream(new Query(), NonMtmPortfolioItemEntity.class);
  }

  @Override
  public void updateEntity(
      NonMtmPortfolioItemEntity entity, List<EmbeddedVersion<TrsTradeValue>> versions) {
    operations.storeSingle(entity, versions);
  }

  @Override
  public void deleteEntity(NonMtmPortfolioItemEntity entity) {
    mongoOperations.remove(entity);
    mongoOperations.remove(
        query(where(DateRangeVersionedEntity.Fields.entityId).is(entity.getId())),
        NonMtmPortfolioItem.class);
  }
}
