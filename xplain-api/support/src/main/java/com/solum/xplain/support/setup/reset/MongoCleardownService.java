package com.solum.xplain.support.setup.reset;

import com.solum.xplain.support.setup.SetupProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.stereotype.Service;

/**
 * This service is used to clear the database quickly, by dropping most collections. The {@link
 * MongoMappingContext} is used to find all the entities in the application, and then any associated
 * collections are dropped (except for those defined as protected in {@link SetupProperties}).
 *
 * @see #clearDatabase()
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MongoCleardownService {
  private static final String LOG_TEMPLATE = "Cleardown completed for %s: collection %s dropped.";
  private final MongoMappingContext mappingContext;
  private final MongoOperations mongoOperations;
  private final SetupProperties setupProperties;

  /**
   * Drops all collections apart from a few protected collections configured in {@link
   * SetupProperties}.
   */
  public void clearDatabase() {
    mappingContext.getPersistentEntities().stream()
        .filter(this::shouldDropCollection)
        .peek(this::dropCollection)
        .forEach(this::logMessage);
  }

  private void logMessage(MongoPersistentEntity<?> entity) {
    log.info(String.format(LOG_TEMPLATE, entity.getType().getSimpleName(), entity.getCollection()));
  }

  private boolean shouldDropCollection(MongoPersistentEntity<?> entity) {
    return !setupProperties.getReset().getProtectedCollections().contains(entity.getCollection());
  }

  private void dropCollection(MongoPersistentEntity<?> entity) {
    mongoOperations.dropCollection(entity.getCollection());
  }
}
