package com.solum.xplain.support.migration.changeunits.v_2_01_0;

import com.solum.xplain.core.lock.CachedLockState;
import com.solum.xplain.shared.datagrid.DataGrid;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import lombok.RequiredArgsConstructor;

@Deprecated(forRemoval = true) // Should remove in 2.2.x as this will be applied in 2.1.x release
@RequiredArgsConstructor
@ChangeUnit(order = "v02.01.00_03", id = "v2.01.0_03", author = "xplain")
public class CU05DeleteCachedLocksOlderThan24HrsFromHazelcast {

  private final DataGrid dataGrid;

  @BeforeExecution
  public void beforeExecution() {
    long yesterday = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
    dataGrid
        .<String, CachedLockState>getKeyValueCache("LOCK_STATE")
        .removeAll(
            v ->
                v.obtained() != null
                    && !v.history().isEmpty()
                    && v.history().getLast().released() < yesterday);
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
