package com.solum.xplain.secmaster.upload;

import static com.google.common.io.ByteSource.wrap;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParseErrorItem;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.common.validation.identifier.IdentifierValidationUtils.isValidReference;
import static com.solum.xplain.secmaster.upload.SecMasterTradeCsvForm.newOf;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Pair.pair;
import static java.util.Spliterators.spliteratorUnknownSize;
import static java.util.stream.StreamSupport.stream;

import com.google.common.io.CharSource;
import com.opengamma.strata.collect.io.CsvIterator;
import com.opengamma.strata.collect.io.UnicodeBom;
import com.solum.xplain.core.common.csv.CsvLoader;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.externalsource.IdentifierSourceRepository;
import com.solum.xplain.core.portfolio.csv.TradeValueCsvLoader;
import com.solum.xplain.core.portfolio.csv.loader.ExternalTradeIdsCsvLoader;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.product.csv.ProductCsvLoaders;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Pair;
import java.util.List;
import java.util.Map;
import java.util.Spliterator;
import java.util.function.Function;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;

@Component
public class SecMasterTradeCsvLoader implements CsvLoader<SecMasterTradeCsvForm> {

  private final TradeValueCsvLoader tradeValueCsvLoader;

  public SecMasterTradeCsvLoader(
      ProductCsvLoaders csvLoaders, IdentifierSourceRepository identifierSourceRepository) {
    var externalTradeIdsCsvLoader =
        new ExternalTradeIdsCsvLoader(identifierSourceRepository.activeIdentifierSourcesExtIds());
    tradeValueCsvLoader =
        new TradeValueCsvLoader(
            csvLoaders, externalTradeIdsCsvLoader, null, Map.of(), false, true, null);
  }

  @Override
  public Either<List<ErrorItem>, CsvParserResult<SecMasterTradeCsvForm>> parse(
      byte[] csvContent, ParsingMode parsingMode) {
    var result =
        new CsvParserResultBuilder<>(
            SecMasterTradeCsvForm::getEntityId, Function.identity(), parsingMode.failOnError());
    parseFile(UnicodeBom.toCharSource(wrap(csvContent))).forEach(result::addLine);
    return result.toEither();
  }

  private Stream<Either<ErrorItem, Pair<Integer, SecMasterTradeCsvForm>>> parseFile(
      CharSource charSource) {
    try {
      var csv = CsvIterator.of(charSource, true);
      return parseFile(csv).onClose(csv::close);
    } catch (RuntimeException ex) {
      return Stream.of(Either.left(Error.PARSING_ERROR.entity(ex.getMessage())));
    }
  }

  private Stream<Either<ErrorItem, Pair<Integer, SecMasterTradeCsvForm>>> parseFile(
      CsvIterator csv) {
    return stream(spliteratorUnknownSize(csv, Spliterator.ORDERED), false)
        .map(r -> tradeValueCsvLoader.parseRow(r).flatMap(this::validate));
  }

  private Either<ErrorItem, Pair<Integer, SecMasterTradeCsvForm>> validate(
      Triple<Integer, String, TradeValue> res) {
    try {
      var tradeValue = res.getRight();
      var type = tradeValue.getProductType();

      // all buy/sell trades must have BUY position
      if (type.isBuySell()) {
        validateValue(tradeValue.getTradeDetails().getPositionType(), List.of(PositionType.BUY));
      }
      return isValidReference(res.getMiddle())
          .leftMap(e -> rowParseErrorItem(e.getDescription(), res.getLeft()))
          .map(id -> pair(res.getLeft(), newOf(id, res.getRight())));
    } catch (Exception ex) {
      return left(rowParseErrorItem(ex.getMessage(), res.getLeft()));
    }
  }
}
