package com.solum.xplain.secmaster.trademerge;

import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverter;
import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.PortfolioItemEntity;
import com.solum.xplain.core.portfolio.PortfolioItemToViewConverter;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
public class TradeMergeToViewConverter
    implements EmbeddedVersionEntityToViewConverter<
        TradeValue, PortfolioItemEntity, PortfolioItem> {

  private final TradeValidityMergeResolver tradeValidityMergeResolver;
  private final PortfolioItemToViewConverter converterCachedPortfolio;
  private final TradeDetailsMerger tradeDetailsMerger;

  @Override
  public List<PortfolioItem> generateViews(PortfolioItemEntity entity) {
    var result = new ArrayList<PortfolioItem>();

    var tradeMergeResults = tradeValidityMergeResolver.resolveMerge(entity);
    for (var tradeMergeResult : tradeMergeResults) {
      var portfolioItem = tradeMergeResult.getPortfolioItem();
      var mergeInfo = tradeMergeResult.getMergeInfo();
      if (mergeInfo == null) {
        result.add(portfolioItem);
        continue;
      }

      var tv = mergeTradeValue(mergeInfo.getSecMasterTrade(), portfolioItem);
      var merged = converterCachedPortfolio.apply(entity, tv);
      merged.setEntityId(entity.getId());
      merged.setVersion(portfolioItem.getVersion());
      merged.setValidFrom(mergeInfo.mergedValidFrom());
      merged.setRecordFrom(mergeInfo.mergedRecordFrom());
      merged.setState(mergeInfo.mergedState());
      merged.setComment(portfolioItem.getComment());
      merged.setModifiedBy(portfolioItem.getModifiedBy());
      merged.setModifiedAt(portfolioItem.getModifiedAt());

      merged.setValidities(mergeInfo.getMergedValidities());

      result.add(merged);
    }

    return result;
  }

  private TradeValue mergeTradeValue(SecMasterTradeReadEntity sm, PortfolioItem portfolioItem) {
    var allocDetails = portfolioItem.getAllocationTradeDetails();
    var value = new TradeValue();
    value.setProductType(sm.getProductType());
    value.setTradeDetails(
        tradeDetailsMerger.mergeDetails(sm.getProductType(), sm.getTradeDetails(), allocDetails));
    value.setClientMetrics(mergeClientMetrics(sm, allocDetails));
    value.setAllocationTradeDetails(allocDetails);
    value.setDescription(mergeDescription(sm, allocDetails));
    value.setOnboardingDetails(portfolioItem.getOnboardingDetails());
    value.setExternalIdentifiers(portfolioItem.getExternalIdentifiers());
    value.setCustomFields(portfolioItem.getCustomFields());
    return value;
  }

  private static String mergeDescription(
      SecMasterTradeReadEntity sm, AllocationTradeDetails allocDetails) {
    if (StringUtils.isNotEmpty(allocDetails.getDescription())) {
      return allocDetails.getDescription();
    }
    return sm.getDescription();
  }

  private static ClientMetrics mergeClientMetrics(
      SecMasterTradeReadEntity sm, AllocationTradeDetails allocDetails) {
    if (allocDetails.getClientMetrics() != null
        && allocDetails.getClientMetrics().getPresentValue() != null) {
      return allocDetails.getClientMetrics();
    }

    if (sm.getClientMetrics() != null
        && sm.getClientMetrics().getPresentValue() != null
        && sm.getTradeDetails().getPayLeg() != null
        && sm.getTradeDetails().getPayLeg().getNotional() != null) {
      var allocNotional = allocDetails.getAllocationNotional();
      var smPv = sm.getClientMetrics().getPresentValue();
      var smNo = sm.getTradeDetails().getPayLeg().getNotional();
      return new ClientMetrics(smNo != 0 ? smPv / smNo * allocNotional : null);
    }

    return new ClientMetrics();
  }
}
