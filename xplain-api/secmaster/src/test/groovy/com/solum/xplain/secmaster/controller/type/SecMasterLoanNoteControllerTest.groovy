package com.solum.xplain.secmaster.controller.type

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.loanNoteTradeForm
import static com.solum.xplain.secmaster.entity.SecMasterTradeReadEntityBuilder.loanNote
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.ReferenceTradesProvider
import com.solum.xplain.core.portfolio.form.LoanNoteTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.secmaster.controller.SecMasterTradeControllerService
import com.solum.xplain.secmaster.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [SecMasterLoanNoteController])
class SecMasterLoanNoteControllerTest extends Specification {
  @SpringBean
  private SecMasterTradeControllerService service = Mock()

  @SpringBean
  private ReferenceTradesProvider referenceTradesProvider = Mock()

  @SpringBean
  private PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def "should get LoanNote"() {
    setup:
    1 * service.itemLatest("1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(loanNote())

    when:
    def results = mockMvc.perform(get("/security-master/loan-note/1")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new Loan Note with form #form and response #response"() {
    setup:
    referenceTradesProvider.existsActiveReferenceTrade("DUPLICATE", LocalDate.parse("2020-01-01"), null) >> true
    service.insert(_ as LoanNoteTradeForm) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(post("/security-master/loan-note")
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                                    | code | response
    refLoanNoteTradeForm()                                  | 200  | """{"id":"1"}"""
    refLoanNoteTradeForm(["externalTradeId": "123"])        | 412  | "Value must start with REF_"
    refLoanNoteTradeForm(["externalTradeId": "DUPLICATE"])  | 412  | "UniqueReferenceTradeId"
    refLoanNoteTradeForm()                                  | 200  | """{"id":"1"}"""
    refLoanNoteTradeForm(["startDate": null])               | 412  | "NotNull.loanNoteTradeForm.startDate"
    refLoanNoteTradeForm(["endDate": null])                 | 412  | "NotNull.loanNoteTradeForm.endDate"
    refLoanNoteTradeForm(["currency": null])                | 412  | "NotEmpty.loanNoteTradeForm.currency"
    refLoanNoteTradeForm(["currency": "KRW"])               | 412  | "ValidStringSet.loanNoteTradeForm.currency"
    refLoanNoteTradeForm(["currency": "EUR"])               | 412  | "Reference is not valid for given currency"
    refLoanNoteTradeForm(["reference": null])               | 412  | "NotEmpty.loanNoteTradeForm.reference"
    refLoanNoteTradeForm(["creditSpread": null])            | 412  | "NotNull.loanNoteTradeForm.creditSpread"
    refLoanNoteTradeForm(["notionalValue": -1d])            | 412  | "Null.loanNoteTradeForm.notionalValue"
    refLoanNoteTradeForm(["businessDayConvention": null])   | 412  | "NotEmpty.loanNoteTradeForm.businessDayConvention"
    refLoanNoteTradeForm(["businessDayConvention": "test"]) | 412  | "Pattern.loanNoteTradeForm.businessDayConvention"
    refLoanNoteTradeForm(["dayCount": null])                | 412  | "NotEmpty.loanNoteTradeForm.dayCount"
    refLoanNoteTradeForm(["dayCount": "test"])              | 412  | "ValidStringSet.loanNoteTradeForm.dayCount"
    refLoanNoteTradeForm(["frequency": null])               | 412  | "NotEmpty.loanNoteTradeForm.frequency"
    refLoanNoteTradeForm(["frequency": "1D"])               | 412  | "ValidStringSet.loanNoteTradeForm.frequency"
    refLoanNoteTradeForm(["fixedRate": null])               | 412  | "NotNull.loanNoteTradeForm.fixedRate"
    refLoanNoteTradeForm(["fixedRate": -1d])                | 412  | "Positive.loanNoteTradeForm.fixedRate"
    refLoanNoteTradeForm(["stubConvention": "test"])        | 412  | "ValidStringSet.loanNoteTradeForm.stubConvention"
    refLoanNoteTradeForm(["rollConvention": "test"])        | 412  | "ValidStringSet.loanNoteTradeForm.rollConvention"
  }

  static refLoanNoteTradeForm(Map<String, Object> params = [:]) {
    def defaultParams = ["notionalValue": null]
    defaultParams.putAll(params)
    loanNoteTradeForm(defaultParams)
  }
}
