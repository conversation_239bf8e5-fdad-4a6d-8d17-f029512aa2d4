package com.solum.xplain.trs.portfolio.csv

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.ParsingMode.LENIENT
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.company.value.CompanyLegalEntityView
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider
import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioCreateForm
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import com.solum.xplain.trs.portfolio.sampledata.NonMtmPortfolioFormSample
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioUniqueKey
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class NonMtmPortfolioUploadServiceTest extends Specification implements NonMtmPortfolioFormSample, SampleNonMtmPortfolioCsv {

  NonMtmPortfolioUploadService testObject
  XplainPrincipal authUser
  TestingAuthenticationToken authToken
  CompanyLegalEntityView companyLegalEntityView
  def nonMtmPortfolioCsvLoaderFactory = Mock(NonMtmPortfolioCsvLoaderFactory)
  def loaderMock = Mock(NonMtmPortfolioCsvLoader)
  def auditEntryService = Mock(AuditEntryService)
  def userRepository = Mock(AuthenticationContext)
  def repository = Mock(NonMtmPortfolioRepository)
  def filterProvider = Mock(NonMtmPortfolioTeamFilterProvider)
  def eventPublisher = Mock(ApplicationEventPublisher)

  def setup() {
    testObject = new NonMtmPortfolioUploadService(auditEntryService, repository, userRepository, filterProvider, nonMtmPortfolioCsvLoaderFactory)
    authUser = user("userId")
    authToken = new TestingAuthenticationToken(authUser, null)
    companyLegalEntityView = new CompanyLegalEntityView()
    companyLegalEntityView.id = "id"
    companyLegalEntityView.name = "name"
  }

  def "should insert non-mtm portfolio"() {
    setup:
    def action = DuplicateAction.REPLACE
    def csv = sampleNonMtmPortfolioCsv()
    def form = sampleNonMtmPortfolioCreateForm()
    def savedEntityId = entityId("000000000000000000000001")

    when:
    def result = testObject.uploadPortfolios(authToken, STRICT, action, csv)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * nonMtmPortfolioCsvLoaderFactory.getLoader(authUser) >> loaderMock
    1 * loaderMock.parse(csv, STRICT) >> right(parsedCsvResult(form))
    1 * repository.importPortfolios(_) >> []
    1 * repository.bulkInsert(_) >> [(new NonMtmPortfolioUniqueKey(form.companyId, form.entityId, form.externalPortfolioId)):right(savedEntityId)]
    0 * repository.bulkUpdate(_)
    0 * repository.bulkArchive(_)

    and:
    result.isRight()
    result.getOrNull() == [savedEntityId]
  }

  def "should update existing non-mtm portfolio"() {
    setup:
    def action = DuplicateAction.REPLACE_DELETE
    def csv = sampleNonMtmPortfolioCsv()
    def teamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def form = sampleNonMtmPortfolioCreateForm()
    def existingPortfolio = sampleImportNonMtmPortoflio(
      id: new ObjectId().toHexString(),
      companyId: form.companyId,
      entityId: form.entityId,
      externalPortfolioId: form.externalPortfolioId)

    when:
    def result = testObject.uploadPortfolios(authToken, LENIENT, action, csv)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * nonMtmPortfolioCsvLoaderFactory.getLoader(authUser) >> loaderMock
    1 * loaderMock.parse(csv, LENIENT) >> right(parsedCsvResult(form))
    1 * repository.activeImportPortfolios() >> [existingPortfolio]
    1 * repository.bulkUpdate([(existingPortfolio.id):form]) >> [entityId(existingPortfolio.id)]
    1 * filterProvider.provideFilter(authUser) >> teamFilter
    2 * repository.duplicatePortfoliosCount(Set.of(existingPortfolio.getKey()), teamFilter) >> 0
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _)
    0 * repository.bulkArchive(_)

    and:
    result.isRight()
    result.getOrNull() == [entityId(existingPortfolio.id)]
  }

  def "should archive another non-mtm portfolio"() {
    setup:
    def action = DuplicateAction.APPEND_DELETE
    def csv = sampleNonMtmPortfolioCsv()
    def teamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def form = sampleNonMtmPortfolioCreateForm()
    def anotherPortfolio = sampleImportNonMtmPortoflio(
      id: new ObjectId().toHexString(),
      companyId: form.companyId,
      entityId: form.entityId,
      externalPortfolioId: "ANOTHER ONE")
    def savedEntityId = entityId("000000000000000000000001")

    when:
    def result = testObject.uploadPortfolios(authToken, STRICT, action, csv)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * nonMtmPortfolioCsvLoaderFactory.getLoader(authUser) >> loaderMock
    1 * loaderMock.parse(csv, STRICT) >> right(parsedCsvResult(form))
    1 * repository.activeImportPortfolios() >> [anotherPortfolio]
    1 * repository.bulkInsert(_) >> [(new NonMtmPortfolioUniqueKey(form.companyId, form.entityId, form.externalPortfolioId)):right(savedEntityId)]
    1 * repository.bulkArchive(Set.of(anotherPortfolio.id)) >> [right(entityId(anotherPortfolio.id))]
    1 * filterProvider.provideFilter(authUser) >> teamFilter
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _)
    0 * repository.duplicatePortfoliosCount()
    0 * repository.bulkArchive(_ as String)
    0 * repository.bulkUpdate(_ as String, _ as NonMtmPortfolioCreateForm)

    and:
    result.isRight()
    result.getOrNull() == [savedEntityId, entityId(anotherPortfolio.id)]
  }

  def "should append non-mtm portfolio to the existing ones"() {
    setup:
    def action = DuplicateAction.APPEND
    def csv = sampleNonMtmPortfolioCsv()
    def teamFilter = NonMtmPortfolioTeamFilter.emptyFilter()
    def form = sampleNonMtmPortfolioCreateForm()
    def anotherPortfolio = sampleImportNonMtmPortoflio(
      id: new ObjectId().toHexString(),
      companyId: form.companyId,
      entityId: form.entityId,
      externalPortfolioId: "ANOTHER ONE")
    def savedEntityId = entityId("000000000000000000000001")

    when:
    def result = testObject.uploadPortfolios(authToken, LENIENT, action, csv)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * nonMtmPortfolioCsvLoaderFactory.getLoader(authUser) >> loaderMock
    1 * loaderMock.parse(csv, LENIENT) >> right(parsedCsvResult(form))
    1 * repository.importPortfolios(_) >> [anotherPortfolio]
    1 * repository.bulkInsert(_) >> [(new NonMtmPortfolioUniqueKey(form.companyId, form.entityId, form.externalPortfolioId)):right(savedEntityId)]
    1 * filterProvider.provideFilter(authUser) >> teamFilter
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _)
    0 * repository.duplicatePortfoliosCount()
    0 * repository.bulkArchive(_)

    and:
    result.isRight()
    result.getOrNull() == [savedEntityId]
  }

  def "should validate non-mtm portfolios and return validation errors"() {
    setup:
    def action = DuplicateAction.ERROR
    def csv = sampleNonMtmPortfolioCsv()
    def form = sampleNonMtmPortfolioCreateForm()
    def duplicatedPortfolio = sampleImportNonMtmPortoflio(
      id: new ObjectId().toHexString(),
      companyId: form.companyId,
      entityId: form.entityId,
      externalPortfolioId: form.externalPortfolioId)
    def anotherPortfolio = sampleImportNonMtmPortoflio(
      id: new ObjectId().toHexString(),
      companyId: form.companyId,
      entityId: form.entityId,
      externalPortfolioId: "ANOTHER ONE")

    when:
    def result = testObject.uploadPortfolios(authToken, STRICT, action, csv)

    then:
    1 * userRepository.userEither(authToken) >> right(authUser)
    1 * nonMtmPortfolioCsvLoaderFactory.getLoader(authUser) >> loaderMock
    1 * loaderMock.parse(csv, STRICT) >> right(parsedCsvResult(form))
    1 * repository.activeImportPortfolios() >> [duplicatedPortfolio, anotherPortfolio]

    and:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.contains(new ErrorItem(Error.DUPLICATE_ENTRY, "EXTERNAL_VALID_ID already exists"))
    errors.contains(new ErrorItem(Error.MISSING_ENTRY, "ANOTHER ONE is missing"))
  }

  private static CsvParserResult<NonMtmPortfolioCreateForm> parsedCsvResult(NonMtmPortfolioCreateForm form) {
    new CsvParserResult<NonMtmPortfolioCreateForm>([form], [])
  }
}
