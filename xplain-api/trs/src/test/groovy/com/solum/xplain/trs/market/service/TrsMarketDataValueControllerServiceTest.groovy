package com.solum.xplain.trs.market.service

import static com.solum.xplain.core.common.EntityId.entityId
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.DataExportForm
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.value.ArchiveForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.service.MarketDataKeyResolver
import com.solum.xplain.core.mdvalue.MarketDataValueRepository
import com.solum.xplain.core.mdvalue.csv.MarketDataValueExportService
import com.solum.xplain.core.mdvalue.csv.MarketDataValueImportOptions
import com.solum.xplain.core.mdvalue.csv.MarketDataValueUploadService
import com.solum.xplain.core.mdvalue.value.MarketDataValueCreateForm
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView
import com.solum.xplain.core.mdvalue.value.MarketDataValueUpdateForm
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueCombinedView
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.trs.market.TrsAssetGroupExportForm
import com.solum.xplain.trs.market.value.TrsMarketDataGroupView
import com.solum.xplain.trs.value.TrsAssetClassGroup
import java.time.LocalDate
import java.util.function.Function
import java.util.function.Supplier
import java.util.stream.Stream
import org.springframework.core.io.ByteArrayResource
import spock.lang.Specification

class TrsMarketDataValueControllerServiceTest extends Specification {
  private static def STATE_DATE = BitemporalDate.newOf(LocalDate.now())
  private static def CURVE_DATE = LocalDate.of(2021, 01, 02)

  private static def ASSET_GROUPS = TrsAssetClassGroup.values()
  private static def GROUP_ID = "groupId"
  private static def VALUE_ID = "valueIs"

  private MarketDataValueRepository repository = Mock()
  private MarketDataKeyRepository mdkRepository = Mock()
  private TrsMarketDataGroupValueService groupValueService = Mock()
  private MarketDataValueUploadService uploadService = Mock()
  private MarketDataValueExportService exportService = Mock()
  private AuditEntryService auditEntryService = Mock()

  def service = new TrsMarketDataValueControllerService(repository,
  mdkRepository,
  groupValueService,
  auditEntryService,
  uploadService,
  exportService)

  def setup() {
    groupValueService.performWithGroupUpdate(GROUP_ID, _ as Supplier) >> { List<?> args ->
      Supplier supplier = args[1] as Supplier
      return supplier.get()
    }
  }

  def "should create value"() {
    setup:
    def form = new MarketDataValueCreateForm()
    1 * repository.createValue(GROUP_ID, form) >> entityId(VALUE_ID)
    1 * mdkRepository.keyResolver(STATE_DATE, ASSET_GROUPS) >> new MarketDataKeyResolver([])
    1 * auditEntryService.newEntryWithLogs(_, _)
    when:
    def result = service.createValue(GROUP_ID, form, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull() == entityId(VALUE_ID)
  }

  def "should get values "() {
    setup:
    def view = new MarketDataValueFlatView()
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.getRecordDate()),
      false
      ) >> [view]
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))

    when:
    def result = service.getValues(GROUP_ID, STATE_DATE, CURVE_DATE, false)

    then:
    result.isRight()
    result.getOrNull() == [view]
  }

  def "should get resolved values"() {
    setup:
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))
    def view = new MarketDataValueFlatView()
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.getRecordDate()),
      false
      ) >> [view]

    def resolver = Mock(MarketDataKeyResolver)
    1 * mdkRepository.keyResolver(STATE_DATE, TrsAssetClassGroup.TRS) >> resolver
    def resolvedView = new ResolvedMarketDataValueCombinedView()
    1 * resolver.resolvedCombinedValues([view]) >> Stream.of(resolvedView)

    when:
    def result = service.getResolvedValues(GROUP_ID, STATE_DATE, CURVE_DATE)

    then:
    result.isRight()
    result.getOrNull() == [resolvedView]
  }

  def "should get unresolved values"() {
    setup:
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))
    def view = new MarketDataValueFlatView()
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.getRecordDate()),
      false
      ) >> [view]

    def resolver = Mock(MarketDataKeyResolver)
    1 * mdkRepository.keyResolver(STATE_DATE, TrsAssetClassGroup.TRS) >> resolver
    1 * resolver.unresolvedValues([view]) >> [view]

    0 * auditEntryService._

    when:
    def result = service.getUnresolvedValues(GROUP_ID, STATE_DATE, CURVE_DATE)

    then:
    result.isRight()
    result.getOrNull() == [view]
  }

  def "should get value"() {
    setup:
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))

    def view = new MarketDataValueFlatView()
    1 * repository.getValueViews(
      GROUP_ID,
      BitemporalDate.newOf(CURVE_DATE, STATE_DATE.getRecordDate()),
      false
      ) >> [view]

    when:
    def result = service.getValues(GROUP_ID, STATE_DATE, CURVE_DATE, false)

    then:
    result.isRight()
    result.getOrNull() == [view]
  }

  def "should update value"() {
    setup:
    def view = new MarketDataValueFlatView(bidAsk: ValueBidAskType.BID)
    1 * repository.getValueView(GROUP_ID, VALUE_ID, STATE_DATE) >> right(view)

    1 * mdkRepository.keyResolver(STATE_DATE, ASSET_GROUPS) >> new MarketDataKeyResolver([])
    1 * auditEntryService.newEntryWithLogs(_, _)

    def form = new MarketDataValueUpdateForm()
    1 * repository.updateValue(VALUE_ID, form) >> right(entityId(VALUE_ID))

    when:
    def result = service.updateValue(GROUP_ID, VALUE_ID, form, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull() == entityId(VALUE_ID)
  }

  def "should archive value"() {
    setup:
    def form = new ArchiveForm()
    1 * repository.archiveValue(VALUE_ID, form) >> right(entityId(VALUE_ID))

    when:
    def result = service.archiveValue(GROUP_ID, VALUE_ID, form)

    then:
    result.isRight()
    result.getOrNull() == entityId(VALUE_ID)
  }

  def "should upload values"() {
    setup:
    def result = entityId(GROUP_ID)
    def importOptions = Mock(MarketDataValueImportOptions)
    def bytes = "".getBytes()
    def view = new TrsMarketDataGroupView(id: GROUP_ID, name: "name")
    1 * groupValueService.performWithGroupUpdate(
      _ as String,
      _ as Function) >> { List<?> args ->
        Function fun = args[1] as Function
        return fun.apply(view)
      }
    1 * uploadService.upload(view, bytes, importOptions, STATE_DATE, ASSET_GROUPS) >> right(result)

    expect:
    service.upload(GROUP_ID, bytes, importOptions, STATE_DATE) == right(result)
  }

  def "should export unresolved values"() {
    setup:
    def form = Mock(DataExportForm)
    def result = FileResponseEntity.csvFile(new ByteArrayResource("resource".bytes), "name")
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))
    1 * exportService.getUnresolvedValuesCsv(
      new TrsMarketDataGroupView(id: GROUP_ID),
      STATE_DATE,
      _ as DataExportForm,
      ASSET_GROUPS
      ) >> result

    expect:
    service.getUnresolvedValuesCsv(GROUP_ID, STATE_DATE, form) == right(result)
  }

  def "should export resolved values"() {
    setup:
    def form = Mock(DataExportForm)
    def result = FileResponseEntity.csvFile(new ByteArrayResource("resource".bytes), "name")
    1 * groupValueService.userTrsGroup(GROUP_ID) >> right(new TrsMarketDataGroupView(id: GROUP_ID))
    1 * exportService.getResolvedValuesCsv(
      new TrsMarketDataGroupView(id: GROUP_ID),
      STATE_DATE,
      _ as TrsAssetGroupExportForm
      ) >> result

    expect:
    service.getResolvedValuesCsv(GROUP_ID, STATE_DATE, form) == right(result)
  }
}
