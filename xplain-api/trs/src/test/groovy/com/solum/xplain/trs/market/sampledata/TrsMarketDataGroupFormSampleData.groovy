package com.solum.xplain.trs.market.sampledata

trait TrsMarketDataGroupFormSampleData {

  def trsMarketDataForm(Closure c = {}) {
    [
      name                : "name",
      allowedCompaniesForm: allowedCompaniesForm(),
      allowedTeamsForm    : allowedTeamsForm()
    ].tap(c)
  }

  def allowedTeamsForm(Closure c = {}) {
    [
      allowAll: false,
      teamIds : ["id"],
    ].tap(c)
  }

  def allowedCompaniesForm(Closure c = {}) {
    [
      allowAll  : false,
      companyIds: ["id"],
    ].tap(c)
  }
}
