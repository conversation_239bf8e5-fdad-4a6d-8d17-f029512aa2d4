package com.solum.xplain.trs.valuation.market.filters

import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.OVERLAY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.PRELIMINARY_PRIMARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.PRELIMINARY_SECONDARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_SECONDARY
import static com.solum.xplain.trs.value.NonMtmInstrumentType.TRS_BOND
import static com.solum.xplain.trs.value.NonMtmInstrumentType.TRS_DIVIDEND
import static com.solum.xplain.trs.value.NonMtmInstrumentType.TRS_SHARE

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.trs.company.LegalEntityDataProvidersSettingsService
import com.solum.xplain.trs.company.view.LegalEntityDataProvidersSettingsView
import com.solum.xplain.trs.company.view.MarketDataProvidersView
import com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolio
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class NonMtmMarketDataProviderFilterFactoryTest extends Specification {
  private static def STATE_DATE = new BitemporalDate(LocalDate.now())
  LegalEntityDataProvidersSettingsService settingsService = Mock()

  MarketDataProviderFilterFactory filterFactory = new MarketDataProviderFilterFactory(settingsService)

  def "should return provider settings filter"() {
    setup:
    def portfolio = new NonMtmValuationPortfolio(companyId: "companyId", entityId: "entityId")
    def settingsView = new LegalEntityDataProvidersSettingsView(
      instruments: [
        (TRS_BOND)    : new MarketDataProvidersView(primary: "P1", secondary: "P2"),
        (TRS_DIVIDEND): new MarketDataProvidersView(primary: "P1", secondary: "P2"),
        (TRS_SHARE)   : new MarketDataProvidersView(primary: "P1", secondary: "P2")
      ]
      )

    1 * settingsService.entitySettings("companyId", "entityId", STATE_DATE) >> Either.right(settingsView)
    def result = filterFactory.marketDataProviderFilter(source, portfolio, STATE_DATE)

    expect:
    result.isRight()
    result.getOrNull() instanceof SettingsNonMtmMarketDataProviderFilter

    where:
    source << [RAW_PRIMARY, RAW_SECONDARY, PRELIMINARY_SECONDARY, PRELIMINARY_PRIMARY, OVERLAY]
  }

  def "should return empty filter when no instruments present"() {
    setup:
    def portfolio = new NonMtmValuationPortfolio(companyId: "companyId", entityId: "entityId")
    def settingsView = new LegalEntityDataProvidersSettingsView()

    1 * settingsService.entitySettings("companyId", "entityId", STATE_DATE) >> Either.right(settingsView)
    def result = filterFactory.marketDataProviderFilter(source, portfolio, STATE_DATE)

    expect:
    result.isRight()
    result.getOrNull() instanceof SettingsNonMtmMarketDataProviderFilter

    where:
    source << [RAW_PRIMARY, RAW_SECONDARY, PRELIMINARY_SECONDARY, PRELIMINARY_PRIMARY, OVERLAY]
  }
}
