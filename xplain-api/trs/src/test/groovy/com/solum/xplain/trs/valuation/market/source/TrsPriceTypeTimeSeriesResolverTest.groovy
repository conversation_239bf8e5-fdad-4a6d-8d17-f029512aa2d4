package com.solum.xplain.trs.valuation.market.source


import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import java.time.LocalDate
import spock.lang.Specification

class TrsPriceTypeTimeSeriesResolverTest extends Specification {

  static def DATE_1 = LocalDate.of(2021, 1, 1)
  static def DATE_2 = LocalDate.of(2021, 1, 2)
  static def DATE_3 = LocalDate.of(2021, 1, 3)

  def "should resolve time series for price type"() {
    setup:
    def trsMarketData =
      [
        new TrsBidAskDateValue(DATE_1, ValueBidAskType.BID, BigDecimal.valueOf(1.0)),
        new TrsBidAskDateValue(DATE_1, ValueBidAskType.ASK, BigDecimal.valueOf(3.0)),
        new TrsBidAskDateValue(DATE_2, ValueBidAskType.BID, BigDecimal.valueOf(4.0)),
        new TrsBidAskDateValue(DATE_2, ValueBidAskType.ASK, BigDecimal.valueOf(5.0)),
        new TrsBidAskDateValue(DATE_3, ValueBidAskType.BID, BigDecimal.valueOf(6.0)),
        new TrsBidAskDateValue(DATE_3, ValueBidAskType.ASK, BigDecimal.valueOf(7.0)),
      ]

    when:
    def result =
      TrsPriceTypeTimeSeriesResolver.timeSeriesForPriceType(trsMarketData, priceType)

    then:
    result.isPresent()
    result.get().get(DATE_1) == OptionalDouble.of(date1Value)
    result.get().get(DATE_2) == OptionalDouble.of(date2Value)
    result.get().get(DATE_3) == OptionalDouble.of(date3Value)

    where:
    priceType                     | date1Value  | date2Value  | date3Value
    InstrumentPriceType.BID_PRICE | 1.0         | 4.0         | 6.0
    InstrumentPriceType.ASK_PRICE | 3.0         | 5.0         | 7.0
    InstrumentPriceType.MID_PRICE | 2.0         | 4.5         | 6.5
  }

  def "should skip dates where value with required price type is not found"() {
    setup:
    def trsMarketData =
      [
        new TrsBidAskDateValue(DATE_1, ValueBidAskType.BID, BigDecimal.valueOf(1.0)),
        new TrsBidAskDateValue(DATE_2, ValueBidAskType.ASK, BigDecimal.valueOf(5.0)),
        new TrsBidAskDateValue(DATE_3, ValueBidAskType.BID, BigDecimal.valueOf(7.0)),
      ]

    when:
    def result =
      TrsPriceTypeTimeSeriesResolver.timeSeriesForPriceType(trsMarketData, InstrumentPriceType.BID_PRICE)

    then:
    result.isPresent()
    result.get().get(DATE_1) == OptionalDouble.of(1.0)
    result.get().get(DATE_2) == OptionalDouble.empty()
    result.get().get(DATE_3) == OptionalDouble.of(7.0)
  }
}
