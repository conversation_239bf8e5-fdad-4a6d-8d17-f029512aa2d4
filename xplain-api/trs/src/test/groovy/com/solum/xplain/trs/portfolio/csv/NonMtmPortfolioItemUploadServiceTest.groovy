package com.solum.xplain.trs.portfolio.csv

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE_DELETE
import static com.solum.xplain.core.common.csv.ParsingMode.LENIENT
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.error.Error.DUPLICATE_ENTRY
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvLoader
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate
import com.solum.xplain.core.common.versions.embedded.update.ImportUpdatesResolver
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.core.portfolio.form.TradeImportOptions
import com.solum.xplain.core.portfolio.trade.TradeValue
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider
import com.solum.xplain.trs.portfolio.csv.form.NonMtmPortfolioItemCsvForm
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemWriteRepository
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.stream.Stream
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class NonMtmPortfolioItemUploadServiceTest extends Specification {
  def static PORTFOLIO_ID = ObjectId.get().toHexString()
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static FILE_CONTENT = [] as byte[]

  def authenticationContext = Mock(AuthenticationContext)
  def auditEntryService = Mock(AuditEntryService)
  def nonMtmPortfolioRepository = Mock(NonMtmPortfolioRepository)
  def nonMtmPortfolioItemWriteRepository = Mock(NonMtmPortfolioItemWriteRepository)
  def nonMtmPortfolioTeamFilterProvider = Mock(NonMtmPortfolioTeamFilterProvider)
  def trsTradeCsvLoaderFactory = Mock(TrsTradeCsvLoaderFactory)
  def csvLoader = Mock(CsvLoader)
  def eventPublisher = Mock(ApplicationEventPublisher)
  NonMtmPortfolioItemUploadService upload

  NonMtmPortfolioItemCsvForm newTradeForm
  EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity> trade
  NonMtmPortfolioItemCsvForm duplicateTradeActiveForm
  NonMtmPortfolioItemCsvForm duplicateTradeArchivedForm
  EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity> duplicateTradeActive
  EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity> duplicateTradeArchived
  EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity> missingTrade

  def setup() {
    upload = new NonMtmPortfolioItemUploadService(authenticationContext, auditEntryService, nonMtmPortfolioRepository, nonMtmPortfolioItemWriteRepository, nonMtmPortfolioTeamFilterProvider, trsTradeCsvLoaderFactory, eventPublisher)
    trade = portfolioItem()

    newTradeForm = new NonMtmPortfolioItemCsvForm(trade.getEntity().uniqueKey(), tradeValue(trade.getEntity().getSemanticId()))
    duplicateTradeActive = portfolioItem(PORTFOLIO_ID, "EXT1")
    duplicateTradeActiveForm = new NonMtmPortfolioItemCsvForm(duplicateTradeActive.getEntity().uniqueKey(), tradeValue(duplicateTradeActive.getEntity().getSemanticId()))
    duplicateTradeArchived = portfolioItem(PORTFOLIO_ID, "EXT2", State.ARCHIVED)
    duplicateTradeArchivedForm = new NonMtmPortfolioItemCsvForm(duplicateTradeArchived.getEntity().uniqueKey(), tradeValue(duplicateTradeArchived.getEntity().getSemanticId()))
    missingTrade = portfolioItem(PORTFOLIO_ID, "EXT_MISSING")
  }

  def "should return parse errors"() {
    setup:
    def error = new ErrorItem(PARSING_ERROR, "Parsing error")
    1 * csvLoader.parse(_, STRICT) >> left([error])

    when:
    def importOptions = new TradeImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    errors[0].description == "Parsing error"
  }

  def "should return DUPLICATE_ENTRY for DUPLICATE trade when ERROR"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(trade)

    def options = new TradeImportOptions(STATE_DATE, ERROR, STRICT, null, null, null, null, null, false, null)

    when:
    def result = upload.validate(csvLoader, FILE_CONTENT, options)

    then:
    result.isRight()
    def errors = result.getOrNull().errors
    errors.size() == 1
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "At least one entry already exists"
  }

  def "should return DUPLICATE_ENTRY and FUTURE_VERSION_EXISTS for DUPLICATE trade when ERROR"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newTradeForm]))

    def tradeWithFutureVersion = portfolioItem()
    tradeWithFutureVersion.getEntity().getVersions().add(new EmbeddedVersion(
      validFrom: FUTURE_VERSION_DATE,
      state: State.ACTIVE,
      value: new TrsTradeValue()))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(tradeWithFutureVersion)

    def options = new TradeImportOptions(STATE_DATE, ERROR, STRICT, null, null, null, null, null, false, null)

    when:
    def result = upload.validate(csvLoader, FILE_CONTENT, options)

    then:
    result.isRight()
    def errors = result.getOrNull().errors
    errors.size() == 2
    errors[0].reason == FUTURE_VERSION_EXISTS
    errors[0].description == "At least one entry has future versions(s)"
    errors[1].reason == DUPLICATE_ENTRY
    errors[1].description == "At least one entry already exists"
  }

  def "should return NEW_VERSION_VIABLE for NEW trade when ERROR"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.empty()

    def options = new TradeImportOptions(STATE_DATE, ERROR, STRICT, null, null, null, null, null, false, null)

    when:
    def result = upload.validate(csvLoader, FILE_CONTENT, options)

    then:
    result.isRight()
    def errors = result.getOrNull().errors
    errors.size() == 1
    errors[0].reason == NEW_VERSION_VIABLE
    errors[0].description == "New version is viable for at least one entry"
  }

  def "should return NEW_VERSION_VIABLE for NEW trade when ERROR for all trades"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.empty()

    def options = new TradeImportOptions(STATE_DATE, ERROR, STRICT, null, null, null, null, null, true, "refId")

    when:
    def result = upload.validate(csvLoader, FILE_CONTENT, options)

    then:
    result.isRight()
    def errors = result.getOrNull().errors
    errors.size() == 1
    errors[0].reason == NEW_VERSION_VIABLE
    errors[0].description == "New version is viable for at least one entry"
  }

  def "should create NEW trade when APPEND"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeArchived, duplicateTradeActive)

    def importOptions = new TradeImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0

    1 * auditEntryService.newEntryWithLogs(_, [])
    1 * eventPublisher.publishEvent(_)
    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }

  def "should create NEW trade when APPEND and log lenient errors"() {
    setup:
    1 * csvLoader.parse(_, LENIENT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm], [PARSING_ERROR.entity("ERR")]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeArchived, duplicateTradeActive)

    def importOptions = new TradeImportOptions(STATE_DATE, APPEND, LENIENT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0

    1 * auditEntryService.newEntryWithLogs(AuditEntry.of("nonMtmPortoflioItem", "Finished TrsTrade import with 0 errors, 1 warnings and 1 changes"), [PARSING_ERROR.entity("ERR")])
    1 * eventPublisher.publishEvent(_)
    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }


  def "should create NEW trade when APPEND for all trades"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeArchived, duplicateTradeActive)

    def importOptions = new TradeImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, true, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0

    1 * auditEntryService.newEntryWithLogs(_, _)
    1 * eventPublisher.publishEvent(_)
    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)


    then:
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }

  def "should return error when portfolio was modified"() {
    setup:
    def duplicateTrade = portfolioItem(PORTFOLIO_ID, "EXT1")
    def newTrade = portfolioItem()
    def duplicateTradeForm = new NonMtmPortfolioItemCsvForm(duplicateTrade.getEntity().uniqueKey(), Mock(TrsTradeValue))
    def newTradeForm = new NonMtmPortfolioItemCsvForm(newTrade.getEntity().uniqueKey(), Mock(TrsTradeValue))
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeForm, newTradeForm]))
    0 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTrade)
    1 * nonMtmPortfolioRepository.hasChanges([PORTFOLIO_ID] as Set<String>, LocalDateTime.MIN) >> true

    def importOptions = new TradeImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, LocalDateTime.MIN, null, null)

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_)
    0 * auditEntryService.newEntryWithLogs(_, _)
    0 * eventPublisher.publishEvent(_)
    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == Error.OPERATION_NOT_ALLOWED
    errors[0].description == "Non-Mtm Portfolios have been modified"
  }

  def "should create NEW trade and archive MISSING when APPEND_DELETE"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeActive, duplicateTradeArchived, missingTrade)

    def importOptions = new TradeImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions && it.getEntitiesToArchive() == [missingTrade]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 2

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0

    1 * auditEntryService.newEntryWithLogs(_, _)
    1 * eventPublisher.publishEvent(_)

    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }

  def "should create NEW trade and replace DUPLICATE when REPLACE"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeActive, duplicateTradeArchived)

    def importOptions = new TradeImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions && it.getEntitiesToReplace() == [(duplicateTradeActive): duplicateTradeActiveForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 2

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0

    1 * auditEntryService.newEntryWithLogs(_, _) >> right(entityId("id"))
    1 * eventPublisher.publishEvent(_)

    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }

  def "should create NEW, replace DUPLICATE and archive MISSING when REPLACE_DELETE"() {
    setup:
    1 * csvLoader.parse(_, STRICT) >> right(csvResult([duplicateTradeActiveForm, duplicateTradeArchivedForm, newTradeForm]))
    1 * nonMtmPortfolioItemWriteRepository.streamEntitiesForUpdate(_, STATE_DATE) >> Stream.of(duplicateTradeActive, duplicateTradeArchived, missingTrade)

    def importOptions = new TradeImportOptions(STATE_DATE, REPLACE_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null, null, null)

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions &&
        it.getEntitiesToAppend().size() == 1 &&
        it.getEntitiesToAppend().entrySet()[0].getKey().getSemanticId() == newTradeForm.getUniqueKey().uniqueId() &&
        it.getEntitiesToAppend().entrySet()[0].getValue() == newTradeForm.toVersionValue() &&
        it.getEntitiesToReplace().size() == 1 &&
        it.getEntitiesToReplace() == [(duplicateTradeArchived): duplicateTradeArchivedForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 1

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions && it.getEntitiesToArchive() == [missingTrade]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 2

    1 * nonMtmPortfolioItemWriteRepository.updateFromImport({
      it.getOptions() == importOptions && it.getEntitiesToReplace() == [(duplicateTradeActive): duplicateTradeActiveForm.toVersionValue()]
    } as ImportUpdatesResolver<TradeValue, PortfolioItemEntity>) >> 2

    0 * nonMtmPortfolioItemWriteRepository.updateFromImport(_) >> 0


    1 * auditEntryService.newEntryWithLogs(_, _) >> right(entityId("id"))

    when:
    def result = upload.importTrades(csvLoader, FILE_CONTENT, importOptions)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId(PORTFOLIO_ID)
  }

  def csvResult(List<NonMtmPortfolioItemCsvForm> items, List<ErrorItem> errors = []) {
    new CsvParserResult(items, errors)
  }

  def portfolioItem(portfolioId = PORTFOLIO_ID,
    externalId = "EXT",
    state = State.ACTIVE) {

    def version = new EmbeddedVersion(
      validFrom: STATE_DATE,
      state: state,
      value: new TradeValue())
    def entity = new NonMtmPortfolioItemEntity(semanticId: portfolioId + externalId, versions: [version], nonMtmPortfolioId: portfolioId, externalTrsTradeId: externalId)
    new EntityForUpdate<>(entity, version)
  }

  def tradeValue(String id) {
    new TrsTradeValue(description: id)
  }
}
