package com.solum.xplain.trs.index.csv

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT

import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.trs.index.sampledata.TrsIndexSample
import com.solum.xplain.trs.index.view.CreateTrsIndexForm
import com.solum.xplain.trs.value.TrsType
import spock.lang.Specification
import spock.lang.Unroll

class TrsIndexCsvLoaderTest extends Specification implements TrsIndexSample {

  def testObject = new TrsIndexCsvLoader()

  def "when correct TRS indexes file is given then create trs index forms are returned"() {
    setup:
    def forms = [
      sampleCreateTrsIndexForm(name: "ONE", type: TrsType.BOND, currency: "USD", description: null),
      sampleCreateTrsIndexForm(name: "TWO", type: TrsType.SHARE, currency: "EUR", description: "second")
    ]
    def file = """
      Index Name,Index Type,Currency,Description
      One,Bond,USD,
      Two,Share,EUR,second
    """.stripIndent().getBytes()

    when:
    def result = testObject.parse(file, STRICT)

    then:
    result.isRight()
    def csvResult = result.right().getOrNull() as CsvParserResult<CreateTrsIndexForm>
    forms.containsAll(csvResult.getParsedLines())
  }

  @Unroll
  def "when incorrect csv file is given then an error is returned: #validationError"() {
    when:
    def result = testObject.parse(file, STRICT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.get(0).description.contains(validationError)

    where:
    file                                    | validationError
    sampleTrsIndexCsv(name: "")             | "Error parsing line 3: No value was found for 'Index Name'"
    sampleTrsIndexCsv(type: null)           | "Error parsing line 3: Unsupported value: null for field Index Type. Supported values: [Bond, Share]"
    sampleTrsIndexCsv(type: "")             | "Error parsing line 3: No value was found for 'Index Type'"
    sampleTrsIndexCsv(currency: "")         | "Error parsing line 3: No value was found for 'Currency'"
    sampleTrsIndexCsv(currency: "LTL")      | "Error parsing line 3: Unsupported value: LTL for field Currency. Supported values: [AED, ARS, AUD, BRL, CAD,"
    sampleTrsIndexCsv(name: "invalid name") | "Error parsing line 3: Index Name is invalid. Value can't contain whitespaces or lowercase characters."
  }
}
