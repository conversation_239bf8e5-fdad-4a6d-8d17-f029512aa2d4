package com.solum.xplain.trs.valuation.csv.accrued

import com.solum.xplain.trs.valuation.value.NonMtmAccruedLegType
import spock.lang.Specification

class NonMtmAccruedPeriodCsvMapperFactoryTest extends Specification {
  def FACTORY = new NonMtmAccruedPeriodCsvMapperFactory()

  def "should return correct csv mapper"() {
    expect:
    FACTORY.getMapper(type) == mapper

    where:
    type                             | mapper
    NonMtmAccruedLegType.FUNDING     | NonMtmFundingAccruedPeriodCsvMapper.INSTANCE
    NonMtmAccruedLegType.PERFORMANCE | NonMtmPerformanceAccruedPeriodCsvMapper.INSTANCE
  }
}
