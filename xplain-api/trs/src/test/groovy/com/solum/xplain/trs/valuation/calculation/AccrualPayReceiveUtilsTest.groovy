package com.solum.xplain.trs.valuation.calculation

import static com.opengamma.strata.product.common.PayReceive.PAY
import static com.opengamma.strata.product.common.PayReceive.RECEIVE

import spock.lang.Specification

class AccrualPayReceiveUtilsTest extends Specification {
  def "should normalize accrual value by pay/receive sign"() {
    expect:
    AccrualPayReceiveUtils.normalizeAccrualPayReceive(payReceive, value) == expected

    where:
    payReceive | value | expected
    PAY        | 1.0   | -1.0
    PAY        | -1.0  | 1.0
    RECEIVE    | 1.0   | 1.0
    RECEIVE    | -1.0  | -1.0
  }
}
