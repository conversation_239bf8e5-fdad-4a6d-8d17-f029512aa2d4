package com.solum.xplain.trs.valuation.calculation.period

import static java.time.LocalDate.now

import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.error.Error
import com.solum.xplain.trs.valuation.value.PeriodStatus
import spock.lang.Specification

class CalculatedAccruedPeriodsTest extends Specification {

  def "should return current period"() {
    setup:
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS)
    def p2 = new TestAccruedPeriod(status: PeriodStatus.CURRENT)

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p1, p2], [])

    expect:
    periods.currentPeriod() == Optional.of(p2)
  }

  def "should return previous period"() {
    setup:
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS)
    def p2 = new TestAccruedPeriod(status: PeriodStatus.CURRENT)

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p1, p2], [])

    expect:
    periods.previousPeriod() == Optional.of(p1)
  }

  def "should return all unpaid accrued"() {
    setup:
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS, accrued: BigDecimal.ONE, paid: false)
    def p2 = new TestAccruedPeriod(status: PeriodStatus.CURRENT, accrued: BigDecimal.ONE, paid: false)
    def p3 = new TestAccruedPeriod(status: PeriodStatus.PAST, accrued: BigDecimal.ONE, paid: true)

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p1, p2, p3], [])

    expect:
    periods.accruedValue() == 2.0
  }

  def "should return t zero cash flow - previous paid period"() {
    setup:
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS, accrued: BigDecimal.TEN, paid: true, paymentDate: now().minusDays(1))
    def p2 = new TestAccruedPeriod(status: PeriodStatus.CURRENT, accrued: BigDecimal.ONE, paid: false, paymentDate: now())

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p1, p2], [])

    expect:
    periods.tZeroCashFlow(now()) == BigDecimal.TEN
  }

  def "should return t zero cash flow - all periods unpaid"() {
    setup:
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS, accrued: BigDecimal.TEN, paid: false, paymentDate: now().minusDays(1))
    def p2 = new TestAccruedPeriod(status: PeriodStatus.CURRENT, accrued: BigDecimal.ONE, paid: false, paymentDate: now())

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p1, p2], [])

    expect:
    periods.tZeroCashFlow(now()) == BigDecimal.ZERO
  }

  def "should return t zero cash flow - previous period unpaid"() {
    setup:
    def p0 = new TestAccruedPeriod(status: PeriodStatus.PAST, accrued: 5.0, paid: true, paymentDate: now().minusDays(3))
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PAST, accrued: BigDecimal.TEN, paid: true, paymentDate: now().minusDays(2))
    def p2 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS, accrued: BigDecimal.ONE, paid: false, paymentDate: now().minusDays(1))
    def p3 = new TestAccruedPeriod(status: PeriodStatus.CURRENT, accrued: BigDecimal.ONE, paid: false, paymentDate: now())

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p0, p1, p2, p3], [])

    expect:
    periods.tZeroCashFlow(now()) == BigDecimal.TEN
  }

  def "should return warnings"() {
    setup:
    def p0 = new TestAccruedPeriod(status: PeriodStatus.PAST, accrued: 5.0, paid: true, paymentDate: now().minusDays(3), warnings: [Error.CALCULATION_WARNING.entity("IGNORE")])
    def p1 = new TestAccruedPeriod(status: PeriodStatus.PAST, accrued: BigDecimal.TEN, paid: true, paymentDate: now().minusDays(2))
    def p2 = new TestAccruedPeriod(status: PeriodStatus.PREVIOUS, accrued: BigDecimal.ONE, paid: false, paymentDate: now().minusDays(1))
    def p3 = new TestAccruedPeriod(status: PeriodStatus.CURRENT, accrued: BigDecimal.ONE, paid: false, paymentDate: now(), warnings: [Error.CALCULATION_WARNING.entity("WARNING")])

    def periods = new CalculatedAccruedPeriods(PayReceive.RECEIVE, [p0, p1, p2, p3], [Error.CALCULATION_WARNING.entity("WARNING2")])

    expect:
    var warnings = periods.warnings()
    warnings.size() == 2
    warnings.contains(Error.CALCULATION_WARNING.entity("WARNING2"))
    warnings.contains(Error.CALCULATION_WARNING.entity("WARNING"))
  }

  static class TestAccruedPeriod extends AccruedPeriod {
    @Override
    BigDecimal accruedTotal() {
      return getAccrued()
    }
  }
}
