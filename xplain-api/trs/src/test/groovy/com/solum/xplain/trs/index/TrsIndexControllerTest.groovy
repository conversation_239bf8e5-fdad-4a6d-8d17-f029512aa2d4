package com.solum.xplain.trs.index

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.trs.helper.MockMvcConfiguration
import com.solum.xplain.trs.index.csv.TrsIndexExportService
import com.solum.xplain.trs.index.csv.TrsIndexUploadService
import com.solum.xplain.trs.index.sampledata.TrsIndexSample
import com.solum.xplain.trs.index.value.TrsIndex
import com.solum.xplain.trs.index.view.TrsIndexView
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@MockMvcConfiguration
@WebMvcTest(controllers = [TrsIndexController])
class TrsIndexControllerTest extends Specification implements TrsIndexSample {

  private static EXPORTED_CSV = FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")

  private final String TRS_INDEX_API = '/trs-indexes'

  @Autowired
  MockMvc mockMvc

  @Autowired
  ObjectMapper mapper

  @SpringBean
  TrsIndexService service = Mock()

  @SpringBean
  TrsIndexExportService exportService = Mock()

  @SpringBean
  TrsIndexUploadService uploadService = Mock()

  @SpringBean
  TrsIndexRepository indexRepository = Mock()

  def "should get all TRS indexes"() {
    setup:
    def items = [sampleTrsIndexView(), sampleTrsIndexView()]

    when:
    def results = mockMvc.perform(get(TRS_INDEX_API)
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.findAll(TableFilter.emptyTableFilter(),
      Sort.by(TrsIndexView.Fields.name,
      TrsIndexView.Fields.type,
      TrsIndexView.Fields.currency), false) >> items

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(items)))
  }

  def "should get one TRS index"() {
    setup:
    def entityId = "000000000000000000000001"
    def view = sampleTrsIndexView()

    when:
    def results = mockMvc.perform(get("${TRS_INDEX_API}/${entityId}")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.trsIndexView(entityId) >> right(view)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(view)))
  }

  @Unroll
  def "when invalid TRS index form is given then validation error is returned: #response"() {
    when:
    indexRepository.findByName("DUPLICATE") >> right(new TrsIndex())
    indexRepository.findByName(_ as String) >> left(OBJECT_NOT_FOUND.entity())

    def results = mockMvc.perform(post(TRS_INDEX_API)
      .with(csrf())
      .content(toJson(createTrsIndexForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.create(createTrsIndexForm)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    createTrsIndexForm                             | response
    sampleCreateTrsIndexForm(name: null)           | "NotEmpty.createTrsIndexForm.name"
    sampleCreateTrsIndexForm(name: "")             | "NotEmpty.createTrsIndexForm.name"
    sampleCreateTrsIndexForm(name: "invalid name") | "Value can't contain whitespaces or lowercase characters."
    sampleCreateTrsIndexForm(name: "DUPLICATE")    | "TRS Index name must be unique"
    sampleCreateTrsIndexForm(type: null)           | "NotNull.createTrsIndexForm.type"
    sampleCreateTrsIndexForm(currency: null)       | "NotEmpty.createTrsIndexForm.currency"
    sampleCreateTrsIndexForm(currency: "LTL")      | "ValidStringSet.createTrsIndexForm.currency"
  }

  def "should create successfully TRS index"() {
    setup:
    def form = sampleCreateTrsIndexForm()
    def trsIndexEntityId = entityId("000000000000000000000001")
    indexRepository.findByName(_ as String) >> left(OBJECT_NOT_FOUND.entity())
    when:
    def results = mockMvc.perform(post(TRS_INDEX_API)
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.create(form) >> right(trsIndexEntityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(trsIndexEntityId)))
  }

  @Unroll
  def "when invalid TRS index form (#updateTrsIndexForm) is given then validation error is returned"() {
    setup:
    def entityId = entityId("000000000000000000000001")

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/${entityId.id}")
      .with(csrf())
      .content(toJson(updateTrsIndexForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.update(entityId.id, updateTrsIndexForm)

    and:
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(response) >= 0

    where:
    updateTrsIndexForm                        | response
    sampleUpdateTrsIndexForm(type: null)      | "NotNull.updateTrsIndexForm.type"
    sampleUpdateTrsIndexForm(currency: "LTL") | "ValidStringSet.updateTrsIndexForm.currency"
  }

  def "when invalid TRS index id is given then validation error is returned"() {
    setup:
    def invalidEntityId = "1"
    def updateTrsIndexForm = sampleUpdateTrsIndexForm()
    def errorItem = new ErrorItem(Error.VALIDATION_ERROR, "update.id: Invalid object id")

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/${invalidEntityId}")
      .with(csrf())
      .content(toJson(updateTrsIndexForm))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.update(_ as EntityId, updateTrsIndexForm)

    and:
    results.andExpect(status().is4xxClientError())
      .andExpect(content().json(mapper.writeValueAsString(errorItem)))
  }

  def "should update successfully TRS index"() {
    setup:
    def form = sampleUpdateTrsIndexForm()
    def entityId = entityId("000000000000000000000001")

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/${entityId.id}")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.update(entityId.id, form) >> right(entityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(entityId)))
  }

  def "when invalid TRS index id is given then archive endpoint returns validation error"() {
    setup:
    def invalidEntityId = "1"
    def errorItem = new ErrorItem(Error.VALIDATION_ERROR, "archive.id: Invalid object id")

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/${invalidEntityId}/archive")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    0 * service.archive(_ as EntityId)

    and:
    results.andExpect(status().is4xxClientError())
      .andExpect(content().json(mapper.writeValueAsString(errorItem)))
  }

  def "should archive successfully TRS index"() {
    setup:
    def entityId = entityId("000000000000000000000001")

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/${entityId.id}/archive")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.archive(entityId.id) >> right(entityId)

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(entityId)))
  }

  def "should archive successfully all TRS indexes"() {
    setup:
    def entityIds = [entityId("000000000000000000000001")]

    when:
    def results = mockMvc.perform(put("${TRS_INDEX_API}/archive-all")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))

    then:
    1 * service.archiveAll(TableFilter.emptyTableFilter()) >> entityIds

    and:
    results.andExpect(status().is2xxSuccessful())
      .andExpect(content().json(mapper.writeValueAsString(entityIds)))
  }

  def "should invoke TRS indexes csv export"() {
    setup:
    def sort = Sort.by(TrsIndexView.Fields.name, TrsIndexView.Fields.type, TrsIndexView.Fields.currency)
    def tableFilter = TableFilter.emptyTableFilter()
    def archived = false
    def stateDate = LocalDate.now()

    when:
    def results = mockMvc.perform(get("${TRS_INDEX_API}/indexes-csv")
      .queryParam("stateDate", stateDate.toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))

    then:
    1 * exportService.exportIndices(tableFilter, sort, archived, stateDate) >> EXPORTED_CSV

    and:
    results.andExpect(status().is2xxSuccessful())
  }

  @Unroll
  def "should invoke TRS indices csv upload and get #code response code"() {
    setup:
    def action = DuplicateAction.REPLACE
    def csv = EXPORTED_CSV.getBytes().getByteArray()

    when:
    def results = mockMvc.perform(multipart("${TRS_INDEX_API}/upload")
      .file("file", csv)
      .param("duplicates", action.name())
      .with(csrf()))

    then:
    1 * uploadService.upload(action, STRICT, csv) >> uploadResult

    and:
    with(results.andReturn().getResponse()) {
      getStatus() == code
    }

    where:
    uploadResult                                  | code
    right([entityId("000000000000000000000001")]) | 200
    left([OBJECT_NOT_FOUND.entity()])             | 422
  }

  def "should invoke TRS indexes MDK definitions csv export"() {
    setup:
    def sort = Sort.unsorted()
    def tableFilter = TableFilter.emptyTableFilter()
    def archived = false
    def stateDate = LocalDate.now()

    when:
    def results = mockMvc.perform(get("${TRS_INDEX_API}/mdk-definitions/csv")
      .queryParam("stateDate", stateDate.toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))

    then:
    1 * exportService.exportMdkDefinitions(tableFilter, sort, archived, stateDate) >> EXPORTED_CSV

    and:
    results.andExpect(status().is2xxSuccessful())
  }
}
