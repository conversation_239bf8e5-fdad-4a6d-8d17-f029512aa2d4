package com.solum.xplain.trs.valuation.market.source;

import static com.solum.xplain.trs.valuation.market.source.TrsPriceTypeTimeSeriesResolver.timeSeriesForPriceType;
import static com.solum.xplain.trs.value.NonMtmInstrumentType.TRS_DIVIDEND;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.google.common.collect.ImmutableMap;
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.mdvalue.MarketDataValueRepository;
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView;
import com.solum.xplain.trs.valuation.market.NonMtmMarketStateKey;
import com.solum.xplain.trs.valuation.market.filters.NonMtmMarketDataProviderFilter;
import com.solum.xplain.trs.value.TrsAssetClassGroup;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class RawNonMtmMarketDataSource implements NonMtmMarketDataSource {

  private final MarketDataKeyRepository keyRepository;
  private final MarketDataValueRepository valueRepository;

  public RawNonMtmMarketDataSource(
      MarketDataKeyRepository keyRepository, MarketDataValueRepository valueRepository) {
    this.keyRepository = keyRepository;
    this.valueRepository = valueRepository;
  }

  @Override
  public TrsMarketData provideCalculationMarketData(
      NonMtmMarketStateKey stateKey, NonMtmMarketDataProviderFilter valueFilter) {
    var keyResolver = keyRepository.keyResolver(stateKey.getStateDate(), TrsAssetClassGroup.TRS);
    var values =
        valueRepository.getLatestValueViews(
            stateKey.getTrsMarketDataGroupId(), stateKey.getStateDate());
    var resolvedValues = keyResolver.resolvedValues(values);
    var unresolvedMarketDataTickers =
        keyResolver.filterUnresolvableValues(values).stream()
            .map(MarketDataValueFlatView::getTicker)
            .toList();
    var marketDataKeys = keyResolver.marketDataKeysHasAnyProvider();
    return buildMarketData(
        resolvedValues,
        valueFilter,
        unresolvedMarketDataTickers,
        marketDataKeys,
        stateKey.getInstrumentPriceType());
  }

  private TrsMarketData buildMarketData(
      List<ResolvedMarketDataValueView> values,
      NonMtmMarketDataProviderFilter valueFilter,
      List<String> unresolvedValues,
      Map<String, Boolean> marketDataKeys,
      InstrumentPriceType priceType) {
    var trsValues = ImmutableMap.<String, LocalDateDoubleTimeSeries>builder();
    var dividendValues = ImmutableMap.<String, LocalDateDoubleTimeSeries>builder();

    values.forEach(
        view -> {
          var optTs = doubleTimeSeries(view, valueFilter, priceType);

          optTs.ifPresent(
              ts -> {
                if (isDividendKey(view)) {
                  dividendValues.put(view.getKey(), ts);
                } else {
                  trsValues.put(view.getKey(), ts);
                }
              });
        });

    return TrsMarketData.newOf(
        trsValues.build(), dividendValues.build(), unresolvedValues, marketDataKeys);
  }

  private boolean isDividendKey(ResolvedMarketDataValueView view) {
    return StringUtils.equals(view.getInstrumentType(), TRS_DIVIDEND.name());
  }

  private Optional<LocalDateDoubleTimeSeries> doubleTimeSeries(
      ResolvedMarketDataValueView view,
      NonMtmMarketDataProviderFilter valueFilter,
      InstrumentPriceType priceType) {

    return view.getValues().stream()
        .filter(v -> valueFilter.isRequiredProvider(view.getInstrumentType(), v.getProvider()))
        .collect(collectingAndThen(toList(), l -> toTimeSeries(l, priceType)));
  }

  public static Optional<LocalDateDoubleTimeSeries> toTimeSeries(
      List<MarketDataValueFlatView> values, InstrumentPriceType priceType) {

    List<TrsBidAskDateValue> trsBidAskDateValueList =
        values.stream().map(TrsBidAskDateValue::ofMarketDataValueFlatView).toList();

    return timeSeriesForPriceType(trsBidAskDateValueList, priceType);
  }
}
