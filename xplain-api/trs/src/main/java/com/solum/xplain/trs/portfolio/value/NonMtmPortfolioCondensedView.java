package com.solum.xplain.trs.portfolio.value;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class NonMtmPortfolioCondensedView {
  @ConfigurableViewIgnore private String id;

  @ConfigurableViewQuery(sortable = true)
  private String externalPortfolioId;

  @ConfigurableViewIgnore private String companyId;

  @ConfigurableViewQuery(sortable = true)
  private String externalCompanyId;

  @ConfigurableViewIgnore private String entityId;

  @ConfigurableViewQuery(sortable = true)
  private String externalEntityId;
}
