package com.solum.xplain.trs.index;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.TRS_INDICES_LOCK_ID;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_MODIFY_TRS_INDICES;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_VIEW_TRS_INDICES;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.index.csv.TrsIndexExportService;
import com.solum.xplain.trs.index.csv.TrsIndexUploadService;
import com.solum.xplain.trs.index.view.CreateTrsIndexForm;
import com.solum.xplain.trs.index.view.TrsIndexView;
import com.solum.xplain.trs.index.view.UpdateTrsIndexForm;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RestController
@RequestMapping("/trs-indexes")
@AllArgsConstructor
public class TrsIndexController {

  private final TrsIndexService service;
  private final TrsIndexExportService exportService;
  private final TrsIndexUploadService uploadService;

  @Operation(summary = "Get all TRS indexes")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_TRS_INDICES)
  public List<TrsIndexView> findAll(
      @RequestParam(required = false) boolean archived,
      @ParameterObject TableFilter tableFilter,
      @ParameterObject
          @SortDefault(
              sort = {
                TrsIndexView.Fields.name,
                TrsIndexView.Fields.type,
                TrsIndexView.Fields.currency
              })
          Sort sort) {
    return service.findAll(tableFilter, sort, archived);
  }

  @Operation(summary = "Get TRS index")
  @CommonErrors
  @GetMapping("/{id}")
  @PreAuthorize(AUTHORITY_VIEW_TRS_INDICES)
  public ResponseEntity<TrsIndexView> findOne(@PathVariable String id) {
    return eitherErrorItemResponse(service.trsIndexView(id));
  }

  @Operation(summary = "Create TRS index")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_TRS_INDICES)
  @RequireLock(name = TRS_INDICES_LOCK_ID)
  public ResponseEntity<EntityId> create(@Valid @RequestBody CreateTrsIndexForm form) {
    return eitherErrorItemResponse(service.create(form));
  }

  @Operation(summary = "Get all TRS indexes in csv format")
  @SortedFiltered
  @CommonErrors
  @GetMapping("/indexes-csv")
  @PreAuthorize(AUTHORITY_VIEW_TRS_INDICES)
  public ResponseEntity<ByteArrayResource> export(
      @RequestParam(required = false, defaultValue = "false") boolean archived,
      @RequestParam LocalDate stateDate,
      @ParameterObject TableFilter tableFilter,
      @ParameterObject
          @SortDefault(
              sort = {
                TrsIndexView.Fields.name,
                TrsIndexView.Fields.type,
                TrsIndexView.Fields.currency
              })
          Sort sort) {
    return exportService.exportIndices(tableFilter, sort, archived, stateDate).toResponse();
  }

  @Operation(summary = "Get Market Data Key definitions for TRS indices in csv format")
  @SortedFiltered
  @CommonErrors
  @GetMapping("/mdk-definitions/csv")
  @PreAuthorize(AUTHORITY_VIEW_TRS_INDICES)
  public ResponseEntity<ByteArrayResource> exportMdkDefinitions(
      @RequestParam(required = false, defaultValue = "false") boolean archived,
      @RequestParam LocalDate stateDate,
      @ParameterObject TableFilter tableFilter,
      @ParameterObject Sort sort) {
    return exportService.exportMdkDefinitions(tableFilter, sort, archived, stateDate).toResponse();
  }

  @Operation(summary = "Upload TRS indexes CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRS_INDICES)
  @RequireLock(name = TRS_INDICES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadIndices(
      @RequestPart MultipartFile file,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode,
      @RequestParam DuplicateAction duplicates)
      throws IOException {
    return eitherErrorItemsResponse(uploadService.upload(duplicates, parsingMode, file.getBytes()));
  }

  @Operation(summary = "Update TRS index")
  @CommonErrors
  @PutMapping("/{id}")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_INDICES)
  @RequireLock(name = TRS_INDICES_LOCK_ID)
  public ResponseEntity<EntityId> update(
      @ValidObjectId @PathVariable String id, @Valid @RequestBody UpdateTrsIndexForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Archive TRS index")
  @CommonErrors
  @PutMapping("/{id}/archive")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_INDICES)
  @RequireLock(name = TRS_INDICES_LOCK_ID)
  public ResponseEntity<EntityId> archive(@ValidObjectId @PathVariable String id) {
    return eitherErrorItemResponse(service.archive(id));
  }

  @Operation(summary = "Archive all TRS indexes")
  @CommonErrors
  @PutMapping("/archive-all")
  @PreAuthorize(AUTHORITY_MODIFY_TRS_INDICES)
  @RequireLock(name = TRS_INDICES_LOCK_ID)
  public List<EntityId> archiveAll(@ParameterObject TableFilter tableFilter) {
    return service.archiveAll(tableFilter);
  }
}
