package com.solum.xplain.trs.portfolio.repository;

import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE_BATCH_1000;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.entityIdCriteria;
import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.latestItemsCriteria;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.CountResult;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.GroupRequestUtils;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionsSupport;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.VersionedTrsTrade;
import com.solum.xplain.trs.portfolio.form.SearchNonMtmPortfolioItemForm;
import io.atlassian.fugue.Either;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class NonMtmPortfolioItemRepository {

  private final MongoOperations mongoOperations;
  private final DateRangeVersionsSupport dateRangeVersionsSupport;
  private final ConversionService conversionService;

  private Criteria nonMtmPortfolioCriteria(String nonMtmPortfolioId) {
    return where(NonMtmPortfolioItem.Fields.portfolioId).is(nonMtmPortfolioId);
  }

  public Either<ErrorItem, NonMtmPortfolioItem> portfolioItemLatest(
      String nonMtmPortfolioId, String entityId, BitemporalDate stateDate) {
    return mongoOperations
        .query(NonMtmPortfolioItem.class)
        .matching(
            entityIdCriteria(
                nonMtmPortfolioCriteria(nonMtmPortfolioId), stateDate, active(), entityId))
        .first()
        .map(Either::<ErrorItem, NonMtmPortfolioItem>right)
        .orElseGet(() -> Either.left(OBJECT_NOT_FOUND.entity("Non-MTM Trade not found")));
  }

  public DateList futureVersions(String nonMtmPortfolioId, SearchNonMtmPortfolioItemForm form) {
    var futureVersionCriteria =
        nonMtmPortfolioCriteria(nonMtmPortfolioId)
            .andOperator(
                dateRangeVersionsSupport
                    .createFutureVersionsCriteria(form.getStateDate())
                    .and(VersionedTrsTrade.Fields.externalTradeId)
                    .is(form.getExternalTradeId()));
    return dateRangeVersionsSupport.futureVersions(
        futureVersionCriteria, NonMtmPortfolioItem.class);
  }

  public ScrollableEntry<NonMtmPortfolioItem> portfolioItems(
      String nonMtmPortfolioId,
      BitemporalDate stateDate,
      TableFilter tableFilter,
      VersionedEntityFilter versionedEntityFilter,
      ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    var itemsCriteria =
        latestItemsCriteria(
                nonMtmPortfolioCriteria(nonMtmPortfolioId), stateDate, versionedEntityFilter)
            .andOperator(
                tableFilter.criteria(NonMtmPortfolioItem.class, conversionService),
                groupRequest.criteria(NonMtmPortfolioItem.class, conversionService));

    var operations = ImmutableList.<AggregationOperation>builder();
    operations.add(match(itemsCriteria));
    operations.addAll(GroupRequestUtils.groupOperations(groupRequest));

    operations.addAll(
        new ScrollSortOperations(scrollRequest, DateRangeVersionedEntity.Fields.id)
            .withGroupRequest(groupRequest)
            .withLimitPlusOne()
            .build());

    var items =
        mongoOperations
            .aggregateAndReturn(NonMtmPortfolioItem.class)
            .by(
                newAggregation(NonMtmPortfolioItem.class, operations.build())
                    .withOptions(ALLOW_DISK_USE_BATCH_1000))
            .all()
            .getMappedResults();

    return ScrollableEntry.limitByPageSize(items, scrollRequest);
  }

  public boolean hasPortfolioItemByExternalTradeId(
      BitemporalDate stateDate, String portfolioId, String externalTradeId, String tradeEntityId) {
    return portfolioItemByExternalId(stateDate, portfolioId, externalTradeId)
        .filter(v -> !StringUtils.equals(tradeEntityId, v.getEntityId()))
        .isPresent();
  }

  private Optional<NonMtmPortfolioItem> portfolioItemByExternalId(
      BitemporalDate stateDate, String portfolioId, String externalTradeId) {
    var criteria =
        latestItemsCriteria(nonMtmPortfolioCriteria(portfolioId), stateDate, active())
            .and(VersionedTrsTrade.Fields.externalTradeId)
            .is(externalTradeId);

    return mongoOperations.query(NonMtmPortfolioItem.class).matching(criteria).first();
  }

  public int tradesCount(String nonMtmPortfolioId, BitemporalDate stateDate) {
    return Math.toIntExact(
        mongoOperations
            .query(NonMtmPortfolioItem.class)
            .matching(
                latestItemsCriteria(
                    nonMtmPortfolioCriteria(nonMtmPortfolioId), stateDate, active()))
            .count());
  }

  /**
   * Counts the number of trades in the specified portfolios at the given state date. If there are
   * no trades in a portfolio, it will be missing from the map.
   */
  public Map<String, Integer> portfoliosTradesCount(
      Collection<String> nonMtmPortfolioIds, BitemporalDate stateDate) {
    TypedAggregation<NonMtmPortfolioItem> aggregation =
        newAggregation(
            NonMtmPortfolioItem.class,
            match(
                latestItemsCriteria(
                    where(NonMtmPortfolioItem.Fields.portfolioId).in(nonMtmPortfolioIds),
                    stateDate,
                    active())),
            group(NonMtmPortfolioItem.Fields.portfolioId).count().as(CountResult.Fields.count));

    return mongoOperations
        .aggregateStream(aggregation, Document.class)
        .collect(
            toMap(
                doc -> doc.getString(UNDERSCORE_ID),
                doc -> doc.getInteger(CountResult.Fields.count)));
  }

  public List<NonMtmPortfolioItem> getVersions(String nonMtmPortfolioId, String tradeEntityId) {
    return mongoOperations
        .query(NonMtmPortfolioItem.class)
        .matching(
            nonMtmPortfolioCriteria(nonMtmPortfolioId)
                .and(DateRangeVersionedEntity.Fields.entityId)
                .is(tradeEntityId))
        .all();
  }

  public Stream<NonMtmPortfolioItem> portfolioItemsStream(
      String nonMtmPortfolioId, BitemporalDate stateDate) {
    var criteria =
        latestItemsCriteria(nonMtmPortfolioCriteria(nonMtmPortfolioId), stateDate, active());
    return mongoOperations.query(NonMtmPortfolioItem.class).matching(criteria).stream();
  }

  public Stream<NonMtmPortfolioItem> portfolioItemsStream(
      Collection<String> nonMtmPortfolioIds, BitemporalDate stateDate) {
    var criteria = where(NonMtmPortfolioItem.Fields.portfolioId).in(nonMtmPortfolioIds);
    return mongoOperations
        .query(NonMtmPortfolioItem.class)
        .matching(latestItemsCriteria(criteria, stateDate, active()))
        .stream();
  }

  public Stream<NonMtmPortfolioItem> portfolioItemStream(
      String nonMtmPortfolioId, BitemporalDate stateDate, Sort sort, TableFilter tableFilter) {
    var criteria = nonMtmPortfolioCriteria(nonMtmPortfolioId);
    var operations = new ArrayList<AggregationOperation>();
    operations.add(match(latestItemsCriteria(criteria, stateDate, active())));
    operations.add(match(tableFilter.criteria(NonMtmPortfolioItem.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    }

    return mongoOperations
        .aggregateAndReturn(NonMtmPortfolioItem.class)
        .by(
            newAggregation(NonMtmPortfolioItem.class, operations)
                .withOptions(ALLOW_DISK_USE_BATCH_1000))
        .stream();
  }
}
