package com.solum.xplain.trs.market;

import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.mdvalue.value.AssetGroupExportForm;
import com.solum.xplain.trs.value.TrsAssetClassGroup;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TrsAssetGroupExportForm extends AssetGroupExportForm {

  @Override
  public AssetGroup getAssetGroup() {
    return TrsAssetClassGroup.TRS;
  }
}
