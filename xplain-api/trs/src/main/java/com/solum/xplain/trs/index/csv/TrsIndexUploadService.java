package com.solum.xplain.trs.index.csv;

import static com.solum.xplain.core.common.CollectionUtils.join;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.trs.index.TrsIndexRepository;
import com.solum.xplain.trs.index.value.TrsIndex;
import com.solum.xplain.trs.index.view.CreateTrsIndexForm;
import com.solum.xplain.trs.index.view.UpdateTrsIndexForm;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TrsIndexUploadService extends LoggingImportService {

  private final TrsIndexCsvLoader trsIndexCsvLoader;
  private final TrsIndexRepository trsIndexRepository;

  public TrsIndexUploadService(
      AuditEntryService auditEntryService,
      TrsIndexCsvLoader trsIndexCsvLoader,
      TrsIndexRepository trsIndexRepository) {
    super(auditEntryService);
    this.trsIndexCsvLoader = trsIndexCsvLoader;
    this.trsIndexRepository = trsIndexRepository;
  }

  @Override
  protected String getCollection() {
    return TrsIndex.class.getName();
  }

  @Override
  protected String getObjectName() {
    return "Trs indices";
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> upload(
      DuplicateAction duplicateAction, ParsingMode parsingMode, byte[] file) {
    return trsIndexCsvLoader
        .parse(file, parsingMode)
        .map(indexes -> importIndexes(indexes, duplicateAction))
        .fold(
            err -> toErrorReturn(duplicateAction, err),
            result -> toReturn(duplicateAction, result));
  }

  private ImportResult importIndexes(
      CsvParserResult<CreateTrsIndexForm> parserResult, DuplicateAction action) {
    var forms = parserResult.getParsedLines();
    var importItems =
        ImportItems.<CreateTrsIndexForm, String, TrsIndex>builder()
            .existingActiveItems(activeIndexes())
            .existingItemToKeyFn(TrsIndex::getName)
            .importItems(forms)
            .importItemToKeyFn(CreateTrsIndexForm::getName)
            .build();

    var importLogs =
        switch (action) {
          case ERROR -> onError(importItems);
          case REPLACE_DELETE -> onReplaceDelete(importItems);
          case REPLACE -> onReplace(importItems);
          case APPEND_DELETE -> onAppendDelete(importItems);
          case APPEND -> onAppend(importItems);
        };
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private List<LogItem> onAppend(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    return append(importItems);
  }

  private List<LogItem> onAppendDelete(
      ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var appendResult = append(importItems);
    var archiveResult = archive(importItems);
    return join(appendResult, archiveResult);
  }

  private List<LogItem> onReplace(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    return join(appendResult, replaceResult);
  }

  private List<LogItem> onReplaceDelete(
      ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    var archiveResult = archive(importItems);
    return join(appendResult, replaceResult, archiveResult);
  }

  private List<LogItem> archive(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    return importItems.getSpareKeys().stream().map(it -> archiveItem(it, importItems)).toList();
  }

  private List<LogItem> replace(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    return importItems.getDuplicateKeys().stream().map(it -> replaceItem(it, importItems)).toList();
  }

  private List<LogItem> append(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    return importItems.getNewKeys().stream().map(it -> appendItem(it, importItems)).toList();
  }

  private LogItem appendItem(
      String key, ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var indexItem = importItems.importItem(key);
    var eitherId = insert(indexItem);
    return createInsertLogItem(key, eitherId);
  }

  private LogItem replaceItem(
      String key, ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var existing = importItems.existingItem(key);
    var imported = importItems.importItem(key);
    var eitherId = update(existing, imported);
    return createUpdateLogItem(key, eitherId);
  }

  private LogItem archiveItem(
      String key, ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var archived = importItems.existingItem(key);
    var eitherId = archive(archived.getId());
    return createArchiveLogItem(key, eitherId);
  }

  private List<LogItem> onError(ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    var errors = validateItems(importItems);
    return asLogItems(errors);
  }

  private List<ErrorItem> validateItems(
      ImportItems<CreateTrsIndexForm, String, TrsIndex> importItems) {
    return Stream.of(
            errorStream(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem),
            errorStream(importItems.getSpareKeys(), ImportErrorUtils::missingItem))
        .flatMap(Function.identity())
        .toList();
  }

  private Stream<ErrorItem> errorStream(Set<String> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(function);
  }

  private Either<ErrorItem, EntityId> insert(CreateTrsIndexForm indexItem) {
    return trsIndexRepository.create(indexItem);
  }

  private Either<ErrorItem, EntityId> update(TrsIndex existing, CreateTrsIndexForm imported) {
    var updateForm = new UpdateTrsIndexForm();
    updateForm.setType(imported.getType());
    updateForm.setCurrency(imported.getCurrency());
    updateForm.setDescription(imported.getDescription());
    return trsIndexRepository.update(existing.getId(), updateForm);
  }

  private Either<ErrorItem, EntityId> archive(String id) {
    return trsIndexRepository.archive(id);
  }

  private List<TrsIndex> activeIndexes() {
    return trsIndexRepository.activeIndexes();
  }
}
