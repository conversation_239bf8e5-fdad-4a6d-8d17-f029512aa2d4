package com.solum.xplain.trs.value;

import com.solum.xplain.core.instrument.AssetClass;
import com.solum.xplain.core.instrument.InstrumentGroup;
import com.solum.xplain.core.instrument.InstrumentPriceGroup;
import com.solum.xplain.core.instrument.InstrumentType;

public enum NonMtmInstrumentType implements InstrumentType {
  TRS_BOND("BOND", TrsAssetClass.TRS_BOND),
  TRS_SHARE("SHARE", TrsAssetClass.TRS_SHARE),
  TRS_DIVIDEND("DIVIDEND", TrsAssetClass.TRS_DIVIDEND);

  private final String label;
  private final TrsAssetClass assetClass;

  NonMtmInstrumentType(String label, TrsAssetClass assetClass) {
    this.label = label;
    this.assetClass = assetClass;
  }

  @Override
  public String getLabel() {
    return label;
  }

  @Override
  public AssetClass getAssetClass() {
    return assetClass;
  }

  @Override
  public InstrumentGroup getInstrumentGroup() {
    return null;
  }

  @Override
  public int getSortOrder() {
    return 0;
  }

  @Override
  public InstrumentPriceGroup getPriceGroup() {
    return null;
  }

  public TrsAssetClassGroup getGroup() {
    return assetClass.getGroup();
  }

  @Override
  public boolean isCurveConfigInstrument() {
    return false;
  }
}
