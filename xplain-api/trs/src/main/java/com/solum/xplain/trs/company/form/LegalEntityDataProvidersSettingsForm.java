package com.solum.xplain.trs.company.form;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm;
import com.solum.xplain.trs.market.validation.ValidCompanyTrsMarketDataGroup;
import com.solum.xplain.trs.value.NonMtmInstrumentType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Size;
import java.util.Map;
import lombok.Data;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@GroupSequenceProvider(value = LegalEntityDataProviderSettingsFormGroupProvider.class)
public class LegalEntityDataProvidersSettingsForm {

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "valuationSettingType")
  private final String settingsType;

  @ValidCompanyTrsMarketDataGroup
  @Null(groups = DefaultSettingsGroup.class)
  @NotEmpty(groups = BespokeSettingsGroup.class)
  private final String marketDataGroupId;

  @Valid
  @Size(min = 3, max = 3, groups = BespokeSettingsGroup.class)
  @Null(groups = DefaultSettingsGroup.class)
  @NotNull(groups = BespokeSettingsGroup.class)
  private final Map<NonMtmInstrumentType, MarketDataProviderForm> instruments;

  @Valid @NotNull private final NewVersionFormV2 versionForm;
}
