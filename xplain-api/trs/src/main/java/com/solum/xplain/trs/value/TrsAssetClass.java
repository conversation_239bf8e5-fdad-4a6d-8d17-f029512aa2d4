package com.solum.xplain.trs.value;

import com.solum.xplain.core.instrument.AssetClass;

public enum TrsAssetClass implements AssetClass {
  TRS_BOND("Bond", TrsAssetClassGroup.TRS),
  TRS_SHARE("Share", TrsAssetClassGroup.TRS),
  TRS_DIVIDEND("Dividend", TrsAssetClassGroup.TRS);

  private final String name;
  private final TrsAssetClassGroup group;

  TrsAssetClass(String name, TrsAssetClassGroup group) {
    this.name = name;
    this.group = group;
  }

  @Override
  public String getLabel() {
    return name;
  }

  public TrsAssetClassGroup getGroup() {
    return group;
  }
}
