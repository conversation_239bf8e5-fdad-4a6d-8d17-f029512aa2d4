package com.solum.xplain.trs.portfolio.csv;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SUPPORTED_CALENDARS_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parsePayReceive;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateCurrency;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateNonNegativeInteger;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateNonPositiveInteger;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validatePositiveValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies.indexAccrualFrequency;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.ACCRUAL_OFFSET_DAYS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_FIXED_DAY_COUNT;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_FIXED_RATE_VALUE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_IBOR_DAY_COUNT;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_IBOR_FIXING_OFFSET;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_IBOR_INDEX;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_IBOR_INITIAL_COUPON;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_IBOR_SPREAD_VALUE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_ACCRUAL_METHOD;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_DAY_COUNT;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_FIXING_OFFSET_DAYS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_INDEX;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.CALCULATION_OVERNIGHT_SPREAD_VALUE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.COMPOUNDING_METHOD;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.DESCRIPTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.DIVIDEND_PAYOUT;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.IBOR_FIXING_CALENDARS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_1;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_2;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_ACCRUAL_FREQUENCY;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_CALENDARS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_CURRENCY;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_END_DATE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_IDENTIFIER;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_NOTIONAL;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_REGULAR_END_DATE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_REGULAR_START_DATE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_ROLL_CONVENTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_START_DATE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_STUB_CONVENTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_TRS_INDEX;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_TRS_INDEX_TYPE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_TRS_INITIAL_INDEX;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LEG_TYPE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LIST_DELIMITER_REGEX;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.PAYMENT_FREQUENCY;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.PAYMENT_OFFSET_DAYS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.PAY_RECEIVE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_CLIENT_METRICS_PV;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_COUNTERPARTY;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_COUNTERPARTY_TYPE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_DESCRIPTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_MANAGEMENT_FEE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_MANAGEMENT_FEE_DAY_COUNT;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_OTHER_LEG_LAST_PAYMENT_WITHHELD;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_PERFORMANCE_LEG_LAST_PAYMENT_WITHHELD;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_TRADE_DATE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_TRADE_ID;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRADE_TRADE_TYPE;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRS_INDEX_FIXING_CALENDARS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TRS_INDEX_FIXING_OFFSET_DAYS;
import static com.solum.xplain.trs.value.NonMtmClassifiersProvider.TRS_CURRENCIES;
import static com.solum.xplain.trs.value.NonMtmClassifiersProvider.TRS_ROLL_CONVENTIONS;
import static io.atlassian.fugue.Either.left;
import static org.apache.commons.lang3.StringUtils.equalsIgnoreCase;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.RollConventions;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.loader.LoaderUtils;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.solum.xplain.core.classifiers.type.SupportedBusinessDayConvention;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.IborIndicesSupplier;
import com.solum.xplain.core.common.validation.StubConventionSupplier;
import com.solum.xplain.core.common.validation.ValidationMessagesBundle;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioCSVFields;
import com.solum.xplain.core.portfolio.csv.loader.CustomFieldsCsvLoader;
import com.solum.xplain.core.portfolio.csv.loader.ExternalTradeIdsCsvLoader;
import com.solum.xplain.core.portfolio.value.CounterpartyType;
import com.solum.xplain.core.utils.FrequencyUtils;
import com.solum.xplain.extensions.calendar.XplainHolidayCalendars;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.trs.classifiers.TrsClassifierSupplier;
import com.solum.xplain.trs.index.validation.ValidTrsIndexValidator;
import com.solum.xplain.trs.portfolio.trade.validation.RequiredEqualAccrualPaymentFrequencies;
import com.solum.xplain.trs.portfolio.trade.validation.RequiredEqualAccrualPaymentFrequenciesValidator;
import com.solum.xplain.trs.portfolio.trade.value.ClientMetrics;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetails;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeInfo;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeLegDetails;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import com.solum.xplain.trs.portfolio.validation.TrsTradeFrequencySupplier;
import com.solum.xplain.trs.value.TrsTradeCalculationType;
import com.solum.xplain.trs.value.TrsType;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;

@EqualsAndHashCode
@ToString
@RequiredArgsConstructor
public class TrsTradeValueCsvLoader {

  private final ValidTrsIndexValidator validTrsIndexValidator;
  private final ExternalTradeIdsCsvLoader externalTradeIdsCsvLoader;
  private final CustomFieldsCsvLoader customFieldsCsvLoader;

  private static final ClassifierSupplier BDA_SUPPLIER =
      new ClassifierSupplier(BUSINESS_DAY_ADJUSTMENT_TYPE);
  private static final ClassifierSupplier CALENDAR_SUPPLIER =
      new ClassifierSupplier(SUPPORTED_CALENDARS_CLASSIFIER);
  private static final ClassifierSupplier OVERNIGHT_ACCRUALS_SUPPLIER =
      new ClassifierSupplier(OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER);
  private static final String VALID_COMPOUNDING_MESSAGE_KEY =
      "com.solum.xplain.trs.portfolio.trade.validation.ValidTrsLegCompoundingMethod.message";
  private static final String VALID_IBOR_FREQ_KEY =
      "com.solum.xplain.api.portfolio.validation.ValidIborLegAccrualFrequency.message";

  private final Collection<String> iborIndices = new IborIndicesSupplier().get();
  private final Collection<String> businessDayConventions =
      new BusinessDayConventionsSupplier().get();
  private final Collection<String> trsTradeRollConventions =
      new TrsClassifierSupplier(TRS_ROLL_CONVENTIONS).get();
  private final Collection<String> trsTradeFrequencies = new TrsTradeFrequencySupplier().get();
  private final Collection<String> stubConventions = new StubConventionSupplier().get();
  private final Collection<String> trsCurrencies = new TrsClassifierSupplier(TRS_CURRENCIES).get();

  public Either<ErrorItem, Triple<Integer, String, TrsTradeValue>> parseRow(CsvRow row) {
    try {
      var description = row.findValue(TRADE_DESCRIPTION).orElse(null);
      var trsType = TrsType.byLabel(row.getValue(TRADE_TRADE_TYPE));
      var tradeDetails = parseTradeDetails(row, trsType);
      var externalSourceIds = externalTradeIdsCsvLoader.parseIdentifiers(row);

      var tradeValue = new TrsTradeValue();
      tradeValue.setTradeDetails(tradeDetails);
      tradeValue.setTrsType(trsType);
      tradeValue.setDescription(description);
      tradeValue.setExternalIdentifiers(externalSourceIds);
      tradeValue.setCustomFields(customFieldsCsvLoader.parseCustomFields(row));

      return Either.right(
          Triple.of(row.lineNumber(), parseIdentifier(row, TRADE_TRADE_ID), tradeValue));
    } catch (RuntimeException e) {
      return left(
          Error.PARSING_ERROR.entity(
              "Error at line number " + row.lineNumber() + ". Error: " + e.getMessage()));
    }
  }

  private void validateRegularDates(String stubConvention, CsvRow row, String legId) {
    if (StubConvention.BOTH.name().equalsIgnoreCase(stubConvention)) {
      var regularStartDate =
          CsvLoaderUtils.parseDate(row.getValue(addField(legId, LEG_REGULAR_START_DATE)));
      var regularEndDate =
          CsvLoaderUtils.parseDate(row.getValue(addField(legId, LEG_REGULAR_END_DATE)));
      if (regularEndDate.isBefore(regularStartDate)) {
        throw new IllegalArgumentException(
            "The regular end date occurs before the regular start date");
      }
    }
  }

  private void validateStartAndEndDates(LocalDate startDate, LocalDate endDate) {
    if (endDate.isBefore(startDate)) {
      throw new IllegalArgumentException("The end date occurs before the start date");
    }
  }

  private String validateLegCurrencies(TrsTradeLegDetails payLeg, TrsTradeLegDetails receiveLeg) {
    if (!payLeg.getCurrency().equalsIgnoreCase(receiveLeg.getCurrency())) {
      throw new IllegalArgumentException("Pay and receive leg currencies must be the same.");
    }
    String tradeCurrency = payLeg.getCurrency();
    return validateValue(tradeCurrency, trsCurrencies);
  }

  private void performanceLegRequired(TrsTradeLegDetails leg1, TrsTradeLegDetails leg2) {
    var isPerformanceLeg1 = TrsTradeCalculationType.PERFORMANCE.equals(leg1.getType());
    var isPerformanceLeg2 = TrsTradeCalculationType.PERFORMANCE.equals(leg2.getType());
    if (!isPerformanceLeg1 && !isPerformanceLeg2) {
      throw new IllegalArgumentException("Performance type leg is required");
    }
  }

  private void validatePaymentOffsetDaysGreaterThanAccrualOffsetDays(
      Integer paymentOffsetDays, Integer accrualOffsetDays) {
    if (paymentOffsetDays < accrualOffsetDays) {
      throw new IllegalArgumentException(
          "Payment OffsetDays should be greater than Accrual Offset Days");
    }
  }

  private TrsTradeDetails parseTradeDetails(CsvRow row, TrsType trsType) {
    var details = new TrsTradeDetails();
    var info = parseTradeInfo(row);
    var leg1 = parseLeg(row, LEG_1, trsType);
    var leg2 = parseLeg(row, LEG_2, trsType);

    var clientMetrics = parseClientMetric(row);
    details.setClientMetrics(clientMetrics);
    row.findValue(TRADE_MANAGEMENT_FEE)
        .map(CsvLoaderUtils::parseBigDecimal)
        .ifPresent(details::setManagementFees);
    row.findValue(TRADE_MANAGEMENT_FEE_DAY_COUNT)
        .map(v -> CsvLoaderUtils.parseDefaultDayCount(v, TRADE_MANAGEMENT_FEE_DAY_COUNT))
        .map(DayCount::getName)
        .ifPresent(details::setManagementFeesDayCount);
    row.findValue(DESCRIPTION).ifPresent(details::setDescription);
    details.setPerformanceLegLastPaymentWithheld(
        row.findValue(TRADE_PERFORMANCE_LEG_LAST_PAYMENT_WITHHELD)
            .map(CsvLoaderUtils::parseBoolean)
            .orElse(Boolean.FALSE));
    details.setOtherLegLastPaymentWithheld(
        row.findValue(TRADE_OTHER_LEG_LAST_PAYMENT_WITHHELD)
            .map(CsvLoaderUtils::parseBoolean)
            .orElse(Boolean.FALSE));
    details.setInfo(info);
    validateLegIdentifiers(leg1, leg2);
    details.setLeg(leg1);
    details.setLeg(leg2);

    performanceLegRequired(details.getPayLeg(), details.getReceiveLeg());
    details.setCurrency(validateLegCurrencies(details.getPayLeg(), details.getReceiveLeg()));

    return details;
  }

  private void validateLegIdentifiers(TrsTradeLegDetails leg1, TrsTradeLegDetails leg2) {
    if (leg1.getExtLegIdentifier() != null
        && leg2.getExtLegIdentifier() != null
        && leg1.getExtLegIdentifier().equalsIgnoreCase(leg2.getExtLegIdentifier())) {
      throw new IllegalArgumentException(
          Messages.format(
              "Detected two legs having the same ID: {}, {}.",
              leg1.getExtLegIdentifier(),
              leg2.getExtLegIdentifier()));
    }
  }

  private ClientMetrics parseClientMetric(CsvRow row) {
    var value = row.findValue(TRADE_CLIENT_METRICS_PV);
    return value.map(Double::parseDouble).map(ClientMetrics::new).orElse(new ClientMetrics());
  }

  private TrsTradeInfo parseTradeInfo(CsvRow row) {
    var info = new TrsTradeInfo();
    row.findValue(TRADE_TRADE_DATE).map(CsvLoaderUtils::parseDate).ifPresent(info::setTradeDate);
    row.findValue(TRADE_COUNTERPARTY).ifPresent(info::setCounterParty);
    row.findValue(TRADE_COUNTERPARTY_TYPE)
        .map(value -> validateValue(value, List.of(CounterpartyType.BILATERAL.toString())))
        .ifPresent(info::setCounterPartyType);
    return info;
  }

  private static double validateNotional(CsvRow row, String legId) {
    double notional = LoaderUtils.parseDouble(row.getValue(addField(legId, LEG_NOTIONAL)));
    return validatePositiveValue(notional, addField(legId, PortfolioCSVFields.LEG_NOTIONAL));
  }

  private TrsTradeLegDetails parseLeg(CsvRow row, String legId, TrsType trsType) {
    var leg = new TrsTradeLegDetails();
    var legIdentifier = legIdentifier(row, addField(legId, LEG_IDENTIFIER));
    var calculationTypeLabel = row.getValue(addField(legId, LEG_TYPE));
    var calculationType = TrsTradeCalculationType.getByLabel(calculationTypeLabel);
    var notionalValue = validateNotional(row, legId);
    var notionalCurrency = row.getValue(addField(legId, LEG_CURRENCY));

    leg.setExtLegIdentifier(legIdentifier);
    leg.setPayReceive(parsePayReceive(row.getValue(addField(legId, PAY_RECEIVE))));
    leg.setType(calculationType);
    leg.setNotional(notionalValue);
    leg.setCurrency(validateCurrency(notionalCurrency));
    var accrualFreq =
        row.findValue(addField(legId, LEG_ACCRUAL_FREQUENCY))
            .map(value -> validateValue(value, trsTradeFrequencies))
            .orElse(null);
    leg.setAccrualFrequency(accrualFreq);

    if (accrualFreq == null && calculationType != TrsTradeCalculationType.IBOR) {
      throw new IllegalArgumentException(
          "No value was found for '" + addField(legId, LEG_ACCRUAL_FREQUENCY) + "'");
    }

    leg.setAccrualOffsetDays(parseOffsetField(row, legId, ACCRUAL_OFFSET_DAYS));
    leg.setPaymentOffsetDays(parseOffsetField(row, legId, PAYMENT_OFFSET_DAYS));

    var paymentFreq =
        row.findValue(addField(legId, PAYMENT_FREQUENCY))
            .map(v -> validateValue(v, trsTradeFrequencies))
            .orElse(accrualFreq);
    leg.setPaymentFrequency(paymentFreq);
    leg.setPaymentCompounding(parseCompoundingMethod(row, legId));

    validatePaymentOffsetDaysGreaterThanAccrualOffsetDays(
        leg.getPaymentOffsetDays(), leg.getAccrualOffsetDays());

    leg.setBusinessDayConvention(
        row.findValue(addField(legId, LEG_BUSINESS_DAY_CONVENTION))
            .map(value -> validateValue(value, businessDayConventions))
            .orElse(SupportedBusinessDayConvention.FOLLOWING.label()));
    leg.setBusinessDayAdjustment(
        row.findValue(addField(legId, LEG_BUSINESS_DAY_ADJUSTMENT_TYPE))
            .map(value -> validateValue(value, BDA_SUPPLIER))
            .map(BusinessDayAdjustmentType::valueOf)
            .orElse(BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT));
    leg.setRollConvention(
        row.findValue(addField(legId, LEG_ROLL_CONVENTION))
            .map(value -> validateValue(value, trsTradeRollConventions))
            .orElse(RollConventions.NONE.getName()));
    leg.setStubConvention(
        row.findValue(addField(legId, LEG_STUB_CONVENTION))
            .map(value -> validateValue(value, stubConventions))
            .orElse(StubConvention.SMART_INITIAL.getName()));

    var calendars =
        row.findValue(addField(legId, LEG_CALENDARS))
            .map(
                value ->
                    Arrays.stream(value.split(LIST_DELIMITER_REGEX))
                        .map(item -> validateValue(item, CALENDAR_SUPPLIER))
                        .toList())
            .orElseGet(
                () ->
                    Stream.of(leg.getCurrency())
                        .map(Currency::of)
                        .map(XplainHolidayCalendars.CCY_CALENDARS::get)
                        .map(ccyCals -> ccyCals.getFirst().getName())
                        .toList());
    leg.setCalendars(calendars);

    leg.setStartDate(CsvLoaderUtils.parseDate(row.getValue(addField(legId, LEG_START_DATE))));
    leg.setEndDate(CsvLoaderUtils.parseDate(row.getValue(addField(legId, LEG_END_DATE))));
    validateStartAndEndDates(leg.getStartDate(), leg.getEndDate());

    row.findValue(addField(legId, LEG_REGULAR_START_DATE))
        .map(CsvLoaderUtils::parseDate)
        .ifPresent(leg::setRegularStartDate);
    row.findValue(addField(legId, LEG_REGULAR_END_DATE))
        .map(CsvLoaderUtils::parseDate)
        .ifPresent(leg::setRegularEndDate);
    validateRegularDates(leg.getStubConvention(), row, legId);

    switch (calculationType) {
      case FIXED -> parseCalculationFixed(legId, row, leg);
      case IBOR -> parseCalculationIbor(legId, row, leg);
      case PERFORMANCE -> parseCalculationPerformance(legId, row, leg, trsType);
      case OVERNIGHT -> parseCalculationOvernight(legId, row, leg);
      default -> throw new IllegalStateException("Unexpected value: " + calculationType);
    }
    validateSameAccrualPaymentFrequencies(leg);
    return leg;
  }

  private static String legIdentifier(CsvRow row, String fieldName) {
    var legIdentifier = row.findValue(fieldName);
    return legIdentifier.map(id -> parseIdentifier(row, fieldName)).orElse(null);
  }

  private void parseCalculationOvernight(String legId, CsvRow row, TrsTradeLegDetails leg) {
    var index = OvernightIndex.of(row.getValue(addField(legId, CALCULATION_OVERNIGHT_INDEX)));

    leg.setIndex(index.getName());
    leg.setInitialValue(
        row.findValue(addField(legId, CALCULATION_OVERNIGHT_SPREAD_VALUE))
            .map(Double::valueOf)
            .orElse(0.0d));
    leg.setOvernightAccrualMethod(
        row.findValue(addField(legId, CALCULATION_OVERNIGHT_ACCRUAL_METHOD))
            .map(value -> validateValue(value, OVERNIGHT_ACCRUALS_SUPPLIER))
            .orElse(OvernightAccrualMethod.COMPOUNDED.getName()));

    leg.setDayCount(dayCount(row, addField(legId, CALCULATION_OVERNIGHT_DAY_COUNT)));
    var rateCutOffDaysField = addField(legId, CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS);
    leg.setOvernightRateCutOffDays(
        row.findValue(rateCutOffDaysField)
            .map(Integer::parseInt)
            .map(i -> validateNonNegativeInteger(i, rateCutOffDaysField))
            .map(String::valueOf)
            .orElse("0"));

    leg.setFixingDateOffsetDays(
        parseFixingOffsetField(row, legId, CALCULATION_OVERNIGHT_FIXING_OFFSET_DAYS, 0));
  }

  private void parseCalculationPerformance(
      String legId, CsvRow row, TrsTradeLegDetails leg, TrsType trsType) {
    var trsIndex = row.getValue(addField(legId, LEG_TRS_INDEX));
    var trsIndexTypeLabel = row.getValue(addField(legId, LEG_TRS_INDEX_TYPE));
    var trsIndexType = TrsType.byIndexLabel(trsIndexTypeLabel);
    var dividendPayout =
        row.findValue(addField(legId, DIVIDEND_PAYOUT))
            .filter(v -> trsIndexType == TrsType.SHARE)
            .map(Double::valueOf)
            .orElseGet(
                () -> {
                  if (TrsType.SHARE == trsIndexType) {
                    return 0.0;
                  } else {
                    return null;
                  }
                });
    var calendars =
        row.findValue(addField(legId, TRS_INDEX_FIXING_CALENDARS))
            .map(
                value ->
                    Arrays.stream(value.split(LIST_DELIMITER_REGEX))
                        .map(item -> validateValue(item, CALENDAR_SUPPLIER))
                        .toList())
            .orElse(
                List.of(
                    XplainHolidayCalendars.CCY_CALENDARS
                        .get(Currency.of(leg.getCurrency()))
                        .getFirst()
                        .getName()));
    leg.setIndex(validateValue(trsIndex, validTrsIndexValidator.isValid(trsIndex, null)));
    leg.setTrsIndexType(
        validateValue(
            trsIndexType,
            addField(legId, LEG_TRS_INDEX_TYPE),
            String.format("Value must match %s '%s'.", TRADE_TRADE_TYPE, trsType),
            trsIndexType == trsType));
    leg.setFixingDateOffsetDays(
        parseFixingOffsetField(row, legId, TRS_INDEX_FIXING_OFFSET_DAYS, 0));
    leg.setFixingCalendars(calendars);
    leg.setDividendPayout(dividendPayout);
    row.findValue(addField(legId, LEG_TRS_INITIAL_INDEX))
        .map(LoaderUtils::parseDouble)
        .ifPresent(leg::setInitialCoupon);
  }

  private void validateSameAccrualPaymentFrequencies(TrsTradeLegDetails leg) {
    var isValid =
        RequiredEqualAccrualPaymentFrequenciesValidator.isValid(
            leg.getAccrualFrequency(), leg.getPaymentFrequency());

    validateValue(
        leg.getAccrualFrequency(),
        LEG_ACCRUAL_FREQUENCY,
        ValidationMessagesBundle.get(RequiredEqualAccrualPaymentFrequencies.MESSAGE_TEMPLATE),
        isValid);
  }

  private void parseCalculationIbor(String legId, CsvRow row, TrsTradeLegDetails leg) {
    leg.setIndex(validateValue(row.getValue(addField(legId, CALCULATION_IBOR_INDEX)), iborIndices));
    leg.setDayCount(dayCount(row, addField(legId, CALCULATION_IBOR_DAY_COUNT)));
    leg.setInitialValue(
        row.findValue(addField(legId, CALCULATION_IBOR_SPREAD_VALUE))
            .map(Double::valueOf)
            .orElse(0.0));
    var iborIndex = IborIndex.of(leg.getIndex());
    var defaultFixingOffset = iborIndex.getFixingDateOffset().getDays();
    leg.setFixingDateOffsetDays(
        parseFixingOffsetField(row, legId, CALCULATION_IBOR_FIXING_OFFSET, defaultFixingOffset));

    var calendars =
        row.findValue(addField(legId, IBOR_FIXING_CALENDARS))
            .map(
                value ->
                    Arrays.stream(value.split(LIST_DELIMITER_REGEX))
                        .map(item -> validateValue(item, CALENDAR_SUPPLIER))
                        .toList())
            .orElse(List.of(iborIndex.getFixingCalendar().getName()));
    leg.setFixingCalendars(calendars);
    var iborFrequency = FrequencyUtils.toStringNoPrefix(indexAccrualFrequency(iborIndex));
    if (leg.getAccrualFrequency() == null) {
      leg.setAccrualFrequency(iborFrequency);
    }
    if (leg.getPaymentFrequency() == null) {
      leg.setPaymentFrequency(leg.getAccrualFrequency());
    }
    validateValue(
        leg.getAccrualFrequency(),
        addField(legId, LEG_ACCRUAL_FREQUENCY),
        ValidationMessagesBundle.get(VALID_IBOR_FREQ_KEY),
        StringUtils.equalsIgnoreCase(iborFrequency, leg.getAccrualFrequency()));

    row.findValue(addField(legId, CALCULATION_IBOR_INITIAL_COUPON))
        .map(LoaderUtils::parseDouble)
        .ifPresent(leg::setInitialCoupon);
  }

  private void parseCalculationFixed(String legId, CsvRow row, TrsTradeLegDetails leg) {
    leg.setInitialValue(
        Double.valueOf(row.getValue(addField(legId, CALCULATION_FIXED_RATE_VALUE))));
    leg.setDayCount(dayCount(row, addField(legId, CALCULATION_FIXED_DAY_COUNT)));
  }

  private String parseCompoundingMethod(CsvRow row, String legId) {
    return row.findValue(addField(legId, COMPOUNDING_METHOD))
        .map(
            v ->
                validateValue(
                    v,
                    COMPOUNDING_METHOD,
                    ValidationMessagesBundle.get(VALID_COMPOUNDING_MESSAGE_KEY),
                    equalsIgnoreCase(CompoundingMethod.NONE.toString(), v)))
        .orElse(CompoundingMethod.NONE.getName());
  }

  private Integer parseOffsetField(CsvRow row, String legId, String offsetField) {
    var fieldName = addField(legId, offsetField);
    return row.findValue(fieldName)
        .map(Integer::valueOf)
        .map(v -> validateNonNegativeInteger(v, fieldName))
        .orElse(0);
  }

  private String dayCount(CsvRow row, String field) {
    var dayCount = row.getValue(field);
    return CsvLoaderUtils.parseDefaultDayCount(dayCount, field).getName();
  }

  private Integer parseFixingOffsetField(
      CsvRow row, String legId, String offsetField, Integer defaultValue) {
    var fieldName = addField(legId, offsetField);
    return row.findValue(fieldName)
        .map(LoaderUtils::parseInteger)
        .map(v -> validateNonPositiveInteger(v, fieldName))
        .orElse(defaultValue);
  }
}
