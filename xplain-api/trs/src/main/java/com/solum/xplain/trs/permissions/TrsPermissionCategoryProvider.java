package com.solum.xplain.trs.permissions;

import com.solum.xplain.core.permissions.extension.PermissionCategory;
import com.solum.xplain.core.permissions.provider.PermissionCategoryProvider;
import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class TrsPermissionCategoryProvider implements PermissionCategoryProvider {
  @Override
  public List<PermissionCategory> permissionCategories() {
    return Arrays.asList(TrsPermissionCategory.values());
  }
}
