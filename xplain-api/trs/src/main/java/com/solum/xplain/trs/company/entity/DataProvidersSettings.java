package com.solum.xplain.trs.company.entity;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.trs.value.NonMtmInstrumentType;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public abstract class DataProvidersSettings extends VersionedEntity {

  private EntityReference marketDataGroup;
  private Map<NonMtmInstrumentType, MarketDataProviders> instruments;

  @Override
  public boolean valueEquals(Object object) {
    DataProvidersSettings other = (DataProvidersSettings) object;
    return super.valueEquals(other)
        && Objects.equals(marketDataGroup, other.marketDataGroup)
        && Objects.equals(instruments, other.instruments);
  }
}
