package com.solum.xplain.trs.value;

import static com.opengamma.strata.basics.currency.Currency.AUD;
import static com.opengamma.strata.basics.currency.Currency.CAD;
import static com.opengamma.strata.basics.currency.Currency.CHF;
import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.NZD;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.AUSY;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CATO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CHZU;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.EUTA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.GBLO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.JPTO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.NZAU;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.USNY;
import static java.util.stream.Collectors.toSet;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.collect.named.ExtendedEnum;
import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.type.SupportedDayCount;
import com.solum.xplain.extensions.calendar.XplainHolidayCalendars;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TrsClassifiersConstants {

  public static final Map<Currency, HolidayCalendarId> TRS_FIXING_OFFSET_DAYS_CALENDARS =
      Map.of(
          USD, USNY,
          EUR, EUTA,
          GBP, GBLO,
          AUD, AUSY,
          CAD, CATO,
          CHF, CHZU,
          JPY, JPTO,
          NZD, NZAU);

  public static final List<Frequency> TRS_TRADE_FREQUENCIES =
      List.of(
          Frequency.P1D,
          Frequency.P1W,
          Frequency.P4W,
          Frequency.P1M,
          Frequency.P13W,
          Frequency.P3M,
          Frequency.P26W,
          Frequency.P6M,
          Frequency.ofYears(1),
          Frequency.P12M,
          Frequency.TERM);

  public static List<String> allTrsFixingOffsetDaysCalendars() {
    return TRS_FIXING_OFFSET_DAYS_CALENDARS.values().stream()
        .map(HolidayCalendarId::getName)
        .toList();
  }

  public static List<SupportedDayCount> supportedDayCounts() {
    return List.of(SupportedDayCount.values());
  }

  public static ExtendedEnum<RollConvention> trsTradeRollConventions() {
    return RollConvention.extendedEnum();
  }

  public static Set<Currency> trsTradeCurrencies() {
    var unsupportedCurrencies = Set.of(Currency.CNH, Currency.HKD, Currency.TRY, Currency.PHP);
    return XplainHolidayCalendars.CCY_CALENDARS.keySet().stream()
        .filter(c -> !unsupportedCurrencies.contains(c))
        .collect(toSet());
  }

  public static List<Classifier> trsTypes() {
    return Arrays.stream(TrsType.values()).map(c -> new Classifier(c.name(), c.label())).toList();
  }
}
