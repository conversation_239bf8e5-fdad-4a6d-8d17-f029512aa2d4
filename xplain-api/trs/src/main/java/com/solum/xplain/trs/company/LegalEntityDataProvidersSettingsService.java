package com.solum.xplain.trs.company;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.UserCompanyService;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.trs.company.form.LegalEntityDataProvidersSettingsForm;
import com.solum.xplain.trs.company.repository.CompanyDataProvidersSettingsRepository;
import com.solum.xplain.trs.company.repository.LegalEntityDataProvidersSettingsRepository;
import com.solum.xplain.trs.company.view.LegalEntityDataProvidersSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class LegalEntityDataProvidersSettingsService {
  private final CompanyDataProvidersSettingsRepository companySettingsRepository;
  private final LegalEntityDataProvidersSettingsRepository repository;
  private final UserCompanyService userCompanyService;

  public Either<ErrorItem, LegalEntityDataProvidersSettingsView> entitySettings(
      Authentication auth, String companyId, String entityId, BitemporalDate stateDate) {
    return userCompanyService
        .userLegalEntity(auth, companyId, entityId)
        .flatMap(u -> entitySettings(companyId, entityId, stateDate));
  }

  public Either<ErrorItem, LegalEntityDataProvidersSettingsView> entitySettings(
      String companyId, String entityId, BitemporalDate stateDate) {
    return companySettingsRepository
        .companySettingsEntity(companyId, stateDate)
        .map(s -> repository.legalEntitySettings(entityId, stateDate, s));
  }

  public Either<ErrorItem, List<LegalEntityDataProvidersSettingsView>> entitySettingsVersions(
      Authentication auth, String companyId, String entityId) {
    return userCompanyService
        .userLegalEntity(auth, companyId, entityId)
        .map(s -> repository.legalEntitySettingsVersions(entityId));
  }

  public Either<ErrorItem, DateList> entitySettingsFutureVersions(
      Authentication auth, String companyId, String entityId, LocalDate stateDate) {
    return userCompanyService
        .userLegalEntity(auth, companyId, entityId)
        .map(u -> repository.entitySettingsFutureVersions(entityId, stateDate));
  }

  @Transactional
  public Either<ErrorItem, EntityId> updateEntitySettings(
      Authentication auth,
      String companyId,
      String entityId,
      LocalDate versionDate,
      LegalEntityDataProvidersSettingsForm form) {
    return userCompanyService
        .userLegalEntity(auth, companyId, entityId)
        .flatMap(u -> repository.updateLegalEntitySettings(entityId, versionDate, form));
  }

  @Transactional
  public Either<ErrorItem, EntityId> deleteEntitySettingsVersion(
      Authentication auth, String companyId, String entityId, LocalDate version) {
    return userCompanyService
        .userLegalEntity(auth, companyId, entityId)
        .flatMap(g -> repository.deleteEntitySettingsVersion(entityId, version));
  }
}
