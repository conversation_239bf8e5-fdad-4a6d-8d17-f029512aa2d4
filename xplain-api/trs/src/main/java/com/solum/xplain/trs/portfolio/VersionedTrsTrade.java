package com.solum.xplain.trs.portfolio;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.trs.portfolio.trade.value.ClientMetrics;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetails;
import com.solum.xplain.trs.value.TrsType;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VersionedTrsTrade extends DateRangeVersionedEntity {

  private String externalTradeId;
  private String description;
  private TrsType trsType;
  private TrsTradeDetails tradeDetails;
  private ClientMetrics clientMetrics;
  private List<ExternalIdentifier> externalIdentifiers;
  private List<CustomTradeField> customFields;
}
