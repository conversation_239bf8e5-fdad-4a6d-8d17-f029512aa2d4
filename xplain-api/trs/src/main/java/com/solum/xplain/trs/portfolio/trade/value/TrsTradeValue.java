package com.solum.xplain.trs.portfolio.trade.value;

import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.trs.value.TrsType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "newOf")
@FieldNameConstants
public class TrsTradeValue {
  private String description;
  private TrsType trsType;
  private TrsTradeDetails tradeDetails;
  private List<ExternalIdentifier> externalIdentifiers;
  private List<CustomTradeField> customFields;
}
