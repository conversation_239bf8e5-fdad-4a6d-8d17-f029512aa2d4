package com.solum.xplain.trs.portfolio.trade;

import static com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter.emptyFilter;
import static com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepositoryFilter.activeNonMtmPortfolios;

import com.solum.xplain.core.common.versions.embedded.convert.DefaultEmbeddedVersionEntityToViewConverter;
import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverter;
import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverterProvider;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioMapper;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import java.util.List;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@NullMarked
public class NonMtmPortfolioItemEntityToViewConverterProvider
    implements EmbeddedVersionEntityToViewConverterProvider<
        TrsTradeValue, NonMtmPortfolioItemEntity, NonMtmPortfolioItem> {

  private final NonMtmPortfolioMapper mapper;
  private final NonMtmPortfolioRepository portfolioRepository;

  @Override
  public EmbeddedVersionEntityToViewConverter<
          TrsTradeValue, NonMtmPortfolioItemEntity, NonMtmPortfolioItem>
      provideForValues(
          List<TrsTradeValue> values, List<NonMtmPortfolioItemEntity> nonMtmPortfolioItemEntity) {
    var portfolios =
        portfolioRepository.portfolioCondensedViews(activeNonMtmPortfolios(), emptyFilter());
    var viewFunction = new NonMtmPortfolioItemToViewConverter(portfolios, mapper);
    return new DefaultEmbeddedVersionEntityToViewConverter<>(viewFunction);
  }
}
