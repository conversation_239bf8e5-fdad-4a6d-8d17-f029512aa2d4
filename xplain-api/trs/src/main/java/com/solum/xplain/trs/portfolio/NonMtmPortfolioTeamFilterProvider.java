package com.solum.xplain.trs.portfolio;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.trs.valuation.NonMtmPortfolioValuationTeamFilter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class NonMtmPortfolioTeamFilterProvider {
  private final CompanyRepository companyRepository;
  private final CompanyLegalEntityRepository entityRepository;

  public NonMtmPortfolioTeamFilter provideFilter(XplainPrincipal user) {
    var excludedCompanies = userExcludedCompanies(user);
    var excludedEntities = userExcludedEntities(user, excludedCompanies);

    return NonMtmPortfolioTeamFilter.portfolioTeamFilter(user, excludedCompanies, excludedEntities);
  }

  public NonMtmPortfolioValuationTeamFilter valuationTeamFilter(XplainPrincipal user) {
    var excludedCompanies = userExcludedCompanies(user);
    var excludedEntities = userExcludedEntities(user, excludedCompanies);

    return NonMtmPortfolioValuationTeamFilter.valuationTeamFilter(
        user, excludedCompanies, excludedEntities);
  }

  private @NonNull List<String> userExcludedCompanies(XplainPrincipal user) {
    return companyRepository
        .streamExcludedCompanyReferencesForUser(user)
        .map(EntityReference::getEntityId)
        .toList();
  }

  private @NonNull List<String> userExcludedEntities(
      XplainPrincipal user, List<String> excludedCompanies) {
    return entityRepository
        .streamExcludedLegalEntityReferencesForUser(user, excludedCompanies)
        .map(EntityReference::getEntityId)
        .toList();
  }
}
