package com.solum.xplain.trs.portfolio.csv;

import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.ALLOW_ALL_TEAMS;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.COMPANY_EXTERNAL_ID;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.DESCRIPTION;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.ENTITY_EXTERNAL_ID;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.EXTERNAL_ID;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.LIST_DELIMITER;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.PORTFOLIO_NAME;
import static com.solum.xplain.trs.portfolio.csv.NonMtmPortfolioCsvFields.TEAM_NAMES;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.teams.value.TeamNameView;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioView;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NonMtmPortfolioCsvMapper {
  public static List<String> getHeader() {
    return List.of(
        EXTERNAL_ID,
        PORTFOLIO_NAME,
        COMPANY_EXTERNAL_ID,
        ENTITY_EXTERNAL_ID,
        TEAM_NAMES,
        ALLOW_ALL_TEAMS,
        DESCRIPTION);
  }

  public static CsvRow toCsvRow(
      NonMtmPortfolioView item, Map<String, ? extends TeamNameView> allTeams) {
    var rows = new ArrayList<CsvField>();
    rows.add(new CsvField(EXTERNAL_ID, item.getExternalPortfolioId()));
    rows.add(new CsvField(PORTFOLIO_NAME, item.getName()));
    rows.add(new CsvField(COMPANY_EXTERNAL_ID, item.getExternalCompanyId()));
    rows.add(new CsvField(ENTITY_EXTERNAL_ID, item.getExternalEntityId()));
    rows.add(new CsvField(TEAM_NAMES, teamNames(item.getTeamIds(), allTeams)));
    rows.add(new CsvField(ALLOW_ALL_TEAMS, String.valueOf(isTrue(item.getAllowAllTeams()))));
    rows.add(new CsvField(DESCRIPTION, item.getDescription()));
    return new CsvRow(rows);
  }

  static String teamNames(List<String> teamIds, Map<String, ? extends TeamNameView> allTeams) {
    if (teamIds.isEmpty()) return "";

    return teamIds.stream()
        .map(allTeams::get)
        .filter(Objects::nonNull)
        .map(TeamNameView::getName)
        .collect(Collectors.joining(LIST_DELIMITER));
  }
}
