package com.solum.xplain.trs.company;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static com.solum.xplain.core.lock.XplainLock.COMPANY_SETTINGS_LOCK_ID;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_MODIFY_COMPANY_PROVIDER_SETTINGS;
import static com.solum.xplain.trs.permissions.TrsAuthorities.AUTHORITY_VIEW_COMPANY_PROVIDER_SETTINGS;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.trs.company.form.CompanyDataProvidersSettingsForm;
import com.solum.xplain.trs.company.view.CompanyDataProvidersSettingsView;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/non-mtm/companies/{companyId}/providers")
public class CompanyDataProvidersSettingsController {

  private final CompanyDataProvidersSettingsService service;

  public CompanyDataProvidersSettingsController(CompanyDataProvidersSettingsService service) {
    this.service = service;
  }

  @Operation(summary = "Get company market data provider settings")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_PROVIDER_SETTINGS)
  public ResponseEntity<CompanyDataProvidersSettingsView> getCompanySettings(
      Authentication auth,
      @RequestParam("stateDate") LocalDate stateDate,
      @PathVariable String companyId) {
    return eitherErrorItemResponse(service.companySettings(auth, companyId, newOf(stateDate)));
  }

  @Operation(summary = "Get default market data provider settings versions")
  @CommonErrors
  @GetMapping("/versions")
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_PROVIDER_SETTINGS)
  public ResponseEntity<List<CompanyDataProvidersSettingsView>> getSettingsVersions(
      Authentication auth, @PathVariable String companyId) {
    return eitherErrorItemResponse(service.companySettingsVersions(auth, companyId));
  }

  @Operation(summary = "Gets default market data provider settings versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_PROVIDER_SETTINGS)
  public ResponseEntity<DateList> getSettingsFutureVersionsDates(
      Authentication auth,
      @PathVariable("companyId") String companyId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.companySettingsFutureVersions(auth, companyId, stateDate));
  }

  @Operation(summary = "Update company market data provider settings")
  @CommonErrors
  @PutMapping("/{version}")
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_PROVIDER_SETTINGS)
  @RequireLock(name = COMPANY_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> updateSettings(
      @Valid @RequestBody CompanyDataProvidersSettingsForm newForm,
      @PathVariable LocalDate version,
      @PathVariable String companyId,
      Authentication auth) {
    return eitherErrorItemResponse(
        service.updateCompanySettings(auth, companyId, version, newForm));
  }

  @Operation(summary = "Deletes (sets status to DELETED) market data provider settings")
  @PutMapping("/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_PROVIDER_SETTINGS)
  @RequireLock(name = COMPANY_SETTINGS_LOCK_ID)
  public ResponseEntity<EntityId> deleteSettings(
      Authentication auth, @PathVariable String companyId, @PathVariable LocalDate version) {
    return eitherErrorItemResponse(service.deleteCompanySettingsVersion(auth, companyId, version));
  }
}
