package com.solum.xplain.trs.valuation.calculation.period;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.trs.valuation.value.PeriodStatus;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;

@Data
public class CalculatedAccruedPeriods<T extends AccruedPeriod> {
  private final PayReceive payReceive;
  private final List<T> periods;
  private final List<ErrorItem> validationWarnings;

  public Optional<T> currentPeriod() {
    return periods.stream().filter(v -> v.getStatus() == PeriodStatus.CURRENT).findFirst();
  }

  public Optional<T> previousPeriod() {
    return periods.stream().filter(v -> v.getStatus() == PeriodStatus.PREVIOUS).findFirst();
  }

  @Nullable
  public BigDecimal accruedValue() {
    return sumRequiredPeriods(AccruedPeriod::accruedTotal);
  }

  @Nullable
  public BigDecimal accruedValueIA() {
    return sumRequiredPeriods(AccruedPeriod::accruedTotalIA);
  }

  public BigDecimal tZeroCashFlow(LocalDate valuationDate) {
    return tZeroCashFlows(valuationDate, AccruedPeriod::accruedTotal, BigDecimal.ZERO);
  }

  @Nullable
  public BigDecimal tZeroCashFlowIA(LocalDate valuationDate) {
    return tZeroCashFlows(valuationDate, AccruedPeriod::accruedTotalIA, null);
  }

  private BigDecimal tZeroCashFlows(
      LocalDate valuationDate,
      Function<AccruedPeriod, BigDecimal> valueFn,
      @Nullable BigDecimal defaultValue) {
    boolean isValuatedAtPaymentDate =
        periods.stream().map(AccruedPeriod::getPaymentDate).anyMatch(valuationDate::equals);

    if (isValuatedAtPaymentDate) {
      return periods.stream()
          .filter(AccruedPeriod::isPaid)
          .max(Comparator.comparing(AccruedPeriod::getPaymentDate))
          .map(valueFn)
          .orElse(defaultValue);
    }
    return BigDecimal.ZERO;
  }

  public List<ErrorItem> warnings() {
    var builder = ImmutableList.<ErrorItem>builder().addAll(validationWarnings);

    builder.addAll(
        getPeriods().stream()
            .filter(AccruedPeriod::isRequiredPeriod)
            .map(AccruedPeriod::getWarnings)
            .map(CollectionUtils::emptyIfNull)
            .flatMap(Collection::stream)
            .toList());

    return builder.build();
  }

  @Nullable
  public BigDecimal sumRequiredPeriods(Function<T, BigDecimal> valueFn) {
    var values = periods.stream().filter(AccruedPeriod::isRequiredPeriod).map(valueFn).toList();
    if (values.contains(null)) {
      return null;
    }

    return values.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
  }
}
