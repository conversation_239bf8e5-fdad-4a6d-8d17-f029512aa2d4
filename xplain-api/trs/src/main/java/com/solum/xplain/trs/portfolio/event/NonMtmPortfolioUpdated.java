package com.solum.xplain.trs.portfolio.event;

import com.solum.xplain.core.common.EntityEvent;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class NonMtmPortfolioUpdated extends EntityEvent {

  private final LocalDateTime modifiedAt;

  public NonMtmPortfolioUpdated(String entityId) {
    this(entityId, LocalDateTime.now());
  }

  public NonMtmPortfolioUpdated(String entityId, LocalDateTime modifiedAt) {
    super(entityId);
    this.modifiedAt = modifiedAt;
  }
}
