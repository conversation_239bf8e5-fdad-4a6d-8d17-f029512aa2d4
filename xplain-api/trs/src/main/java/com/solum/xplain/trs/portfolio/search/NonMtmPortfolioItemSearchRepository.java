package com.solum.xplain.trs.portfolio.search;

import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.latestItemsCriteria;
import static com.solum.xplain.core.search.SearchOptions.REGEX_OPTIONS;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.search.SearchRequest;
import com.solum.xplain.core.search.SearchResponse;
import com.solum.xplain.core.search.SearchTradeView;
import com.solum.xplain.core.utils.PathUtils;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.VersionedTrsTrade;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetails;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeLegDetails;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class NonMtmPortfolioItemSearchRepository {

  private final MongoOperations mongoOperations;

  public SearchResponse<SearchTradeView> nonMtmPortfolioItems(
      List<String> portfolioIds, SearchRequest searchRequest) {
    var scrollRequest = ScrollRequest.of(0, 10);
    var itemsCriteria =
        latestItemsCriteria(
                nonMtmPortfoliosCriteria(portfolioIds),
                BitemporalDate.newOf(searchRequest.getStateDate()),
                VersionedEntityFilter.active())
            .andOperator(searchCriteria(searchRequest));

    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(itemsCriteria))
            .add(projectSearchView());
    operations.addAll(
        new ScrollSortOperations(scrollRequest, DateRangeVersionedEntity.Fields.id).build());
    var items =
        mongoOperations
            .aggregateAndReturn(SearchTradeView.class)
            .by(newAggregation(NonMtmPortfolioItem.class, operations.build()))
            .all()
            .getMappedResults();
    return new SearchResponse<>(ScrollableEntry.limitByPageSize(items, scrollRequest).getContent());
  }

  private Criteria nonMtmPortfoliosCriteria(List<String> portfolioIds) {
    return Criteria.where(NonMtmPortfolioItem.Fields.portfolioId).in(portfolioIds);
  }

  private ProjectionOperation projectSearchView() {
    return project(
            SearchTradeView.Fields.portfolioId,
            SearchTradeView.Fields.externalPortfolioId,
            SearchTradeView.Fields.externalCompanyId,
            SearchTradeView.Fields.externalEntityId,
            SearchTradeView.Fields.externalTradeId)
        .and(DateRangeVersionedEntity.Fields.entityId)
        .as(SearchTradeView.Fields.tradeId)
        .and(VersionedTrsTrade.Fields.trsType)
        .as(SearchTradeView.Fields.productType)
        .and(
            PathUtils.joinPaths(
                VersionedTrsTrade.Fields.tradeDetails,
                TrsTradeDetails.Fields.info,
                TradeInfoDetails.Fields.tradeDate))
        .as(SearchTradeView.Fields.tradeDate)
        .and(VersionedTrsTrade.Fields.externalIdentifiers)
        .as(SearchTradeView.Fields.externalIdentifiers);
  }

  private Criteria searchCriteria(SearchRequest searchRequest) {
    var tradeDetailsField = VersionedTrsTrade.Fields.tradeDetails;
    var legIdentifier = TrsTradeLegDetails.Fields.extLegIdentifier;
    var externalIdentifier =
        PathUtils.joinPaths(
            VersionedTrsTrade.Fields.externalIdentifiers, ExternalIdentifier.Fields.identifier);
    return new Criteria()
        .orOperator(
            Criteria.where(VersionedTradeEntity.Fields.externalTradeId)
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(
                    PathUtils.joinPaths(
                        tradeDetailsField, TradeDetails.Fields.payLeg, legIdentifier))
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(
                    PathUtils.joinPaths(
                        tradeDetailsField, TradeDetails.Fields.receiveLeg, legIdentifier))
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(externalIdentifier).regex(searchRequest.getQuery(), REGEX_OPTIONS));
  }
}
