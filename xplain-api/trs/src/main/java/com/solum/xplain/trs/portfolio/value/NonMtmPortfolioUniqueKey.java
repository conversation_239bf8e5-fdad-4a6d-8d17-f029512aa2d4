package com.solum.xplain.trs.portfolio.value;

import com.solum.xplain.trs.portfolio.form.NonMtmPortfolioCreateForm;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class NonMtmPortfolioUniqueKey {

  private final String companyId;
  private final String entityId;
  private final String externalPortfolioId;

  public NonMtmPortfolioUniqueKey(String companyId, String entityId, String externalPortfolioId) {
    this.companyId = companyId;
    this.entityId = entityId;
    this.externalPortfolioId = externalPortfolioId;
  }

  public static NonMtmPortfolioUniqueKey fromImportNonMtmPortfolio(
      ImportNonMtmPortfolio importNonMtmPortfolio) {
    return new NonMtmPortfolioUniqueKey(
        importNonMtmPortfolio.getCompanyId(),
        importNonMtmPortfolio.getEntityId(),
        importNonMtmPortfolio.getExternalPortfolioId());
  }

  public static NonMtmPortfolioUniqueKey fromForm(NonMtmPortfolioCreateForm form) {
    return new NonMtmPortfolioUniqueKey(
        form.getCompanyId(), form.getEntityId(), form.getExternalPortfolioId());
  }
}
