package com.solum.xplain.trs.valuation;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.sockets.events.EventType.VALUATION_PROGRESS_UPDATED;
import static com.solum.xplain.trs.socket.type.TrsValuationSocketEvent.trsValuationEvent;
import static com.solum.xplain.trs.valuation.NonMtmPortfolioValuationTeamFilter.emptyFilter;
import static com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolioItem.ofError;
import static com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolioItem.ofMetrics;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.calculation.CalculationTradesStatistics;
import com.solum.xplain.calculation.value.InProgressValuationStatus;
import com.solum.xplain.core.audit.AuditEntryRepository;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemRepository;
import com.solum.xplain.trs.valuation.calculation.TrsTradePricer;
import com.solum.xplain.trs.valuation.entity.NonMtmValuation;
import com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolioItem;
import com.solum.xplain.trs.valuation.events.NonMtmValuationFinishedEvent;
import com.solum.xplain.trs.valuation.events.NonMtmValuationRequestedEvent;
import com.solum.xplain.trs.valuation.market.NonMtmMarketData;
import com.solum.xplain.trs.valuation.repository.NonMtmValuationPortfolioItemRepository;
import com.solum.xplain.trs.valuation.repository.NonMtmValuationRepository;
import com.solum.xplain.trs.valuation.value.NonMtmValuationView;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Consumer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class NonMtmValuationsExecutor {

  private final NonMtmValuationRepository valuationRepository;
  private final NonMtmValuationPortfolioItemRepository valuationItemRepository;
  private final NonMtmPortfolioItemRepository portfolioItemRepository;
  private final TrsTradePricer trsTradePricer;
  private final AuditEntryRepository auditEntryRepository;
  private final ApplicationEventPublisher eventPublisher;

  public NonMtmValuationsExecutor(
      NonMtmValuationRepository valuationRepository,
      NonMtmValuationPortfolioItemRepository valuationItemRepository,
      NonMtmPortfolioItemRepository portfolioItemRepository,
      TrsTradePricer trsTradePricer,
      AuditEntryRepository auditEntryRepository,
      ApplicationEventPublisher eventPublisher) {
    this.valuationRepository = valuationRepository;
    this.valuationItemRepository = valuationItemRepository;
    this.portfolioItemRepository = portfolioItemRepository;
    this.trsTradePricer = trsTradePricer;
    this.auditEntryRepository = auditEntryRepository;
    this.eventPublisher = eventPublisher;
  }

  @Async
  @EventListener(NonMtmValuationRequestedEvent.class)
  public void valuatePortfolio(NonMtmValuationRequestedEvent event) {
    var valuationId = event.getValuationId();
    var reportingCurrency =
        event.getCalculationCcy() == null ? null : Currency.parse(event.getCalculationCcy());
    var marketData = event.getMarketData();
    var audit = auditValuation(event);
    Consumer<List<ErrorItem>> warningsConsumer = err -> auditEntryRepository.addLogs(audit, err);

    CalculationTradesStatistics statistics;
    if (event.getTrade() != null) {
      var calculatedTrade =
          calculate(valuationId, event.getTrade(), marketData, reportingCurrency, warningsConsumer);
      statistics = processResults(valuationId, List.of(calculatedTrade), audit, 1);
    } else {
      var totalCnt =
          portfolioItemRepository.tradesCount(event.getPortfolioId(), marketData.getStateDate());
      statistics =
          chunked(
                  portfolioItemRepository
                      .portfolioItemsStream(event.getPortfolioId(), marketData.getStateDate())
                      .map(
                          p ->
                              calculate(
                                  valuationId, p, marketData, reportingCurrency, warningsConsumer)),
                  1000)
              .map(items -> processResults(valuationId, items, audit, totalCnt))
              .reduce(CalculationTradesStatistics::add)
              .orElse(CalculationTradesStatistics.newOf(null));
    }
    finalizeValuation(valuationId, statistics);
  }

  private void finalizeValuation(String valuationId, CalculationTradesStatistics statistics) {
    valuation(valuationId)
        .map(v -> new NonMtmValuationFinishedEvent(valuationId, statistics))
        .toOptional()
        .ifPresent(eventPublisher::publishEvent);
  }

  private NonMtmValuationPortfolioItem calculate(
      String valuationId,
      NonMtmPortfolioItem item,
      NonMtmMarketData marketData,
      @Nullable Currency reportingCurrency,
      Consumer<List<ErrorItem>> errorsConsumer) {
    return trsTradePricer
        .calculateMetrics(item, marketData, reportingCurrency, errorsConsumer)
        .fold(
            err -> ofError(valuationId, item, err),
            metrics -> ofMetrics(valuationId, item, metrics));
  }

  private CalculationTradesStatistics processResults(
      String valuationId,
      List<NonMtmValuationPortfolioItem> items,
      AuditEntry auditEntry,
      long totalCnt) {
    return valuation(valuationId)
        .map(v -> saveResults(v, auditEntry, items, totalCnt))
        .rightOr(l -> CalculationTradesStatistics.newOf(null));
  }

  private CalculationTradesStatistics saveResults(
      NonMtmValuationView v,
      AuditEntry auditEntry,
      List<NonMtmValuationPortfolioItem> items,
      long totalCnt) {
    valuationItemRepository.saveResults(items);
    var errors =
        items.stream()
            .map(NonMtmValuationPortfolioItem::getValuationError)
            .filter(StringUtils::isNotEmpty)
            .map(Error.CALCULATION_ERROR::entity)
            .toList();
    auditEntryRepository.addLogs(auditEntry, errors);
    var processedCnt = valuationItemRepository.countResults(v.getId());
    var status = InProgressValuationStatus.statusOf(v.getId(), totalCnt, totalCnt - processedCnt);
    eventPublisher.publishEvent(trsValuationEvent(VALUATION_PROGRESS_UPDATED, status));
    return CalculationTradesStatistics.newOf((long) items.size(), (long) errors.size());
  }

  private Either<ErrorItem, NonMtmValuationView> valuation(String valuationId) {
    return valuationRepository.getValuation(emptyFilter(), valuationId);
  }

  private AuditEntry auditValuation(NonMtmValuationRequestedEvent event) {
    var audit =
        AuditEntry.of(NonMtmValuation.COLLECTION_NAME, auditMessage(event), event.getValuationId());
    auditEntryRepository.insert(audit);
    return auditEntryRepository.addLogs(audit, event.getWarnings());
  }

  private String auditMessage(NonMtmValuationRequestedEvent event) {
    if (event.getTrade() != null) {
      return String.format(
          "%s TRS valuation trade %s",
          event.getExternalPortfolioId(), event.getTrade().getExternalTradeId());
    }
    return String.format("%s TRS valuation", event.getExternalPortfolioId());
  }
}
