plugins {
  id 'org.owasp.dependencycheck' version "${dependencyCheckVersion}"
  id 'io.spring.dependency-management' version "${dependencyManagementVersion}"
  id "org.sonarqube" version "${sonarVersion}"
}

java {
  toolchain {
    languageVersion = JavaLanguageVersion.of(21)
  }
}



sonarqube {
  properties {
    property "sonar.projectKey", "solumxplain_solum-xplain-valuation"
    property "sonar.organization", "solumxplain"
    property "sonar.host.url", "https://sonarcloud.io"
  }
}

subprojects {
  group = 'com.solum.xplain'

  apply plugin: 'java'
  apply plugin: 'io.spring.dependency-management'
  apply plugin: 'groovy'
  apply plugin: 'jacoco'
  apply plugin: 'idea'
  apply plugin: 'org.owasp.dependencycheck'

  java {
    toolchain {
      languageVersion = JavaLanguageVersion.of(21)
    }
  }

  compileJava {
    sourceCompatibility = 21
    targetCompatibility = 21
    options.compilerArgs = [
      '-Amapstruct.unmappedTargetPolicy=ERROR',
      '-Amapstruct.defaultComponentModel=spring',
      '-parameters'
    ]
  }

  configurations {
    all*.exclude module: 'spring-boot-starter-logging'
    implementation.exclude group: 'com.google.code.findbugs'
  }

  dependencyManagement {
    imports {
      mavenBom("org.springframework.boot:spring-boot-dependencies:${springBootVersion}")
    }
  }
}
