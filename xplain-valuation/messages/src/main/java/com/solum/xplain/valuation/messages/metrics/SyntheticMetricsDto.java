package com.solum.xplain.valuation.messages.metrics;

import lombok.Data;

/** DTO cuts down the fields transferred for synthetic sub-trade metrics. */
@Data
public class SyntheticMetricsDto {
  private String localCcy;
  private Double presentValuePercent;
  private Double presentValue;
  private Double presentValuePayLegCurrency;
  private Double accruedPercent;
  private Double accrued;
  private Double accruedLocalCcy;
  private Double cleanPresentValuePercent;
  private Double cleanPresentValue;
  private Double cleanPresentValueLocalCcy;
  private Double payLegPV;
  private Integer payLegAccruedDays;
  private Double payLegAccrued;
  private Double receiveLegPV;
  private Integer receiveLegAccruedDays;
  private Double receiveLegAccrued;
  private Double pv01;
  private Double pv01LocalCcy;
  private Double dv01;
  private Double dv01LocalCcy;
  private Double br01;
  private Double br01LocalCcy;
  private Double inf01;
  private Double inf01LocalCcy;
  private Double cs01;
  private Double cs01LocalCcy;
  private Double infcsbr01;
  private Double infcsbr01LocalCcy;
  private Double deltaFwd;
  private Double deltaFwdLocalCcy;
  private Double deltaSpot;
  private Double deltaSpotLocalCcy;
  private Double fxSpot;
  private OptionMetricsDto optionMetrics;
}
