package com.solum.xplain.valuation.messages.calibration.credits;

import com.opengamma.strata.pricer.credit.ImmutableCreditRatesProvider;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class ValuationCreditRates implements Serializable {
  private final ImmutableCreditRatesProvider creditRatesProvider;
  private final List<FxShiftedValuationCreditCurveRates> fxShiftedRates;
}
