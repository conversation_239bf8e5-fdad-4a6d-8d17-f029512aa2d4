spring:
  hazelcast:
    config:
  data:
    redis:
      # cluster:
      # If wanting a clustered configuration then set the following environment variable in docker-compose.yml or Kubernetes
      # SPRING_DATA_REDIS_CLUSTER_NODES = localhost:6379,localhost:6380,localhost:6381,localhost:6382,localhost:6383,localhost:6384
      # SPRING_DATA_REDIS_CLUSTER_MAX-REDIRECTS = 3
      # should be unique for each instance for locking purposes
      client-name: xplain
      client-type: lettuce
      repositories:
        enabled: false
      timeout: 2000ms
  cache:
    type: redis

app:
  data-grid:
    hazelcast:
      enabled: false
    redis:
      enabled: true
