package com.solum.xplain.valuation.calibration.vols;

import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(exclude = "expiryYearFraction")
public class ExpirySkewKey {
  private final String expiry;
  private final double expiryYearFraction;
  private final BigDecimal skew;

  public ExpirySkewKey(String expiry, double expiryYearFraction, BigDecimal skew) {
    this.expiry = expiry;
    this.expiryYearFraction = expiryYearFraction;
    this.skew = skew != null ? skew.stripTrailingZeros() : null;
  }
}
