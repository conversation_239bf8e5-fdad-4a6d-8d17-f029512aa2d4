package com.solum.xplain.valuation.calculation.sensitivity;

import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.pricer.fra.DiscountingFraTradePricer;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.fra.Fra;
import com.opengamma.strata.product.fra.FraTrade;
import com.opengamma.strata.product.fra.ResolvedFraTrade;
import java.util.function.Function;

public final class FraPv01Calculation {

  private FraPv01Calculation() {}

  /**
   * @return a shifted fra trade by updating the fixed rate by 1 basis point (0.0001)
   */
  public static FraTrade buildShiftedFraTrade(FraTrade trade) {
    Fra currentProduct = trade.getProduct();
    Fra shiftedProduct =
        currentProduct.toBuilder()
            .fixedRate(currentProduct.getFixedRate() + ONE_BASIS_POINT)
            .build();

    return trade.toBuilder().product(shiftedProduct).build();
  }

  public static CurrencyAmount calculatePv01(
      FraTrade trade,
      CurrencyAmount pv,
      double tZeroNet,
      RatesProvider rates,
      ReferenceData refData,
      DiscountingFraTradePricer fraTradePricer,
      Function<ResolvedFraTrade, Double> tZeroNetFn) {

    FraTrade shiftedTrade = buildShiftedFraTrade(trade);

    var cfAdjPv = pv.minus(tZeroNet);
    var resolvedShiftedTrade = shiftedTrade.resolve(refData);
    var shiftedPv = fraTradePricer.presentValue(resolvedShiftedTrade, rates);

    return shiftedPv.minus(cfAdjPv);
  }
}
