package com.solum.xplain.valuation.calculation.credit;

import static com.opengamma.strata.pricer.common.PriceType.CLEAN;
import static com.opengamma.strata.pricer.common.PriceType.DIRTY;
import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;
import static com.solum.xplain.valuation.calculation.TradeUtils.getClientPv;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.currency.Payment;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.market.curve.CurveInfoType;
import com.opengamma.strata.pricer.credit.AccrualOnDefaultFormula;
import com.opengamma.strata.pricer.credit.CreditDiscountFactors;
import com.opengamma.strata.pricer.credit.CreditRatesProvider;
import com.opengamma.strata.pricer.credit.IsdaCdsProductPricer;
import com.opengamma.strata.pricer.credit.IsdaCreditDiscountFactors;
import com.opengamma.strata.pricer.credit.IsdaHomogenousCdsIndexProductPricer;
import com.opengamma.strata.pricer.credit.IsdaHomogenousCdsIndexTradePricer;
import com.opengamma.strata.pricer.credit.LegalEntitySurvivalProbabilities;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.credit.CdsIndexTrade;
import com.opengamma.strata.product.credit.ResolvedCds;
import com.opengamma.strata.product.credit.ResolvedCdsIndexTrade;
import com.solum.xplain.extensions.pricers.XplainIsdaCdsProductPricer;
import com.solum.xplain.valuation.calculation.TradeCalculationUtils;
import com.solum.xplain.valuation.calculation.TradeCalculator;
import com.solum.xplain.valuation.calculation.ValuationOptions;
import com.solum.xplain.valuation.calculation.sensitivity.Dv01Calculation;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import com.solum.xplain.valuation.metrics.Metrics;
import io.atlassian.fugue.Either;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CreditIndexTradeCalculator extends CreditTradeCalculation<ResolvedCdsIndexTrade>
    implements TradeCalculator {
  private static final IsdaCdsProductPricer ISDA_CDS_PRODUCT_PRICER =
      new XplainIsdaCdsProductPricer(AccrualOnDefaultFormula.ORIGINAL_ISDA);
  private static final IsdaHomogenousCdsIndexProductPricer PRODUCT_PRICER =
      new IsdaHomogenousCdsIndexProductPricer(ISDA_CDS_PRODUCT_PRICER);
  private static final IsdaHomogenousCdsIndexTradePricer TRADE_PRICER =
      new IsdaHomogenousCdsIndexTradePricer(PRODUCT_PRICER);

  private final CdsIndexTrade trade;
  private final ValuationOptions options;
  private final Supplier<Map<String, CalibrationCurve>> curvesSupplier;
  private final RatesProvider ratesProvider;
  private final CreditRatesProvider creditRatesProvider;

  public CreditIndexTradeCalculator(
      CdsIndexTrade trade,
      ValuationOptions options,
      Supplier<Map<String, CalibrationCurve>> curvesSupplier,
      RatesProvider ratesProvider,
      ValuationCreditRates valuationCreditRates) {
    super(options, ratesProvider, valuationCreditRates);
    this.trade = trade;
    this.options = options;
    this.curvesSupplier = curvesSupplier;
    this.ratesProvider = ratesProvider;
    this.creditRatesProvider = valuationCreditRates.getCreditRatesProvider();
  }

  @Override
  public Metrics calculate(ReferenceData refData) {
    var resolvedTrade = trade.resolve(refData);
    var cds = resolvedTrade.toSingleNameCds().getProduct();
    return calculateCreditMetrics(resolvedTrade, cds, getClientPv(trade), refData);
  }

  @Override
  protected CurrencyAmount productPv(
      CreditRatesProvider creditRates, ResolvedCdsIndexTrade resolvedTrade, ReferenceData refData) {
    return PRODUCT_PRICER.presentValue(
        resolvedTrade.getProduct(), creditRates, creditRates.getValuationDate(), DIRTY, refData);
  }

  @Override
  protected double protectionLegPv(ResolvedCds cds, ReferenceData refData) {
      //recovery rate here is used to calculate the protection leg
      // using cds and creditRatesProvider
      //double recoveryRate = this.recoveryRate(cds, ratesProvider);
    var protectionLeg =
        ISDA_CDS_PRODUCT_PRICER.protectionLeg(
            cds, creditRatesProvider, ratesProvider.getValuationDate(), refData);

    double protectionLegPv =
        cds.getNotional() * getCdxFactor(cds, creditRatesProvider) * protectionLeg;
    if (!Objects.equals(options.getReportingCcy(), options.getTradeCcy())) {
      protectionLegPv =
          ratesProvider.convert(protectionLegPv, options.getTradeCcy(), options.getReportingCcy());
    }
    return protectionLegPv;
  }

  @Override
  protected CurrencyAmount pv01(ResolvedCdsIndexTrade resolvedTrade, ReferenceData refData) {
    return TRADE_PRICER
        .rpv01OnSettle(resolvedTrade, creditRatesProvider, DIRTY, refData)
        .multipliedBy(ONE_BASIS_POINT);
  }

  @Override
  protected CurrencyAmount cleanValue(ResolvedCdsIndexTrade resolvedTrade, ReferenceData refData) {
    return TRADE_PRICER.presentValue(resolvedTrade, creditRatesProvider, CLEAN, refData);
  }

  @Override
  protected Either<ValuationError, BreakevenMetrics> breakevenMetrics(
      ResolvedCdsIndexTrade resolvedTrade, ReferenceData refData) {
    return TradeCalculationUtils.eitherBreakevenMetrics(
        resolvedTrade, () -> calculateBreakevenMetrics(resolvedTrade, refData));
  }

  private BreakevenMetrics calculateBreakevenMetrics(
      ResolvedCdsIndexTrade resolvedTrade, ReferenceData refData) {
    var parSpread = TRADE_PRICER.parSpread(resolvedTrade, creditRatesProvider, refData);
    return BreakevenMetrics.parRate(parSpread);
  }

  @Override
  protected Optional<Payment> upfront(ResolvedCdsIndexTrade resolvedTrade) {
    return resolvedTrade.getUpfrontFee();
  }

  @Override
  protected Dv01Calculation dv01Calculation(
      ResolvedCdsIndexTrade trade, ReferenceData referenceData) {
    var sensitivity =
        TRADE_PRICER.presentValueSensitivity(trade, creditRatesProvider, referenceData);
    return new Dv01Calculation(
        options.getReportingCcy(), options.getTradeCcy(), sensitivity, curvesSupplier.get());
  }

  @Override
  public double getCdxFactor(ResolvedCds cds, CreditRatesProvider ratesProvider) {
    Currency currency = cds.getCurrency();
    CreditDiscountFactors discountFactors = ratesProvider.discountFactors(currency);
    ArgChecker.isTrue(
        discountFactors.isIsdaCompliant(),
        "discount factors must be IsdaCompliantZeroRateDiscountFactors");
    LegalEntitySurvivalProbabilities survivalProbabilities =
        ratesProvider.survivalProbabilities(cds.getLegalEntityId(), currency);
    ArgChecker.isTrue(
        survivalProbabilities.getSurvivalProbabilities().isIsdaCompliant(),
        "survival probabilities must be IsdaCompliantZeroRateDiscountFactors");
    ArgChecker.isTrue(
        discountFactors
            .getDayCount()
            .equals(survivalProbabilities.getSurvivalProbabilities().getDayCount()),
        "day count conventions of discounting curve and credit curve must be the same");
    return ((IsdaCreditDiscountFactors) survivalProbabilities.getSurvivalProbabilities())
        .getCurve()
        .getMetadata()
        .getInfo(CurveInfoType.CDS_INDEX_FACTOR);
  }
}
