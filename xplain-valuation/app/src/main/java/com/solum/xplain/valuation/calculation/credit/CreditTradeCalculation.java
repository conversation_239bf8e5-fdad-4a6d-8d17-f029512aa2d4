package com.solum.xplain.valuation.calculation.credit;

import static com.opengamma.strata.basics.currency.CurrencyAmount.zero;
import static com.opengamma.strata.product.common.BuySell.BUY;
import static com.opengamma.strata.product.common.BuySell.SELL;
import static com.solum.xplain.valuation.metrics.CashFlowMetrics.ofPayments;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.currency.Payment;
import com.opengamma.strata.pricer.DiscountingPaymentPricer;
import com.opengamma.strata.pricer.credit.CreditRatesProvider;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.ResolvedTrade;
import com.opengamma.strata.product.credit.CreditCouponPaymentPeriod;
import com.opengamma.strata.product.credit.ResolvedCds;
import com.solum.xplain.valuation.calculation.ValuationOptions;
import com.solum.xplain.valuation.calculation.sensitivity.Dv01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.CreditSpot01Calculation;
import com.solum.xplain.valuation.calculation.sensitivity.spot01.Spot01CalculationResult;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import com.solum.xplain.valuation.metrics.CashFlowMetrics;
import com.solum.xplain.valuation.metrics.Metrics;
import io.atlassian.fugue.Either;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor
public abstract class CreditTradeCalculation<T extends ResolvedTrade> {
  private static final DiscountingPaymentPricer PAYMENT_PRICER = DiscountingPaymentPricer.DEFAULT;

  private final ValuationOptions options;
  private final RatesProvider ratesProvider;
  private final ValuationCreditRates creditRates;

  protected Metrics calculateCreditMetrics(
      T resolvedTrade, ResolvedCds cds, Double clientPv, ReferenceData refData) {
    var tradeCcy = options.getTradeCcy();
    var reportingCcy = options.getReportingCcy();

    var premiumPv = premiumPv(resolvedTrade);
    var protectionLegPv = protectionLegPv(cds, refData);
    var feeCurrentCash = feeCurrentCash(resolvedTrade);
    var productPv = productPv(creditRates.getCreditRatesProvider(), resolvedTrade, refData);
    var pv01 = pv01(resolvedTrade, refData);
    var breakEvenMetrics = breakevenMetrics(resolvedTrade, refData);

    var premiumT0TradeCcy = feeCurrentCash.convertedTo(tradeCcy, ratesProvider);
    var premiumT0Amount = feeCurrentCash.convertedTo(tradeCcy, ratesProvider).getAmount();
    var payPremiumT0 = premiumT0Amount > 0 ? premiumT0Amount : 0;
    var recPremiumT0 = premiumT0Amount < 0 ? premiumT0Amount : 0;
    var cashFlowMetrics = cashFlowMetrics(cds, recPremiumT0, payPremiumT0);
    var pv = productPv.plus(premiumPv).minus(premiumT0TradeCcy);
    var pvReportingCurrency = pv.convertedTo(reportingCcy, ratesProvider);

    var builder =
        Metrics.builder(clientPv)
            .localCcy(tradeCcy.getCode())
            .presentValue(pvReportingCurrency.getAmount())
            .presentValuePayLegCurrency(pv.getAmount());

    var quotedAccrued = riskAnnuityAccountingBasedQuotedAccrued(cds, refData);

    if (cds.getBuySell() == BUY) {
      double plusPremium = premiumT0Amount < 0 ? premiumT0Amount : 0;
      builder.payLegPV(productPv.getAmount() + plusPremium - protectionLegPv - payPremiumT0);
      builder.receiveLegPV(protectionLegPv - recPremiumT0);
      quotedAccrued = quotedAccrued.negated();
      builder.payLegAccrued(quotedAccrued.getAmount());
    } else {
      double plusPremium = premiumT0Amount > 0 ? premiumT0Amount : 0;
      builder.payLegPV(-protectionLegPv - payPremiumT0);
      builder.receiveLegPV(productPv.getAmount() + plusPremium + protectionLegPv - recPremiumT0);
      builder.receiveLegAccrued(quotedAccrued.getAmount());
    }

    var cleanValue = pv.minus(quotedAccrued);
    var cleanValueReportingCurrency =
        pvReportingCurrency
            .minus(quotedAccrued.convertedTo(reportingCcy, ratesProvider))
            .getAmount();

    builder
        .pv01(pv01.convertedTo(reportingCcy, ratesProvider).getAmount())
        .pv01LocalCcy(pv01.getAmount())
        .cleanPresentValue(cleanValueReportingCurrency)
        .cashFlowMetrics(cashFlowMetrics)
        .cleanPresentValueLocalCcy(cleanValue.getAmount())
        .accrued(quotedAccrued.convertedTo(reportingCcy, ratesProvider).getAmount())
        .accruedLocalCcy(quotedAccrued.getAmount())
        .breakevenMetrics(breakEvenMetrics);
    if (options.isCalculateSensitivities()) {
      var dv01Calculation = dv01Calculation(resolvedTrade, refData);
      builder.with(
          dv01Calculation.calculateCredit(ratesProvider, creditRates.getCreditRatesProvider()));
      var spot01 = calculateSpot01(productPv, resolvedTrade, refData);
      builder.with(spot01);
    }
    return builder.build();
  }

  private CurrencyAmount premiumPv(T resolvedTrade) {
    return upfront(resolvedTrade)
        .map(p -> PAYMENT_PRICER.presentValue(p, ratesProvider))
        .orElse(zero(options.getTradeCcy()));
  }

  private CurrencyAmount feeCurrentCash(T resolvedTrade) {
    return upfront(resolvedTrade)
        .map(uf -> PAYMENT_PRICER.currentCash(uf, ratesProvider))
        .orElse(zero(options.getTradeCcy()));
  }

  private CashFlowMetrics cashFlowMetrics(
      ResolvedCds cds, double recPremiumT0, double payPremiumT0) {
    var normalizedNotional = cds.getBuySell().normalize(cds.getNotional());
    var notionalValue = normalizedNotional * cds.getFixedRate();
    var t0 =
        cds.getPaymentPeriods().stream()
            .filter(coupons -> coupons.getEndDate().equals(ratesProvider.getValuationDate()))
            .findAny()
            .map(
                coupon ->
                    CurrencyAmount.of(cds.getCurrency(), notionalValue * coupon.getYearFraction()))
            .orElse(zero(cds.getCurrency()));

    return ofPayments(
        currencyAmount(
            options.getTradeCcy(), recPremiumT0, t0.getAmount(), cds.getBuySell() == BUY),
        currencyAmount(
            options.getTradeCcy(), payPremiumT0, t0.getAmount(), cds.getBuySell() == SELL),
        null,
        options.getReportingCcy(),
        options.getTradeCcy(),
        ratesProvider);
  }

  private Spot01CalculationResult calculateSpot01(
      CurrencyAmount presentValue, T resolvedTrade, ReferenceData refData) {
    var spot01Calculation =
        CreditSpot01Calculation.fromShiftedCreditRates(
            ratesProvider, creditRates.getFxShiftedRates());
    return spot01Calculation.calculateSpot01(
        presentValue, provider -> productPv(provider, resolvedTrade, refData));
  }

  protected abstract double protectionLegPv(ResolvedCds cds, ReferenceData refData);

  protected abstract CurrencyAmount productPv(
      CreditRatesProvider ratesProvider, T resolvedTrade, ReferenceData refData);

  protected abstract CurrencyAmount pv01(T resolvedTrade, ReferenceData refData);

  protected abstract CurrencyAmount cleanValue(T resolvedTrade, ReferenceData refData);

  protected abstract Either<ValuationError, BreakevenMetrics> breakevenMetrics(
      T resolvedTrade, ReferenceData refData);

  protected abstract Optional<Payment> upfront(T resolvedTrade);

  protected abstract Dv01Calculation dv01Calculation(T resolvedTrade, ReferenceData referenceData);

  protected abstract double getCdxFactor(ResolvedCds cds, CreditRatesProvider creditRatesProvider);

  private CurrencyAmount currencyAmount(
      Currency tradeCcy, double premium, double t0, boolean addT0) {
    var amount = premium;
    if (addT0) {
      amount += t0;
    }
    return CurrencyAmount.of(tradeCcy, amount);
  }

  /**
   * Accrued interest based on the risky annuity (fixed rate payment) for trades as if they were
   * newly quoted. For new trades which have a step-in date equal to an IMM date, the accrued
   * interest is removed. The only time this differs from market-quoted cds is when the valuation
   * date is equal to a period end date - step-in offset. Here, the accrued interest is not removed.
   * For market-quoted cds, the accrued interest would be removed.
   *
   * @param cds - the resolved cds
   * @param referenceData - the reference data
   * @return the accrued interest
   */
  private CurrencyAmount riskAnnuityAccountingBasedQuotedAccrued(
      ResolvedCds cds, ReferenceData referenceData) {

    var firstApplicablePeriod = resolveFirstApplicablePeriod(cds);

    // short-circuit - matured trade
    if (firstApplicablePeriod.isEmpty()) {
      return CurrencyAmount.zero(cds.getCurrency());
    }

    var firstPeriod = firstApplicablePeriod.get();
    var quotedStartDate =
        cds.getStepinDateOffset().adjust(ratesProvider.getValuationDate(), referenceData);
    var firstPeriodStartDate = firstPeriod.getStartDate();
    var firstPeriodEndDate = firstPeriod.getEndDate();
    var dayCount = cds.getDayCount();

    // for forward-starting trades, the accrued interest is zero
    if (quotedStartDate.isBefore(firstPeriodStartDate)) {
      return CurrencyAmount.zero(cds.getCurrency());
    }

    if (!quotedStartDate.isBefore(firstPeriodEndDate)) {
      // bifurcation point from market-quoted cds - include accrued interest when valuation date
      // equals end date - step-in offset
      var accruedYearFraction =
          quotedStartDate.isEqual(firstPeriodEndDate)
              ? dayCount.yearFraction(firstPeriodStartDate, firstPeriodEndDate)
              : dayCount.yearFraction(firstPeriodEndDate, quotedStartDate);
      return riskAnnuityAccrued(cds, accruedYearFraction);
    }
    var accruedYearFraction = dayCount.yearFraction(firstPeriodStartDate, quotedStartDate);
    return riskAnnuityAccrued(cds, accruedYearFraction);
  }

  private CurrencyAmount riskAnnuityAccrued(ResolvedCds cds, double accruedYearFraction) {
    var currency = options.getTradeCcy();
    var notional = resolveFactorAdjustedNotional(cds, creditRates);
    var coupon = cds.getFixedRate();

    return CurrencyAmount.of(currency, notional * coupon * accruedYearFraction);
  }

  /**
   * Loops through the payment periods of the cds, returning the first payment period (excluding
   * payment periods which have already occurred, by reference to the valuation date). If no payment
   * period is returned, the caller of this method should assume that the cds has not started yet,
   * or it is expired.
   *
   * @param cds - the cds resolved with reference data
   * @return the first applicable payment period
   */
  private Optional<CreditCouponPaymentPeriod> resolveFirstApplicablePeriod(ResolvedCds cds) {
    var paymentPeriods = cds.getPaymentPeriods();

    if (!ratesProvider.getValuationDate().isBefore(cds.getProtectionEndDate())) {
      return Optional.empty();
    }

    for (CreditCouponPaymentPeriod creditCouponPaymentPeriod : paymentPeriods) {
      if (ratesProvider.getValuationDate().isBefore(creditCouponPaymentPeriod.getEndDate())) {
        return Optional.of(creditCouponPaymentPeriod);
      }
    }
    return Optional.empty();
  }

  private double resolveFactorAdjustedNotional(ResolvedCds cds, ValuationCreditRates creditRates) {
    return cds.getNotional() * getCdxFactor(cds, creditRates.getCreditRatesProvider());
  }
}
