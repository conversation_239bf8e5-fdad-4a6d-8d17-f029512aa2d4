package com.solum.xplain.valuation.calculation.credit;

import static com.opengamma.strata.pricer.common.PriceType.CLEAN;
import static com.opengamma.strata.pricer.common.PriceType.DIRTY;
import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;
import static com.solum.xplain.valuation.calculation.TradeUtils.getClientPv;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.currency.Payment;
import com.opengamma.strata.pricer.credit.AccrualOnDefaultFormula;
import com.opengamma.strata.pricer.credit.CreditRatesProvider;
import com.opengamma.strata.pricer.credit.IsdaCdsProductPricer;
import com.opengamma.strata.pricer.credit.IsdaCdsTradePricer;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.credit.CdsTrade;
import com.opengamma.strata.product.credit.ResolvedCds;
import com.opengamma.strata.product.credit.ResolvedCdsTrade;
import com.solum.xplain.extensions.pricers.XplainIsdaCdsProductPricer;
import com.solum.xplain.valuation.calculation.TradeCalculationUtils;
import com.solum.xplain.valuation.calculation.TradeCalculator;
import com.solum.xplain.valuation.calculation.ValuationOptions;
import com.solum.xplain.valuation.calculation.sensitivity.Dv01Calculation;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.calibration.credits.ValuationCreditRates;
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve;
import com.solum.xplain.valuation.metrics.BreakevenMetrics;
import com.solum.xplain.valuation.metrics.Metrics;
import io.atlassian.fugue.Either;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CdsTradeCalculator extends CreditTradeCalculation<ResolvedCdsTrade>
    implements TradeCalculator {

  private static final IsdaCdsProductPricer PRODUCT_PRICER =
      new XplainIsdaCdsProductPricer(AccrualOnDefaultFormula.ORIGINAL_ISDA);
  private static final IsdaCdsTradePricer TRADE_PRICER = new IsdaCdsTradePricer(PRODUCT_PRICER);

  private final CdsTrade trade;
  private final ValuationOptions options;
  private final Supplier<Map<String, CalibrationCurve>> curvesSupplier;
  private final RatesProvider ratesProvider;
  private final CreditRatesProvider creditRatesProvider;

  public CdsTradeCalculator(
      CdsTrade trade,
      ValuationOptions options,
      Supplier<Map<String, CalibrationCurve>> curvesSupplier,
      RatesProvider ratesProvider,
      ValuationCreditRates creditRates) {
    super(options, ratesProvider, creditRates);
    this.trade = trade;
    this.options = options;
    this.curvesSupplier = curvesSupplier;
    this.ratesProvider = ratesProvider;
    this.creditRatesProvider = creditRates.getCreditRatesProvider();
  }

  @Override
  public Metrics calculate(ReferenceData refData) {
    var resolvedTrade = trade.resolve(refData);
    var cds = resolvedTrade.getProduct();
    return calculateCreditMetrics(resolvedTrade, cds, getClientPv(trade), refData);
  }

  @Override
  protected CurrencyAmount productPv(
      CreditRatesProvider creditRates, ResolvedCdsTrade resolvedTrade, ReferenceData refData) {
    return PRODUCT_PRICER.presentValue(
        resolvedTrade.getProduct(), creditRates, creditRates.getValuationDate(), DIRTY, refData);
  }

  @Override
  protected double protectionLegPv(ResolvedCds cds, ReferenceData refData) {
    double protectionLegPv =
        cds.getNotional()
            * XplainIsdaCdsProductPricer.DEFAULT.protectionLeg(
                cds, creditRatesProvider, ratesProvider.getValuationDate(), refData);
    if (!Objects.equals(options.getReportingCcy(), options.getTradeCcy())) {
      protectionLegPv =
          ratesProvider.convert(protectionLegPv, options.getTradeCcy(), options.getReportingCcy());
    }
    return protectionLegPv;
  }

  @Override
  protected CurrencyAmount pv01(ResolvedCdsTrade resolvedTrade, ReferenceData refData) {
    return TRADE_PRICER
        .rpv01OnSettle(resolvedTrade, creditRatesProvider, DIRTY, refData)
        .multipliedBy(ONE_BASIS_POINT);
  }

  @Override
  protected CurrencyAmount cleanValue(ResolvedCdsTrade resolvedTrade, ReferenceData refData) {
    return TRADE_PRICER.presentValue(resolvedTrade, creditRatesProvider, CLEAN, refData);
  }

  @Override
  protected Either<ValuationError, BreakevenMetrics> breakevenMetrics(
      ResolvedCdsTrade resolvedTrade, ReferenceData refData) {
    return TradeCalculationUtils.eitherBreakevenMetrics(
        resolvedTrade, () -> calculateBreakEvenMetrics(resolvedTrade, refData));
  }

  private BreakevenMetrics calculateBreakEvenMetrics(
      ResolvedCdsTrade resolvedTrade, ReferenceData refData) {
    var parSpread = TRADE_PRICER.parSpread(resolvedTrade, creditRatesProvider, refData);
    return BreakevenMetrics.parRate(parSpread);
  }

  @Override
  protected Optional<Payment> upfront(ResolvedCdsTrade resolvedTrade) {
    return resolvedTrade.getUpfrontFee();
  }

  @Override
  protected Dv01Calculation dv01Calculation(
      ResolvedCdsTrade resolvedTrade, ReferenceData referenceData) {
    var sensitivity =
        TRADE_PRICER.presentValueSensitivity(resolvedTrade, creditRatesProvider, referenceData);
    return new Dv01Calculation(
        options.getReportingCcy(), options.getTradeCcy(), sensitivity, curvesSupplier.get());
  }

  @Override
  public double getCdxFactor(ResolvedCds cds, CreditRatesProvider creditRatesProvider) {
    // for CDS, factor is just 1.0
    return 1.0;
  }
}
