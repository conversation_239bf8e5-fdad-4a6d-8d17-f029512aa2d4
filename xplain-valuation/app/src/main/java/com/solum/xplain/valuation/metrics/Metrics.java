package com.solum.xplain.valuation.metrics;

import static com.solum.xplain.valuation.calculation.difference.PresentValueDifferencePricer.DIFFERENCE_PRICER;

import com.solum.xplain.valuation.calculation.difference.DifferenceMetrics;
import com.solum.xplain.valuation.calculation.loan.LoanMetrics;
import com.solum.xplain.valuation.common.ValuationError;
import io.atlassian.fugue.Either;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import org.springframework.lang.Nullable;

@Data
@Builder(buildMethodName = "buildMetrics")
public class Metrics {

  private String localCcy;
  private Double presentValue;
  private Double presentValuePayLegCurrency;
  private Double presentValuePercent;
  private Double accrued;
  private Double accruedLocalCcy;
  private Double accruedPercent;
  private Double cleanPresentValue;
  private Double cleanPresentValueLocalCcy;
  private Double cleanPresentValuePercent;
  private Double premiumValue;
  private Double discountedPremiumValue;
  private Double premiumValueTradeCcy;
  private Double discountedPremiumValueTradeCcy;
  private Double payLegPV;
  private Integer payLegAccruedDays;
  private Double payLegAccrued;
  private Double receiveLegPV;
  private Integer receiveLegAccruedDays;
  private Double receiveLegAccrued;
  private Double pv01;
  private Double pv01LocalCcy;
  private Double dv01;
  private Double dv01LocalCcy;
  private Double br01;
  private Double br01LocalCcy;
  private Double inf01;
  private Double inf01LocalCcy;
  private Double cs01;
  private Double cs01LocalCcy;
  private Double infcsbr01;
  private Double infcsbr01LocalCcy;
  private Double fxSpot;
  private Double deltaFwd;
  private Double deltaFwdLocalCcy;
  private Double deltaSpot;
  private Double deltaSpotLocalCcy;
  private List<DV01TradeValue> dv01TradeValues;
  private List<DV01TradeValue> br01TradeValues;
  private List<DV01TradeValue> inf01TradeValues;
  private List<DV01TradeValue> cs01TradeValues;
  private List<DV01TradeValue> deltaFwdTradeValues;
  private List<DV01TradeValue> deltaSpotTradeValues;
  private List<Spot01TradeValue> spot01TradeValues;
  private List<CashFlowValue> cashFlows;
  private CashFlowMetrics cashFlowMetrics;
  private ClientMetrics clientMetrics;
  private OptionMetrics optionMetrics;
  private BreakevenMetrics breakevenMetrics;
  private InflationMetrics inflationMetrics;
  private DifferenceMetrics differenceMetrics;
  private LoanMetrics loanMetrics;

  /**
   * If this trade is calculated by synthesising multiple individual sub-trades and combining
   * metrics then this holds the metrics for the synthetic sub-trades e.g. 2 x FxSingle for FxSwap
   * and 2 x FxOption for FxCollar. Null if there aren't any.
   */
  private List<Metrics> syntheticMetrics;

  private List<String> warnings;

  public static MetricsBuilder builder(@Nullable Double clientPv) {
    return new MetricsBuilder()
        .clientMetrics(new ClientMetrics(clientPv))
        .warnings(new ArrayList<>());
  }

  public static class MetricsBuilder {

    public MetricsBuilder with(AssignableToMetrics metrics) {
      return metrics.assignToMetrics(this);
    }

    public MetricsBuilder breakevenMetrics(Either<ValuationError, BreakevenMetrics> eitherMetrics) {
      eitherMetrics.fold(
          err -> warnings.add(err.getDescription()), metric -> this.breakevenMetrics = metric);
      return this;
    }

    public Metrics build() {
      differenceMetrics = DIFFERENCE_PRICER.calculate(presentValue, dv01, clientMetrics);
      return buildMetrics();
    }
  }
}
