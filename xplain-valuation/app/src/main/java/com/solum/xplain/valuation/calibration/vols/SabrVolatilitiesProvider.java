package com.solum.xplain.valuation.calibration.vols;

import static com.opengamma.strata.basics.date.DayCounts.ACT_365F;

import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.surface.ConstantSurface;
import com.opengamma.strata.market.surface.DefaultSurfaceMetadata;
import com.opengamma.strata.market.surface.Surface;
import com.opengamma.strata.market.surface.SurfaceMetadata;
import com.opengamma.strata.pricer.DiscountingPaymentPricer;
import com.opengamma.strata.pricer.option.TenorRawOptionData;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.pricer.swaption.SabrSwaptionCalibrator;
import com.opengamma.strata.pricer.swaption.SabrSwaptionCashParYieldProductPricer;
import com.opengamma.strata.pricer.swaption.SabrSwaptionDefinition;
import com.opengamma.strata.pricer.swaption.SabrSwaptionPhysicalProductPricer;
import com.opengamma.strata.pricer.swaption.SwaptionVolatilities;
import com.opengamma.strata.pricer.swaption.SwaptionVolatilitiesName;
import com.opengamma.strata.pricer.swaption.VolatilitySwaptionProductPricer;
import com.opengamma.strata.pricer.swaption.VolatilitySwaptionTradePricer;
import com.opengamma.strata.product.swap.type.FixedFloatSwapConvention;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceConfig;
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceNode;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;

@AllArgsConstructor(staticName = "newOf")
public class SabrVolatilitiesProvider implements VolatilitiesProvider {
  private static final VolatilitySwaptionProductPricer SABR_PRODUCT_PRICER =
      new VolatilitySwaptionProductPricer(
          SabrSwaptionCashParYieldProductPricer.DEFAULT, SabrSwaptionPhysicalProductPricer.DEFAULT);
  private static final VolatilitySwaptionTradePricer SABR_TRADE_PRICER =
      new VolatilitySwaptionTradePricer(SABR_PRODUCT_PRICER, DiscountingPaymentPricer.DEFAULT);

  private static final SurfaceMetadata BETA_META =
      DefaultSurfaceMetadata.builder()
          .xValueType(ValueType.YEAR_FRACTION)
          .yValueType(ValueType.YEAR_FRACTION)
          .zValueType(ValueType.SABR_BETA)
          .surfaceName("Beta")
          .build();
  private final SurfaceConfig zippedSurfaceConfig;
  private final TenorRawOptionData tenorRawOptionData;
  private final Surface betaSurface;
  private final Surface shiftSurface;

  public static Either<ValuationError, VolatilitiesProvider> newOf(
      SabrVolatilitiesDataFactory dataFactory,
      SurfaceConfig surfaceConfig,
      Map<BigDecimal, List<SurfaceNode>> skews) {
    return dataFactory
        .buildSabrData(surfaceConfig, skews)
        .map(
            data ->
                SabrVolatilitiesProvider.newOf(
                    surfaceConfig,
                    data,
                    ConstantSurface.of(BETA_META, surfaceConfig.getSabrBeta()),
                    ConstantSurface.of("SABR-Shift", surfaceConfig.getSabrShift())));
  }

  @Override
  public SwaptionVolatilities swaptionVolatilities(
      LocalDate startDate, LocalDate endDate, RatesProvider rates) {
    var definition =
        SabrSwaptionDefinition.of(
            SwaptionVolatilitiesName.of(zippedSurfaceConfig.getSurfaceName()),
            FixedFloatSwapConvention.of(zippedSurfaceConfig.getConvention()),
            ACT_365F,
            VolatilitySurfaceUtils.gridSurfaceInterpolator(zippedSurfaceConfig));
    var calibrationDateTime =
        ZonedDateTime.of(
            rates.toImmutableRatesProvider().getValuationDate(),
            LocalTime.MIDNIGHT,
            ZoneId.systemDefault());

    return SabrSwaptionCalibrator.DEFAULT.calibrateWithFixedBetaAndShift(
        definition,
        calibrationDateTime,
        tenorRawOptionData,
        rates.toImmutableRatesProvider(),
        betaSurface,
        shiftSurface);
  }

  @Override
  public VolatilitySwaptionProductPricer productPricer() {
    return SABR_PRODUCT_PRICER;
  }

  @Override
  public VolatilitySwaptionTradePricer tradePricer() {
    return SABR_TRADE_PRICER;
  }
}
