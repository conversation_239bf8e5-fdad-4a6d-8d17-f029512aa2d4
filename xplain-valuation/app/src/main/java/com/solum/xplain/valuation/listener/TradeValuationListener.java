package com.solum.xplain.valuation.listener;

import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.valuation.calculation.CalculationExecutor;
import com.solum.xplain.valuation.messages.metrics.ValuationResponse;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class TradeValuationListener {

  private static final Logger LOG = getLogger(TradeValuationListener.class);

  private final CalculationExecutor executor;
  private final KafkaTemplate<String, ValuationResponse> kafkaTemplate;
  private final String metricsTopic;

  public TradeValuationListener(
      CalculationExecutor executor,
      KafkaTemplate<String, ValuationResponse> kafkaTemplate,
      @Value("${app.calculation.topics.metrics-topic}") String metricsTopic) {
    this.executor = executor;
    this.kafkaTemplate = kafkaTemplate;
    this.metricsTopic = metricsTopic;
  }

  @KafkaListener(topics = "${app.calculation.topics.valuations-topic}")
  public void onRequest(@Payload ValuationRequest valuationRequest) {
    LOG.debug("Processing {}", valuationRequest.getExternalTradeId());
    var response = executor.valuate(valuationRequest);
    kafkaTemplate.send(metricsTopic, response);
  }
}
