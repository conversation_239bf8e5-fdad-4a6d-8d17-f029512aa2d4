package com.solum.xplain.valuation.calculation

import static com.opengamma.strata.basics.currency.Currency.AUD
import static com.opengamma.strata.basics.currency.Currency.BRL
import static com.opengamma.strata.basics.currency.Currency.CLP
import static com.opengamma.strata.basics.currency.Currency.CNY
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.currency.FxRate
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.array.DoubleMatrix
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries
import com.opengamma.strata.data.FxRateId
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.ShiftType
import com.opengamma.strata.market.curve.Curve
import com.opengamma.strata.market.curve.CurveInfoType
import com.opengamma.strata.market.curve.CurveMetadata
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveParameterSize
import com.opengamma.strata.market.curve.Curves
import com.opengamma.strata.market.curve.InflationNodalCurve
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix
import com.opengamma.strata.market.curve.NodalCurve
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.extensions.constants.OvernightIndexConstants
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurve
import com.solum.xplain.valuation.messages.calibration.curves.CalibrationCurveType
import com.solum.xplain.valuation.messages.calibration.rates.FxShiftedValuationCurveRates
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates
import java.time.LocalDate

class CalibrationRatesSample {
  private static final double[][] USD_FED_FUND_JACOBIAN_MATRIX = [
    [
      -2.5599466396211516E-27,
      4.525409161560521E-28,
      -1.2208809130342854E-27,
      -1.5086093223410398E-16,
      1.4678834531101932E-18,
      1.0138745256664423,
      -9.247439850001461E-17,
      -2.462247249359301E-16,
      -3.475542205048085E-16,
      1.2874900798265365E-18,
      2.3107058801097313E-18
    ],
    [
      2.0437581264186737E-28,
      -3.61310548827075E-29,
      9.747251711150156E-29,
      -1.353802987248864E-17,
      6.023920356286969E-19,
      0.010990509763321814,
      0.9977390809290416,
      2.3735943165327346E-16,
      -1.4032406435367888E-16,
      -3.903127820947816E-18,
      4.87890977618477E-19
    ]
  ]

  private static final double[][] EUR_EONIA_JACOBIAN_MATRIX = [
    [
      -3.659913290736309E-24,
      6.51230892392421E-25,
      -1.7949440475327334E-24,
      1.0138745256664425,
      -5.200206313737271E-16,
      -2.3142611252547762E-17,
      -2.9103821636379322E-18,
      2.396086801192965E-16,
      7.979727989493313E-17,
      -3.5603423145095156E-21,
      -5.2160753361256546E-20
    ],
    [
      1.6377322744985656E-23,
      -4.25524083213429E-24,
      8.609724638050593E-24,
      0.01099050976332169,
      0.9977390809290421,
      -1.1558475417676876E-19,
      4.4754232357841254E-20,
      -3.0357660829594124E-17,
      2.76146293332058E-16,
      1.2529529120860093E-22,
      1.1577644908590902E-21
    ]
  ]

  private static final double[][] GBP_SONIA_JACOBIAN_MATRIX = [
    [
      0.9999805154481485,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.9994548726683539,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.9988298366398755,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.9983179300170382,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.9977988284474907,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9972714652710186,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9968117045903453,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.1102230246251565E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9963599286639047,
      0.0,
      0.0,
      0.0,
      0.0,
      -3.148944155648185E-18,
      0.0,
      6.844240336761216E-16,
      4.947490307632722E-16,
      3.0410594891527794E-17,
      7.52448029989699E-17,
      -7.26018385875449E-17,
      1.7719173160293325E-16,
      2.272250917841257E-16,
      -2.5697429789906787E-17,
      4.3262686104732666E-16,
      -2.2147335577252373E-16,
      -8.84066947303348E-18,
      1.617645969409813E-16,
      1.7322256389056697E-16,
      -2.7595968747427074E-17,
      -7.269525152536752E-17,
      9.529536494556045E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9959157087503717,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9954879033491978,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9950849618534859,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9946844065314959,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      7.097101153850024E-19,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9942828734775053,
      0.0,
      1.8535020339470958E-16,
      -9.435738818128029E-17,
      -2.0858962790238392E-16,
      -2.5307137442686626E-17,
      7.227440870532689E-17,
      1.4536333776543774E-16,
      -4.401804145494491E-17,
      -1.4367820389643046E-16,
      -3.520479370257548E-16,
      7.835626033712823E-17,
      3.130906453140822E-17,
      2.0298841750127052E-16,
      2.504491053855693E-16,
      2.4532858773298636E-16,
      9.456784042915362E-17,
      3.3006375207305684E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.794811343953435E-4,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9962518946499396,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.131050238350416E-18,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0025737256187044442,
      0.0,
      0.9971385094196744,
      -9.669172961002038E-17,
      -2.796485385381366E-16,
      -4.040177579398103E-16,
      -4.874052817197103E-16,
      -3.786856234867532E-16,
      -4.0160687715294854E-16,
      -1.064274336873821E-16,
      1.9750488843154382E-16,
      -4.0744355775031495E-16,
      -1.909871873475255E-16,
      -1.8297086422598835E-16,
      -2.5566961924804254E-16,
      -1.0979640755982227E-16,
      1.799521496441761E-17,
      -4.831602986080617E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.0384668648997079E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0016625937152792616,
      0.0,
      -0.0033366383549731805,
      0.9996490086592251,
      -1.027104317406188E-16,
      1.5070459414794959E-18,
      2.5434012638427663E-16,
      2.657850383883982E-16,
      1.3815166719926589E-17,
      -1.4250282147577626E-16,
      -4.2026466327103324E-16,
      -1.2096167244290895E-16,
      5.973521189498642E-17,
      -1.6034198033903447E-17,
      -1.2772998102499243E-16,
      -1.0993132402645212E-16,
      -3.91380043608322E-17,
      3.4829486571328477E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      2.847824484159E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0012433289891409566,
      0.0,
      -0.0024952212647577233,
      -0.0037808430526737923,
      1.0022484597437207,
      -1.7538736832305325E-16,
      -2.7855132385818805E-16,
      6.127829799235727E-17,
      2.1776026037223562E-17,
      -3.421329522781208E-16,
      1.3117318670598856E-16,
      1.711931283475878E-16,
      2.2544611718763717E-16,
      2.2377659539626424E-16,
      1.0173422885384763E-16,
      -2.0625146386523806E-16,
      9.86014113239786E-17,
      -1.0433074217920668E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.458483721311194E-18,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0010008773140100745,
      0.0,
      -0.002008648056261368,
      -0.0030435709874889277,
      -0.004031033695910093,
      1.0049006629974544,
      8.225240096344181E-17,
      4.1041175734269677E-16,
      -9.537016364391397E-17,
      3.835007822463334E-17,
      2.4355278481342895E-17,
      -1.044197211705184E-16,
      -5.761691153407897E-17,
      3.348086321202284E-17,
      1.0931886626602805E-17,
      1.399967584892435E-16,
      1.314154677006102E-16,
      1.6595069502606252E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.469640866955871E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.443267710661465E-4,
      0.0,
      -0.001694468746380886,
      -0.0025675159466665835,
      -0.003400526335131917,
      -0.004272512906005791,
      1.0077151735632444,
      1.8570948056313911E-16,
      -5.805201949642498E-17,
      -2.5790617996676547E-17,
      -1.284350354511372E-16,
      -3.0421379875668064E-16,
      2.167361104782457E-17,
      -3.7397073721538796E-17,
      8.980762114470285E-17,
      -1.9121292328233E-16,
      5.213318383760768E-17,
      -5.841816830623459E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.872998148538517E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -7.413262634653774E-4,
      0.0,
      -0.001487758326941703,
      -0.0022543013775658707,
      -0.0029856917584831686,
      -0.00375130356724047,
      -0.004512391794577011,
      1.0109112314947175,
      1.983015511459749E-16,
      -5.351070715043663E-17,
      1.472518732015721E-16,
      2.574139280929576E-16,
      -7.837093927748344E-17,
      1.8495133148142609E-16,
      2.933395798536626E-16,
      1.4852648614721272E-16,
      1.4237946216986985E-16,
      1.6473096758201633E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.018959392221874E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -6.647802300782181E-4,
      0.0,
      -0.0013341390581008035,
      -0.002021532302711998,
      -0.0026774025850223385,
      -0.003363960743635549,
      -0.004046462405607503,
      -0.004732081695236031,
      1.0142981248517635,
      4.882380572268303E-16,
      2.457818297691062E-19,
      -2.5566457344630597E-16,
      -5.3497416425944433E-17,
      1.1514094722898049E-16,
      -7.851730723251347E-19,
      1.5700814468542525E-17,
      6.940249156622835E-17,
      -4.274467065024101E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.364411533313783E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -6.096625330551543E-4,
      0.0,
      -0.0012235240472082617,
      -0.0018539247236327918,
      -0.002455416042984862,
      -0.003085050871355355,
      -0.00371096553190924,
      -0.004339739334007863,
      -0.005017481380720322,
      1.0181625866571153,
      4.326141037890289E-17,
      5.321192661805033E-17,
      4.0470971507247923E-16,
      1.5526934486338978E-17,
      1.8366387448882405E-16,
      7.407144305061246E-17,
      6.19858710800697E-17,
      1.7096513007380798E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.058145519854269E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -5.656702069426746E-4,
      0.0,
      -0.0011352364028589525,
      -0.0017201483201168134,
      -0.0022782369357761604,
      -0.0028624382674177417,
      -0.0034431878729234166,
      -0.004026590308646656,
      -0.004655427514528768,
      -0.005191000275890714,
      1.0221910068194286,
      4.16939861670484E-16,
      1.6970684277832702E-16,
      -3.993442708948806E-17,
      1.439730487810104E-16,
      1.3219982020976767E-16,
      1.3614868780986722E-16,
      1.916327339868129E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      5.485714650482148E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -4.987517183257026E-4,
      0.0,
      -0.0010009385321748598,
      -0.001516655676582694,
      -0.0020087227018950387,
      -0.0025238133225929404,
      -0.0030358605545462284,
      -0.0035502467882930436,
      -0.004104692882734017,
      -0.00457690766749605,
      -0.007914255401411104,
      1.0280240825156612,
      5.498138770928743E-16,
      1.052935010028796E-16,
      4.270030729962857E-16,
      2.073003024121757E-16,
      1.1809672163798357E-16,
      2.4223787038757383E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -5.4299622559084766E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -4.264876193984609E-4,
      0.0,
      -8.559126235880796E-4,
      -0.0012969075497612683,
      -0.0017176790208133905,
      -0.0021581382002493605,
      -0.002595994947306254,
      -0.0030358517983633732,
      -0.0035099642399135855,
      -0.003913759860054412,
      -0.006767559532004199,
      -0.01357089489718215,
      1.0385443733751583,
      -2.3887313788372453E-16,
      -2.569537972966964E-17,
      1.4944710508175973E-16,
      2.0897996874658098E-16,
      -8.857931749206571E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.7982504952618917E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -3.354859836441296E-4,
      0.0,
      -6.732825887026759E-4,
      -0.001020180387982604,
      -0.0013511699042894772,
      -0.0016976462715908658,
      -0.0020420754995462195,
      -0.002388078060060601,
      -0.002761026937301347,
      -0.0030786628185151452,
      -0.005323534056322364,
      -0.01067521029083934,
      -0.021913273152990204,
      1.052733823072864,
      -2.447035832970311E-16,
      -2.3943927352984562E-17,
      -4.886499191392168E-16,
      -1.8431436932253575E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.669919266815117E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -2.6970887153803864E-4,
      0.0,
      -5.412753321392793E-4,
      -8.201585598867769E-4,
      -0.0010862525646622536,
      -0.0013647969885586418,
      -0.0016416956458063217,
      -0.001919859159917726,
      -0.0022196857569314597,
      -0.002475044309176913,
      -0.00427977451495191,
      -0.00858217350750804,
      -0.017616843808477847,
      -0.028522321446334098,
      1.069901000050908,
      -3.7669249230293245E-16,
      -2.647621705209602E-16,
      -7.762887554996212E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      8.279930727711544E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -2.2114477612108563E-4,
      0.0,
      -4.438126616424224E-4,
      -6.724798486449243E-4,
      -8.906606551483291E-4,
      -0.0011190500437179168,
      -0.0013460900043098117,
      -0.0015741670701565976,
      -0.0018200065388167504,
      -0.0020293849309510055,
      -0.0035091533013399847,
      -0.007036857290338422,
      -0.014444734271334964,
      -0.023386558828182823,
      -0.029677200307055063,
      1.0846839878917112,
      4.666406150377611E-16,
      8.291978215169138E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      2.0444538157471748E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.5537747737737268E-4,
      0.0,
      -3.118250993924842E-4,
      -4.724878620341043E-4,
      -6.257828388421748E-4,
      -7.862504188506193E-4,
      -9.457698836980394E-4,
      -0.0011060180241265073,
      -0.0012787461217563046,
      -0.00142585647614846,
      -0.002465549479648794,
      -0.004944132769558054,
      -0.010148945916029084,
      -0.016431518659383283,
      -0.02085135629342625,
      -0.04064550892499566,
      1.0928652666728227,
      4.440892098500626E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      6.982292804965894E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.147025958580719E-4,
      0.0,
      -2.3019519275149688E-4,
      -3.4879948626854714E-4,
      -4.6196474077486667E-4,
      -5.804249467765807E-4,
      -6.981852362131795E-4,
      -8.164834477617608E-4,
      -9.439946000228916E-4,
      -0.0010525942491565097,
      -0.001820115310833927,
      -0.003649852427195752,
      -0.007492144044588545,
      -0.012130058203680755,
      -0.01539286603435833,
      -0.030005284307476076,
      -0.051987466236570246,
      1.110355507440781
    ]
  ]

  private static final double[][] BRL_CDI_JACOBIAN_MATRIX = [
    [
      0.9999805154481485,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.9994548726683539,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.9988298366398755,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.9983179300170382,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.9977988284474907,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9972714652710186,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9968117045903453,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.1102230246251565E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9963599286639047,
      0.0,
      0.0,
      0.0,
      0.0,
      -3.148944155648185E-18,
      0.0,
      6.844240336761216E-16,
      4.947490307632722E-16,
      3.0410594891527794E-17,
      7.52448029989699E-17,
      -7.26018385875449E-17,
      1.7719173160293325E-16,
      2.272250917841257E-16,
      -2.5697429789906787E-17,
      4.3262686104732666E-16,
      -2.2147335577252373E-16,
      -8.84066947303348E-18,
      1.617645969409813E-16,
      1.7322256389056697E-16,
      -2.7595968747427074E-17,
      -7.269525152536752E-17,
      9.529536494556045E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9959157087503717,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9954879033491978,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9950849618534859,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9946844065314959,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      7.097101153850024E-19,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9942828734775053,
      0.0,
      1.8535020339470958E-16,
      -9.435738818128029E-17,
      -2.0858962790238392E-16,
      -2.5307137442686626E-17,
      7.227440870532689E-17,
      1.4536333776543774E-16,
      -4.401804145494491E-17,
      -1.4367820389643046E-16,
      -3.520479370257548E-16,
      7.835626033712823E-17,
      3.130906453140822E-17,
      2.0298841750127052E-16,
      2.504491053855693E-16,
      2.4532858773298636E-16,
      9.456784042915362E-17,
      3.3006375207305684E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.794811343953435E-4,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.9962518946499396,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.131050238350416E-18,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0025737256187044442,
      0.0,
      0.9971385094196744,
      -9.669172961002038E-17,
      -2.796485385381366E-16,
      -4.040177579398103E-16,
      -4.874052817197103E-16,
      -3.786856234867532E-16,
      -4.0160687715294854E-16,
      -1.064274336873821E-16,
      1.9750488843154382E-16,
      -4.0744355775031495E-16,
      -1.909871873475255E-16,
      -1.8297086422598835E-16,
      -2.5566961924804254E-16,
      -1.0979640755982227E-16,
      1.799521496441761E-17,
      -4.831602986080617E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.0384668648997079E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0016625937152792616,
      0.0,
      -0.0033366383549731805,
      0.9996490086592251,
      -1.027104317406188E-16,
      1.5070459414794959E-18,
      2.5434012638427663E-16,
      2.657850383883982E-16,
      1.3815166719926589E-17,
      -1.4250282147577626E-16,
      -4.2026466327103324E-16,
      -1.2096167244290895E-16,
      5.973521189498642E-17,
      -1.6034198033903447E-17,
      -1.2772998102499243E-16,
      -1.0993132402645212E-16,
      -3.91380043608322E-17,
      3.4829486571328477E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      2.847824484159E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0012433289891409566,
      0.0,
      -0.0024952212647577233,
      -0.0037808430526737923,
      1.0022484597437207,
      -1.7538736832305325E-16,
      -2.7855132385818805E-16,
      6.127829799235727E-17,
      2.1776026037223562E-17,
      -3.421329522781208E-16,
      1.3117318670598856E-16,
      1.711931283475878E-16,
      2.2544611718763717E-16,
      2.2377659539626424E-16,
      1.0173422885384763E-16,
      -2.0625146386523806E-16,
      9.86014113239786E-17,
      -1.0433074217920668E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.458483721311194E-18,
      0.0,
      0.0,
      0.0,
      0.0,
      -0.0010008773140100745,
      0.0,
      -0.002008648056261368,
      -0.0030435709874889277,
      -0.004031033695910093,
      1.0049006629974544,
      8.225240096344181E-17,
      4.1041175734269677E-16,
      -9.537016364391397E-17,
      3.835007822463334E-17,
      2.4355278481342895E-17,
      -1.044197211705184E-16,
      -5.761691153407897E-17,
      3.348086321202284E-17,
      1.0931886626602805E-17,
      1.399967584892435E-16,
      1.314154677006102E-16,
      1.6595069502606252E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.469640866955871E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.443267710661465E-4,
      0.0,
      -0.001694468746380886,
      -0.0025675159466665835,
      -0.003400526335131917,
      -0.004272512906005791,
      1.0077151735632444,
      1.8570948056313911E-16,
      -5.805201949642498E-17,
      -2.5790617996676547E-17,
      -1.284350354511372E-16,
      -3.0421379875668064E-16,
      2.167361104782457E-17,
      -3.7397073721538796E-17,
      8.980762114470285E-17,
      -1.9121292328233E-16,
      5.213318383760768E-17,
      -5.841816830623459E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.872998148538517E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -7.413262634653774E-4,
      0.0,
      -0.001487758326941703,
      -0.0022543013775658707,
      -0.0029856917584831686,
      -0.00375130356724047,
      -0.004512391794577011,
      1.0109112314947175,
      1.983015511459749E-16,
      -5.351070715043663E-17,
      1.472518732015721E-16,
      2.574139280929576E-16,
      -7.837093927748344E-17,
      1.8495133148142609E-16,
      2.933395798536626E-16,
      1.4852648614721272E-16,
      1.4237946216986985E-16,
      1.6473096758201633E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.018959392221874E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -6.647802300782181E-4,
      0.0,
      -0.0013341390581008035,
      -0.002021532302711998,
      -0.0026774025850223385,
      -0.003363960743635549,
      -0.004046462405607503,
      -0.004732081695236031,
      1.0142981248517635,
      4.882380572268303E-16,
      2.457818297691062E-19,
      -2.5566457344630597E-16,
      -5.3497416425944433E-17,
      1.1514094722898049E-16,
      -7.851730723251347E-19,
      1.5700814468542525E-17,
      6.940249156622835E-17,
      -4.274467065024101E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.364411533313783E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -6.096625330551543E-4,
      0.0,
      -0.0012235240472082617,
      -0.0018539247236327918,
      -0.002455416042984862,
      -0.003085050871355355,
      -0.00371096553190924,
      -0.004339739334007863,
      -0.005017481380720322,
      1.0181625866571153,
      4.326141037890289E-17,
      5.321192661805033E-17,
      4.0470971507247923E-16,
      1.5526934486338978E-17,
      1.8366387448882405E-16,
      7.407144305061246E-17,
      6.19858710800697E-17,
      1.7096513007380798E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -8.058145519854269E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -5.656702069426746E-4,
      0.0,
      -0.0011352364028589525,
      -0.0017201483201168134,
      -0.0022782369357761604,
      -0.0028624382674177417,
      -0.0034431878729234166,
      -0.004026590308646656,
      -0.004655427514528768,
      -0.005191000275890714,
      1.0221910068194286,
      4.16939861670484E-16,
      1.6970684277832702E-16,
      -3.993442708948806E-17,
      1.439730487810104E-16,
      1.3219982020976767E-16,
      1.3614868780986722E-16,
      1.916327339868129E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      5.485714650482148E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -4.987517183257026E-4,
      0.0,
      -0.0010009385321748598,
      -0.001516655676582694,
      -0.0020087227018950387,
      -0.0025238133225929404,
      -0.0030358605545462284,
      -0.0035502467882930436,
      -0.004104692882734017,
      -0.00457690766749605,
      -0.007914255401411104,
      1.0280240825156612,
      5.498138770928743E-16,
      1.052935010028796E-16,
      4.270030729962857E-16,
      2.073003024121757E-16,
      1.1809672163798357E-16,
      2.4223787038757383E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      -5.4299622559084766E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -4.264876193984609E-4,
      0.0,
      -8.559126235880796E-4,
      -0.0012969075497612683,
      -0.0017176790208133905,
      -0.0021581382002493605,
      -0.002595994947306254,
      -0.0030358517983633732,
      -0.0035099642399135855,
      -0.003913759860054412,
      -0.006767559532004199,
      -0.01357089489718215,
      1.0385443733751583,
      -2.3887313788372453E-16,
      -2.569537972966964E-17,
      1.4944710508175973E-16,
      2.0897996874658098E-16,
      -8.857931749206571E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      1.7982504952618917E-16,
      0.0,
      0.0,
      0.0,
      0.0,
      -3.354859836441296E-4,
      0.0,
      -6.732825887026759E-4,
      -0.001020180387982604,
      -0.0013511699042894772,
      -0.0016976462715908658,
      -0.0020420754995462195,
      -0.002388078060060601,
      -0.002761026937301347,
      -0.0030786628185151452,
      -0.005323534056322364,
      -0.01067521029083934,
      -0.021913273152990204,
      1.052733823072864,
      -2.447035832970311E-16,
      -2.3943927352984562E-17,
      -4.886499191392168E-16,
      -1.8431436932253575E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      9.669919266815117E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -2.6970887153803864E-4,
      0.0,
      -5.412753321392793E-4,
      -8.201585598867769E-4,
      -0.0010862525646622536,
      -0.0013647969885586418,
      -0.0016416956458063217,
      -0.001919859159917726,
      -0.0022196857569314597,
      -0.002475044309176913,
      -0.00427977451495191,
      -0.00858217350750804,
      -0.017616843808477847,
      -0.028522321446334098,
      1.069901000050908,
      -3.7669249230293245E-16,
      -2.647621705209602E-16,
      -7.762887554996212E-17
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      8.279930727711544E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -2.2114477612108563E-4,
      0.0,
      -4.438126616424224E-4,
      -6.724798486449243E-4,
      -8.906606551483291E-4,
      -0.0011190500437179168,
      -0.0013460900043098117,
      -0.0015741670701565976,
      -0.0018200065388167504,
      -0.0020293849309510055,
      -0.0035091533013399847,
      -0.007036857290338422,
      -0.014444734271334964,
      -0.023386558828182823,
      -0.029677200307055063,
      1.0846839878917112,
      4.666406150377611E-16,
      8.291978215169138E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      2.0444538157471748E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.5537747737737268E-4,
      0.0,
      -3.118250993924842E-4,
      -4.724878620341043E-4,
      -6.257828388421748E-4,
      -7.862504188506193E-4,
      -9.457698836980394E-4,
      -0.0011060180241265073,
      -0.0012787461217563046,
      -0.00142585647614846,
      -0.002465549479648794,
      -0.004944132769558054,
      -0.010148945916029084,
      -0.016431518659383283,
      -0.02085135629342625,
      -0.04064550892499566,
      1.0928652666728227,
      4.440892098500626E-16
    ],
    [
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      0.0,
      6.982292804965894E-17,
      0.0,
      0.0,
      0.0,
      0.0,
      -1.147025958580719E-4,
      0.0,
      -2.3019519275149688E-4,
      -3.4879948626854714E-4,
      -4.6196474077486667E-4,
      -5.804249467765807E-4,
      -6.981852362131795E-4,
      -8.164834477617608E-4,
      -9.439946000228916E-4,
      -0.0010525942491565097,
      -0.001820115310833927,
      -0.003649852427195752,
      -0.007492144044588545,
      -0.012130058203680755,
      -0.01539286603435833,
      -0.030005284307476076,
      -0.051987466236570246,
      1.110355507440781
    ]
  ]

  private static final double[][] EUR_3M_JACOBIAN_MATRIX = [
    [
      1.9180354689524151E-25,
      -3.3387302588216837E-26,
      9.533057127432343E-26,
      -2.793845548545767E-4,
      9.534371575762295E-4,
      -3.229156477964089E-18,
      4.1006092768988405E-17,
      1.364786396006559,
      -0.34673090776639026,
      1.2145867566736735E-19,
      1.025442873943839E-18
    ],
    [
      2.2083774314929393E-26,
      -6.379848052321796E-27,
      2.936568404199944E-26,
      7.915895720880792E-4,
      -0.002701405279798474,
      -4.676052339881083E-17,
      -1.7860054830697383E-17,
      0.01462037728917287,
      0.9824042386714407,
      -3.904356574093985E-20,
      -3.9757805107994545E-19
    ]
  ]

  private static final double[][] USD_3M_JACOBIAN_MATRIX = [
    [
      3.767008882094992E-31,
      -6.659462289367361E-32,
      1.7965762784439954E-31,
      -8.151638938919869E-21,
      6.37722408168511E-22,
      -5.5512160250139404E-5,
      2.8031356387448977E-4,
      2.9451756746893224E-19,
      -1.4825949028694821E-19,
      1.3647860671003322,
      -0.347180797149939
    ],
    [
      2.7494283728620715E-31,
      -4.860921550426743E-32,
      1.311311138921975E-31,
      -5.3840766558316003E-20,
      1.8125495718772558E-21,
      1.5728445404206268E-4,
      -7.942217643110388E-4,
      6.225088021429504E-19,
      -4.2284857769144387E-19,
      0.014621309190144871,
      0.9836789252581601
    ]
  ]

  private static final double[][] EU_AI_CPI_JACOBIAN_MATRIX = [
    [
      1182.9553470168005,
      -1.6697754290362354E-13,
      2.7000623958883807E-13,
      3.7118003092556474E-15,
      -1.1932578842994742E-14,
      -9.523998921050411E-25,
      -2.1696751722802545E-26,
      4.898004174764475E-15,
      1.0632342860795341E-14,
      -4.607883432622329E-30,
      -7.624507234506368E-30
    ],
    [
      596.7566117444645,
      142.59048014103632,
      2.7711166694643907E-13,
      1.7947467711683E-15,
      -5.7650644444561954E-15,
      -4.601563319579867E-25,
      -1.048292887556906E-26,
      2.36647458586927E-15,
      5.1370248514601325E-15,
      -2.2264521781272855E-30,
      -3.684835537907178E-30
    ],
    [
      815.0648653669407,
      -47.530160047012004,
      277.94946247710004,
      2.986730151569664E-15,
      -1.0125652470757581E-14,
      -7.696975758257227E-25,
      -1.7534470327718658E-26,
      3.958449509064694E-15,
      8.592804512982418E-15,
      -3.723596372801785E-30,
      -6.1592491616782234E-30
    ]
  ]

  private static final double[][] CNY_REPO_1W_MATRIX = [
    [0.9994578283561251, 2.220446049250313E-16, 5.551115123125783E-17],
    [0.16822557506984298, 0.9262006936099679, -0.029854638541992745],
    [0.06003810276344215, 0.0011070287166008441, 0.958667316777993]
  ]
  public static final double[][] EUR_USD_JACOBIAN_MATRIX = [
    [
      1.0133633864305929,
      -3.5340848852594886E-15,
      5.8640209680192144E-15,
      8.836864145564754E-16,
      3.3654704042113635E-14,
      -1.7114814907829126E-14,
      -3.3953058867652044E-15,
      1.8038915290064424E-14,
      -1.6541639855433533E-14,
      -6.906006394553947E-14,
      -9.302653299922878E-14,
      -5.077841163121863E-14,
      2.910571893585373E-14,
      2.9331691404608744E-14,
      -5.666012140747822E-14,
      -3.0184178979345566E-14,
      6.104936927191004E-14,
      -2.999075395011214E-14,
      4.655108917265034E-14,
      2.2288895701300755E-14,
      -2.431934126890595E-14,
      -1.5252361890887412E-13,
      -2.9185053934282807E-14,
      4.536965451981507E-14,
      -5.037774243866684E-14,
      -2.805432934074592E-14,
      2.7370185749318905E-15,
      3.2345875218766654E-15,
      3.1468352534754353E-14,
      -3.7315416547543234E-14,
      5.87795446321528E-14,
      6.474481678036355E-14,
      -4.821056271101807E-14,
      -4.784020642675679E-15,
      -6.161377371048635E-15,
      -1.0875782953594464E-13,
      3.4361691488020327E-14,
      -2.3126361738462353E-14,
      -2.1302616876616472E-14,
      -337.5250856793986,
      4.601843349944124E-12,
      4.785741027324962E-13,
      1.6609325367426803E-13,
      -1.9412419722387603E-13,
      7.251055622410393E-14,
      -3.5380202787822414E-13,
      4.254471968751192E-14,
      -6.376406628040487E-15,
      5.52384203368965E-15,
      2.7131264467869168E-14,
      1.0006571028366522E-13,
      1.291167830444368E-13,
      2.1762845260751206E-14,
      -4.211574984495755E-14,
      6.4322557710602986E-15,
      -9.394397194378996E-15,
      1.203454567347876E-14,
      1.8751078777708726E-14,
      -1.9204243508837255E-14,
      7.659321114468114E-15,
      -2.9529581237151374E-16,
      2.688341431102868E-15,
      5.067311066242918E-15,
      1.3928869734429016E-14,
      -7.588634559831337E-16,
      -3.58239599339565E-16,
      9.6510632233988E-16,
      1.4028372216755305E-15,
      -2.073637093216455E-15
    ],
    [
      1.0133633864306182,
      1.2912278668161803E-14,
      -1.2384272210161362E-14,
      -3.13598431077311E-14,
      -3.0870488457179768E-15,
      -3.1951438023147816E-15,
      1.0221424401324342E-14,
      1.3856537445233741E-14,
      1.1291965626436529E-14,
      -4.954673823998057E-14,
      6.903115232215207E-15,
      -3.391601235969155E-15,
      -4.3181170444883676E-14,
      -1.423058719474124E-14,
      -1.675569405445998E-14,
      -6.619271103458502E-14,
      9.962516922534803E-15,
      -2.2200557364682183E-14,
      2.642287430520973E-14,
      3.2286456494445837E-14,
      -2.59763998505802E-14,
      -6.259489454540912E-14,
      1.9210110932532665E-14,
      -9.948747554944237E-15,
      -1.1418166759313841E-14,
      -4.3381097325490003E-14,
      2.004147715839455E-15,
      6.3949636500148805E-15,
      8.706031239662869E-15,
      -4.705621774720147E-14,
      -1.1810584815329244E-14,
      -4.302739066039759E-15,
      -7.941621115396865E-15,
      -2.7634536539760185E-14,
      1.0793703312714074E-14,
      -5.379784583038529E-14,
      -2.6073778146328344E-14,
      -8.008588413709516E-15,
      3.0318979799508336E-14,
      -168.78078849064704,
      -168.7559568930154,
      4.383439039534493E-14,
      -4.878492053416502E-13,
      2.4555586784855837E-13,
      4.696295876325824E-13,
      5.972846051300121E-14,
      -3.163002628911449E-14,
      2.569488675649434E-14,
      1.3745515142771225E-14,
      5.4580905767265264E-15,
      6.81314813594236E-14,
      3.2309224740068032E-15,
      -1.9264754722025934E-14,
      4.962393343466154E-15,
      4.7956864174247826E-14,
      -1.453958481389961E-14,
      6.930220286527344E-16,
      2.735572185441626E-14,
      -7.989268968611185E-15,
      6.3694709229178414E-15,
      -2.8839777788114418E-15,
      -3.978913552804553E-15,
      4.646294200078005E-15,
      5.1362722668785166E-15,
      -2.7502143357810427E-15,
      1.6626375840336771E-15,
      -1.1224067465384624E-15,
      1.755187791982471E-15,
      -1.3587424913495683E-15
    ],
    [
      1.0133633864306344,
      6.063719134013351E-15,
      -4.2730355793432415E-15,
      -6.871146514719935E-15,
      -1.9672055596910848E-14,
      3.827302278542558E-14,
      -1.5152740105955735E-14,
      -6.3986531137748834E-15,
      3.747834367409165E-14,
      -2.1254479690235135E-14,
      2.212476612711129E-14,
      5.3009471714673365E-15,
      -2.3807074966083984E-14,
      5.635243864815178E-15,
      4.1916014082777394E-15,
      -2.66398658292088E-14,
      3.549630902388969E-15,
      5.165869239181917E-15,
      1.0792489567440218E-14,
      1.0617138580174866E-14,
      -7.89818222183701E-15,
      -2.3083789522251927E-14,
      4.258272073005285E-15,
      -5.791528405135632E-15,
      -2.096988323740259E-15,
      -2.1886840832330432E-14,
      -5.984131547712423E-15,
      -2.843129297292748E-15,
      4.256095754196213E-15,
      -4.43229041274681E-15,
      6.385777260064552E-15,
      6.978712922757653E-16,
      -1.477351374106087E-14,
      -1.3540304047123452E-14,
      1.3502899958910319E-15,
      -3.167702248944224E-14,
      -1.8674893196670048E-14,
      -1.3896156355859099E-14,
      2.5074759847269273E-15,
      -67.53950509631963,
      -67.5295684570275,
      -67.4997673097604,
      -2.41233224725868E-13,
      7.026554243591082E-14,
      2.10690701073118E-13,
      3.932082604634344E-14,
      2.044212797761057E-15,
      -1.9605620155879733E-14,
      -3.0763944417909386E-14,
      1.0255271520259268E-14,
      2.464115574725336E-14,
      -1.1053590438046671E-14,
      -1.2418546974855534E-14,
      8.21391904688197E-15,
      1.0345371473649892E-14,
      -7.436367047620638E-15,
      -7.983564519348783E-15,
      1.283720973793668E-14,
      -1.0463748457314299E-15,
      8.966989888143523E-16,
      -4.8495065891378334E-15,
      -8.964521117974521E-16,
      2.198695937230717E-15,
      1.0295854218720981E-16,
      -6.5184113633308E-17,
      5.4265840745226594E-17,
      -1.3468852460435085E-15,
      1.33589069386806E-15,
      -3.4219495215336864E-16
    ],
    [
      0.2251918636512484,
      0.7877078769433586,
      -4.0536277714394005E-15,
      -6.938005875888816E-15,
      -3.08686122792016E-15,
      1.1160122301218437E-16,
      3.734922747732636E-15,
      5.988254268824946E-15,
      3.3629007421613047E-15,
      -1.2467158950087177E-14,
      1.9980965571320233E-15,
      -2.82819574909063E-15,
      -8.679544545872007E-15,
      -6.099141204834579E-16,
      -5.688375965195395E-15,
      -8.73758467278597E-15,
      8.03344088977755E-15,
      -2.58127509675883E-15,
      4.19445897980442E-15,
      7.19019926199516E-15,
      -3.661840903862213E-15,
      -1.0768517052725263E-14,
      8.610249256913165E-16,
      -7.747064450145909E-16,
      -7.732214162635529E-15,
      -9.373056741903405E-15,
      -1.5010985542342702E-16,
      6.358877670799128E-15,
      -4.953283040897291E-15,
      -8.170019724740834E-15,
      -4.487788243061483E-15,
      2.0273917292802114E-15,
      -1.8983398838578572E-15,
      -4.867857458678424E-15,
      -3.3922153150035796E-16,
      -1.0907455824049994E-14,
      -4.167915619178514E-15,
      1.0416403203311662E-15,
      8.711967012465114E-15,
      -37.535299741838195,
      -37.529777422216185,
      -3.529900116915627E-14,
      -37.491148001304666,
      1.090309939430505E-13,
      1.1574000213065415E-13,
      1.4920782186412425E-14,
      -1.0522145032897823E-14,
      3.295001509083326E-15,
      -3.892916441721971E-15,
      -3.3491484740483615E-15,
      1.7827055313344227E-14,
      8.631944682368104E-15,
      -4.259505498560275E-15,
      -2.919979564275726E-15,
      1.4895241772353073E-14,
      -4.9781150161210225E-15,
      1.960493120045626E-15,
      3.860399985077402E-15,
      -3.2675706250191814E-15,
      1.0359666636942466E-15,
      -5.648579498597191E-16,
      -8.33394194791179E-16,
      1.1635300836310954E-15,
      1.0137231649090424E-15,
      -5.263402316966281E-16,
      3.847522192455715E-16,
      1.7160204260186156E-17,
      4.0583350776344275E-16,
      -3.11309952929924E-16
    ],
    [
      0.1266704233038296,
      3.97325827115776E-15,
      0.8852587885428167,
      -3.3321617576867685E-15,
      -3.913951260887391E-15,
      -3.0289517028987516E-16,
      4.217728245414917E-15,
      1.887398676560112E-15,
      9.201040443942055E-16,
      -4.678269164441263E-15,
      1.9457435765164093E-15,
      -5.158785873220944E-16,
      -8.888555060744248E-15,
      -1.557304378361395E-15,
      -3.358452601578358E-15,
      -5.854784481529448E-15,
      1.5162757964622912E-15,
      -1.0921160013117014E-15,
      2.982166261573634E-15,
      3.786271155544613E-15,
      -1.0197354477820453E-15,
      -9.397058373375922E-15,
      3.942896123700524E-15,
      -1.3335875186402469E-15,
      -1.9745716546619397E-15,
      -3.2436692610717964E-15,
      9.071511106856743E-17,
      -3.855662503333629E-15,
      1.2852481953733912E-15,
      -1.2007586838229143E-15,
      -7.554292853891733E-16,
      -6.478472045949531E-15,
      -1.0673419618180729E-15,
      -2.476266476758914E-16,
      9.889133581257271E-16,
      -7.097344995688081E-15,
      -4.922923037492202E-15,
      -5.123536198419901E-15,
      6.033560893194554E-15,
      -21.12957917652443,
      -21.126470521724727,
      -1.565017036862619E-14,
      -1.6394359919054345E-13,
      -21.08299153678901,
      4.490537218677753E-14,
      1.791753501873663E-14,
      -6.3967367282015E-15,
      3.1387539867496454E-15,
      3.52078344836477E-15,
      2.257631854425961E-16,
      4.699626570810688E-15,
      -4.2343515846421376E-16,
      4.855656604200271E-16,
      3.6146060586190174E-15,
      8.501313725995329E-15,
      -4.153487043233664E-15,
      1.0274674821619378E-15,
      2.3449130939263594E-15,
      -5.967852156801346E-16,
      6.8700417719984785E-16,
      -5.08406327359213E-16,
      -3.040627257983264E-16,
      3.3645800379187955E-16,
      7.542608762303299E-16,
      -2.768754364984112E-16,
      8.30477886823562E-17,
      -2.5457881617426886E-16,
      3.6375128470676483E-17,
      -1.6749341333825352E-16
    ]
  ]

  private static final double[][] AUD_6M_JACOBIAN_MATRIX = [
    [
      0.9819802454899418,
      8.369149316457006E-17,
      -1.4808680369513353E-16,
      -6.843970149063792E-17,
      -9.916605039357643E-18,
      -1.8576587298101446E-16,
      -1.227325530174362E-16,
      -8.337279249452166E-17,
      -3.175064518204746E-17,
      -4.481535994265598E-17,
      -6.905017797254915E-17,
      -4.496711470463011E-17,
      -2.576775207756762E-16,
      -1.3930777659599094E-16,
      1.1979248159838668E-16,
      1.702883507489518E-16
    ],
    [
      -0.0069264922370332,
      0.9880656001207173,
      3.7222226641122083E-16,
      3.298985672066573E-16,
      5.364241523162457E-16,
      1.432835366649852E-16,
      4.0539444277584144E-16,
      2.461838809058376E-16,
      2.790791904068597E-16,
      1.562841151496188E-16,
      2.5568947489146626E-16,
      3.1438539721250636E-16,
      4.875993865263847E-16,
      2.96814956036247E-16,
      -9.530442028008596E-16,
      -1.9315739329187065E-17
    ],
    [
      -0.003636066503010915,
      -0.020113081301223922,
      1.0073661003626273,
      -0.0028013336085905773,
      7.719209184231049E-6,
      1.2230456708016428E-16,
      3.781274769577833E-17,
      7.183059422759406E-18,
      -2.6244882428033518E-17,
      8.440395460535934E-18,
      1.5438331531333728E-16,
      -3.1262260253526195E-16,
      -1.3778849065619234E-16,
      9.207163433359519E-17,
      8.786543812411979E-16,
      -4.111497925972374E-17
    ],
    [
      -0.0024085961476903056,
      -0.013356759291645868,
      -0.026991518034292176,
      1.0259331596893497,
      -0.002827008052289849,
      3.179744065045596E-17,
      7.831290428570672E-17,
      4.731106414637774E-16,
      7.231801867534706E-18,
      -7.256762489196024E-16,
      -6.856314808994835E-17,
      -1.8993764900578964E-16,
      -8.392296562277201E-17,
      2.9812933941214884E-16,
      8.040511073788281E-16,
      -2.421497789610594E-16
    ],
    [
      -0.0018235388051371548,
      -0.010080140310882479,
      -0.02038806941785899,
      -0.029595090067309853,
      1.042383866986205,
      1.3533746847441401E-17,
      -8.076072362415324E-17,
      7.04453082383066E-17,
      -3.0500585066297977E-16,
      -1.0616894925853632E-16,
      1.4969030941674013E-16,
      6.86894909949652E-16,
      -8.341326354676173E-17,
      1.13305056255366E-16,
      -5.598684493443584E-16,
      1.456761144005836E-16
    ],
    [
      -0.0014949206552647772,
      -0.008196028245093355,
      -0.016615127010619294,
      -0.024135068370502593,
      -0.037261053952050545,
      1.0690994173849824,
      -1.0504243845207996E-16,
      -1.939879090966535E-16,
      -4.103527875334303E-17,
      1.5224124178162506E-16,
      2.1379296877155752E-16,
      3.621538070380228E-16,
      -2.216714869117658E-16,
      -1.8089235621562838E-16,
      -2.813504637599884E-17,
      2.4271220883803624E-16
    ],
    [
      -0.001283690369003668,
      -0.006967558356632406,
      -0.014164505504241894,
      -0.020592841614078518,
      -0.03171253378663734,
      -0.03868688578598377,
      1.0962271309908451,
      7.131522431002657E-17,
      5.772057447018861E-16,
      -1.4199302985746468E-17,
      1.1465043574318144E-16,
      -7.383035629800021E-16,
      3.7950845333607937E-16,
      7.400526860160822E-17,
      -1.2319247184866544E-17,
      -5.092768654707536E-16
    ],
    [
      -0.0011302470545495223,
      -0.006080188715724841,
      -0.012391655234780289,
      -0.018029093501265685,
      -0.027702182022646467,
      -0.033822415196846256,
      -0.041569861560352105,
      1.125001021761711,
      -2.7657200679739207E-17,
      7.553103963740923E-17,
      1.2768095237572518E-16,
      -3.1395339717451686E-16,
      -5.858715137921182E-17,
      -2.6944287458748745E-16,
      -2.1754516590921646E-16,
      -2.5500435096859064E-16
    ],
    [
      -0.0010140904740191293,
      -0.005412686332230684,
      -0.011055802943183228,
      -0.01609627999812096,
      -0.024683415185488684,
      -0.030158669884457433,
      -0.0370668896542356,
      -0.047123643830091745,
      1.1616092970392882,
      -0.003245435171966733,
      8.9635304545403E-6,
      4.815594063377511E-17,
      -3.522026522154425E-17,
      3.2510818581514556E-17,
      1.7602022270302164E-16,
      -2.8349176305064727E-16
    ],
    [
      -9.209396215761051E-4,
      -0.004883113455260362,
      -0.009992899519763317,
      -0.014557011188392338,
      -0.02228560766882385,
      -0.027245775052497694,
      -0.03348675990301293,
      -0.042541952634513276,
      -0.043882490374756784,
      1.1902169927498283,
      -0.0032872467625195355,
      2.55506006231602E-16,
      -3.3884621318666305E-16,
      2.3101807196824337E-16,
      1.3658236867886142E-16,
      2.3765711620882257E-16
    ],
    [
      -8.417243671189653E-4,
      -0.004441515436225299,
      -0.009101804213269345,
      -0.013264426947116239,
      -0.0202817713158888,
      -0.02480719238112851,
      -0.030489589433006694,
      -0.03871407314573556,
      -0.03997495113874602,
      -0.04551315541550559,
      1.215516015595206,
      3.081399982992738E-16,
      7.888926057547652E-17,
      -3.476900841889452E-16,
      1.8735013540549517E-16,
      -2.1846673775582914E-16
    ],
    [
      -7.236192080749984E-4,
      -0.0037819672236545326,
      -0.007771549908960466,
      -0.011335101348983804,
      -0.01728952975233638,
      -0.02116633801540472,
      -0.026014751930608045,
      -0.03299793096687434,
      -0.03414194501309178,
      -0.03887443375335337,
      -0.07621450190528742,
      1.2609287452433426,
      -5.247877328008743E-17,
      9.953653569774734E-16,
      2.693158196453993E-16,
      -1.0408340855860843E-17
    ],
    [
      -5.898021019270528E-4,
      -0.003064410593627716,
      -0.006307809423745342,
      -0.009204864450278896,
      -0.014019022257685478,
      -0.017172086067417675,
      -0.02110556671871174,
      -0.02675372670653724,
      -0.02771619924175628,
      -0.031559209002964723,
      -0.061811409368110075,
      -0.12487182201805999,
      1.335379785913178,
      9.643978324258562E-17,
      7.632783294297951E-17,
      9.71445146547012E-17
    ],
    [
      -4.1443192105534825E-4,
      -0.0021943121959301357,
      -0.004492315808647021,
      -0.006544916407968216,
      -0.010016103605296032,
      -0.0122470509145841,
      -0.015052390791101936,
      -0.019119781616066568,
      -0.01972822040306676,
      -0.022460910575856474,
      -0.04413090083846246,
      -0.08905565923507035,
      -0.19975890127246979,
      1.4218351489762313,
      -7.485381694345046E-4,
      3.89879817992711E-7
    ],
    [
      -2.813911536095503E-4,
      -0.0015715367836040104,
      -0.0031695868738643076,
      -0.004596957104884017,
      -0.0071296888664194105,
      -0.00867508914801062,
      -0.010662226597589312,
      -0.013620010624049881,
      -0.013898226705405317,
      -0.01581798610861419,
      -0.03135236758665071,
      -0.06307720500197606,
      -0.1415337900635073,
      -0.25301522283136485,
      1.5105299000498065,
      -7.867669885222028E-4
    ],
    [
      -1.962854868783456E-4,
      -0.0011697979618953188,
      -0.002318540632421653,
      -0.0033445700769195257,
      -0.005269755460579962,
      -0.006375356407467136,
      -0.007835711368152507,
      -0.01007563313055327,
      -0.010148145390881635,
      -0.011545235616514007,
      -0.02312099059799636,
      -0.04635172834355747,
      -0.1040451293413265,
      -0.18592486365347458,
      -0.26391239050345516,
      1.5903883269909822
    ],
  ]

  private static final List<CurveParameterSize> CURVES_PARAMETER_SIZE = [
    CurveParameterSize.of(CurveName.of("EU-AI-CPI"), 3),
    CurveParameterSize.of(CurveName.of("EUR-EONIA"), 2),
    CurveParameterSize.of(CurveName.of("USD-FED-FUND"), 2),
    CurveParameterSize.of(CurveName.of("EUR-EURIBOR-3M"), 2),
    CurveParameterSize.of(CurveName.of("USD-LIBOR-3M"), 2),
  ]

  static Map<String, CalibrationCurve> calibrationCurves() {
    return [
      "EUR-EONIA"     : new CalibrationCurve(name: "EUR-EONIA", type: CalibrationCurveType.IR_INDEX),
      "USD-FED-FUND"  : new CalibrationCurve(name: "USD-FED-FUND", type: CalibrationCurveType.IR_INDEX),
      "EUR-EURIBOR-3M": new CalibrationCurve(name: "EUR-EURIBOR-3M", type: CalibrationCurveType.INDEX_BASIS),
      "USD-LIBOR-3M"  : new CalibrationCurve(name: "USD-LIBOR-3M", type: CalibrationCurveType.IR_INDEX),
      "EU-AI-CPI"     : new CalibrationCurve(name: "EU-AI-CPI", type: CalibrationCurveType.INFLATION),
      "US-CPI-U"      : new CalibrationCurve(name: "US-CPI-U", type: CalibrationCurveType.INFLATION),
      "CNY-REPO-1W"   : new CalibrationCurve(name: "CNY-REPO-1W", type: CalibrationCurveType.INDEX_BASIS),
      "CLP-TNA"       : new CalibrationCurve(name: "CLP-TNA", type: CalibrationCurveType.IR_INDEX),
      "EUR/USD"       : new CalibrationCurve(name: "EUR/USD", type: CalibrationCurveType.XCCY),
    ]
  }


  static gbpValuationRates(LocalDate date = MarketDataSample.VAL_DT) {
    var gbpSoniaFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .build()

    def ratesProvider = ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(GBP): gbpOIS(),])
      .indexCurve(OvernightIndices.GBP_SONIA, gbpOIS())
      .timeSeries(PriceIndices.GB_RPI, timeSeriesProvider())
      .timeSeries(OvernightIndices.GBP_SONIA, gbpSoniaFixings)
      .build()
    new ValuationCurveRates(ratesProvider, [])
  }

  static clpRatesProvider(LocalDate date = MarketDataSample.VAL_DT, Currency ccy = CLP, String curveName = "CLP-TNA", OvernightIndex index = OvernightIndexConstants.CLP_TNA) {
    var clpTnaFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2022-11-29"), 23071.2)
      .put(LocalDate.parse("2023-11-27"), 23075.2)
      .put(LocalDate.parse("2023-11-28"), 23080.97)
      .put(LocalDate.parse("2023-11-29"), 23086.74)
      .put(LocalDate.parse("2023-11-30"), 23092.51)
      .put(LocalDate.parse("2023-12-01"), 23092.28)
      .put(LocalDate.parse("2023-12-04"), 23115.6)
      .build()

    def ratesProvider =  ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(ccy): clpOIS(curveName),])
      .indexCurve(index, clpOIS(curveName))
      .timeSeries(index, clpTnaFixings)
      .build()
    new ValuationCurveRates(ratesProvider, [])
  }

  static brlValuationRates(LocalDate date = MarketDataSample.VAL_DT) {
    var brlCdiFixings = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2021-02-08"), 0.00048)
      .put(LocalDate.parse("2021-02-09"), 0.000482)
      .put(LocalDate.parse("2021-02-10"), 0.000483)
      .build()

    def ratesProvider = ImmutableRatesProvider.builder(date)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([(BRL): brlOIS(),])
      .indexCurve(OvernightIndices.BRL_CDI, brlOIS())
      .timeSeries(OvernightIndices.BRL_CDI, brlCdiFixings)
      .build()
    new ValuationCurveRates(ratesProvider, [])
  }

  static sofrRates(LocalDate vd = MarketDataSample.VAL_DT) {
    def rates = sofrRatesBuilder(vd).build()
    new ValuationCurveRates(rates, [])
  }

  static valuationRates(LocalDate vd = MarketDataSample.VAL_DT) {
    def rates = valuationRatesBuilder(vd).build()
    new ValuationCurveRates(rates, [])
  }

  static sofrRatesBuilder(LocalDate vd = MarketDataSample.VAL_DT) {
    return ImmutableRatesProvider.builder(vd)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData(vd)))
      .discountCurves([
        (USD): usdSofr(),
        (EUR): eurUsd()
      ])
      .indexCurve(OvernightIndices.USD_SOFR, usdSofr())
      .timeSeries(OvernightIndices.USD_SOFR, timeSeriesProvider())
      .timeSeries(OvernightIndices.EUR_EONIA, timeSeriesProvider())
  }

  static valuationRatesBuilder(LocalDate vd = MarketDataSample.VAL_DT) {
    return ImmutableRatesProvider.builder(vd)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData(vd)))
      .discountCurves([
        (USD): usdOIS(),
        (EUR): eurOIS(),
        (CLP): clpOIS(),
        (AUD): aud6M(),
      ])
      .indexCurve(OvernightIndices.EUR_EONIA, eurOIS())
      .indexCurve(OvernightIndices.USD_FED_FUND, usdOIS())
      .indexCurve(PriceIndices.EU_AI_CPI, inflation(eurInflation()))
      .indexCurve(PriceIndices.US_CPI_U, inflation(usdInflation()))
      .indexCurve(IborIndices.EUR_EURIBOR_3M, eur3M())
      .indexCurve(IborIndices.AUD_BBSW_6M, aud6M())
      .indexCurve(IborIndices.USD_LIBOR_3M, usd3M())
      .indexCurve(OvernightIndex.of("CLP-TNA") , clpOIS())
      .timeSeries(PriceIndices.GB_RPI, timeSeriesProvider())
      .timeSeries(PriceIndices.EU_AI_CPI, timeSeriesProvider())
      .timeSeries(PriceIndices.US_CPI_U, timeSeriesProvider())
  }

  static shiftedValuationRates() {
    def rates = valuationRates()
    def immutableRates = (ImmutableRatesProvider) rates.getRatesProvider()

    // shift EUR/USD by 0.000001
    def shiftedFxMd = MarketDataSample.ogMarketData(MarketDataSample.VAL_DT).toBuilder().addValue(FxRateId.of(EUR, USD), FxRate.of(EUR, USD, 1.238851 as double)).build()
    def shiftedFxRatesProvider = MarketDataFxRateProvider.of(shiftedFxMd)

    def shiftedRates = new FxShiftedValuationCurveRates(CurrencyPair.of(EUR, USD), immutableRates.discountCurves, immutableRates.indexCurves, shiftedFxRatesProvider)
    return new ValuationCurveRates(rates.ratesProvider, [shiftedRates])
  }

  static cnyValuationRates(LocalDate valuationDate = MarketDataSample.VAL_DT) {
    var timeSeries = LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2023-09-27"), 100)
      .put(LocalDate.parse("2023-09-28"), 101)
      .build()

    def ratesProvider = ImmutableRatesProvider.builder(valuationDate)
      .fxRateProvider(MarketDataFxRateProvider.of(MarketDataSample.ogMarketData()))
      .discountCurves([
        (CNY): cny1W(),
      ])
      .indexCurve(IborIndex.of("CNY-REPO-1W"), cny1W())
      .timeSeries(IborIndex.of("CNY-REPO-1W"), timeSeries)
      .build()
    new ValuationCurveRates(ratesProvider, [])
  }

  static Curve usdOIS() {
    nodalCurve(
      Curves.zeroRates("USD-FED-FUND", DayCounts.ACT_365F),
      DoubleArray.of(0.0136986301369863, 1.010958904109589),
      DoubleArray.of(0.005170796706909363, 0.005157654505868138),
      USD_FED_FUND_JACOBIAN_MATRIX
      )
  }

  static Curve usdSofr() {
    nodalCurve(
      Curves.zeroRates("USD-SOFR", DayCounts.ACT_365F),
      DoubleArray.of(0.01, 1.0),
      DoubleArray.of(0.99, 0.9),
      USD_FED_FUND_JACOBIAN_MATRIX
      )
  }

  static Curve eurUsd() {
    nodalCurve(
      Curves.zeroRates("EUR/USD", DayCounts.ACT_365F),
      //        DoubleArray.of(0.0027397260273972603, 0.005479452054794521),
      DoubleArray.of(0.0027397260273972603, 0.005479452054794521, 0.5123287671232877, 0.7643835616438356, 1.010958904109589),
      //        DoubleArray.of(0.03983036566617228, 0.039644983375683285),
      DoubleArray.of(0.03983036566617228, 0.039644983375683285, 0.03591614985285742, 0.03418576845987707, 0.033142517640865475),
      EUR_USD_JACOBIAN_MATRIX
      )
  }

  static Curve eurOIS() {
    nodalCurve(
      Curves.zeroRates("EUR-EONIA", DayCounts.ACT_365F),
      DoubleArray.of(0.0136986301369863, 1.010958904109589),
      DoubleArray.of(0.005170796706909036, 0.0051576545058682165),
      EUR_EONIA_JACOBIAN_MATRIX
      )
  }

  static Curve clpOIS(String curveName = "CLP-TNA") {
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      GBP_SONIA_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve gbpOIS() {
    def curveName = "GBP-SONIA"
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      GBP_SONIA_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve brlOIS() {
    def curveName = "BRL-CDI"
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.0027397260273972603, 0.07671232876712329, 0.16712328767123288, 0.24383561643835616, 0.3287671232876712, 0.41643835616438357, 0.4958904109589041, 0.5808219178082191, 0.6657534246575343, 0.7479452054794521, 0.8301369863013699, 0.915068493150685, 1.0, 1.4958904109589042, 2.0, 3.0054794520547947, 4.002739726027397, 5.002739726027397, 6.002739726027397, 7.002739726027397, 8.01095890410959, 9.008219178082191, 10.005479452054795, 12.008219178082191, 15.01095890410959, 20.016438356164382, 25.02191780821918, 30.019178082191782, 40.02739726027397, 50.032876712328765),
      DoubleArray.of(0.0071119307124541545, 0.007108061720100539, 0.007005896976707344, 0.006904185186986854, 0.006702609644969989, 0.006561028600107863, 0.006439706802230806, 0.006278538059769573, 0.006147403221617649, 0.006046307738025543, 0.00593535374618033, 0.005824450891690162, 0.005733531847760627, 0.005357079284064805, 0.005165162093707441, 0.005015670098133967, 0.004996062255463457, 0.005026493636395212, 0.0050870904671968115, 0.005208355446444291, 0.005339904583506066, 0.005502260221939582, 0.005664971090560569, 0.005981357895837914, 0.0063712873738967606, 0.006657544822502992, 0.006681718416544124, 0.006578338926827578, 0.006210704326641415, 0.005800396468906743),
      BRL_CDI_JACOBIAN_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 30)]
      )
  }

  static Curve eur3M() {
    nodalCurve(
      Curves.zeroRates("EUR-EURIBOR-3M", DayCounts.ACT_365F),
      DoubleArray.of(0.0136986301369863, 1.010958904109589),
      DoubleArray.of(0.005195488978156161, 0.005088069856568079),
      EUR_3M_JACOBIAN_MATRIX
      )
  }

  static Curve usd3M() {
    nodalCurve(
      Curves.zeroRates("USD-LIBOR-3M", DayCounts.ACT_365F),
      DoubleArray.of(0.0136986301369863, 1.010958904109589),
      DoubleArray.of(0.005193189952683165, 0.005094583762077739),
      USD_3M_JACOBIAN_MATRIX
      )
  }

  static Curve cny1W() {
    def curveName = "CNY-REPO-1W"
    nodalCurve(
      Curves.zeroRates(curveName, DayCounts.ACT_365F),
      DoubleArray.of(0.04657534246575343, 0.27671232876712326, 0.7753424657534247),
      DoubleArray.of(0.02199403503381226, 0.020421910583922395, 0.020262117037991814),
      CNY_REPO_1W_MATRIX,
      [CurveParameterSize.of(CurveName.of(curveName), 2)]
      )
  }

  static Curve inflation(curve) {
    InflationNodalCurve.of(
      curve,
      DoubleArray.of(1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0),
      ShiftType.SCALED
      )
  }

  static NodalCurve eurInflation() {
    nodalCurve(
      Curves.prices("EU-AI-CPI"),
      DoubleArray.of(-10.0, 1.0, 12.0, 24.0),
      DoubleArray.of(100.0, 105.8176915897558, 103.63605724359724, 105.1381499533332),
      EU_AI_CPI_JACOBIAN_MATRIX
      )
  }

  static NodalCurve usdInflation() {
    nodalCurve(
      Curves.prices("US-CPI-U"),
      DoubleArray.of(-10.0, 1.0, 12.0, 24.0),
      DoubleArray.of(100.0, 105.8176915897558, 103.63605724359724, 105.1381499533332),
      EU_AI_CPI_JACOBIAN_MATRIX
      )
  }

  static Curve aud6M() {
    nodalCurve(
      Curves.zeroRates("AUD-BBSW-6M", DayCounts.ACT_365F),
      DoubleArray.of(0.4958904109589041, 1.0027397260273974, 2.0082191780821916, 3.0054794520547947, 4.005479452054795, 5.005479452054795, 6.008219178082192, 7.013698630136986, 8.01095890410959, 9.008219178082191, 10.01095890410959, 12.01095890410959, 15.013698630136986, 20.016438356164382, 25.02191780821918, 30.027397260273972),
      DoubleArray.of(0.036669568592526534, 0.03947534468321341, 0.0409646470484226, 0.04074310351552037, 0.04099591501054812, 0.041657124294605025, 0.04247784279500873, 0.043213160011896935, 0.04386212929719635, 0.04440621077495012, 0.04479471206903159, 0.04555722274353507, 0.04594963093134579, 0.04440960115471988, 0.041276305122388356, 0.03858634199211416),
      AUD_6M_JACOBIAN_MATRIX
      )
  }

  static NodalCurve nodalCurve(
    CurveMetadata metadata,
    DoubleArray xValues,
    DoubleArray yValues,
    double[][] jacobianMatrix,
    List<CurveParameterSize> parameterSizes = CURVES_PARAMETER_SIZE) {
    var jacobianMetadata = metadata
      .withInfo(CurveInfoType.JACOBIAN, JacobianCalibrationMatrix.of(parameterSizes, DoubleMatrix.copyOf(jacobianMatrix)))
    return InterpolatedNodalCurve.of(jacobianMetadata,
      xValues,
      yValues,
      CurveInterpolators.LINEAR)
  }

  static LocalDateDoubleTimeSeries timeSeriesProvider() {
    LocalDateDoubleTimeSeries.builder()
      .put(LocalDate.parse("2017-01-01"), 100)
      .build()
  }
}
