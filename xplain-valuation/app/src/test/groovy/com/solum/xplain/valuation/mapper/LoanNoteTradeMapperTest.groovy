package com.solum.xplain.valuation.mapper

import static com.opengamma.strata.basics.date.BusinessDayAdjustment.of
import static com.opengamma.strata.basics.date.BusinessDayConventions.NO_ADJUST
import static com.opengamma.strata.basics.date.DaysAdjustment.ofBusinessDays
import static com.opengamma.strata.basics.date.HolidayCalendarIds.USNY

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention
import com.solum.xplain.valuation.messages.trade.ValuationLoanNoteTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails
import java.time.LocalDate
import spock.lang.Specification

class LoanNoteTradeMapperTest extends Specification {
  def "should correctly map to strata FixedCouponBond"() {
    setup:
    def mapper = new LoanNoteTradeMapper()
    def result = mapper.toStrataTrade(valuationRequest())

    expect:
    result.dayCount == DayCounts.ACT_365F
    result.currency == Currency.USD
    result.notional == 100000
    result.fixedRate == 0.5d
    result.legalEntityId.toString() == "CurveId~UST"
    result.securityId.toString() == "TradeId~EXT"
    result.yieldConvention == FixedCouponBondYieldConvention.DE_BONDS
    result.settlementDateOffset == ofBusinessDays(1, USNY, of(NO_ADJUST, USNY))
    result.exCouponPeriod == DaysAdjustment.NONE
    result.accrualSchedule.rollConvention == Optional.of(RollConventions.NONE)
    result.accrualSchedule.stubConvention == Optional.of(StubConvention.SMART_INITIAL)
    result.accrualSchedule.startDate == LocalDate.parse("2023-01-01")
    result.accrualSchedule.endDate == LocalDate.parse("2024-01-01")
    result.accrualSchedule.businessDayAdjustment == BusinessDayAdjustment.NONE
  }


  def valuationRequest() {
    new ValuationRequest(
      externalTradeId: "EXT",
      tradeDetails: new ValuationTradeDetails(
      startDate: LocalDate.parse("2023-01-01"),
      endDate: LocalDate.parse("2024-01-01"),
      calendar: "USNY",
      rollConvention: RollConventions.NONE,
      stubConvention: StubConvention.SMART_INITIAL,
      businessDayConvention: NO_ADJUST,
      loanNoteTradeDetails: new ValuationLoanNoteTradeDetails(
      settlementOffsetDays: 1,
      yieldConvention: FixedCouponBondYieldConvention.DE_BONDS.name(),
      reference: "UST",
      fixedRate: 0.5d
      ),
      receiveLeg: new ValuationTradeLegDetails(
      currency: "USD",
      paymentFrequency: "6M",
      notional: 100000,
      dayCount: DayCounts.ACT_365F.toString()
      )
      )
      )
  }
}
