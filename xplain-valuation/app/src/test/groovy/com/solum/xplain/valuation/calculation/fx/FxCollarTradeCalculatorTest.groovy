package com.solum.xplain.valuation.calculation.fx

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.date.DayCounts.ACT_365F
import static com.solum.xplain.valuation.calculation.TradeSamples.fxCollarTrade
import static java.time.ZoneId.systemDefault
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.collect.array.DoubleMatrix
import com.opengamma.strata.pricer.fxopt.BlackFxOptionSmileVolatilities
import com.opengamma.strata.pricer.fxopt.BlackFxOptionVolatilities
import com.opengamma.strata.pricer.fxopt.FxOptionVolatilitiesName
import com.opengamma.strata.pricer.fxopt.InterpolatedStrikeSmileDeltaTermStructure
import com.solum.xplain.valuation.calculation.CalibrationRatesSample
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.ValuationOptions
import com.solum.xplain.valuation.metrics.Metrics
import java.time.LocalDate
import spock.lang.Specification

class FxCollarTradeCalculatorTest extends Specification {

  def "should calculate FX collar metrics"() {
    setup:
    def calculator = new FxCollarTradeCalculator(
    fxCollarTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, EUR, true, underlying),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    eurUsdFxVolatility(MarketDataSample.VAL_DT)
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(43.41548117497657, 0.00000001)
    that metrics.dv01, closeTo(-0.03734316385523988, 0.00000001)
    that metrics.dv01LocalCcy, closeTo( -0.03734316385523988, 0.00000001)
    that metrics.br01, closeTo(0, 0.00000001)
    that metrics.br01LocalCcy, closeTo(0, 0.00000001)
    that metrics.optionMetrics.pvDeltaLocalCcy, closeTo(  0.007705700688019401, 0.00000001)
    that metrics.optionMetrics.pvGammaLocalCcy, closeTo(-0.0010345192862838486, 0.00000001)
    that metrics.optionMetrics.pvThetaLocalCcy, closeTo(0, 0.001)
    that metrics.optionMetrics.pvVegaLocalCcy, closeTo(-0.019526402611471098, 0.00000001)
    that metrics.optionMetrics.pvDelta, closeTo(0.007705700688019401, 0.00000001)
    that metrics.optionMetrics.pvGamma, closeTo(-0.0010345192862838486, 0.00000001)
    that metrics.optionMetrics.pvTheta, closeTo(0, 0.001)
    that metrics.optionMetrics.pvVega, closeTo(-0.019526402611471098, 0.00000001)
    that metrics.breakevenMetrics.impliedVol, closeTo( 0.16264097915558215, 0.001)
    that metrics.breakevenMetrics.parRate, closeTo(parRate, 0.00000001)
    that metrics.payLegPV, closeTo(  53.785268853619726, 0.00000001)
    that metrics.deltaFwd, closeTo(100.9119140658924, 0.00000001)
    that metrics.deltaSpot, closeTo(95.46207297352836, 0.00000001)
    that metrics.fxSpot, closeTo(fxSpot, 0.00000001)
    metrics.receiveLegPV == null
    metrics.dv01TradeValues.size() == 2

    metrics.syntheticMetrics*.deltaFwdLocalCcy == [99.9219022339694, 0.9900118319230022]
    metrics.syntheticMetrics*.deltaSpotLocalCcy == [94.5255276446787, 0.936545328849663]
    metrics.syntheticMetrics*.optionMetrics*.pvVegaLocalCcy == [0.002206747569172115, -0.021733150180643215]

    metrics.spot01TradeValues == []

    where:
    underlying  | fxSpot      | parRate
    "EUR/USD"   | 1.23885     | 1.23885
    "USD/EUR"   | 0.80720022  | 0.80720023
  }

  def "should calculate FX collar metrics with shifted rates"() {
    setup:
    def calculator = new FxCollarTradeCalculator(
    fxCollarTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, EUR, true, "EUR/USD"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.shiftedValuationRates(),
    eurUsdFxVolatility(MarketDataSample.VAL_DT)
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(43.41548117497657, 0.00000001)
    metrics.spot01TradeValues.size() == 1
    with(metrics.spot01TradeValues.get(0)) {
      currencyPair() == "EUR/USD"
      //Small spot01 difference expected since FX rates was shifted
      that spot01(), closeTo(0.005004245079476277, 0.0001)
      that spot01TradeCcy(), closeTo(0.005004245079476277, 0.0001)
      fxSpot() == 1.23885
    }
  }

  def "should calculate FX collar metrics on expiry date"() {
    setup:
    def calculator = new FxCollarTradeCalculator(
    fxCollarTrade(MarketDataSample.VAL_DT, MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, EUR, true, "EUR/USD"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    eurUsdFxVolatility(MarketDataSample.VAL_DT)
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(43.459431864587536, 0.00000001)
    that metrics.dv01, closeTo(-0.03739088706206905, 0.00000001)
    that metrics.dv01LocalCcy, closeTo( -0.03739088706206905, 0.00000001)
    that metrics.br01, closeTo(0, 0.00000001)
    that metrics.br01LocalCcy, closeTo(0, 0.00000001)
    that metrics.optionMetrics.pvDeltaLocalCcy, closeTo(  0.007636066325119762, 0.00000001)
    that metrics.optionMetrics.pvGammaLocalCcy, closeTo( 0.0, 0.00000001)
    that metrics.optionMetrics.pvThetaLocalCcy, closeTo(0, 0.001)
    that metrics.optionMetrics.pvVegaLocalCcy, closeTo(0.0, 0.00000001)
    that metrics.optionMetrics.pvDelta, closeTo(0.007636066325119762, 0.00000001)
    that metrics.optionMetrics.pvGamma, closeTo(0.0, 0.00000001)
    that metrics.optionMetrics.pvTheta, closeTo(0, 0.001)
    that metrics.optionMetrics.pvVega, closeTo(0.0, 0.00000001)
    //    that metrics.breakevenMetrics.impliedVol, closeTo( null, 0.001) // TODO
    that metrics.breakevenMetrics.parRate, closeTo(1.2388499999999991, 0.00000001)
    that metrics.payLegPV, closeTo(  53.83971716544427, 0.00000001)
    that metrics.deltaFwd, closeTo(99.99999999999999, 0.00000001)
    that metrics.deltaSpot, closeTo(94.59940766874615, 0.00000001)
    that metrics.fxSpot, closeTo( 1.23885, 0.00000001)
    metrics.receiveLegPV == null
    metrics.dv01TradeValues.size() == 2
  }

  def "should return empty metrics when FX collar after expiry"() {
    setup:
    def calculator = new FxCollarTradeCalculator(
    fxCollarTrade(MarketDataSample.VAL_DT, MarketDataSample.VAL_DT.minusDays(1)),
    ValuationOptions.newOf(EUR, EUR, true, "EUR/USD"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    eurUsdFxVolatility(MarketDataSample.VAL_DT)
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics == Metrics.builder(null).build()
  }

  static BlackFxOptionVolatilities eurUsdFxVolatility(LocalDate date) {
    InterpolatedStrikeSmileDeltaTermStructure smile =
    InterpolatedStrikeSmileDeltaTermStructure.of(
    DoubleArray.copyOf(0.1, 2.0),
    DoubleArray.copyOf(0.01, 0.02),
    DoubleArray.copyOf(0.01, 0.02),
    DoubleMatrix.of(2, 2, 0.05, 0.07, 0.5, 0.7),
    DoubleMatrix.of(2, 2, 0.04, 0.06, 0.4, 0.6),
    ACT_365F)

    return BlackFxOptionSmileVolatilities
    .builder()
    .name(FxOptionVolatilitiesName.of("EUR/USD"))
    .currencyPair(CurrencyPair.of(EUR, USD))
    .smile(smile)
    .valuationDateTime(date.atStartOfDay(systemDefault()))
    .build()
  }
}
