package com.solum.xplain.valuation.calculation.fx

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.valuation.calculation.TradeSamples.fxSwapEurReceive
import static com.solum.xplain.valuation.calculation.TradeSamples.fxSwapOvernightNodeTrade
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.product.fx.type.FxSwapConvention
import com.solum.xplain.valuation.calculation.CalibrationRatesSample
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.ValuationOptions
import com.solum.xplain.valuation.metrics.BreakevenMetrics
import com.solum.xplain.valuation.metrics.CashFlowMetrics
import org.hamcrest.Matchers
import spock.lang.PendingFeature
import spock.lang.Specification

class FxSwapTradeCalculatorTest extends Specification {

  def "should calculate FX Swap in future metrics"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT.plusYears(1)),
    ValuationOptions.newOf(EUR, USD, true, xplainUnderlying),
    () -> Map.of(),
    CalibrationRatesSample.valuationRates()
    )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())
    then:
    metrics.localCcy == EUR.getCode()
    metrics.presentValue == 3.5917710965106195
    metrics.presentValuePayLegCurrency == 2.899278440901334
    metrics.dv01 == 0.0
    metrics.dv01LocalCcy == 0.0
    metrics.payLegPV == -28.454794426790613
    metrics.receiveLegPV == 25.867994933447335
    metrics.cashFlows.size() == 4
    metrics.cashFlowMetrics == new CashFlowMetrics(0.0, 0.0, 0.0, 0.0)
    metrics.dv01TradeValues.size() == 0
    metrics.deltaFwd == 0.0
    metrics.deltaSpot == 32.04656552330123
    that metrics.breakevenMetrics.parRate, closeTo(parRate, 1e-15)
    metrics.fxSpot == spotRate

    metrics.spot01TradeValues == []

    metrics.syntheticMetrics*.cashFlows*.size() == [2, 2]
    metrics.syntheticMetrics*.presentValue == [1384.9486046321217, -1381.3568335356122]
    metrics.syntheticMetrics*.deltaFwd == [12388.5, -12388.5]
    metrics.syntheticMetrics*.deltaSpot == [12356.81367553837, -12324.76711001507]

    // tradeCcy should be ignored for FXFWD reported values
    where:
    tradeCcy | xplainUnderlying | parRate            | spotRate
    EUR      | "EUR/USD"        | 1.23885            | 1.23885
    USD      | "EUR/USD"        | 1.23885            | 1.23885
    EUR      | "USD/EUR"        | 0.8072002260160632 | 0.8072002260160632
    USD      | "USD/EUR"        | 0.8072002260160632 | 0.8072002260160632
  }

  def "should combine deltaTradeValues"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT.plusYears(1)),
    ValuationOptions.newOf(EUR, USD, true, "EUR/USD"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.sofrRates()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then: "check per-option metrics are populated"
    metrics.syntheticMetrics*.deltaFwdTradeValues*.size() == [1, 1]
    metrics.syntheticMetrics*.deltaFwdTradeValues*.get(0)*.curveName == ["EUR/USD", "EUR/USD"]
    metrics.syntheticMetrics*.deltaFwdTradeValues*.get(0)*.sensitivities*.size() == [1, 1]
    metrics.syntheticMetrics*.deltaFwdTradeValues*.get(0)*.sensitivities*.get("EUR")*.size() == [5, 5]

    def nearDeltaFwdValues = metrics.syntheticMetrics[0].deltaFwdTradeValues.get(0).sensitivities.get("EUR")*.sensitivity
    def farDeltaFwdValues = metrics.syntheticMetrics[1].deltaFwdTradeValues.get(0).sensitivities.get("EUR")*.sensitivity
    that nearDeltaFwdValues, Matchers.<Double> contains(0.0d, 324.32432432432404d, 9675.675675675675d, 0.0d, 0.0d)
    that farDeltaFwdValues, Matchers.<Double> contains(-0.0d, -0.0d, -0.0d, -444.44444444444275d, -9555.555555555558d)

    metrics.syntheticMetrics*.deltaSpotTradeValues*.size() == [1, 1]
    metrics.syntheticMetrics*.deltaSpotTradeValues*.get(0)*.curveName == ["EUR/USD", "EUR/USD"]
    metrics.syntheticMetrics*.deltaSpotTradeValues*.get(0)*.sensitivities*.size() == [1, 1]
    metrics.syntheticMetrics*.deltaSpotTradeValues*.get(0)*.sensitivities*.get("EUR")*.size() == [5, 5]

    def nearDeltaSpotValues = metrics.syntheticMetrics[0].deltaSpotTradeValues.get(0).sensitivities.get("EUR")*.sensitivity
    def farDeltaSpotValues = metrics.syntheticMetrics[1].deltaSpotTradeValues.get(0).sensitivities.get("EUR")*.sensitivity
    that nearDeltaSpotValues, Matchers.<Double> contains(0.0d, 318.5799841894853d, 9504.302861652985d, 0.0d, 0.0d)
    that farDeltaSpotValues, Matchers.<Double> contains(-0.0d, -0.0d, -0.0d, -429.93592176065044d, -9243.622317854022d)

    and: "combined metrics should be populated"
    metrics.deltaFwdTradeValues.size() == 1
    metrics.deltaFwdTradeValues.get(0).curveName == "EUR/USD"
    metrics.deltaFwdTradeValues.get(0).sensitivities.size() == 1
    metrics.deltaFwdTradeValues.get(0).sensitivities.get("EUR").size() == 5
    that metrics.deltaFwdTradeValues.get(0).sensitivities.get("EUR")*.sensitivity, Matchers.<Double> contains(
    nearDeltaFwdValues[0], nearDeltaFwdValues[1], nearDeltaFwdValues[2], farDeltaFwdValues[3], farDeltaFwdValues[4])
    metrics.deltaSpotTradeValues.size() == 1
    metrics.deltaSpotTradeValues.get(0).curveName == "EUR/USD"
    metrics.deltaSpotTradeValues.get(0).sensitivities.size() == 1
    metrics.deltaSpotTradeValues.get(0).sensitivities.get("EUR").size() == 5
    that metrics.deltaSpotTradeValues.get(0).sensitivities.get("EUR")*.sensitivity, Matchers.<Double> contains(
    nearDeltaSpotValues[0], nearDeltaSpotValues[1], nearDeltaSpotValues[2], farDeltaSpotValues[3], farDeltaSpotValues[4])
  }

  def "should combine deltaTradeValues mid-swap"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT.plusMonths(6)),
    ValuationOptions.newOf(EUR, USD, true, "EUR/USD"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.sofrRates()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then: "check per-option metrics are populated for far leg only"
    metrics.syntheticMetrics[0].deltaFwdTradeValues == null
    metrics.syntheticMetrics[1].deltaFwdTradeValues.size() == 1
    metrics.syntheticMetrics[1].deltaFwdTradeValues.get(0).curveName == "EUR/USD"
    metrics.syntheticMetrics[1].deltaFwdTradeValues.get(0).sensitivities.size() == 1
    metrics.syntheticMetrics[1].deltaFwdTradeValues.get(0).sensitivities.get("EUR").size() == 5

    metrics.syntheticMetrics[0].deltaSpotTradeValues == null
    metrics.syntheticMetrics[1].deltaSpotTradeValues.size() == 1
    metrics.syntheticMetrics[1].deltaSpotTradeValues.get(0).curveName == "EUR/USD"
    metrics.syntheticMetrics[1].deltaSpotTradeValues.get(0).sensitivities.size() == 1
    metrics.syntheticMetrics[1].deltaSpotTradeValues.get(0).sensitivities.get("EUR").size() == 5

    def farDeltaFwdValues = metrics.syntheticMetrics[1].deltaFwdTradeValues.get(0).sensitivities.get("EUR")*.sensitivity
    def farDeltaSpotValues = metrics.syntheticMetrics[1].deltaSpotTradeValues.get(0).sensitivities.get("EUR")*.sensitivity

    and: "combined metrics should be populated just from far leg"
    metrics.deltaFwdTradeValues.size() == 1
    metrics.deltaFwdTradeValues.get(0).curveName == "EUR/USD"
    metrics.deltaFwdTradeValues.get(0).sensitivities.size() == 1
    metrics.deltaFwdTradeValues.get(0).sensitivities.get("EUR").size() == 5
    that metrics.deltaFwdTradeValues.get(0).sensitivities.get("EUR")*.sensitivity, Matchers.<Double> contains(
    farDeltaFwdValues[0], farDeltaFwdValues[1], farDeltaFwdValues[2], farDeltaFwdValues[3], farDeltaFwdValues[4])
    metrics.deltaSpotTradeValues.size() == 1
    metrics.deltaSpotTradeValues.get(0).curveName == "EUR/USD"
    metrics.deltaSpotTradeValues.get(0).sensitivities.size() == 1
    metrics.deltaSpotTradeValues.get(0).sensitivities.get("EUR").size() == 5
    that metrics.deltaSpotTradeValues.get(0).sensitivities.get("EUR")*.sensitivity, Matchers.<Double> contains(
    farDeltaSpotValues[0], farDeltaSpotValues[1], farDeltaSpotValues[2], farDeltaSpotValues[3], farDeltaSpotValues[4])
  }

  def "should calculate shifted FX Swap metrics"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT.plusYears(1)),
    ValuationOptions.newOf(EUR, USD, true, "EUR/USD"),
    () -> Map.of(),
    CalibrationRatesSample.shiftedValuationRates()
    )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())
    then:
    metrics.localCcy == EUR.getCode()
    metrics.presentValue == 3.5917710965106195
    metrics.spot01TradeValues.size() == 1
    with(metrics.spot01TradeValues.get(0)) {
      currencyPair() == "EUR/USD"
      //Small spot01 difference expected since FX rates was shifted
      that spot01(), closeTo(0.001854033817849654, 0.0001)
      that spot01TradeCcy(), closeTo(0.001854033817849654, 0.0001)
      fxSpot() == 1.23885
    }
  }

  def "should calculate FX Swap metrics"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "EUR/USD"),
    () -> Map.of(),
    CalibrationRatesSample.valuationRates()
    )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())
    then:
    metrics.localCcy == EUR.getCode()
    metrics.presentValue == 0
    metrics.dv01 == 0.0
    metrics.dv01LocalCcy == 0.0
    metrics.payLegPV == 0
    metrics.receiveLegPV == 0
    metrics.cashFlows.size() == 2
    metrics.cashFlowMetrics == new CashFlowMetrics(11000.0, -10000.0, -1120.797513823305, -1388.5)
    metrics.dv01TradeValues.size() == 0
    metrics.breakevenMetrics == new BreakevenMetrics(1.23885, null)
    metrics.fxSpot == 1.23885
  }

  @PendingFeature
  def "should calculate correct FX Swap par rate"() {
    setup:
    def rates = CalibrationRatesSample.valuationRates()
    def trade = fxSwapOvernightNodeTrade(MarketDataSample.VAL_DT, 1000000, days)

    def calculator = new FxSwapTradeCalculator(trade,
    ValuationOptions.newOf(EUR, USD, true, "EUR/USD"),
    () -> Map.of(),
    rates
    )
    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    def resolvedTrade = trade.resolve(ReferenceData.standard())
    def baseCcy = trade.product.baseCurrencyAmount.currency
    def counterCcy = trade.product.counterCurrencyAmount.currency
    def spotDate = FxSwapConvention.of(trade.product.currencyPair).calculateSpotDateFromTradeDate(MarketDataSample.VAL_DT, ReferenceData.standard())

    def ratesProvider = rates.ratesProvider
    def dfBaseCcySpot = ratesProvider.discountFactor(baseCcy, spotDate)
    def dfBaseCcyReference = ratesProvider.discountFactor(baseCcy, resolvedTrade.product.paymentDate)
    def dfCounterCcySpot = ratesProvider.discountFactor(counterCcy, spotDate)
    def dfCounterCcyReference = ratesProvider.discountFactor(counterCcy, resolvedTrade.product.paymentDate)
    def expectedParRate = ratesProvider.fxRate(CurrencyPair.of(baseCcy, counterCcy))
    * (dfBaseCcyReference / dfBaseCcySpot) / (dfCounterCcyReference / dfCounterCcySpot)

    then:
    metrics.breakevenMetrics.parRate == expectedParRate

    where:
    days | _
    2    | _
    3    | _
    30   | _
  }

  def "should calculate matured Fx Swap trade metrics"() {
    setup:
    def calculator = new FxSwapTradeCalculator(
    fxSwapEurReceive(MarketDataSample.VAL_DT.minusYears(1)),
    ValuationOptions.newOf(EUR, EUR, true, "EUR/USD"),
    () -> Map.of(),
    CalibrationRatesSample.valuationRates()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    metrics.presentValue == 0
    metrics.dv01 == 0
    metrics.dv01LocalCcy == 0.0
    metrics.payLegPV == 0
    metrics.receiveLegPV == 0
    metrics.cashFlows.size() == 0
    metrics.dv01TradeValues.size() == 0
    that metrics.deltaFwd, closeTo(0.0, 0.1)
    that metrics.deltaSpot, closeTo(0.0, 0.1)
    metrics.breakevenMetrics == new BreakevenMetrics(null, null)
    metrics.fxSpot == 1.23885 // Technically, there is still a spot rate but no one cares
  }
}
