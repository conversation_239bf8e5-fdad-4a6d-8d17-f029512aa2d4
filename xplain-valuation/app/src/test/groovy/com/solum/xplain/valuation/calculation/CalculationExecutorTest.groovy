package com.solum.xplain.valuation.calculation

import static com.solum.xplain.valuation.messages.trade.constant.ValuationProductType.INFLATION

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.valuation.common.ValuationError
import com.solum.xplain.valuation.executor.TradeValuationExecutor
import com.solum.xplain.valuation.executor.TradeValuationExecutorResolver
import com.solum.xplain.valuation.executor.TradeValuationExecutorResolverFactory
import com.solum.xplain.valuation.messages.metrics.ValuationMetricsDto
import com.solum.xplain.valuation.messages.metrics.ValuationResponse
import com.solum.xplain.valuation.messages.metrics.ValuationStatus
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.constant.ValuationProductType
import com.solum.xplain.valuation.metrics.BsonValuationMetricsMapper
import com.solum.xplain.valuation.metrics.Metrics
import com.solum.xplain.valuation.metrics.MetricsMapper
import com.solum.xplain.valuation.refdata.ReferenceDataCacheService
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class CalculationExecutorTest extends Specification {
  TradeValuationExecutorResolverFactory resolverFactory = Mock(TradeValuationExecutorResolverFactory)
  MetricsMapper metricsMapper = Mock()
  BsonValuationMetricsMapper bsonValuationMetricsMapper = Mock()
  ReferenceDataCacheService referenceDataCacheService = Mock()
  CalculationExecutor executor = new CalculationExecutor(resolverFactory, metricsMapper, referenceDataCacheService, bsonValuationMetricsMapper, "version")

  def "should correctly execute"() {
    setup:
    def request = new ValuationRequest(productType: INFLATION, tradeId: "entityId", calculationId: "calcId", simulationId: "simId", valuationDate: LocalDate.now(), discountingCcy: "EUR")
    def calculator = Mock(TradeCalculator)
    referenceDataCacheService.getReferenceData(_ as ValuationRequest) >> Mock(ReferenceData)
    calculator.calculate(_ as ReferenceData) >> Metrics.builder(0d).build()
    def tradeValuationExecutor = TradeValuationExecutor.newOf(calculator)

    def resolver = Mock(TradeValuationExecutorResolver)
    resolver.executor(request) >> Either.right(tradeValuationExecutor)
    1 * resolverFactory.getResolver(INFLATION) >> Either.right(resolver)

    def valuationMetrics = new ValuationMetricsDto()
    def valuationMetricsBytes = [1] as byte[]

    def response = new ValuationResponse(
    productType: INFLATION,
    valuationMetricsBytes: valuationMetricsBytes,
    warnings: [],
    status: ValuationStatus.OK,
    calculationId: "calcId",
    tradeId: "entityId",
    appVersion: "version",
    simulationId: "simId",
    discountingCcy: "EUR",
    valuationDate: LocalDate.now()
    )
    when:
    def result = executor.valuate(request)
    then:
    1 * metricsMapper.toValuationMetrics(_ as Metrics) >> valuationMetrics
    1 * bsonValuationMetricsMapper.toBsonBytes(_ as ValuationMetricsDto) >> valuationMetricsBytes
    result == response
  }

  def "should correctly execute with warnings"() {
    setup:
    def request = new ValuationRequest(productType: INFLATION, tradeId: "entityId", calculationId: "calcId", discountingCcy: "USD")
    def calculator = Mock(TradeCalculator)
    referenceDataCacheService.getReferenceData(_ as ValuationRequest) >> Mock(ReferenceData)
    calculator.calculate(_ as ReferenceData) >> Metrics.builder(0d).build()
    def tradeValuationExecutor = TradeValuationExecutor.newOf(calculator, _ -> ["ERROR"])

    def resolver = Mock(TradeValuationExecutorResolver)
    resolver.executor(request) >> Either.right(tradeValuationExecutor)
    1 * resolverFactory.getResolver(INFLATION) >> Either.right(resolver)

    def valuationMetrics = new ValuationMetricsDto()
    def valuationMetricsBytes = [1] as byte[]

    def response = new ValuationResponse(
    productType: INFLATION,
    valuationMetricsBytes: valuationMetricsBytes,
    warnings: ["ERROR"],
    status: ValuationStatus.OK,
    calculationId: "calcId",
    discountingCcy: "USD",
    tradeId: "entityId",
    appVersion: "version"
    )
    when:
    def result = executor.valuate(request)
    then:
    1 * metricsMapper.toValuationMetrics(_ as Metrics) >> valuationMetrics
    1 * bsonValuationMetricsMapper.toBsonBytes(_ as ValuationMetricsDto) >> valuationMetricsBytes
    result == response
  }

  def "should return error when calculator throws error"() {
    setup:
    def errorMessage = "ERR"
    def request = new ValuationRequest(productType: INFLATION, tradeId: "entityId", calculationId: "calcId", tradeDetails: new ValuationTradeDetails())
    def calculator = Mock(TradeCalculator)
    referenceDataCacheService.getReferenceData(_ as ValuationRequest) >> Mock(ReferenceData)
    calculator.calculate(_ as ReferenceData) >> { throw new IllegalArgumentException(errorMessage) }
    def tradeValuationExecutor = TradeValuationExecutor.newOf(calculator)
    def resolver = Mock(TradeValuationExecutorResolver)
    resolver.executor(request) >> Either.right(tradeValuationExecutor)
    1 * resolverFactory.getResolver(INFLATION) >> Either.right(resolver)
    def response = new ValuationResponse(
    productType: INFLATION,
    errorMessage: errorMessage,
    status: ValuationStatus.ERROR,
    calculationId: "calcId",
    tradeId: "entityId",
    appVersion: "version",
    tradeDetails: new ValuationTradeDetails()
    )
    when:
    def result = executor.valuate(request)
    then:
    0 * metricsMapper.toValuationMetrics(_)
    result == response
  }

  def "should return error when valuation throws error"() {
    setup:
    def errorMessage = "Something was missing"
    def request = new ValuationRequest(productType: INFLATION, tradeId: "entityId", calculationId: "calcId", tradeDetails: new ValuationTradeDetails())
    referenceDataCacheService.getReferenceData(_ as ValuationRequest) >> Mock(ReferenceData)
    def resolver = Mock(TradeValuationExecutorResolver)
    resolver.executor(request) >> Either.left(new ValuationError(errorMessage))
    1 * resolverFactory.getResolver(_ as ValuationProductType) >> Either.right(resolver)

    def response = new ValuationResponse(
    productType: INFLATION,
    tradeDetails: new ValuationTradeDetails(),
    errorMessage: errorMessage,
    status: ValuationStatus.ERROR,
    calculationId: "calcId",
    tradeId: "entityId",
    appVersion: "version"
    )
    when:
    def result = executor.valuate(request)
    then:
    0 * metricsMapper.toValuationMetrics(_)
    result == response
  }
}
