package com.solum.xplain.valuation.calculation.breakeven

import static com.solum.xplain.valuation.calculation.MarketDataSample.VAL_DT
import static com.solum.xplain.valuation.calculation.TradeSamples.swapFloatFloatTrade
import static com.solum.xplain.valuation.calculation.TradeSamples.swapTrade
import static com.solum.xplain.valuation.calculation.TradeSamples.swapTradeInflation
import static com.solum.xplain.valuation.calculation.TradeSamples.swapTradeInflationActDayCount
import static com.solum.xplain.valuation.calculation.breakeven.SwapParRateCalculationsFactory.parRateCalculation

import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.product.swap.CompoundingMethod
import java.time.Period
import spock.lang.Specification

class SwapParRateCalculationsFactoryTest extends Specification {

  def "should correctly return fixed swap trade par rate calculation"() {
    setup:
    def calculation = parRateCalculation(swapTrade(VAL_DT))
    expect:
    calculation instanceof FixedLegSwapParRateCalculation
  }

  def "should correctly return 1/1 daycount inflation par rate calculation"() {
    setup:
    def calculation = parRateCalculation(swapTradeInflation(VAL_DT))
    expect:
    calculation instanceof FixedLegSwapParRateCalculation
  }

  def "should correctly return float float swap trade par rate calculation"() {
    setup:
    def calculation = parRateCalculation(swapFloatFloatTrade(VAL_DT))
    expect:
    calculation instanceof SpreadLegSwapParRateCalculation
  }

  def "should correctly return inflation with custom daycount par rate calculation"() {
    setup:
    def calculation = parRateCalculation(swapTradeInflationActDayCount(VAL_DT))
    expect:
    calculation instanceof IterativeFixedLegParRateCalculation
  }

  def "should correctly return non-compounded inflation with custom daycount par rate calculation"() {
    setup:
    def calculation = parRateCalculation(swapTradeInflationActDayCount(VAL_DT, Frequency.TERM, Period.ofMonths(3), CompoundingMethod.NONE, Frequency.TERM))
    expect:
    calculation instanceof FixedInflationParRateCalculation
  }
}
