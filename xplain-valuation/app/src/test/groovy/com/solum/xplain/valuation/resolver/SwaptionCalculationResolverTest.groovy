package com.solum.xplain.valuation.resolver

import com.opengamma.strata.basics.index.IborIndices
import com.solum.xplain.extensions.validation.TradeMarketDataValidator
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.TradeSamples
import com.solum.xplain.valuation.calculation.swap.SwaptionTradeCalculator
import com.solum.xplain.valuation.calibration.CalibrationCacheService
import com.solum.xplain.valuation.calibration.vols.VolatilitiesProvider
import com.solum.xplain.valuation.calibration.vols.VolatilityProviderFactory
import com.solum.xplain.valuation.executor.SwaptionValuationExecutorResolver
import com.solum.xplain.valuation.mapper.SwaptionTradeMapper
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates
import com.solum.xplain.valuation.messages.calibration.vols.SurfaceForCalculation
import com.solum.xplain.valuation.messages.trade.ValuationRequest
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo
import io.atlassian.fugue.Either
import spock.lang.Specification

class SwaptionCalculationResolverTest extends Specification {
  CalibrationCacheService cacheService = Mock()
  SwaptionTradeMapper tradeMapper = Mock()
  VolatilityProviderFactory factory = Mock()

  SwaptionValuationExecutorResolver resolver = new SwaptionValuationExecutorResolver(cacheService, tradeMapper, factory)

  def "should reolve swaption calculator"() {
    setup:
    def surface = Mock(SurfaceForCalculation)
    def request = new ValuationRequest(
      tradeDetails: new ValuationTradeDetails(info: new ValuationTradeInfo(tradeCurrency: "EUR")),
      reportingCurrency: "USD"
      )
    1 * tradeMapper.toStrataTrade(request) >> TradeSamples.cashSwaptionTrade(MarketDataSample.VAL_DT)
    1 * cacheService.calibrationRates(request) >> Either.right(Mock(ValuationCurveRates))
    1 * cacheService.volatilities(request, IborIndices.EUR_EURIBOR_3M) >> Either.right(surface)
    1 * factory.resolveProvider(surface) >> Either.right(Mock(VolatilitiesProvider))

    when:
    def result = resolver.executor(request)
    then:
    result.getOrNull().calculator instanceof SwaptionTradeCalculator
    result.getOrNull().validator == TradeMarketDataValidator.EMPTY_VALIDATOR
  }
}
